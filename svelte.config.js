import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

const mode = process.env.NODE_ENV;
const nodeEnv = process.env.NODE_ENV || 'production';
const isProduction = nodeEnv === 'production';
const gitTag = process.env.GIT_TAG || 'latest';
if (mode === 'production' && gitTag === "latest") {
	console.warn('⚠️GIT TAG 为 latest, 无法出发流水线，docker 中是通过 arg 传入的');
	process.exit(1);
}

console.log("✅\x1b[36mGIT TAG:\x1b[0m", "\x1b[32m" + gitTag + "\x1b[0m");

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	// for more information about preprocessors
	preprocess: vitePreprocess(),
	lintOnSave: mode === 'development',
	kit: {
		// adapter-auto only supports some environments, see https://kit.svelte.dev/docs/adapter-auto for a list.
		// If your environment is not supported or you settled on a specific environment, switch out the adapter.
		// See https://kit.svelte.dev/docs/adapters for more information about adapters.
		adapter: adapter({
			pages: 'build',
			assets: 'build',
			fallback: 'index.html'
		}),
		// 为CDN设置基础路径
		...(mode === 'production' ? {
			paths: {
				assets: isProduction ? 'https://z-cdn.chatglm.cn/z-ai/frontend/' + gitTag : ''
			}
		} : {}),

	},
	vitePlugin: {
		// inspector: {
		//  toggleKeyCombo: 'meta-shift', // Key combination to open the inspector
		//  holdMode: false, // Enable or disable hold mode
		//  showToggleButton: 'always', // Show toggle button ('always', 'active', 'never')
		//  toggleButtonPos: 'bottom-right' // Position of the toggle button
		// }
	},
	onwarn: (warning, handler) => {
		const { code } = warning;
		if (code === 'css-unused-selector') return;

		handler(warning);
	}
};

export default config;