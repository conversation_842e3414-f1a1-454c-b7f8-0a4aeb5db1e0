import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

// /** @type {import('vite').Plugin} */
// const viteServerConfig = {
// 	name: 'log-request-middleware',
// 	configureServer(server) {
// 		server.middlewares.use((req, res, next) => {
// 			res.setHeader('Access-Control-Allow-Origin', '*');
// 			res.setHeader('Access-Control-Allow-Methods', 'GET');
// 			res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
// 			res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
// 			next();
// 		});
// 	}
// };
const gitTag = process.env.GIT_TAG || 'latest';
const isStaging = gitTag.includes("staging-fe-")
console.log("是否开启 sourcemap", isStaging)


export default defineConfig({
	plugins: [
		sveltekit(),
		// viteStaticCopy({
		// 	targets: [
		// 		{
		// 			src: 'node_modules/onnxruntime-web/dist/*.jsep.*',

		// 			dest: 'wasm'
		// 		}
		// 	]
		// })
	],
	define: {
		APP_VERSION: JSON.stringify(process.env.npm_package_version),
		APP_BUILD_HASH: JSON.stringify(process.env.APP_BUILD_HASH || 'dev-build'),
		GIT_TAG: JSON.stringify(process.env.GIT_TAG || 'latest'),
	},
	build: {
		sourcemap: isStaging
	},
	worker: {
		format: 'es'
	},
	server: {
		open: true,
		proxy: {
			'/api': {
				target: "http://localhost:8080",
				changeOrigin: true,
			}
		},
		allowedHosts: ['niubi.space.chatglm.site'],
		watch: {
			ignored: ['**/backend/**']
		}
	}
});
