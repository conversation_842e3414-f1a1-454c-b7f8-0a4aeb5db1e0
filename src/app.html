<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" type="image/png" sizes="512x512"
			href="https://z-cdn.chatglm.cn/z-ai/frontend/static/logo.png" />
		<link rel="shortcut icon" href="https://z-cdn.chatglm.cn/z-ai/static/favicon.ico">
		<link rel="apple-touch-icon" sizes="512x512" href="https://z-cdn.chatglm.cn/z-ai/frontend/static/apple-touch-icon.png">
		<meta name="apple-mobile-web-app-title" content="Chat with Z.ai - Free AI for Presentations, Writing & Coding" />
		<meta name="description" content="Start a free chat with your AI assistant. Tell Z.ai what you need—a stunning presentation, professional-grade writing, or a complex code script—and get instant results.">
		<meta name="keywords" content="z.ai, zai, z chat, AI assistant, free AI, AI chatbot, ai chat, chat ai, AI tool, AI model, AI helper, code agent, AI agent, AI workflow, AI copilot, LLM, reasoning, AI inference engine, local LLM, open source LLM, AI PPT, AI Slides, AI Presentation, AI presentation maker, free AI presentation maker, best AI presentation maker, AI slides maker, AI generates presentation, AI for code generation, ai writer, AI writing assistant, code writer ai, ai code generator, ai content generator free, artificial intelligence search engine, deep research, AI, chat, ai app, ask ai, what is ai, artificial intelligence, artificial intelligence writing tools, ai for writing research papers, artificial intelligence code generator, ai code generator python, best ai code generator, artificial intelligence email writing, ChatGPT, ChatGPT alternative, chat gpt free, openai, openai chat, gpt chat, Claude AI, Gemini, Poe AI, YouChat, Jasper AI, grok, xai, deepseek, qwen, doubao, GLM, AutoGLM, GLM-Experimental, GLM-4, GLM-4-32B, Z1-32B, Z1-Rumination, AI助手, AI聊天, AI聊天机器人, 中文AI, Chinese AI, Chinese LLM, Chinese open source, 文心一言, 豆包, 千问, AI写作, AI做PPT, AI搜问答, AI写新闻, AI写代码, AI for students, AI for marketers, developer AI tool, AI for content creation, AI assistant for researchers, overcome writer's block, automate tedious tasks, summarize text AI, explain code tool, presentation idea generator, free ChatGPT alternative, alternative to Poe AI, Jasper AI free alternative, sites like ChatGPT, how to make a ppt with AI, what is the best free AI writer, can AI write python code, free ai chat no login, 学生AI助手, 市场营销AI, 程序员AI工具, 内容创作AI, 科研AI助手, 写作灵感AI, AI自动总结, 代码解释工具, PPT思路生成, 工作效率提升, 免费ChatGPT, 类似ChatGPT的AI, ChatGPT替代品, Claude AI免费版, 如何用AI做PPT, AI可以写代码吗, 哪个AI写作工具免费, AI聊天需要登录吗">
		
		<link rel="manifest" href="https://z-cdn.chatglm.cn/z-ai/frontend/static/manifest.json" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover"
		/>
		<meta name="theme-color" content="#F4F6F8" />
		<link
			rel="search"
			type="application/opensearchdescription+xml"
			title="Chat with Z.ai - Free AI for Presentations, Writing & Coding"
			href="https://z-cdn.chatglm.cn/z-ai/frontend/static/opensearch.xml"
		/>
		<script async src="https://www.googletagmanager.com/gtag/js?id=__VITE_GA_MEASUREMENT_ID__"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() { dataLayer.push(arguments); }
			gtag('js', new Date());

			gtag('config', '__VITE_GA_MEASUREMENT_ID__');
		</script>


		<script>
					let metrics = {}
					const observer = new PerformanceObserver((list) => {
						const entries = list.getEntries();

						entries.forEach((entry) => {
							// 根据不同的entry类型处理不同的性能指标
							switch (entry.entryType) {
								case 'navigation':
									{
										const navEntry = entry;
										metrics = {
											...metrics,
											dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
											response: navEntry.responseEnd - navEntry.responseStart,
											dom_parse: navEntry.domInteractive - navEntry.responseEnd,
											dom_ready:
												navEntry.domContentLoadedEventEnd - navEntry.fetchStart,
											resource: navEntry.domComplete - navEntry.domContentLoadedEventEnd,
											first_interactive: navEntry.domInteractive - navEntry.fetchStart,
											first_byte: navEntry.responseStart - navEntry.domainLookupStart,
											loaded: navEntry.loadEventEnd - navEntry.fetchStart
										};
										sessionStorage.setItem('performance', JSON.stringify(metrics));
										// 发送性能指标到阿里云日志服务
									}
									break;

								case 'paint':
									if (entry.name === 'first-paint') {
										metrics = {
											...metrics,
											first_paint: entry.startTime,
										};
										sessionStorage.setItem('performance', JSON.stringify(metrics));
									}
									if (entry.name === 'first-contentful-paint') {
										metrics = {
											...metrics,
											first_contentful_paint: entry.startTime,
										};
										sessionStorage.setItem('performance', JSON.stringify(metrics));
									}
									break;
							}
						});
					});

					// 观察的性能指标类型
					observer.observe({
						entryTypes: ['navigation', 'paint']
					});
		</script>

		<script>
			function resizeIframe(obj) {
				obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 'px';
			}
		</script>

		<script>
			// On page load or when changing themes, best to add inline in `head` to avoid FOUC
			const initTheme = () => {
				const metaThemeColorTag = document.querySelector('meta[name="theme-color"]');
				// 取用户浏览器设置的主题
				const prefersTheme = window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';

				if (!localStorage?.theme) {
					localStorage.theme = prefersTheme;
				}

				if (localStorage.theme === 'system') {
					document.documentElement.classList.add(prefersTheme);
					metaThemeColorTag.setAttribute('content', prefersTheme === 'light' ? '#F4F6F8' : '#141618');
				} else if (localStorage.theme === 'light') {
					document.documentElement.classList.add('light');
					metaThemeColorTag.setAttribute('content', '#F4F6F8');
				} else {
					document.documentElement.classList.add('dark');
					metaThemeColorTag.setAttribute('content', '#141618');
				}

				window.matchMedia('(prefers-color-scheme: dark)').addListener((e) => {
					if (localStorage.theme === 'system') {
						if (e.matches) {
							document.documentElement.classList.add('dark');
							document.documentElement.classList.remove('light');
							metaThemeColorTag.setAttribute('content', '#141618');
						} else {
							document.documentElement.classList.add('light');
							document.documentElement.classList.remove('dark');
							metaThemeColorTag.setAttribute('content', '#F4F6F8');
						}
					}
				});

				function setSplashImage() {
					const logo = document.getElementById('logo');
					const isDarkMode = document.documentElement.classList.contains('dark');
					const splashContainer = document.getElementById('splash-screen');

					if (isDarkMode) {
						const darkImage = new Image();
						darkImage.src = '/static/logoDark.svg';
						splashContainer.style.backgroundColor = '#141618';

						darkImage.onload = () => {
							logo.src = '/static/logoDark.svg';
							logo.style.filter = ''; // Ensure no inversion is applied if splash-dark.png exists
						};

						darkImage.onerror = () => {
							logo.style.filter = 'invert(1)'; // Invert image if splash-dark.png is missing
						};
					} else {
						splashContainer.style.backgroundColor = '#F4F6F8';
					}
				}

				// Runs after classes are assigned
				window.addEventListener('DOMContentLoaded', setSplashImage, { once: true });
			};
			initTheme();
		</script>

		<title>Chat with Z.ai - Free AI for Presentations, Writing & Coding</title>

		<script>
			// 炫酷的控制台介绍 - 根据语言显示
			(function() {
				// 检测用户语言偏好
				const userLang = navigator.language || navigator.userLanguage;
				const isChinese = userLang.startsWith('zh') || userLang.includes('CN') || userLang.includes('TW') || userLang.includes('HK');
				
				// 共同的 ASCII Logo
				const logoLines = [
					'%c███████╗     █████╗ ██╗',
					'%c╚══███╔╝    ██╔══██╗██║',
					'%c  ███╔╝     ███████║██║',
					'%c ███╔╝      ██╔══██║██║',
					'%c███████╗ ██╗██║  ██║██║',
					'%c╚══════╝ ╚═╝╚═╝  ╚═╝╚═╝'
				];
				const logoColors = [
					'color: #ff6b6b; font-family: monospace; font-size: 12px;',
					'color: #4ecdc4; font-family: monospace; font-size: 12px;',
					'color: #45b7d1; font-family: monospace; font-size: 12px;',
					'color: #f9ca24; font-family: monospace; font-size: 12px;',
					'color: #ff9ff3; font-family: monospace; font-size: 12px;',
					'color: #6c5ce7; font-family: monospace; font-size: 12px;'
				];

				if (isChinese) {
					// 中文版本
					console.log('%c🚀 欢迎来到 Z.ai 🚀', 'color: #00d4ff; font-size: 20px; font-weight: bold; text-shadow: 0 0 10px #00d4ff;');
					logoLines.forEach((line, index) => console.log(line, logoColors[index]));
					console.log('');
					console.log('%c✨ 基于开源GLM模型的智能AI助手', 'color: #a29bfe; font-size: 14px; font-weight: bold;');
					console.log('%c🧠 支持文本生成、推理和深度研究', 'color: #fd79a8; font-size: 12px;');
					console.log('%c🌍 为中英文用户量身定制', 'color: #00b894; font-size: 12px;');
					console.log('%c💡 免费开源的ChatGPT替代方案', 'color: #fdcb6e; font-size: 12px;');
					console.log('');
					console.log('%c---', 'color: #ddd;');
					console.log('%c由 ❤️ 和大量 ☕ 驱动', 'color: #e84393; font-size: 11px; font-style: italic;');
					console.log('');
					console.log('%c🚀 加入我们！我们正在招聘优秀人才！', 'color: #00b894; font-size: 13px; font-weight: bold;');
					console.log('%c💼 查看职位: https://zhipu-ai.jobs.feishu.cn/s/stmyTm5lxaU', 'color: #0984e3; font-size: 12px; text-decoration: underline;');
				} else {
					// 英文版本
					console.log('%c🚀 Welcome to Z.ai 🚀', 'color: #00d4ff; font-size: 20px; font-weight: bold; text-shadow: 0 0 10px #00d4ff;');
					logoLines.forEach((line, index) => console.log(line, logoColors[index]));
					console.log('');
					console.log('%c✨ Advanced AI assistant powered by open-source GLM models', 'color: #a29bfe; font-size: 14px; font-weight: bold;');
					console.log('%c🧠 Supports text generation, reasoning, and deep research', 'color: #fd79a8; font-size: 12px;');
					console.log('%c🌍 Tailored for both English and Chinese users', 'color: #00b894; font-size: 12px;');
					console.log('%c💡 Free and open-source ChatGPT alternative', 'color: #fdcb6e; font-size: 12px;');
					console.log('');
					console.log('%c---', 'color: #ddd;');
					console.log('%cPowered by ❤️ and lots of ☕', 'color: #e84393; font-size: 11px; font-style: italic;');
					console.log('');
					console.log('%c🚀 Join us! We are hiring talented people!', 'color: #00b894; font-size: 13px; font-weight: bold;');
					console.log('%c💼 View jobs: https://zhipu-ai.jobs.feishu.cn/s/stmyTm5lxaU', 'color: #0984e3; font-size: 12px; text-decoration: underline;');
				}
			})();
		</script>

		%sveltekit.head%
	</head>

	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>

		<div
			id="splash-screen"
			style="position: fixed; z-index: 100; top: 0; left: 0; width: 100%; height: 100%; background-color: #F4F6F8"
		>
			<style type="text/css" nonce="">
				html {
					overflow-y: scroll !important;
				}
			</style>

			<img
				id="logo"
				style="
					position: absolute;
					width: auto;
					height: 6rem;
					top: 44%;
					left: 50%;
					transform: translateX(-50%);
				"
				src="https://z-cdn.chatglm.cn/z-ai/frontend/static/logoLight.svg"
			/>

			<div
				style="
					position: absolute;
					top: 33%;
					left: 50%;

					width: 24rem;
					transform: translateX(-50%);

					display: flex;
					flex-direction: column;
					align-items: center;
				"
			>

				<div style="position: relative; width: 24rem; margin-top: 0.5rem">
					<div
						id="progress-background"
						style="
							position: absolute;
							width: 100%;
							height: 0.75rem;

							border-radius: 9999px;
							background-color: #F4F6F8;
						"
					></div>

					<div
						id="progress-bar"
						style="
							position: absolute;
							width: 0%;
							height: 0.75rem;
							border-radius: 9999px;
							background-color: #fff;
						"
						class="bg-white"
					></div>
				</div>
			</div>

			<!-- <span style="position: absolute; bottom: 32px; left: 50%; margin: -36px 0 0 -36px">
				Footer content
			</span> -->
		</div>
	</body>
</html>

<style type="text/css" nonce="">
	html {
		overflow-y: hidden !important;
	}

	#splash-screen {
		background: #F4F6F8;
	}

	html.dark #splash-screen {
		background: #141618;
	}

	#progress-background {
		display: none;
	}

	#progress-bar {
		display: none;
	}

	html.her #logo {
		display: none;
	}

	html.her #progress-background {
		display: block;
	}

	html.her #progress-bar {
		display: block;
	}

	@media (max-width: 24rem) {
		html.her #progress-background {
			display: none;
		}

		html.her #progress-bar {
			display: none;
		}
	}

	@keyframes pulse {
		50% {
			opacity: 0.65;
		}
	}

	.animate-pulse-fast {
		animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}
</style>
