<script>
	import { onMount, getContext, tick, onDestroy } from 'svelte';
	import { page } from '$app/stores';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { getBackendConfig } from '$lib/apis';
	import { WEBUI_NAME, config, mobile, userTheme, user } from '$lib/stores';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import Email from '$lib/components/icons/Email.svelte';
	import { resendVerificationEmail, verifyEmailAndToken, finishSignup } from '$lib/apis/auths';
	import { generateInitialsImage } from '$lib/utils';
	const i18n = getContext('i18n');

	// 从 URL 参数中获取数据
	let email = '';
	let username = '';
	let token = '';

	// 页面状态
	let loading = true;
	let paramsError = false;
	let tokenValid = false;
	let registrationComplete = false;

	// 密码相关
	let password = '';
	let confirmPassword = '';
	let passwordError = '';
	let passwordVisible = false;
	let confirmPasswordVisible = false;

	// 重发验证邮件相关
	let countdown = 0;
	let countdownInterval;
	let canResend = true;

	// 从查询参数中获取值
	const getQueryParam = (key) => {
		const querystring = window.location.search;
		const urlParams = new URLSearchParams(querystring);
		return urlParams.get(key);
	};
	const setSessionUser = async (sessionUser) => {
		if (sessionUser) {
			console.log(sessionUser);
			toast.success($i18n.t(`You're now logged in.`));
			if (sessionUser.token) {
				localStorage.token = sessionUser.token;
			}

			// $socket.emit('user-join', { auth: { token: sessionUser.token } });
			await user.set(sessionUser);
			await config.set(await getBackendConfig());
		}
	};

	// 验证 token
	const verifyToken = async () => {
		loading = true;

		try {
			const res = await verifyEmailAndToken(email, username, token);

			if (res.success) {
				tokenValid = true;
			} else {
				toast.error($i18n.t('Token is invalid or expired'));
			}
		} catch (error) {
			toast.error(
				$i18n.t('Failed to verify token: {{error}}', {
					error: error.message || error.detail || 'error to verify token'
				})
			);
		} finally {
			loading = false;
		}
	};

	// 重新发送验证邮件
	const resendVerification = async () => {
		if (!canResend) return;

		canResend = false;
		countdown = 60;

		try {
			const sso_redirect = getQueryParam('sso_redirect');
			const response = await resendVerificationEmail(email, username, sso_redirect);
			if (response.success) {
				toast.success($i18n.t('Verification email has been resent to {{email}}', { email }));
				startCountdown();
			} else {
				toast.error(
					$i18n.t('Failed to send: {{error}}', { error: response.message || response.detail })
				);
			}
		} catch (error) {
			toast.error(
				$i18n.t('Failed to send: {{error}}', { error: error.message || error.detail || error })
			);
		}
	};

	// 开始倒计时
	const startCountdown = () => {
		countdownInterval = setInterval(() => {
			countdown--;
			if (countdown <= 0) {
				clearInterval(countdownInterval);
				canResend = true;
			}
		}, 1000);
	};

	// 完成注册
	const completeRegistration = async () => {
		// 验证密码
		if (!password) {
			passwordError = $i18n.t('Password is required');
			return;
		}

		if (password.length < 8) {
			passwordError = $i18n.t('Password must be at least 8 characters');
			return;
		}

		if (password !== confirmPassword) {
			passwordError = $i18n.t('Passwords do not match');
			return;
		}

		passwordError = '';
		loading = true;

		try {
			const sso_redirect = getQueryParam('sso_redirect');
			const res = await finishSignup(
				email,
				username,
				token,
				password,
				generateInitialsImage(username),
				sso_redirect
			);
			console.log('RES', res);
			if (!res.success) {
				toast.error(
					$i18n.t('Failed to complete registration: {{error}}', {
						error: res.message || res.detail
					})
				);
				setTimeout(() => {
					window.location.reload();
				}, 2000);
			} else {
				toast.success($i18n.t('Registration completed successfully'));
				registrationComplete = true;
				if (res.user) {
					setSessionUser(res.user);
				}
				// 注册成功后跳转到登录页
				setTimeout(() => {
					const sso_redirect = getQueryParam('sso_redirect');
					if (sso_redirect) {
						try {
							const objUrl = new URL(sso_redirect);
							window.location.href = objUrl.toString();
						} catch (error) {
							console.log('SSO_REDIRECT_ERROR', error);
							goto('/');
							return;
						}
					} else {
						const redirectPath = getQueryParam('redirect') || '/';
						goto(redirectPath);
					}
				}, 2000);
			}
		} catch (error) {
			toast.error(
				$i18n.t('Failed to complete registration: {{error}}', {
					error: error.message || error.detail || error
				})
			);
			setTimeout(() => {
				window.location.reload();
			}, 1000);
		} finally {
			loading = false;
		}
	};

	// 切换密码可见性
	const togglePasswordVisibility = () => {
		passwordVisible = !passwordVisible;
	};

	// 切换确认密码可见性
	const toggleConfirmPasswordVisibility = () => {
		confirmPasswordVisible = !confirmPasswordVisible;
	};

	// 返回登录页
	const backToLogin = () => {
		goto('/auth');
	};

	onMount(async () => {
		// 获取 URL 参数
		email = getQueryParam('email') || '';
		username = getQueryParam('username') || '';
		token = getQueryParam('token') || '';

		// 验证参数
		if (!email || !username || !token) {
			paramsError = true;
			loading = false;
		} else {
			// 验证 token
			await verifyToken();
		}

		// 设置页面图标的暗黑模式适配
		await tick();
		const logo = document.getElementById('logo');

		if (logo) {
			const isDarkMode = document.documentElement.classList.contains('dark');

			if (isDarkMode) {
				const darkImage = new Image();
				darkImage.src = '/static/favicon-dark.png';

				darkImage.onload = () => {
					logo.src = '/static/favicon-dark.png';
					logo.style.filter = '';
				};

				darkImage.onerror = () => {
					logo.style.filter = 'invert(1)';
				};
			}
		}
	});

	// 组件销毁时清除定时器
	onDestroy(() => {
		if (countdownInterval) {
			clearInterval(countdownInterval);
		}
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Email Verification')} - {`${$WEBUI_NAME}`}
	</title>
</svelte:head>

<div class="w-full h-screen max-h-[100dvh] text-black dark:text-white relative">
	<div class="w-full h-full absolute top-0 left-0 bg-[#F5F6F8] dark:bg-[#141618]"></div>
	<div class="w-full absolute top-0 left-0 right-0 h-8 drag-region" />

	<div class="fixed bg-transparent min-h-screen w-full flex justify-center z-50">
		<div class="w-full sm:max-w-md {$mobile ? 'px-8' : ''} min-h-screen flex flex-col">
			<div class="my-auto w-full">
				<div
					class={`dark:text-gray-100 rounded-2xl ${$mobile ? '' : 'bg-white dark:bg-[#26282A] border-b-2 border-black/10 p-10'}`}
				>
					<div class="flex flex-col items-center justify-center">
						<!-- 图标 -->
						<div class="mb-6">
							<img
								id="logo"
								class="size-[79px]"
								src="/static/{$userTheme === 'light' ? 'loginLight.png' : 'loginDark.png'}"
								alt="logo"
							/>
						</div>

						<!-- 加载中状态 -->
						{#if loading}
							<div class="flex flex-col items-center space-y-4">
								<Spinner size="lg" />
								<p class="text-base text-gray-700 dark:text-gray-300">
									{$i18n.t('Verifying your email...')}
								</p>
							</div>
						{/if}

						<!-- 参数错误状态 -->
						{#if !loading && paramsError}
							<div class="text-center space-y-4">
								<div class="text-2xl font-bold text-lineGradient mb-2">
									{$i18n.t('Invalid Parameters')}
								</div>
								<p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
									{$i18n.t(
										'The verification link is invalid or incomplete. Please check your email for the correct link.'
									)}
								</p>
								<button
									class="flex justify-center items-center buttonGradient dark:bg-gray-100/10 hover:bg-gray-700/90 dark:hover:bg-gray-100/20 text-white transition w-full rounded-lg font-medium text-sm py-2.5"
									on:click={backToLogin}
								>
									{$i18n.t('Back to Login')}
								</button>
							</div>
						{/if}

						<!-- Token 无效状态 -->
						{#if !loading && !paramsError && !tokenValid}
							<div class="text-center space-y-4">
								<div class="text-2xl font-bold text-lineGradient mb-2">
									{$i18n.t('Verification Failed')}
								</div>
								<p class="text-sm text-gray-700 dark:text-gray-300 mb-2">
									{$i18n.t('The verification token is invalid or has expired.')}
								</p>
								<p class="text-xs text-gray-500 dark:text-gray-400 mb-4">
									{$i18n.t('Please request a new verification email or register again.')}
								</p>

								<!-- 按钮区域 -->
								<div class="flex flex-col w-full gap-3">
									<!-- 重新发送按钮 -->
									<button
										class={`flex justify-center items-center ${
											canResend
												? 'buttonGradient dark:bg-gray-100/10 hover:bg-gray-700/90 dark:hover:bg-gray-100/20 text-white'
												: 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
										} transition w-full rounded-lg font-medium text-sm py-2.5`}
										on:click={resendVerification}
										disabled={!canResend}
									>
										{#if !canResend}
											{$i18n.t('Resend')} ({countdown}s)
										{:else}
											{$i18n.t('Resend Verification Email')}
										{/if}
									</button>

									<!-- 返回登录按钮 -->
									<button
										class="flex justify-center items-center dark:text-gray-300 dark:hover:text-white transition w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5"
										on:click={backToLogin}
									>
										{$i18n.t('Back to Login')}
									</button>
								</div>
							</div>
						{/if}

						<!-- Token 有效，显示设置密码表单 -->
						{#if !loading && !paramsError && tokenValid && !registrationComplete}
							<div class="text-center space-y-4 w-full">
								<div class="text-2xl font-bold text-lineGradient mb-2">
									{$i18n.t('Complete Registration')}
								</div>
								<p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
									{$i18n.t(
										'Your email has been verified. Please set your password to complete registration.'
									)}
								</p>

								<!-- 用户信息展示 -->
								<div class="w-full mb-4">
									<div class="mb-4">
										<label
											for="username"
											class="block text-sm font-medium text-gray-700 dark:text-gray-300 text-left mb-1"
										>
											{$i18n.t('Username')}
										</label>
										<input
											type="text"
											id="username"
											value={username}
											disabled
											class="bg-gray-100 dark:bg-gray-700/50 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 w-full px-4 py-2 rounded-lg focus:outline-none"
										/>
									</div>

									<div class="mb-4">
										<label
											for="email"
											class="block text-sm font-medium text-gray-700 dark:text-gray-300 text-left mb-1"
										>
											{$i18n.t('Email')}
										</label>
										<input
											type="email"
											id="email"
											value={email}
											disabled
											class="bg-gray-100 dark:bg-gray-700/50 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 w-full px-4 py-2 rounded-lg focus:outline-none"
										/>
									</div>

									<!-- 密码输入框 -->
									<div class="mb-4">
										<div
											class="text-sm font-medium text-gray-700 dark:text-gray-300 text-left mb-1"
										>
											{$i18n.t('Password')}
										</div>
										<div
											class="flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="size-5"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
												/>
											</svg>
											{#if passwordVisible}
												<input
													type="text"
													id="password"
													bind:value={password}
													class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
													placeholder={$i18n.t('Enter your password')}
													autocomplete="new-password"
												/>
											{:else}
												<input
													type="password"
													id="password"
													bind:value={password}
													class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
													placeholder={$i18n.t('Enter your password')}
													autocomplete="new-password"
												/>
											{/if}
											<button
												type="button"
												class="text-gray-500 dark:text-gray-400"
												on:click={togglePasswordVisibility}
											>
												{#if passwordVisible}
													<svg
														xmlns="http://www.w3.org/2000/svg"
														class="size-5"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7A9.97 9.97 0 014.02 8.971m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l18 18"
														/>
													</svg>
												{:else}
													<svg
														xmlns="http://www.w3.org/2000/svg"
														class="size-5"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
														/>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
														/>
													</svg>
												{/if}
											</button>
										</div>
									</div>

									<!-- 确认密码输入框 -->
									<div class="mb-4">
										<div
											class="text-sm font-medium text-gray-700 dark:text-gray-300 text-left mb-1"
										>
											{$i18n.t('Confirm Password')}
										</div>
										<div
											class="flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												class="size-5"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
												/>
											</svg>
											{#if confirmPasswordVisible}
												<input
													type="text"
													id="confirmPassword"
													bind:value={confirmPassword}
													class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
													placeholder={$i18n.t('Confirm your password')}
													autocomplete="new-password"
												/>
											{:else}
												<input
													type="password"
													id="confirmPassword"
													bind:value={confirmPassword}
													class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
													placeholder={$i18n.t('Confirm your password')}
													autocomplete="new-password"
												/>
											{/if}
											<button
												type="button"
												class="text-gray-500 dark:text-gray-400"
												on:click={toggleConfirmPasswordVisibility}
											>
												{#if confirmPasswordVisible}
													<svg
														xmlns="http://www.w3.org/2000/svg"
														class="size-5"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7A9.97 9.97 0 014.02 8.971m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l18 18"
														/>
													</svg>
												{:else}
													<svg
														xmlns="http://www.w3.org/2000/svg"
														class="size-5"
														fill="none"
														viewBox="0 0 24 24"
														stroke="currentColor"
													>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
														/>
														<path
															stroke-linecap="round"
															stroke-linejoin="round"
															stroke-width="2"
															d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
														/>
													</svg>
												{/if}
											</button>
										</div>
									</div>

									<!-- 密码错误信息 -->
									{#if passwordError}
										<p class="text-xs text-red-500 text-left mb-4">
											{passwordError}
										</p>
									{/if}
								</div>

								<!-- 按钮区域 -->
								<div class="flex flex-col w-full gap-3">
									<button
										class="flex justify-center items-center buttonGradient dark:bg-gray-100/10 hover:bg-gray-700/90 dark:hover:bg-gray-100/20 text-white transition w-full rounded-lg font-medium text-sm py-2.5"
										on:click={completeRegistration}
									>
										{$i18n.t('Complete Registration')}
									</button>
								</div>
							</div>
						{/if}

						<!-- 注册完成状态 -->
						{#if !loading && registrationComplete}
							<div class="text-center space-y-4">
								<div class="text-2xl font-bold text-lineGradient mb-2">
									{$i18n.t('Registration Complete')}
								</div>
								<p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
									{$i18n.t(
										'Your account has been created successfully. You will be redirected to the login page.'
									)}
								</p>
								<div class="flex justify-center">
									<Spinner size="md" />
								</div>
							</div>
						{/if}
					</div>
				</div>

				<!-- 页脚 -->
				{#if !$mobile}
					<div
						class="w-full mt-4 flex justify-center gap-3 font-normal text-xs text-gray-500 dark:text-white/70 text-center"
					>
						<div>copyright © 2025</div>
						<div>Powered by Open WebUI</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.buttonGradient {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}

	.text-lineGradient {
		background: linear-gradient(135deg, #191a1d 0%, #747689 46%, #191a1d 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.drag-region {
		-webkit-app-region: drag;
	}
</style>
