<script>
	import { toast } from 'svelte-sonner';

	import { onMount, getContext, tick, onDestroy } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';

	import { getBackendConfig } from '$lib/apis';
	import {
		ldapUserSignIn,
		getSessionUser,
		userSignIn,
		userSignUp,
		sendPasswordResetEmail
	} from '$lib/apis/auths';

	import { WEBUI_API_BASE_URL, WEBUI_BASE_URL } from '$lib/constants';
	import { WEBUI_NAME, config, user, socket, mobile, userTheme, theme } from '$lib/stores';

	import { generateInitialsImage, canvasPixelTest } from '$lib/utils';

	import Spinner from '$lib/components/common/Spinner.svelte';
	import OnBoarding from '$lib/components/OnBoarding.svelte';
	import Email from '$lib/components/icons/Email.svelte';
	import LockClosed from '$lib/components/icons/LockClosed.svelte';
	import Info from '$lib/components/icons/Info.svelte';
	import { trackButtonClick } from '$lib/utils/analytics';
	import Divider from '$lib/components/common/Divider.svelte';
	import LoginLogo from '$lib/components/common/LoginLogo.svelte';
	import GithubIcon from '$lib/components/icons/GithubIcon.svelte';
	import GoogleIcon from '$lib/components/icons/GoogleIcon.svelte';
	import MailIcon from '$lib/components/icons/MailIcon.svelte';

	const i18n = getContext('i18n');

	let loaded = false;
	let isLoading = false;

	let mode = $config?.features.enable_ldap ? 'ldap' : 'signin';

	let name = '';
	let email = '';
	let password = '';

	let ldapUsername = '';
	let layoutType = $i18n.language === 'zh-CN' ? 'zh' : 'en';

	// 忘记密码相关状态
	let lastMode = '';
	let forgotPasswordEmail = '';
	let isSendingReset = false;

	onMount(async () => {
		// 如果 url 中有 sso_redirect 参数，携带 token 调和钻过去
		const ssoRedirect = querystringValue('sso_redirect');
		// 保存到 sessionStorage
		if (ssoRedirect) {
			sessionStorage.setItem('sso_redirect', ssoRedirect);
			console.log('init_auth_page_with_sso_redirect', ssoRedirect);
		}
	});

	const querystringValue = (key) => {
		const querystring = window.location.search;
		const urlParams = new URLSearchParams(querystring);
		return urlParams.get(key);
	};

	const handleClickSkipForNow = () => {
		trackButtonClick('Signin', 'signin_click', 'stay_logout');
		const ssoRedirect = querystringValue('sso_redirect');
		if (ssoRedirect) {
			location.href = ssoRedirect;
		} else {
			location.href = '/';
		}
	};

	const handleForgotPassword = () => {
		lastMode = mode;
		mode = 'forgot-password';
		forgotPasswordEmail = email; // 预填充当前输入的邮箱
	};

	const closeForgotPasswordModal = () => {
		mode = lastMode;
		forgotPasswordEmail = '';
		isSendingReset = false;
	};

	const sendPasswordReset = async () => {
		if (!forgotPasswordEmail || !forgotPasswordEmail.trim()) {
			toast.error($i18n.t('Please enter your email address'));
			return;
		}

		isLoading = true;
		try {
			await sendPasswordResetEmail(forgotPasswordEmail);
			toast.success(
				$i18n.t('Password reset email sent to {{email}}', { email: forgotPasswordEmail })
			);
			if (mode === 'forgot-password') {
				// 在忘记密码模式下，发送成功后切换到等待验证页面
				let verifyPath = `/auth/verify?email=${forgotPasswordEmail}&mode=password_reset`;
				goto(verifyPath);
			} else {
				closeForgotPasswordModal();
			}
		} catch (error) {
			const errorMessage =
				error && typeof error === 'object' && 'message' in error ? error.message : String(error);
			toast.error(
				$i18n.t('Failed to send password reset email: {{error}}', { error: errorMessage })
			);
		} finally {
			isLoading = false;
			isSendingReset = false;
		}
	};

	const setSessionUser = async (sessionUser) => {
		if (sessionUser) {
			console.log(sessionUser);
			toast.success($i18n.t(`You're now logged in.`));
			if (sessionUser.token) {
				localStorage.token = sessionUser.token;
			}

			// $socket.emit('user-join', { auth: { token: sessionUser.token } });
			await user.set(sessionUser);
			await config.set(await getBackendConfig());

			// 如果 url 中有 sso_redirect 参数，携带 token 调和钻过去
			const ssoRedirect = sessionStorage.getItem('sso_redirect');
			if (ssoRedirect) {
				// 用 window.location 跳转，携带 token 参数
				try {
					const objUrl = new URL(ssoRedirect);
					objUrl.searchParams.set('token', sessionUser.token);
					window.location.href = objUrl.toString();
				} catch (error) {
					console.log('SSO_REDIRECT_ERROR', error);
					return;
				} finally {
					sessionStorage.removeItem('sso_redirect');
				}
				return;
			}

			const redirectPath = querystringValue('redirect') || '/';
			goto(redirectPath);
		}
	};

	const signInHandler = async () => {
		isLoading = true;
		const sessionUser = await userSignIn(email, password).catch((error) => {
			toast.error(`${error}`);
			return null;
		});
		isLoading = false;
		await setSessionUser(sessionUser);
	};

	const signUpHandler = async () => {
		isLoading = true;
		const ssoRedirect = querystringValue('sso_redirect');
		const res = await userSignUp(
			name,
			email,
			password,
			generateInitialsImage(name),
			ssoRedirect
		).catch((error) => {
			toast.error(`${error}`);
			isLoading = false;
			return null;
		});

		if (res.success) {
			toast.success($i18n.t(`Email verify sent to {{email}}`, { email }));
			// to wait verify page
			let verifyPath = `/auth/verify?email=${email}&username=${name}`;
			if (ssoRedirect) {
				verifyPath = `${verifyPath}&sso_redirect=${ssoRedirect}`;
			}
			goto(verifyPath);
		} else {
			toast.error($i18n.t(`Failed to send email verify`));
		}
		isLoading = false;
	};

	const ldapSignInHandler = async () => {
		isLoading = true;
		const sessionUser = await ldapUserSignIn(ldapUsername, password).catch((error) => {
			toast.error(`${error}`);
			return null;
		});
		isLoading = false;
		await setSessionUser(sessionUser);
	};

	const submitHandler = async () => {
		if (mode === 'ldap') {
			await ldapSignInHandler();
		} else if (mode === 'signin') {
			await signInHandler();
		} else if (mode === 'forgot-password') {
			await sendPasswordReset();
		} else {
			await signUpHandler();
		}
	};

	const checkOauthCallback = async () => {
		const ssoRedirect = sessionStorage.getItem('sso_redirect');
		console.log('checkOauthCallback', ssoRedirect);
		if (!$page.url.hash) {
			return;
		}
		const hash = $page.url.hash.substring(1);
		if (!hash) {
			return;
		}
		const params = new URLSearchParams(hash);
		const token = params.get('token');
		if (!token) {
			return;
		}
		const sessionUser = await getSessionUser(token).catch((error) => {
			toast.error(`${error}`);
			return null;
		});
		if (!sessionUser) {
			return;
		}
		localStorage.token = token;
		await setSessionUser(sessionUser);
	};

	let onboarding = false;

	async function setLogoImage() {
		await tick();
		const logo = document.getElementById('logo');

		if (logo) {
			const isDarkMode = document.documentElement.classList.contains('dark');

			if (isDarkMode) {
				const darkImage = new Image();
				darkImage.src = '/static/favicon-dark.png';

				darkImage.onload = () => {
					logo.src = '/static/favicon-dark.png';
					logo.style.filter = ''; // Ensure no inversion is applied if favicon-dark.png exists
				};

				darkImage.onerror = () => {
					logo.style.filter = 'invert(1)'; // Invert image if favicon-dark.png is missing
				};
			}
		}
	}

	let viewportHeight = 0;

	function updateViewportHeight() {
		viewportHeight = window.innerHeight;
	}

	onMount(async () => {
		updateViewportHeight();
		window.addEventListener('resize', updateViewportHeight);

		// if ($user !== undefined) {
		// 	const redirectPath = querystringValue('redirect') || '/';
		// 	goto(redirectPath);
		// }

		const action = querystringValue('action');
		if (action === 'signup') {
			mode = 'signup';
		} else if (action === 'forgot-password') {
			mode = 'forgot-password';
		}

		await checkOauthCallback();

		loaded = true;
		setLogoImage();

		if ($theme === 'system') {
			const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
				? 'dark'
				: 'light';
			userTheme.set(systemTheme);
		} else {
			userTheme.set($theme ?? 'light');
		}

		if (($config?.features.auth_trusted_header ?? false) || $config?.features.auth === false) {
			await signInHandler();
		} else {
			onboarding = $config?.onboarding ?? false;
		}
	});

	onDestroy(() => {
		window.removeEventListener('resize', updateViewportHeight);
	});
</script>

<svelte:head>
	<title>
		{`${$WEBUI_NAME}`}
	</title>
</svelte:head>

<OnBoarding
	bind:show={onboarding}
	getStartedHandler={() => {
		onboarding = false;
		mode = $config?.features.enable_ldap ? 'ldap' : 'signup';
	}}
/>

<div class="w-full h-screen max-h-[100dvh] text-white relative">
	<div
		class="w-full h-full absolute top-0 left-0 {$mobile
			? 'bg-white'
			: 'bg-[#F5F6F8]'} dark:bg-[#141618]"
	></div>

	<div class="w-full absolute top-0 left-0 right-0 h-8 drag-region" />

	{#if loaded}
		<div
			class="fixed bg-transparent min-h-screen w-full flex justify-center z-50 text-black dark:text-white"
		>
			<div
				class="w-full sm:max-w-md {$mobile ? 'px-10' : ''} min-h-screen flex flex-col text-center"
			>
				{#if ($config?.features.auth_trusted_header ?? false) || $config?.features.auth === false}
					<div class=" my-auto pb-10 w-full">
						<div
							class="flex items-center justify-center gap-3 text-xl sm:text-2xl text-center font-semibold dark:text-gray-200"
						>
							<div>
								{$i18n.t('Signing in to {{WEBUI_NAME}}', { WEBUI_NAME: $WEBUI_NAME })}
							</div>

							<div>
								<Spinner />
							</div>
						</div>
					</div>
				{:else}
					<div class=" my-auto w-full">
						<div
							class={`dark:text-gray-100 rounded-2xl ${$mobile ? '' : 'bg-white dark:bg-[#26282A] border-b-2 border-black/10 p-10'}`}
						>
							<form
								class=" flex flex-col justify-center"
								on:submit={(e) => {
									e.preventDefault();
									submitHandler();
								}}
							>
								{#if $userTheme === 'light'}
									<div class=" self-center mb-6 size-14">
										<LoginLogo />
									</div>
								{:else}
									<div class=" self-center mb-6">
										<img class=" size-[79px]" src="/static/loginDark.png" alt="logo" />
									</div>
								{/if}
								<div class="">
									<div
										class=" text-2xl font-bold whitespace-nowrap {$userTheme === 'light'
											? 'textGradient'
											: 'textGradientDark'}"
									>
										{#if $config?.onboarding ?? false}
											{$i18n.t(`Get started with {{WEBUI_NAME}}`, { WEBUI_NAME: $WEBUI_NAME })}
										{:else if mode === 'ldap'}
											{$i18n.t(`Welcome to {{WEBUI_NAME}}`, { WEBUI_NAME: $WEBUI_NAME })}
										{:else if mode === 'forgot-password'}
											{$i18n.t('Reset Password')}
										{:else if mode === 'signin'}
											{$i18n.t(`Welcome to {{WEBUI_NAME}}`, { WEBUI_NAME: $WEBUI_NAME })}
										{:else}
											{$i18n.t(`Welcome to {{WEBUI_NAME}}`, { WEBUI_NAME: $WEBUI_NAME })}
										{/if}
									</div>

									<div class=" text-sm text-gray-500 font-normal mb-4 mt-2">
										{#if mode === 'forgot-password'}
											{$i18n.t(
												'Enter your email address and we will send you a password reset link'
											)}
										{:else}
											{$i18n.t('Unlock all features by logging in')}
										{/if}
									</div>

									{#if $config?.onboarding ?? false}
										<div class=" mt-1 text-xs font-medium text-gray-500">
											ⓘ {$WEBUI_NAME}
											{$i18n.t(
												'does not make any external connections, and your data stays securely on your locally hosted server.'
											)}
										</div>
									{/if}
								</div>

								{#if $config?.features.enable_login_form || $config?.features.enable_ldap}
									{#if layoutType === 'zh'}
										<div class="loginForm flex flex-col">
											{#if mode === 'signup'}
												<div class="mb-2">
													<div class=" text-sm font-medium text-left mb-1">{$i18n.t('Name')}</div>
													<div
														class=" flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
													>
														<Info className="size-5" />
														<input
															bind:value={name}
															type="text"
															class="my-0.5 w-full text-sm outline-hidden bg-transparent"
															autocomplete="name"
															placeholder={$i18n.t('Enter Your Full Name')}
															required
														/>
													</div>
												</div>
											{/if}

											{#if mode === 'ldap'}
												<div class="mb-3">
													<div class=" text-sm font-medium text-left mb-1">
														{$i18n.t('Username')}
													</div>
													<div
														class=" flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
													>
														<input
															bind:value={ldapUsername}
															type="text"
															class="my-0.5 w-full text-sm outline-hidden bg-transparent"
															autocomplete="username"
															name="username"
															placeholder={$i18n.t('Enter Your Username')}
															required
														/>
													</div>
												</div>
											{:else if mode === 'forgot-password'}
												<div class="mb-2">
													<div class=" text-sm font-medium text-left mb-1">{$i18n.t('Email')}</div>
													<div
														class=" flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
													>
														<Email className="size-5" />
														<input
															bind:value={forgotPasswordEmail}
															type="email"
															class="my-0.5 w-full text-sm bg-transparent outline-none dark:text-white"
															autocomplete="email"
															name="email"
															placeholder={$i18n.t('Enter Your Email')}
															required
														/>
													</div>
												</div>
											{:else}
												<div class="mb-2">
													<div class=" text-sm font-medium text-left mb-1">{$i18n.t('Email')}</div>
													<div
														class=" flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
													>
														<Email className="size-5" />
														<input
															bind:value={email}
															type="email"
															class="my-0.5 w-full text-sm bg-transparent outline-none dark:text-white"
															autocomplete="email"
															name="email"
															placeholder={$i18n.t('Enter Your Email')}
															required
														/>
													</div>
												</div>
											{/if}

											<!-- !等认证完邮箱再输入密码 -->
											{#if mode === 'signin'}
												<div class=" text-sm font-medium text-left mb-1">{$i18n.t('Password')}</div>
												<div>
													<div
														class=" flex gap-2 items-center px-3 py-2.5 mb-2 rounded-lg border-1 border-black/10 dark:border-white/10"
													>
														<LockClosed className="size-5" />
														<input
															bind:value={password}
															type="password"
															class="my-0.5 w-full text-sm outline-hidden bg-transparent dark:text-white"
															placeholder={$i18n.t('Enter Your Password')}
															autocomplete="current-password"
															name="current-password"
															required
														/>
													</div>
													<!-- 忘记密码链接 -->
													<div class="text-left mb-2">
														<button
															type="button"
															class="text-sm font-medium text-black dark:text-gray-400 underline"
															on:click|preventDefault={handleForgotPassword}
														>
															{$i18n.t('Forgot Password?')}
														</button>
													</div>
												</div>
											{/if}
											<button
												class="ButtonSignIn ButtonCreateAccount button-gradient dark:bg-[#484A58] dark:hover:bg-[#5A5C68] text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-3"
												type="submit"
												disabled={isLoading}
											>
												{#if isLoading}
													<span class="flex items-center justify-center">
														<Spinner size="sm" />
														<span class="ml-2">
															{mode === 'signin'
																? $i18n.t('Signing in...')
																: mode === 'forgot-password'
																	? $i18n.t('Sending...')
																	: ($config?.onboarding ?? false)
																		? $i18n.t('Creating Admin Account...')
																		: $i18n.t('Creating Account...')}
														</span>
													</span>
												{:else}
													{mode === 'signin'
														? $i18n.t('Sign in')
														: mode === 'forgot-password'
															? $i18n.t('Send Reset Link')
															: ($config?.onboarding ?? false)
																? $i18n.t('Create Admin Account')
																: $i18n.t('Create Account')}
												{/if}
											</button>

											{#if mode === 'forgot-password'}
												<button
													type="button"
													class="flex justify-center items-center mt-3 dark:text-gray-300 dark:hover:text-white w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5 dark:hover:bg-white/5"
													on:click={() => (mode = 'signin')}
												>
													{$i18n.t('Back to Sign in')}
												</button>
											{:else}
												<button
													class="ButtonSkipForNow flex justify-center items-center mt-3 dark:text-gray-300 dark:hover:text-white w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5 dark:hover:bg-white/5"
													on:click|preventDefault={handleClickSkipForNow}
												>
													{$i18n.t('Skip for now')}
												</button>
											{/if}
											{#if mode !== 'forgot-password'}
												<div class=" flex items-center justify-center gap-2 mt-3 text-sm">
													{#if $config?.features.enable_signup && !($config?.onboarding ?? false)}
														<div class="flex-1 text-sm text-center font-normal">
															{mode === 'signin'
																? $i18n.t("Don't have an account?")
																: $i18n.t('Already have an account?')}

															<button
																class=" font-medium underline"
																type="button"
																on:click={() => {
																	if (mode === 'signin') {
																		mode = 'signup';
																	} else {
																		mode = 'signin';
																	}
																}}
															>
																{mode === 'signin' ? $i18n.t('Sign up') : $i18n.t('Sign in')}
															</button>
														</div>
													{/if}
													{#if Object.keys($config?.oauth?.providers ?? {}).length > 0}
														<Divider orientation="vertical" />
														<div class="flex flex-1 justify-center items-center gap-3">
															<div>{$i18n.t('Quick Login')}:</div>
															{#if $config?.oauth?.providers?.google}
																<button
																	class="ButtonContinueWithGoogle"
																	on:click|preventDefault={() => {
																		trackButtonClick('Signin', 'signin_click', 'google');
																		window.location.href = `${WEBUI_BASE_URL}/oauth/google/login?t=2`;
																	}}
																>
																	<GoogleIcon />
																</button>
															{/if}
															{#if $config?.oauth?.providers?.github}
																<button
																	class="ButtonContinueWithGithub"
																	on:click|preventDefault={() => {
																		trackButtonClick('Signin', 'signin_click', 'github');
																		window.location.href = `${WEBUI_BASE_URL}/oauth/github/login?t=2`;
																	}}
																>
																	<GithubIcon className="size-5" theme={$userTheme} />
																</button>
															{/if}
														</div>
													{/if}
												</div>
											{/if}
										</div>
									{:else}
										<div class="loginFormUni flex flex-col gap-3 text-sm">
											{#if $config?.oauth?.providers?.google}
												<button
													class="flex gap-2 items-center justify-center button-gradient dark:bg-[#484A58] dark:hover:bg-[#5A5C68] text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-3"
													on:click|preventDefault={() => {
														trackButtonClick('Signin', 'signin_click', 'google');
														window.location.href = `${WEBUI_BASE_URL}/oauth/google/login?t=2`;
													}}
												>
													<span class="inline-flex items-center justify-center w-full gap-2">
														<GoogleIcon className="size-5" />
														<span class="inline-flex item-center gap-1 whitespace-nowrap">
															<span>{$i18n.t('Continue')}</span>
															<span>{$i18n.t('with')}</span>
															<span class="w-12 text-left"
																>{$i18n.t('{{provider}}', { provider: 'Google' })}</span
															>
														</span>
													</span>
												</button>
											{/if}
											{#if $config?.features.enable_login_form && Object.keys($config?.oauth?.providers ?? {}).length > 0}
												<div class="inline-flex items-center justify-center w-full">
													<hr
														class="flex-1 h-px my-4 border-0 dark:bg-gray-100/10 bg-gray-700/10"
													/>
													<span
														class="px-3 text-sm font-medium text-gray-900 dark:text-white bg-transparent whitespace-nowrap"
														>{$i18n.t('or')}</span
													>
													<hr
														class="flex-1 h-px my-4 border-0 dark:bg-gray-100/10 bg-gray-700/10"
													/>
												</div>
											{/if}
											{#if $config?.features.enable_login_form}
												<button
													class=" flex gap-2 items-center justify-center rounded-lg bg-black/5 dark:bg-white/10 p-2 hover:bg-black/10 hover:dark:bg-white/15"
													on:click|preventDefault={() => (layoutType = 'zh')}
												>
													<span class="inline-flex items-center justify-center w-full gap-2">
														<MailIcon className="size-5" theme={$userTheme} />
														<span class="inline-flex item-center gap-1 whitespace-nowrap">
															<span>
																{$i18n.t('Continue')}
															</span>
															<span>
																{$i18n.t('with')}
															</span>
															<span class="w-12 text-left">
																{$i18n.t('{{provider}}', { provider: 'Email' })}
															</span>
														</span>
													</span>
												</button>
											{/if}
											{#if $config?.oauth?.providers?.github}
												<button
													class=" flex gap-2 items-center justify-center rounded-lg bg-black/5 dark:bg-white/10 p-2 hover:bg-black/10 hover:dark:bg-white/15"
													on:click|preventDefault={() => {
														trackButtonClick('Signin', 'signin_click', 'github');
														window.location.href = `${WEBUI_BASE_URL}/oauth/github/login?t=2`;
													}}
												>
													<span class="inline-flex items-center justify-center w-full gap-2">
														<GithubIcon className="size-5" theme={$userTheme} />
														<span class="inline-flex item-center gap-1 whitespace-nowrap">
															<span>{$i18n.t('Continue')}</span>
															<span>{$i18n.t('with')}</span>
															<span class="w-12 text-left"
																>{$i18n.t('{{provider}}', { provider: 'Github' })}</span
															>
														</span>
													</span>
												</button>
											{/if}
											<button
												on:click|preventDefault={handleClickSkipForNow}
												class="  rounded-lg border-1 border-black/10 dark:border-white/10 p-2 hover:bg-black/2 dark:hover:bg-white/5 dark:hover:text-white"
												>{$i18n.t('Skip for now')}</button
											>
										</div>
									{/if}
								{/if}
							</form>

							{#if $config?.features.enable_ldap && $config?.features.enable_login_form && mode !== 'forgot-password'}
								<div class="mt-2">
									<button
										class="flex justify-center items-center text-xs w-full text-center underline"
										type="button"
										on:click={() => {
											if (mode === 'ldap')
												mode = ($config?.onboarding ?? false) ? 'signup' : 'signin';
											else mode = 'ldap';
										}}
									>
										<span
											>{mode === 'ldap'
												? $i18n.t('Continue with Email')
												: $i18n.t('Continue with LDAP')}</span
										>
									</button>
								</div>
							{/if}
						</div>
						{#if !$mobile && $user?.role === 'guest'}
							<div class="font-normal text-xs text-gray-400 dark:text-white text-center mb-2 mt-3">
								{$i18n.t('By logging in, you agree to our ')}
								<a
									href="https://chat.z.ai/legal-agreement/terms-of-service"
									class="text-gray-600 underline"
									target="_blank"
									rel="noopener noreferrer">{$i18n.t('Terms of Service')}</a
								>
								<span>{$i18n.t(' and ')}</span>
								<a
									href="https://chat.z.ai/legal-agreement/privacy-policy"
									class="text-gray-600 underline"
									target="_blank"
									rel="noopener noreferrer">{$i18n.t('Privacy Policy')}</a
								>
							</div>
							<div
								class="flex justify-center gap-3 font-normal text-xs text-gray-500 dark:text-white/70 text-center"
							>
								<div>copyright © 2025</div>
								<div>Powered by Open WebUI</div>
							</div>
						{/if}
					</div>
				{/if}
				{#if $mobile && $user?.role === 'guest'}
					<div class="font-normal text-xs text-gray-400 dark:text-white text-center mb-1 mt-3">
						{$i18n.t('By logging in, you agree to our ')}
						<a
							href="https://chat.z.ai/legal-agreement/terms-of-service"
							class="text-gray-600 underline"
							target="_blank"
							rel="noopener noreferrer">{$i18n.t('Terms of Service')}</a
						>
						<span>{$i18n.t(' and ')}</span>
						<a
							href="https://chat.z.ai/legal-agreement/privacy-policy"
							class="text-gray-600 underline"
							target="_blank"
							rel="noopener noreferrer">{$i18n.t('Privacy Policy')}</a
						>
					</div>
					<div
						class="flex justify-center gap-3 mb-3 font-normal text-xs text-gray-500 dark:text-white/70 text-center"
					>
						<div>copyright © 2025</div>
						<div>Powered by Open WebUI</div>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>

<style>
	.buttonGradient {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}

	.textGradient {
		background: linear-gradient(135deg, #191a1d 0%, #747689 46%, #191a1d 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.textGradientDark {
		background: linear-gradient(135deg, #ffffff 0%, #a4a6b3 46%, #ffffff 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	input:-webkit-autofill,
	input:-webkit-autofill:hover,
	input:-webkit-autofill:focus {
		-webkit-box-shadow: 0 0 0 30px transparent inset !important;
		-webkit-text-fill-color: gray !important;
		transition: background-color 5000s ease-in-out 0s;
	}
</style>
