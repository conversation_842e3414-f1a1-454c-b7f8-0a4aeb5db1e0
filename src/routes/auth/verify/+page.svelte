<script>
	import { onMount, getContext, tick, onDestroy } from 'svelte';
	import { page } from '$app/stores';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';

	import { WEBUI_NAME, config, mobile, userTheme } from '$lib/stores';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import Email from '$lib/components/icons/Email.svelte';
	import { resendVerificationEmail } from '$lib/apis/auths';

	const i18n = getContext('i18n');

	// 从 URL 参数中获取 email 和 name
	let email = '';
	let name = '';
	let mode = 'signup'; // 也可能是 password_reset

	// 计时器相关状态
	let countdown = 60;
	let countdownInterval;
	let canResend = false;

	// 从查询参数中获取值
	const querystringValue = (key) => {
		const querystring = window.location.search;
		const urlParams = new URLSearchParams(querystring);
		return urlParams.get(key);
	};

	// 重新发送验证邮件
	const resendVerification = async () => {
		if (!canResend) return;

		canResend = false;
		countdown = 60;

		try {
			const sso_redirect = querystringValue('sso_redirect');
			const response = await resendVerificationEmail(email, name, sso_redirect);
			if (response.success) {
				// 模拟 API 调用成功
				toast.success($i18n.t('Verification email has been resent to {{email}}', { email }));
				// 启动倒计时
				startCountdown();
			} else {
				toast.error(
					$i18n.t('Failed to send: {{error}}', { error: response.message || response.detail })
				);
			}
		} catch (error) {
			toast.error(
				$i18n.t('Failed to send: {{error}}', { error: error.message || error.detail || error })
			);
			canResend = true;
			countdown = 0;
			clearInterval(countdownInterval);
		}
	};

	// 开始倒计时
	const startCountdown = () => {
		countdownInterval = setInterval(() => {
			countdown = countdown - 1;
			if (countdown <= 0) {
				clearInterval(countdownInterval);
				canResend = true;
			}
		}, 1000);
	};

	// 返回登录页
	const backToLogin = () => {
		const sso_redirect = querystringValue('sso_redirect');
		if (sso_redirect) {
			goto(`/auth?sso_redirect=${sso_redirect}`);
		} else {
			goto('/auth');
		}
	};

	onMount(async () => {
		// 获取 URL 参数
		email = querystringValue('email') || '';
		name = querystringValue('username') || '';
		mode = querystringValue('mode') || 'signup';

		if (!email) {
			toast.error($i18n.t('Email information is missing'));
			goto('/auth');
		}

		// 开始倒计时
		startCountdown();

		// 设置页面图标的暗黑模式适配
		await tick();
		const logo = document.getElementById('logo');

		if (logo) {
			const isDarkMode = document.documentElement.classList.contains('dark');

			if (isDarkMode) {
				const darkImage = new Image();
				darkImage.src = '/static/favicon-dark.png';

				darkImage.onload = () => {
					logo.src = '/static/favicon-dark.png';
					logo.style.filter = '';
				};

				darkImage.onerror = () => {
					logo.style.filter = 'invert(1)';
				};
			}
		}
	});

	// 组件销毁时清除定时器
	onDestroy(() => {
		if (countdownInterval) {
			clearInterval(countdownInterval);
		}
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Email Verification')} - {`${$WEBUI_NAME}`}
	</title>
</svelte:head>

<div class="w-full h-screen max-h-[100dvh] text-black dark:text-white relative">
	<div class="w-full h-full absolute top-0 left-0 bg-[#F5F6F8] dark:bg-[#141618]"></div>
	<div class="w-full absolute top-0 left-0 right-0 h-8 drag-region" />

	<div class="fixed bg-transparent min-h-screen w-full flex justify-center z-50">
		<div class="w-full sm:max-w-md {$mobile ? 'px-8' : ''} min-h-screen flex flex-col">
			<div class="my-auto w-full">
				<div
					class={`dark:text-gray-100 rounded-2xl ${$mobile ? '' : 'bg-white dark:bg-[#26282A] border-b-2 border-black/10 p-10'}`}
				>
					<div class="flex flex-col items-center justify-center">
						<!-- 图标 -->
						<div class="mb-6">
							<img
								id="logo"
								class="size-[79px]"
								src="/static/{$userTheme === 'light' ? 'loginLight.png' : 'loginDark.png'}"
								alt="logo"
							/>
						</div>

						<!-- 标题 -->
						<div class="text-2xl font-bold text-lineGradient mb-2">
							{$i18n.t('Verify Your Email')}
						</div>

						<!-- 说明文本 -->
						<div class="text-center mb-6">
							<p class="text-sm text-gray-700 dark:text-gray-300 mb-2">
								{$i18n.t('We have sent a verification link to')}
							</p>
							<p class="text-base font-medium text-gray-900 dark:text-white mb-3">
								{email}
							</p>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								{$i18n.t('Please click the link in the email to complete verification')}
							</p>
						</div>

						<!-- 按钮区域 -->
						<div class="flex flex-col w-full gap-3">
							<!-- 重新发送按钮 -->
							<button
								class={`flex justify-center items-center ${
									canResend
										? 'buttonGradient dark:bg-gray-100/10 hover:bg-gray-700/90 dark:hover:bg-gray-100/20 text-white'
										: 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
								} transition w-full rounded-lg font-medium text-sm py-2.5`}
								on:click={resendVerification}
								disabled={!canResend}
							>
								{#if !canResend}
									{$i18n.t('Resend')} ({countdown}s)
								{:else}
									{$i18n.t('Resend Verification Email')}
								{/if}
							</button>

							<!-- 返回登录按钮 -->
							<button
								class="flex justify-center items-center dark:text-gray-300 dark:hover:text-white transition w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5"
								on:click={backToLogin}
							>
								{$i18n.t('Back to Login')}
							</button>
						</div>
					</div>
				</div>

				<!-- 页脚 -->
				{#if !$mobile}
					<div
						class="w-full mt-4 flex justify-center gap-3 font-normal text-xs text-gray-500 dark:text-white/70 text-center"
					>
						<div>copyright © 2025</div>
						<div>Powered by Open WebUI</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.buttonGradient {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}

	.drag-region {
		-webkit-app-region: drag;
	}
</style>
