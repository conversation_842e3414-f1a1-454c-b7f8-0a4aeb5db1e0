<script>
	import { toast } from 'svelte-sonner';
	import { onMount, getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';

	import { resetPassword } from '$lib/apis/auths';
	import { WEBUI_NAME, mobile, userTheme } from '$lib/stores';

	import Spinner from '$lib/components/common/Spinner.svelte';
	import LockClosed from '$lib/components/icons/LockClosed.svelte';
	import LoginLogo from '$lib/components/common/LoginLogo.svelte';
	import UserIcon from '$lib/components/icons/UserIcon.svelte';
	import EmailIcon2 from '$lib/components/icons/EmailIcon2.svelte';

	const i18n = getContext('i18n');

	let loaded = false;
	let isLoading = false;
	let paramsError = false;
	let username = '';
	let email = '';
	let token = '';
	let newPassword = '';
	let confirmPassword = '';
	let passwordError = '';

	
	// 密码验证状态
	let passwordValidation = {
		length: false,
		uppercase: false,
		lowercase: false,
		number: false,
		special: false,
		match: false
	};

	// 从查询参数中获取值
	const getQueryParam = (key) => {
		const querystring = window.location.search;
		const urlParams = new URLSearchParams(querystring);
		return urlParams.get(key);
	};

	// 验证密码强度
	const validatePassword = (password) => {
		return {
			length: password.length >= 8,
			uppercase: /[A-Z]/.test(password),
			lowercase: /[a-z]/.test(password),
			number: /[0-9]/.test(password),
			special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
		};
	};

	// 监听新密码变化
	$: {
		if (newPassword) {
			passwordValidation = {
				...validatePassword(newPassword),
				match: newPassword === confirmPassword && confirmPassword !== ''
			};
		} else {
			passwordValidation = {
				length: false,
				uppercase: false,
				lowercase: false,
				number: false,
				special: false,
				match: false
			};
		}
	}

	// 监听确认密码变化
	$: {
		if (confirmPassword) {
			passwordValidation.match = newPassword === confirmPassword && newPassword !== '';
		} else {
			passwordValidation.match = false;
		}
	}

	// 检查密码是否完全符合要求
	$: isPasswordValid =
		passwordValidation.length &&
		passwordValidation.uppercase &&
		passwordValidation.lowercase &&
		passwordValidation.number &&
		passwordValidation.special &&
		passwordValidation.match;

	// 计算密码强度
	$: passwordStrength = (() => {
		const validCount = [
			passwordValidation.length,
			passwordValidation.uppercase,
			passwordValidation.lowercase,
			passwordValidation.number,
			passwordValidation.special
		].filter(Boolean).length;

		if (validCount === 0) return { level: 0, text: '', color: '' };
		if (validCount <= 2) return { level: 1, text: $i18n.t('Weak'), color: 'text-red-500' };
		if (validCount <= 4) return { level: 2, text: $i18n.t('Medium'), color: 'text-yellow-500' };
		return { level: 3, text: $i18n.t('Strong'), color: 'text-green-500' };
	})();

	// 显示密码强度提示
	$: showPasswordStrength = newPassword.length > 0;

	const resetPasswordHandler = async () => {
		// 验证密码
		if (!newPassword) {
			passwordError = $i18n.t('Password is required');
			return;
		}

		if (!isPasswordValid) {
			passwordError = $i18n.t('Password does not meet all requirements');
			return;
		}

		passwordError = '';
		isLoading = true;

		try {
			const response = await resetPassword(token, email, newPassword);
			if (response.success) {
				toast.success($i18n.t('Password reset successfully'));
				// 跳转到登录页
				setTimeout(() => {
					goto('/auth');
				}, 2000);
			} else {
				toast.error($i18n.t('Failed to reset password'));
			}
		} catch (error) {
			const errorMessage =
				error && typeof error === 'object' && 'message' in error ? error.message : String(error);
			toast.error($i18n.t('Failed to reset password: {{error}}', { error: errorMessage }));
		} finally {
			isLoading = false;
		}
	};

	onMount(async () => {
		// 获取URL参数
		email = getQueryParam('email') || '';
		username = getQueryParam('username') || '';
		token = getQueryParam('token') || '';

		if (!email || !token) {
			paramsError = true;
		}

		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Reset Password')} - {`${$WEBUI_NAME}`}
	</title>
</svelte:head>

<div class="w-full h-screen max-h-[100dvh] text-white relative">
	<div
		class="w-full h-full absolute top-0 left-0 {$mobile
			? 'bg-white'
			: 'bg-[#F5F6F8]'} dark:bg-[#141618]"
	></div>

	<div class="w-full absolute top-0 left-0 right-0 h-8 drag-region" />

	{#if loaded}
		<div
			class="fixed bg-transparent min-h-screen w-full flex justify-center z-50 text-black dark:text-white"
		>
			<div
				class="w-full sm:max-w-md {$mobile ? 'px-10' : ''} min-h-screen flex flex-col text-center"
			>
				<div class="my-auto w-full relative">
					<div
						class={`dark:text-gray-100 rounded-2xl ${$mobile ? '' : 'bg-white dark:bg-[#26282A] border-b-2 border-black/10 p-10'}`}
					>
						{#if paramsError}
							<!-- 参数错误显示 -->
							<div class="text-center space-y-4">
								{#if $userTheme === 'light'}
									<div class="self-center mb-6 size-14">
										<LoginLogo />
									</div>
								{:else}
									<div class="self-center mb-6">
										<img class="size-[79px]" src="/static/loginDark.png" alt="logo" />
									</div>
								{/if}

								<div class="text-2xl font-bold text-red-600 mb-2">
									{$i18n.t('Invalid Parameters')}
								</div>
								<p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
									{$i18n.t('The password reset link is invalid or incomplete')}
								</p>

								<button
									class="flex justify-center items-center dark:text-gray-300 dark:hover:text-white transition w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5"
									on:click={() => goto('/auth')}
								>
									{$i18n.t('Back to Login')}
								</button>
							</div>
						{:else}
							<!-- 正常重置密码表单 -->
							<form on:submit|preventDefault={resetPasswordHandler}>
								<div class="flex justify-center">
									{#if $userTheme === 'light'}
										<div class="self-center mb-6 size-14">
											<LoginLogo />
										</div>
									{:else}
										<div class="self-center mb-6">
											<img class="size-[79px]" src="/static/loginDark.png" alt="logo" />
										</div>
									{/if}
								</div>

								<div
									class="text-2xl font-bold whitespace-nowrap {$userTheme === 'light'
										? 'textGradient'
										: 'textGradientDark'} mb-2"
								>
									{$i18n.t('Reset Password')}
								</div>

								<div class="text-sm text-gray-500 font-normal mb-6">
									{$i18n.t('Enter your new password')}
								</div>

								<!-- 用户名展示，无法输入 -->
								<div class="mb-4">
									<div class="text-sm font-medium text-left mb-1">{$i18n.t('Username')}</div>
									<div
										class="flex gap-2 items-center px-3 py-2.5 rounded-lg bg-[#F5F6F8] dark:bg-[#26282A]"
									>
										<UserIcon className="size-5" />
										<input
											bind:value={username}
											class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
											disabled
										/>
									</div>
								</div>
								<!-- email展示，无法输入 -->
								<div class="mb-4">
									<div class="text-sm font-medium text-left mb-1">{$i18n.t('Email')}</div>
									<div
										class="flex gap-2 items-center px-3 py-2.5 rounded-lg bg-[#F5F6F8] dark:bg-[#26282A]"
									>
										<EmailIcon2 className="size-5" />
										<input
											bind:value={email}
											class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
											disabled
										/>
									</div>
								</div>

								<!-- 新密码输入 -->
								<div class="mb-4">
									<div class="text-sm font-medium text-left mb-1">{$i18n.t('New Password')}</div>
									<div
										class="flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
									>
										<LockClosed className="size-5" />
										<input
											bind:value={newPassword}
											type="password"
											class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
											placeholder={$i18n.t('Enter your new password')}
											autocomplete="new-password"
											required
										/>
									</div>
								</div>

								<!-- 确认密码输入 -->
								<div class="mb-4">
									<div class="text-sm font-medium text-left mb-1">
										{$i18n.t('Confirm Password')}
									</div>
									<div
										class="flex gap-2 items-center px-3 py-2.5 rounded-lg border-1 border-black/10 dark:border-white/10"
									>
										<LockClosed className="size-5" />
										<input
											bind:value={confirmPassword}
											type="password"
											class="my-0.5 w-full text-sm outline-none bg-transparent dark:text-white"
											placeholder={$i18n.t('Confirm your new password')}
											autocomplete="new-password"
											required
										/>
									</div>
								</div>

								<!-- 错误消息 -->
								{#if passwordError}
									<div class="text-red-500 text-sm mb-4">
										{passwordError}
									</div>
								{/if}

								<!-- 重置按钮 -->
								<button
									class="button-gradient dark:bg-[#484A58] dark:hover:bg-[#5A5C68] text-white dark:text-gray-300 dark:hover:text-white w-full rounded-lg font-medium text-sm py-2.5 mt-3 {!isPasswordValid
										? 'opacity-50 cursor-not-allowed'
										: ''}"
									type="submit"
									disabled={isLoading || !isPasswordValid}
								>
									{#if isLoading}
										<span class="flex items-center justify-center">
											<Spinner />
											<span class="ml-2">{$i18n.t('Resetting...')}</span>
										</span>
									{:else}
										{$i18n.t('Reset Password')}
									{/if}
								</button>

								<!-- 返回登录链接 -->
								<div class="mt-4">
									<button
										type="button"
										class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 underline"
										on:click={() => goto('/auth')}
									>
										{$i18n.t('Back to Login')}
									</button>
								</div>
							</form>
						{/if}
					</div>
					<!-- 密码强度浮窗 -->
					{#if showPasswordStrength}
						<div class="password-strength-float {$mobile ? 'mobile-position' : 'desktop-position'}">
							<div
								class="bg-white dark:bg-[#26282A] rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-64"
							>
								<!-- 强度指示器 -->
								<div class="mb-3">
									<div class="flex items-center gap-2 mb-2">
										<span class="text-sm font-medium text-gray-700 dark:text-gray-300">
											{$i18n.t('强度:')}
										</span>
										<span class="text-sm font-medium {passwordStrength.color}">
											{passwordStrength.text}
										</span>
									</div>
									<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
										<div
											class="h-2 rounded-full transition-all duration-300 {passwordStrength.level ===
											1
												? 'bg-red-500 w-1/3'
												: passwordStrength.level === 2
													? 'bg-yellow-500 w-2/3'
													: passwordStrength.level === 3
														? 'bg-green-500 w-full'
														: 'w-0'}"
										></div>
									</div>
								</div>

								<!-- 密码要求列表 -->
								<div class="space-y-2">
									<div class="flex items-center gap-2">
										<div
											class="w-2 h-2 rounded-full {passwordValidation.length
												? 'bg-green-500'
												: 'bg-gray-300'}"
										></div>
										<span
											class="text-xs {passwordValidation.length
												? 'text-green-600 dark:text-green-400'
												: 'text-gray-500'}"
										>
											{$i18n.t('至少8个字符')}
										</span>
									</div>
									<div class="flex items-center gap-2">
										<div
											class="w-2 h-2 rounded-full {passwordValidation.uppercase
												? 'bg-green-500'
												: 'bg-gray-300'}"
										></div>
										<span
											class="text-xs {passwordValidation.uppercase
												? 'text-green-600 dark:text-green-400'
												: 'text-gray-500'}"
										>
											{$i18n.t('大写字母')}
										</span>
									</div>
									<div class="flex items-center gap-2">
										<div
											class="w-2 h-2 rounded-full {passwordValidation.lowercase
												? 'bg-green-500'
												: 'bg-gray-300'}"
										></div>
										<span
											class="text-xs {passwordValidation.lowercase
												? 'text-green-600 dark:text-green-400'
												: 'text-gray-500'}"
										>
											{$i18n.t('小写字母')}
										</span>
									</div>
									<div class="flex items-center gap-2">
										<div
											class="w-2 h-2 rounded-full {passwordValidation.number
												? 'bg-green-500'
												: 'bg-gray-300'}"
										></div>
										<span
											class="text-xs {passwordValidation.number
												? 'text-green-600 dark:text-green-400'
												: 'text-gray-500'}"
										>
											{$i18n.t('数字')}
										</span>
									</div>
									<div class="flex items-center gap-2">
										<div
											class="w-2 h-2 rounded-full {passwordValidation.special
												? 'bg-green-500'
												: 'bg-gray-300'}"
										></div>
										<span
											class="text-xs {passwordValidation.special
												? 'text-green-600 dark:text-green-400'
												: 'text-gray-500'}"
										>
											{$i18n.t('特殊字符')}
										</span>
									</div>
									{#if confirmPassword}
										<div
											class="flex items-center gap-2 pt-1 border-t border-gray-200 dark:border-gray-600"
										>
											<div
												class="w-2 h-2 rounded-full {passwordValidation.match
													? 'bg-green-500'
													: 'bg-red-500'}"
											></div>
											<span
												class="text-xs {passwordValidation.match
													? 'text-green-600 dark:text-green-400'
													: 'text-red-600 dark:text-red-400'}"
											>
												{$i18n.t('密码匹配')}
											</span>
										</div>
									{/if}
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.button-gradient {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}

	.textGradient {
		background: linear-gradient(135deg, #191a1d 0%, #747689 46%, #191a1d 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.textGradientDark {
		background: linear-gradient(135deg, #ffffff 0%, #a4a6b3 46%, #ffffff 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.password-strength-float {
		position: fixed;
		z-index: 60;
		animation: slideIn 0.3s ease-out;
	}

	.desktop-position {
		left: 102%;
		top: 70%;
		transform: translateY(-50%);
		position: absolute;
	}

	.mobile-position {
		bottom: 20px;
		left: 50%;
		transform: translateX(-50%);
		width: calc(100% - 40px);
		max-width: 280px;
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: translateY(-50%) translateX(20px);
		}
		to {
			opacity: 1;
			transform: translateY(-50%) translateX(0);
		}
	}

	@media (max-width: 640px) {
		@keyframes slideIn {
			from {
				opacity: 0;
				transform: translateX(-50%) translateY(20px);
			}
			to {
				opacity: 1;
				transform: translateX(-50%) translateY(0);
			}
		}
	}
</style>
