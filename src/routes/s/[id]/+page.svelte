<script lang="ts">
	import { onMount, tick, getContext } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';

	import dayjs from 'dayjs';

	import {
		settings,
		chatId,
		WEBUI_NAME,
		models,
		config,
		mobile,
		showControls,
		currentHistory
	} from '$lib/stores';
	import {
		convertMessagesToHistory,
		copyToClipboard,
		createMessagesList,
		findLatestMessageId
	} from '$lib/utils';

	import { getChatByShareId, cloneSharedChatById } from '$lib/apis/chats';

	import Messages from '$lib/components/chat/Messages.svelte';

	import { getUserById } from '$lib/apis/users';
	import { getModels } from '$lib/apis';
	import { toast } from 'svelte-sonner';
	import localizedFormat from 'dayjs/plugin/localizedFormat';
	import ShareControls from '$lib/components/layout/ShareControls.svelte';
	import { Pane, PaneGroup } from 'paneforge';
	import Link from '$lib/components/icons/Link.svelte';
	import Kira<PERSON><PERSON> from '$lib/components/icons/KiraKira.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';

	const i18n = getContext('i18n');
	dayjs.extend(localizedFormat);

	let loaded = false;

	let autoScroll = true;
	let processing = '';
	let messagesContainerElement: HTMLDivElement;
	let controlPane;
	let controlPaneComponent;

	let shareId = $page.params.id;
	let showModelSelector = false;
	let selectedModels = [''];

	let chat = null;
	let user = null;

	let title = '';
	let files = [];

	let messages = [];
	let history = {
		messages: {},
		currentId: null
	};

	$: currentHistory.set(history);

	$: messages = createMessagesList(history, history.currentId);

	$: if ($page.params.id) {
		(async () => {
			if (await loadSharedChat()) {
				await tick();
				loaded = true;
			} else {
				await goto('/');
			}
		})();
	}

	$: if (loaded) {
		(async () => {
			await tick();
			const autoArtifacts = document.querySelectorAll('.autoShowArtifacts');

			if (autoArtifacts.length > 0) {
				showControls.set('artifacts');
				const lastArtifactCard = autoArtifacts[autoArtifacts.length - 1];
				lastArtifactCard.scrollIntoView({
					behavior: 'smooth',
					inline: 'nearest'
				});
				lastArtifactCard.click();
			}
		})();
	}

	//////////////////////////
	// Web functions
	//////////////////////////

	const loadSharedChat = async () => {
		await models.set(
			await getModels(
				localStorage.token,
				$config?.features?.enable_direct_connections && ($settings?.directConnections ?? null)
			)
		);
		// await chatId.set($page.params.id);
		shareId = $page.params.id;
		chat = await getChatByShareId(localStorage.token, shareId).catch(async (error) => {
			await goto('/');
			return null;
		});

		chatId.set(shareId);
		// if (chat.user_id.startsWith('shared-')) {
		// 	chatId.set(chat.user_id.slice(7));
		// }

		if (chat) {
			user = await getUserById(localStorage.token, chat.user_id).catch((error) => {
				console.error(error);
				return null;
			});

			const chatContent = chat.chat;

			if (chatContent) {
				console.log(chatContent);

				selectedModels =
					(chatContent?.models ?? undefined) !== undefined
						? chatContent.models
						: [chatContent.models ?? ''];
				history =
					(chatContent?.history ?? undefined) !== undefined
						? chatContent.history
						: convertMessagesToHistory(chatContent.messages);

				// 确保 currentId 指向最新的消息
				const latestMessageId = findLatestMessageId(history);
				if (latestMessageId) {
					history.currentId = latestMessageId;
					console.log(`[loadSharedChat] Set currentId to latest message: ${latestMessageId}`);
				}

				title = chatContent.title;

				autoScroll = true;
				await tick();

				if (messages.length > 0) {
					history.messages[messages.at(-1).id].done = true;
				}
				await tick();

				return true;
			} else {
				return null;
			}
		}
	};

	const cloneSharedChat = async () => {
		if (!chat) return;

		const res = await cloneSharedChatById(localStorage.token, chat.id).catch((error) => {
			toast.error(`${error}`);
			return null;
		});

		if (res) {
			goto(`/c/${res.id}`);
		}
	};

	onMount(() => {
		showControls.subscribe(async (value) => {
			if (controlPane && !$mobile) {
				try {
					if (value) {
						controlPaneComponent.openPane();
					} else {
						controlPane.collapse();
					}
				} catch (e) {
					// ignore
				}
			}
		});
	});
</script>

<svelte:head>
	<title>
		{title
			? `${title.length > 30 ? `${title.slice(0, 30)}...` : title} | ${$WEBUI_NAME}`
			: `${$WEBUI_NAME}`}
	</title>
</svelte:head>

{#if loaded}
	<div
		class="h-screen max-h-[100dvh] w-full flex flex-col text-gray-700 dark:text-gray-100 bg-color-primary"
	>
		<div class="flex flex-col flex-1 justify-center relative">
			<div class=" flex flex-col w-full flex-auto h-0" id="messages-container">
				<div id="share-container" class="flex flex-1 w-full h-full overflow-hidden">
					<PaneGroup direction="horizontal" class="w-full h-full">
						<Pane defaultSize={50} class="h-full flex flex-col w-full relative">
							<div class="h-full w-full flex flex-col overflow-auto scrollbar-hidden">
								<div class="top-0 pt-2 px-2 w-full max-w-5xl mx-auto">
									<div class="px-3">
										<div class="flex justify-between items-center">
											<div class=" text-2xl font-semibold line-clamp-1">
												{title}
											</div>
											<Tooltip content={$i18n.t('Copy Link')}>
												<button
													on:click={() => {
														copyToClipboard(location.href);
														toast.success($i18n.t('Copied shared chat URL to clipboard!'));
													}}
													class="hover:bg-black/5 dark:hover:bg-white/5 p-2 rounded-lg"
												>
													<Link className="size-5" />
												</button>
											</Tooltip>
										</div>

										<!-- <div class="flex text-sm justify-between items-center mt-1">
											<div class="text-gray-400">
												{dayjs(chat.chat.timestamp).format('LLL')}
											</div>
										</div> -->
									</div>
								</div>
								<div class="">
									<Messages
										className="h-full flex pt-4 pb-8"
										{user}
										chatId={$chatId}
										readOnly={true}
										{selectedModels}
										{processing}
										bind:history
										bind:messages
										bind:autoScroll
										bottomPadding={files.length > 0}
										sendPrompt={() => {}}
										continueResponse={() => {}}
										regenerateResponse={() => {}}
									/>
								</div>
							</div>
							<div class="p-5 w-full max-w-5xl self-center">
								<div class="flex gap-2 p-2 rounded-xl border-b-2 bg-white text-sm border-black/30">
									<input
										id="share-input"
										class="flex-1 p-2 h-10 outline-0 min-w-[100px]"
										placeholder={$i18n.t(
											'Start a new conversation to generate the content you want'
										)}
									/>
									<button
										on:click={() => {
											const input = document.getElementById('share-input');
											const value = input?.value?.trim();
											if (value) {
												location.replace(`/?model=${chat?.chat?.models[0] ?? ''}&q=${value}`);
											} else {
												location.replace(`/?model=${chat?.chat?.models[0] ?? ''}`);
											}
										}}
										class="gradientButton flex gap-2 items-center text-white px-4 py-2 rounded-lg whitespace-nowrap"
									>
										<KiraKira className="size-4.5" />
										<span>{$i18n.t('Create for Free')}</span>
									</button>
								</div>
							</div>
						</Pane>
						<ShareControls
							bind:this={controlPaneComponent}
							bind:pane={controlPane}
							{history}
							chatId={shareId}
						/>
					</PaneGroup>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes gradientBreathing {
		0% {
			background-position: 100% 50%;
		}
		100% {
			background-position: 0% 50%;
		}
	}

	.gradientButton {
		position: relative;
		background: linear-gradient(
			145deg,
			#191a1d 0%,
			#222327 20%,
			#44454d 35%,
			#747689 44%,
			#a8aab8 50%,
			#747689 56%,
			#44454d 65%,
			#222327 80%,
			#191a1d 100%
		);
		animation: gradientBreathing 5s ease-in-out normal infinite;
		background-size: 600% 600%;
		border: none;
		font-weight: 500;
		letter-spacing: 0.3px;
	}
</style>
