<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import ArtifactsViewer from '$lib/components/ArtifactsViewer.svelte';
	import PptViewer from '$lib/components/PPTViewer.svelte';

	let viewType: 'ppt' | 'artifacts' | '' = '';

	onMount(() => {
		// 检测 subdomain 路由
		const id = $page.params.id;
		// 如果是支持的域名格式，显示 PPT 组件
		if (id.includes('-ppt')) {
			viewType = 'ppt';
		} else if (id.includes('-art')) {
			viewType = 'artifacts';
		} else {
			viewType = 'artifacts';
		}
	});
</script>

{#if viewType === 'ppt'}
	<!-- 动态加载 PPT 组件，组件内部自己处理 subdomain 逻辑 -->
	<PptViewer />
{:else if viewType === 'artifacts'}
	<!-- 动态加载 Artifacts 组件，组件内部自己处理 subdomain 逻辑 -->
	<ArtifactsViewer />
{/if}
