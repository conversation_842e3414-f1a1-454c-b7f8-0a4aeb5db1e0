const GA_ID_MAP = {
  prod: 'G-Z8QTHYBHP3',
  staging: 'G-4LN10C6KWR',
  dev: 'G-S8K7PRNXYP'
}

const getGAId = () => {
  const gitTag = process.env.GIT_TAG || 'dev';
  if (gitTag.includes('prod')) {
    console.log("🔥使用 prod 的 GTAG 为：", GA_ID_MAP.prod);
    return GA_ID_MAP.prod;
  } else if (gitTag.includes('staging')) {
    console.log("🔥使用 staging 的 GTAG 为：", GA_ID_MAP.staging);
    return GA_ID_MAP.staging;
  } else {
    console.log("🔥使用 dev 的 GTAG 为：", GA_ID_MAP.dev);
    return GA_ID_MAP.dev;
  }
}

export async function handle({ event, resolve }) {
  return await resolve(event, {
    transformPageChunk: ({ html }) => html.replaceAll(
      '__VITE_GA_MEASUREMENT_ID__',
      getGAId()
    )
  });
}