@import 'tailwindcss';

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@layer base {
	html,
	pre {
		font-family: system-ui,ui-sans-serif,-apple-system,BlinkMacSystemFont,sans-serif,Inter,NotoSansHans;
	}

	pre {
		white-space: pre-wrap;
	}

	button {
		@apply cursor-pointer;
	}

	input::placeholder,
	textarea::placeholder {
		color: theme(--color-gray-400);
	}
}

@custom-variant hover (&:hover);
