import { get } from 'svelte/store';
import { language } from '$lib/stores';
import { fetchESPromisify } from '$lib/utils/fetchEventSource';
import i18n from '$lib/i18n';
import { WEBUI_BASE_URL } from '$lib/constants';

export async function getPPT(data: {
	chatId: string;
	callback: (data: any) => void;
	onClose: () => void;
	signal?: AbortSignal;
	version?: number;
	onError?: (err: Error) => void;
}) {
	const { chatId, callback, onClose, signal, version, onError } = data;

	await fetchESPromisify('GET', {
		url: `${WEBUI_BASE_URL}/api/chat/ppt/${chatId}?v=${version || 'latest'}`,
		header: {
			'Accept-Language': get(language) ?? 'en-US'
		},
		callback,
		onClose,
		signal,
		onError
	});
}

export async function deployPPT(chatId: string, version?: number) {
	const token = localStorage.getItem('token') ?? '';
	const response = await fetch(`${WEBUI_BASE_URL}/api/chat/ppt_deploy?v=${version || 'latest'}`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`
		},
		body: JSON.stringify({
			chat_id: chatId
		})
	});

	if (!response.ok) {
		throw new Error('Failed to deploy PPT');
	}

	return await response.json();
}

export async function getPPTSnapshot(
	chatId: string,
	controller?: AbortController
	// baseUrl?: string
) {
	const token = localStorage.getItem('token') ?? '';
	const response = await fetch(`${WEBUI_BASE_URL}/api/p/${chatId}`, {
		method: 'GET',
		headers: {
			Authorization: `Bearer ${token}`
		},
		signal: controller?.signal
	});

	if (!response.ok) {
		throw new Error('Failed to get PPT');
	}

	return await response.json();
}

export async function updatePPT(params: { chatId: string; html: string; index: number }) {
	const token = localStorage.getItem('token') ?? '';
	const response = await fetch(`${WEBUI_BASE_URL}/api/chat/ppt/${params.chatId}`, {
		method: 'PATCH',
		headers: {
			Authorization: `Bearer ${token}`,
			'Accept-Language': get(language) ?? 'en-US'
		},
		body: JSON.stringify({
			html: params.html,
			index: params.index
		})
	});

	if (response.status === 400) {
		const data = await response.json();
		throw new Error(get(i18n)?.t(data.details));
	}

	if (!response.ok) {
		throw new Error('Failed to update PPT');
	}

	return await response.json();
}

export async function getSharedPPT(params: { chatId: string; version?: number }) {
	const token = localStorage.getItem('token') ?? '';
	const response = await fetch(
		`${WEBUI_BASE_URL}/api/ppt/share/${params.chatId}?v=${params.version ?? 'latest'}`,
		{
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		}
	);

	if (!response.ok) {
		throw new Error('Failed to get shared PPT');
	}

	return await response.json();
}
