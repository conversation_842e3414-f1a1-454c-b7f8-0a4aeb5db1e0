import { WEBUI_BASE_URL } from '$lib/constants';

// 会返回一个 { "site_url": "http://xxxx" }
export const deployProject = async (chatId: string, messageId: string, codeBlockIndex: number) => {
	const token = localStorage.getItem('token') ?? '';
	const res = await fetch(`${WEBUI_BASE_URL}/api/chat/preview_deploy`, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`
		},
		body: JSON.stringify({
			chat_id: chatId,
			message_id: messageId,
			code_block_index: codeBlockIndex
		})
	}).then(async (res) => {
		if (!res.ok) throw await res.json();
		return res.json();
	});

	return res;
};

// 会返回一个 { "lang": "html", "code": "<div>...</div>" }
export const getSnippet = async (id: string) => {
	let error = null;
	const res = await fetch(`${WEBUI_BASE_URL}/api/x/${id}`, {
		method: 'GET',
		headers: { Accept: 'text/plain', 'Content-Type': 'application/json' }
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			console.log(err);
			if ('detail' in err) {
				error = err.detail;
			}
			return null;
		});

	if (error) {
		throw error;
	}

	return res;
};
