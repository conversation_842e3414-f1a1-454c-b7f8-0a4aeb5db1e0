/**
 * SSE 转换服务
 * 处理与后端 SSE 转换接口的通信
 */

import { WEBUI_API_BASE_URL } from '$lib/constants';

export interface ConversionProgress {
  status: 'processing' | 'completed' | 'failed';
  message: string;
  progress?: string;
  pdf_url?: string;
  ppt_url?: string;
  heartbeat?: number;
  aidrive_upload_result?: {
    success: boolean;
    filename: string;
    directory: string;
    download_url: string;
    mime_type: string;
    file_size: number;
    created_at: string;
  };
}

export interface ConversionOptions {
  pageSize?: string;
  pageWidth?: number;
  pageHeight?: number;
  orientation?: string;
  margin?: string;
  quality?: number;
  slideSize?: string;
}

export interface HtmlPage {
  content: string;
  metadata?: any; // 支持页面元数据，包含页面尺寸等信息
}

export interface ConversionCallbacks {
  onProgress: (progress: ConversionProgress) => void;
  onComplete: (result: ConversionProgress) => void;
  onError: (error: string) => void;
}

// 自定义错误类，支持结构化错误信息
export class ConversionError extends Error {
  public errorType?: string;
  public data?: any;

  constructor(message: string, errorType?: string, data?: any) {
    super(message);
    this.name = 'ConversionError';
    this.errorType = errorType;
    this.data = data;
  }
}

export class SSEConversionService {
  private baseUrl: string;

  constructor(baseUrl: string) {
    // 如果提供了 baseUrl 参数，则使用它；否则使用统一的 API 基础 URL
    this.baseUrl = baseUrl;
  }

  /**
   * 更新转换服务的基础 URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取当前配置的基础 URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * 解析错误响应，优先提取detail字段，支持结构化错误处理
   */
  private async parseErrorResponse(response: Response): Promise<{ message: string; errorType?: string; data?: any }> {
    try {
      const errorText = await response.text();

      // 尝试解析为JSON
      try {
        const errorJson = JSON.parse(errorText);

        // 如果有detail字段
        if (errorJson.detail) {
          // 检查是否为结构化错误（对象类型）
          if (typeof errorJson.detail === 'object' && errorJson.detail.error_type) {
            return {
              message: '', // 消息将由前端i18n处理
              errorType: errorJson.detail.error_type,
              data: errorJson.detail
            };
          }
          // 如果是字符串类型的detail，直接返回
          return { message: errorJson.detail };
        }

        // 如果没有detail字段，返回整个错误信息
        return { message: errorText };
      } catch (parseError) {
        // 如果不是JSON格式，返回原始文本
        return { message: errorText };
      }
    } catch (textError) {
      // 如果无法读取响应文本，返回通用错误信息
      return { message: `HTTP ${response.status}: ${response.statusText}` };
    }
  }

  /**
   * 转换 HTML 到 PDF (流式) - 使用chatId
   */
  async convertToPdf(
    chatId: string,
    options: ConversionOptions = {},
    callbacks: ConversionCallbacks,
    pageMetadata?: any[],
    pptVersion?: number
  ): Promise<void> {
    return this.performConversionWithChatId('/convert/pdf/stream', chatId, options, callbacks, pageMetadata, pptVersion);
  }

  /**
   * 转换 HTML 到 PPT (流式) - 使用chatId
   */
  async convertToPpt(
    chatId: string,
    options: ConversionOptions = {},
    callbacks: ConversionCallbacks,
    pageMetadata?: any[],
    pptVersion?: number
  ): Promise<void> {
    return this.performConversionWithChatId('/convert/ppt/stream', chatId, options, callbacks, pageMetadata, pptVersion);
  }


  /**
   * 执行转换请求 - 使用chatId
   */
  private async performConversionWithChatId(
    endpoint: string,
    chatId: string,
    options: ConversionOptions,
    callbacks: ConversionCallbacks,
    pageMetadata?: any[],
    pptVersion?: number
  ): Promise<void> {
    try {
      const token = localStorage.getItem('token') ?? '';

      // 构造请求体
      const requestBody: any = {
        chatId,
        options,
        pptVersion
      };

      // 如果有页面尺寸数据，则包含在请求中
      if (pageMetadata && pageMetadata.length > 0) {
        requestBody.pageMetadata = pageMetadata;
      }
      // 发送 POST 请求建立 SSE 连接
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorInfo = await this.parseErrorResponse(response);
        throw new ConversionError(errorInfo.message, errorInfo.errorType, errorInfo.data);
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('text/event-stream')) {
        const errorInfo = await this.parseErrorResponse(response);
        throw new ConversionError(`Expected SSE stream, got: ${contentType}. Response: ${errorInfo.message}`, errorInfo.errorType, errorInfo.data);
      }

      // 处理 SSE 流
      await this.handleSSEStream(response, callbacks);
    } catch (error) {
      console.error('Conversion request failed:', error);

      // 如果是ConversionError，传递完整的错误信息给回调
      if (error instanceof ConversionError) {
        // 将错误类型和数据编码到错误消息中，让上层组件处理
        const errorMessage = error.errorType ?
          `STRUCTURED_ERROR:${error.errorType}:${JSON.stringify(error.data || {})}` :
          error.message;
        callbacks.onError(errorMessage);
      } else {
        callbacks.onError(error instanceof Error ? error.message : 'Unknown error');
      }
    }
  }

  /**
   * 处理 SSE 数据流
   */
  private async handleSSEStream(
    response: Response,
    callbacks: ConversionCallbacks
  ): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body reader available');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        // 解码数据并添加到缓冲区
        buffer += decoder.decode(value, { stream: true });

        // 处理缓冲区中的完整消息
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6)) as ConversionProgress;
              this.handleProgressUpdate(data, callbacks);
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', line, parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('SSE stream error:', error);
      callbacks.onError(error instanceof Error ? error.message : 'Stream error');
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 处理进度更新
   */
  private handleProgressUpdate(
    data: ConversionProgress,
    callbacks: ConversionCallbacks
  ): void {
    console.log('SSE Progress:', data);

    // 调用进度回调
    callbacks.onProgress(data);

    // 检查是否完成
    if (data.status === 'completed') {
      callbacks.onComplete(data);
    } else if (data.status === 'failed') {
      callbacks.onError(data.message || 'Conversion failed');
    }
  }

  /**
   * 下载文件 - 使用直接导航方式避免CORS问题
   */
  async downloadFile(url: string, filename: string): Promise<void> {
    try {
      // 创建一个隐藏的a标签直接导航下载
      // 这种方式不受CORS限制，因为不是JavaScript跨域请求
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('File download initiated:', { url, filename });
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    }
  }
}

// 创建默认实例
export const sseConversionService = new SSEConversionService(WEBUI_API_BASE_URL);

// 工具函数：获取当前环境的默认配置
export const getDefaultConversionConfig = () => ({
  baseUrl: WEBUI_API_BASE_URL.replace('/api/v1', ''),
  apiBaseUrl: WEBUI_API_BASE_URL
});
