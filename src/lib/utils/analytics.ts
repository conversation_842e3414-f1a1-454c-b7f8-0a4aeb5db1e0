import { browser } from '$app/environment';
import { user } from '$lib/stores';
import { get } from 'svelte/store';
import { isMobile, isTablet } from 'mobile-device-detect';


const getUTMId = () => {
	// 从 query 中获取 utm_id 参数，当作 fr
	try {
		const urlParams = new URLSearchParams(window.location.search);
		const utm_id = urlParams.get('utm_id');
		return utm_id && utm_id.trim() ? utm_id.trim() : 'default';
	} catch (error) {
		console.error('[Analytics] Error:', error);
		return 'default';
	}
};

const patchUrl = (oldUrl: string) => {
	const utm_id = getUTMId();
	// 这个 oldUrl 可能是完整的 url，但大概率是 pathname，给这个 pathname 加一个 query 参数叫 fr，value 是 utm_id
	const newUrl = oldUrl.includes('?') ? `${oldUrl}&fr=${utm_id}` : `${oldUrl}?fr=${utm_id}`;
	return newUrl;
}

export const isMobileDevice = () => isMobile || isTablet;
export interface TrackingParams {
	bt: string; // 日志类型：pv、cl(click)、er(error)、pf(performance)
	md: string;
	ct: string;
	ctvl?: string;
	url?: string;
	usid?: string;
	pvid?: string;
	data?: Record<string, any>;
}


export const trackNetworkError = (detail: string, request_url: string, isGlobalError: boolean = false) => {
	if (request_url.includes("google-analytics")) {
		return;
	}
	const params = {
		bt: 'er',
		md: 'network',
		ct: isGlobalError ? 'global_network_error' : 'network_error',
		ctvl: detail,
		request_url: request_url,
		usid: get(user)?.id || 'anonymous',
		fr: getUTMId()
	}
	window.setTimeout(() => {
		trackEvent(params);
	}, 1000);
}

export const trackEvent = async (params: TrackingParams): Promise<void> => {
	if (!browser) return;

	try {
		const currentUser = get(user);
		const usid = params.usid || currentUser?.id || 'anonymous';
		const is_guest = currentUser?.role == 'guest' ? '1' : '0';
		const utm_id = getUTMId();

		const baseUrl = 'https://analysis.chatglm.cn/bdms/p.gif';

		const queryParams = new URLSearchParams({
			pd: 'zai',
			bt: params.bt,
			tm: isMobileDevice() ? 'h5' : 'pc',
			ct: params.ct,
			ctvl: params.ctvl ?? '',
			usid,
			fr: utm_id,
			pvid: params.pvid || '',
			_n_is_guest: is_guest,
			url: patchUrl(params.url || window.location.pathname),
			...params.data
		});

		const img = new Image();
		img.src = `${baseUrl}?${queryParams.toString()}`;

		// 同步到 GA
		if (params.bt === "cl") {
			window.gtag('event', params.ct, {
				module: params.md,
				page_type: isMobileDevice() ? 'h5' : 'pc',
				value: params.ctvl ?? '',
				pvid: params.pvid || '',
				usid: usid,
				fr: utm_id,
				url: patchUrl(params.url || window.location.pathname),
				is_guest: is_guest,
			});
		}

		if (process.env.NODE_ENV === 'development') {
			console.log('[Analytics]', params);
		}
	} catch (error) {
		console.error('[Analytics] Error:', error);
	}
};

export const trackPageView = (md: string, ct: string, ctvl: string): void => {
	trackEvent({
		bt: 'pv',
		md: md,
		ct: ct,
		ctvl: ctvl
	});
};

export const trackButtonClick = (md: string, ct: string, ctvl?: string): void => {
	trackEvent({
		bt: 'cl',
		md: md,
		ct: ct,
		ctvl: ctvl
	});
};

// 全局 fetch 错误捕获
let originalFetch: typeof fetch;
let isGlobalFetchInterceptorInitialized = false;

export const initGlobalFetchErrorTracking = (): void => {
	if (!browser || isGlobalFetchInterceptorInitialized) return;

	try {
		// 保存原始的 fetch 函数
		originalFetch = window.fetch;

		// 重写 fetch 函数
		window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
			const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
			
			try {
				const response = await originalFetch(input, init);
				
				// 检查 HTTP 状态码错误
				if (!response.ok) {
					const errorDetail = `HTTP status Error: ${response.status} ${response.statusText}`;
					trackNetworkError(errorDetail, url, true);
				}
				
				return response;
			} catch (error) {
				// 捕获网络错误（连接失败、超时等）
				const errorDetail = error instanceof Error ? error.message : 'Unknown fetch error';
				trackNetworkError(errorDetail, url, true);
				
				// 重新抛出错误，保持原有的错误处理逻辑
				throw error;
			}
		};

		isGlobalFetchInterceptorInitialized = true;
		console.log('[Analytics] Global fetch error tracking initialized');
	} catch (error) {
		console.error('[Analytics] Failed to initialize global fetch error tracking:', error);
	}
};

export const destroyGlobalFetchErrorTracking = (): void => {
	if (!browser || !isGlobalFetchInterceptorInitialized) return;

	try {
		// 恢复原始的 fetch 函数
		if (originalFetch) {
			window.fetch = originalFetch;
		}
		
		isGlobalFetchInterceptorInitialized = false;
		console.log('[Analytics] Global fetch error tracking destroyed');
	} catch (error) {
		console.error('[Analytics] Failed to destroy global fetch error tracking:', error);
	}
};
