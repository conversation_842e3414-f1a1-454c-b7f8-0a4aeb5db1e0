// 日志级别枚举
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

// 日志级别样式配置
const LOG_STYLES = {
  [LogLevel.DEBUG]: { color: '#9CA3AF', icon: '🐛' },
  [LogLevel.INFO]: { color: '#3B82F6', icon: '💡' },
  [LogLevel.WARN]: { color: '#F59E0B', icon: '⚠️' },
  [LogLevel.ERROR]: { color: '#EF4444', icon: '❌' }
};

class Logger {
  private enabled = false;
  private level = LogLevel.DEBUG;

  /**
   * 获取时间戳
   */
  private getTimestamp(): string {
    return new Date().toTimeString().split(' ')[0];
  }

  /**
   * 内部日志方法
   */
  private log(level: LogLevel, ...args: any[]): void {
    if (!this.enabled || level < this.level) return;

    const style = LOG_STYLES[level];
    const timestamp = `[${this.getTimestamp()}]`;
    const prefix = `${style.icon} ${timestamp}`;
    const prefixStyle = `color: ${style.color}; font-weight: bold; padding: 2px 6px; border-radius: 3px; font-size: 12px;`;
    
    // 事件样式（第一个参数）
    const eventStyle = `color: ${style.color}; font-weight: bold; background-color: ${style.color}20; padding: 1px 4px; border-radius: 2px;`;

    if (args.length > 0) {
      const [event, ...restArgs] = args;
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(`%c${prefix} %c${event}`, prefixStyle, eventStyle, ...restArgs);
          break;
        case LogLevel.INFO:
          console.info(`%c${prefix} %c${event}`, prefixStyle, eventStyle, ...restArgs);
          break;
        case LogLevel.WARN:
          console.warn(`%c${prefix} %c${event}`, prefixStyle, eventStyle, ...restArgs);
          break;
        case LogLevel.ERROR:
          console.error(`%c${prefix} %c${event}`, prefixStyle, eventStyle, ...restArgs);
          break;
      }
    } else {
      // 如果没有参数，只显示前缀
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(`%c${prefix}`, prefixStyle);
          break;
        case LogLevel.INFO:
          console.info(`%c${prefix}`, prefixStyle);
          break;
        case LogLevel.WARN:
          console.warn(`%c${prefix}`, prefixStyle);
          break;
        case LogLevel.ERROR:
          console.error(`%c${prefix}`, prefixStyle);
          break;
      }
    }
  }

  /**
   * 公共日志方法
   */
  debug(...args: any[]): void {
    this.log(LogLevel.DEBUG, ...args);
  }

  info(...args: any[]): void {
    this.log(LogLevel.INFO, ...args);
  }

  warn(...args: any[]): void {
    this.log(LogLevel.WARN, ...args);
  }

  error(...args: any[]): void {
    this.log(LogLevel.ERROR, ...args);
  }
}

// 创建单例实例
const logger = new Logger();

// 导出日志实例
export default logger;
