import Tracker from '@aliyun-sls/web-track-browser';
import { mainTracker } from '$lib/stores';
import { get } from 'svelte/store';

export const initMainTracker = () => {
	if (get(mainTracker)) {
		return get(mainTracker);
	}
	const tracker = new Tracker({
		host: 'cn-hongkong.log.aliyuncs.com', // 阿里云SLS的Endpoint
		project: 'k8s-log-c1f874a604cb64b29bb69219c8faaf8b7', // Project名称
		logstore: 'prod-open-webui-frontend', // Logstore名称
		time: 10, // 发送日志间隔(秒)
		count: 10, // 发送日志数量
		topic: 'zai-performance-events', // 日志主题
		source: 'app-performance', // 日志来源
		tags: {
			version: '1.0.0',
			type: 'app-performance'
		}
	});
	mainTracker.set(tracker);
	return tracker;
};
