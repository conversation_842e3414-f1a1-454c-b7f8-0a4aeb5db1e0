import { fetchEventSource } from '@microsoft/fetch-event-source';

export function FetchEventSource<Params, Data>(
	method: string,
	data: {
		url: string;
		body: Params;
		callback: (res: Data) => void;
		header?: Record<string, string>;
		signal?: AbortSignal;
		onFinish?: () => void;
		onError?: (err: Error) => void;
	}
) {
	const { url, body, callback, header, signal, onFinish, onError } = data;

	let retryCount = 0;

	const headers: Record<string, string> = {
		'Content-Type': 'application/json',
		Accept: 'text/event-stream',
		...header
	};
	const token = localStorage.getItem('token');
	if (token) {
		headers.Authorization = `Bearer ${token}`;
	}
	return fetchEventSource(url, {
		method,
		headers,
		signal,
		...(method.toUpperCase() === 'POST' ? { body: JSON.stringify(body) } : {}),
		openWhenHidden: true,
		onopen(res) {
			if (res.ok && res.status === 200) {
				return Promise.resolve();
			}
			return Promise.reject('retry');
		},
		onmessage(ev) {
			callback(JSON.parse(ev.data));
		},
		onclose() {
			// console.log('Connection closed by the server');
			onFinish?.();
			retryCount++;
			if (retryCount > 3) {
				throw new Error('Connection closed by the server');
			}
		},
		onerror(err) {
			onError?.(err);
			console.log('There was an error from server', err);
			retryCount++;

			if (retryCount > 3) {
				throw new Error('Retry count exceeded');
			}
			onFinish?.();
		}
	});
}

export function fetchESPromisify<Params, Data>(
	method: string,
	data: {
		url: string;
		body?: Params;
		header?: Record<string, string>;
		callback: (res: Data) => void;
		signal?: AbortSignal;
		onClose?: () => void;
		onError?: (err: Error) => void;
	}
) {
	return new Promise<void>((resolve, reject) => {
		const { url, body, callback, signal, onClose, header } = data;

		// 将fetchEventSource使用Promise封装，通过onFinish事件将结果resolve
		FetchEventSource(method, {
			url,
			body,
			header,
			callback,
			signal,
			onError: (err) => {
				reject(err);
			},
			onFinish: () => {
				resolve();
				onClose?.();
			}
		}).catch(reject);
	});
}
