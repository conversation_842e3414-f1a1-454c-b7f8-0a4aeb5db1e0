import type { CitationItem } from '$lib/stores/mcp';

/**
 * 搜索缓存管理器类
 * 负责管理单个会话的搜索结果缓存
 */
export class SearchCacheManager {
	private citations: CitationItem[] = [];

	/**
	 * 标准化 URL，移除尾部斜杠和 hash
	 * @param url 原始 URL
	 * @returns 标准化后的 URL
	 */
	private normalizeUrl(url: string): string {
		try {
			const urlObj = new URL(url);
			// 移除尾部斜杠（除非是根路径）
			if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
				urlObj.pathname = urlObj.pathname.slice(0, -1);
			}
			// 返回不包含 hash 的 URL
			return urlObj.origin + urlObj.pathname + urlObj.search;
		} catch {
			// 如果 URL 解析失败，返回原始 URL
			return url;
		}
	}

	/**
	 * 添加搜索结果到缓存
	 * @param newCitations 新的搜索结果引用列表
	 */
	addCitations(newCitations: CitationItem[]): void {
		if (!newCitations || newCitations.length === 0) {
			return;
		}

		// 获取现有的标准化 URL 集合
		const existingUrls = new Set(this.citations.map(item => this.normalizeUrl(item.url)));
		
		// 过滤出不重复的新引用
		const uniqueNewCitations = newCitations.filter(citation => 
			!existingUrls.has(this.normalizeUrl(citation.url))
		);

		// 添加到缓存
		this.citations = [...this.citations, ...uniqueNewCitations];
	}

	/**
	 * 根据 URL 查找对应的标题
	 * @param url 要查找的URL
	 * @returns 找到的标题，如果没找到则返回 null
	 */
	findTitleByUrl(url: string): string | null {
		if (!url) {
			return null;
		}

		const normalizedUrl = this.normalizeUrl(url);
		
		// 尝试精确匹配和标准化匹配
		const citation = this.citations.find(item => 
			item.url === url || this.normalizeUrl(item.url) === normalizedUrl
		);
		
		return citation ? citation.title : null;
	}

	/**
	 * 获取所有缓存的引用
	 * @returns 所有缓存的引用列表
	 */
	getAllCitations(): CitationItem[] {
		return [...this.citations];
	}

	/**
	 * 根据关键词搜索引用
	 * @param keyword 搜索关键词
	 * @returns 匹配的引用列表
	 */
	searchCitations(keyword: string): CitationItem[] {
		if (!keyword) {
			return [];
		}

		const lowerKeyword = keyword.toLowerCase();
		return this.citations.filter(citation => 
			citation.title.toLowerCase().includes(lowerKeyword) ||
			citation.text.toLowerCase().includes(lowerKeyword) ||
			citation.url.toLowerCase().includes(lowerKeyword)
		);
	}

	/**
	 * 检查是否包含指定的 URL
	 * @param url 要检查的 URL
	 * @returns 是否包含该 URL
	 */
	hasUrl(url: string): boolean {
		return this.findTitleByUrl(url) !== null;
	}

	/**
	 * 获取缓存的引用数量
	 * @returns 缓存的引用数量
	 */
	getCount(): number {
		return this.citations.length;
	}

	/**
	 * 清空缓存
	 */
	clear(): void {
		this.citations = [];
	}

	/**
	 * 获取指定域名的所有引用
	 * @param domain 域名
	 * @returns 该域名下的所有引用
	 */
	getCitationsByDomain(domain: string): CitationItem[] {
		return this.citations.filter(citation => {
			try {
				const url = new URL(citation.url);
				return url.hostname === domain || url.hostname.endsWith('.' + domain);
			} catch {
				return false;
			}
		});
	}

	/**
	 * 移除指定 URL 的引用
	 * @param url 要移除的 URL
	 * @returns 是否成功移除
	 */
	removeCitationByUrl(url: string): boolean {
		const normalizedUrl = this.normalizeUrl(url);
		const initialLength = this.citations.length;
		
		this.citations = this.citations.filter(citation => 
			citation.url !== url && this.normalizeUrl(citation.url) !== normalizedUrl
		);
		
		return this.citations.length < initialLength;
	}

	/**
	 * 更新指定 URL 的引用信息
	 * @param url 要更新的 URL
	 * @param updates 更新的字段
	 * @returns 是否成功更新
	 */
	updateCitation(url: string, updates: Partial<CitationItem>): boolean {
		const normalizedUrl = this.normalizeUrl(url);
		const citation = this.citations.find(item => 
			item.url === url || this.normalizeUrl(item.url) === normalizedUrl
		);
		
		if (citation) {
			Object.assign(citation, updates);
			return true;
		}
		
		return false;
	}

	/**
	 * 导出缓存数据为 JSON
	 * @returns JSON 字符串
	 */
	exportToJson(): string {
		return JSON.stringify(this.citations, null, 2);
	}

	/**
	 * 从 JSON 导入缓存数据
	 * @param jsonString JSON 字符串
	 * @returns 是否成功导入
	 */
	importFromJson(jsonString: string): boolean {
		try {
			const data = JSON.parse(jsonString);
			if (Array.isArray(data)) {
				this.citations = data;
				return true;
			}
		} catch (error) {
			console.error('Failed to import citations from JSON:', error);
		}
		return false;
	}

	/**
	 * 获取缓存统计信息
	 * @returns 统计信息对象
	 */
	getStats(): {
		totalCount: number;
		uniqueDomains: number;
		domains: string[];
		averageTitleLength: number;
		averageTextLength: number;
	} {
		const domains = new Set<string>();
		let totalTitleLength = 0;
		let totalTextLength = 0;

		this.citations.forEach(citation => {
			try {
				const url = new URL(citation.url);
				domains.add(url.hostname);
			} catch {
				// 忽略无效 URL
			}
			totalTitleLength += citation.title.length;
			totalTextLength += citation.text.length;
		});

		return {
			totalCount: this.citations.length,
			uniqueDomains: domains.size,
			domains: Array.from(domains),
			averageTitleLength: this.citations.length > 0 ? totalTitleLength / this.citations.length : 0,
			averageTextLength: this.citations.length > 0 ? totalTextLength / this.citations.length : 0
		};
	}
}
