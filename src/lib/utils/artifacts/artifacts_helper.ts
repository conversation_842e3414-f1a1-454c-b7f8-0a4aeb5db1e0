const innerDepsMap = {
	react: 'https://esm.sh/react@18.2.0',
	'react-dom/client': 'https://esm.sh/react-dom@18.2.0/client',
	'react-dom/': 'https://esm.sh/react-dom@18.2.0/',
	'react-dom': 'https://esm.sh/react-dom@18.2.0/',
	'lucide-react': 'https://esm.sh/lucide-react/?deps=react@18.2.0',
	'react-error-boundary': 'https://esm.sh/react-error-boundary/?deps=react@18.2.0',
	antd: 'https://esm.sh/antd?standalone&deps=react@18.2.0'
};

/**
 * 从代码中提取所有导入语句，生成importmap
 * @param code 源代码字符串
 * @returns importmap对象
 */
export function generateImportMap(code: string): { imports: Record<string, string> } {
	const imports: Record<string, string> = {
		react: proxyCdnUrl('https://esm.sh/react@18.2.0'),
		'react-dom/': proxyCdnUrl('https://esm.sh/react-dom@18.2.0/'),
		'react-error-boundary': proxyCdnUrl('https://esm.sh/react-error-boundary/?deps=react@18.2.0')
	};

	// 匹配import语句的正则表达式
	// 支持以下格式:
	// import pkg from 'package'
	// import { something } from 'package'
	// import * as pkg from 'package'
	const importRegex =
		/import(?:(?:(?:[ \n\t]+([^ *\n\t{},]+)[ \n\t]*(?:,|[ \n\t]+))?([ \n\t]*\{(?:[ \n\t]*[^ \n\t"'{}]+[ \n\t]*,?)+\})?[ \n\t]*)|[ \n\t]*\*[ \n\t]*as[ \n\t]+([^ \n\t{}]+)[ \n\t]+)from[ \n\t]*['"]([^'"]+)['"]|import[ \n\t]+['"]([^'"]+)['"];?/g;

	let match;
	while ((match = importRegex.exec(code)) !== null) {
		// 获取包名（从正则表达式的第4或第5个捕获组）
		const packageName = match[4] || match[5] || '';

		if (packageName) {
			// 优先使用innerDepsMap中预定义的URL
			if (packageName in innerDepsMap) {
				imports[packageName] = proxyCdnUrl(innerDepsMap[packageName as keyof typeof innerDepsMap]);
			} else {
				// 如果包名包含react，添加react依赖
				let url = packageName.includes('react')
					? `https://esm.sh/${packageName}?deps=react@18.2.0`
					: `https://esm.sh/${packageName}`;

				// 添加代理
				url = proxyCdnUrl(url);
				imports[packageName] = url;
			}
		}
	}

	return { imports };
}

/**
 * 为CDN链接添加代理前缀
 * @param url 原始URL
 * @returns 添加代理前缀后的URL
 */
export function proxyCdnUrl(url: string): string {
	// 只处理http或https开头的URL，忽略相对路径
	if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
		// console.log('代理前URL:', url);
		const proxiedUrl = `https://artifacts-cdn.chatglm.site/${url}`;
		// console.log('代理后URL:', proxiedUrl);
		return proxiedUrl;
	}
	return url;
}

/**
 * 处理iframe中的所有外部资源链接，添加代理。 在加载前就处理完了。
 * @param iframeElement iframe元素
 */
export function proxyIframeResources(htmlContent: string): string {
	try {
		// const showLog = window.localStorage.getItem('showLog') === 'true';
		const parser = new DOMParser();
		const doc = parser.parseFromString(htmlContent, 'text/html');

		// 处理script标签
		const scripts = doc.querySelectorAll('script[src]');
		scripts.forEach((script) => {
			const originalSrc = script.getAttribute('src') || '';
			if (originalSrc.startsWith('http')) {
				const newSrc = proxyCdnUrl(originalSrc);
				script.setAttribute('src', newSrc);
			}
		});

		// 处理link标签
		const links = doc.querySelectorAll('link[href]');
		links.forEach((link) => {
			const originalHref = link.getAttribute('href') || '';
			if (originalHref.startsWith('http')) {
				const newHref = proxyCdnUrl(originalHref);
				link.setAttribute('href', newHref);
			}
		});

		// 处理img标签
		const images = doc.querySelectorAll('img[src]');
		images.forEach((img) => {
			const originalSrc = img.getAttribute('src') || '';
			if (originalSrc.startsWith('http')) {
				const newSrc = proxyCdnUrl(originalSrc);
				img.setAttribute('src', newSrc);
			}
		});

		// 处理style标签中的CSS
		const styles = doc.querySelectorAll('style');
		styles.forEach((style) => {
			if (style.textContent) {
				style.textContent = style.textContent.replace(
					/url\(['"]?(https?:\/\/[^'")\s]+)['"]?\)/g,
					(match, url) => `url('${proxyCdnUrl(url)}')`
				);
			}
		});

		// 处理内联样式
		const elementsWithStyle = doc.querySelectorAll('[style*="url("]');
		elementsWithStyle.forEach((el) => {
			const styleAttr = el.getAttribute('style');
			if (styleAttr && styleAttr.includes('url(')) {
				const newStyle = styleAttr.replace(
					/url\(['"]?(https?:\/\/[^'")\s]+)['"]?\)/g,
					(match, url) => `url('${proxyCdnUrl(url)}')`
				);
				el.setAttribute('style', newStyle);
			}
		});

		// if (showLog) {
		// 	console.log('✅✅✅处理前的HTML:', htmlContent);
		// 	console.log('✅✅✅处理后的HTML:', doc.documentElement.outerHTML);
		// }
		// 返回处理后的HTML
		return doc.documentElement.outerHTML;
	} catch (error) {
		console.error('处理HTML资源时出错:', error);
		return htmlContent;
	}
}

export const isSupportPreview = (lang: string, content: string = '') =>
	['html', 'svg'].includes(lang) || (lang === 'xml' && content.includes('svg'));

export const checkCodePreview = (content: string) => {
	const codeBlockContents = content.match(/```[\s\S]*?```/g);
	let enable = false;
	if (codeBlockContents) {
		codeBlockContents.forEach((block) => {
			const lang = block.split('\n')[0].replace('```', '').trim().toLowerCase();
			const code = block.replace(/```[\s\S]*?\n/, '').replace(/```$/, '');
			if (isSupportPreview(lang, code)) {
				enable = true;
			}
		});
	}
	return enable;
};

export const handleHTMLContent = (htmlContent: string) => {
	const proxyHtmlContent = proxyIframeResources(htmlContent);

	return `
		<!DOCTYPE html>
		<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<${''}style>
					body {
						background-color: white; /* Ensure the iframe has a white background */
					}
				</${''}style>
			</head>
			<body>
				${proxyHtmlContent}
			</body>
		</html>
`;
};

export const handleJSXContent = (jsxContent: string) => {
	// 匹配export default 后的组件名称
	const match = jsxContent.match(/export\s+(default\s+)?(\w+)/);
	const componentName = match ? match[2] : 'App'; // 如果没有匹配到组件名称，则默认为 'App'
	const appComponent = `<${componentName} />`;
	const importMap = generateImportMap(jsxContent);

	return `
			<!DOCTYPE html>
				<html lang="en">
					<head>
						<meta charset="UTF-8">
						<meta name="viewport" content="width=device-width, initial-scale=1.0">
						<${''}style>
							body {
								background-color: white; /* Ensure the iframe has a white background */
							}
							
							/* 加载提示样式 */
							#loading-container {
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
								text-align: center;
								font-family: system-ui, -apple-system, sans-serif;
								color: #666;
								max-width: 80%;
							}

							#error-message {
								display: none;
								width: 100%;
								border: 2px solid #ff1212;
								background-color: #ff121203;
								padding: 8px;
								margin-top: 8px;
							}

							#error-message > pre {
								color: red;
								overflow: hidden;
								white-space: pre-wrap;
								word-wrap: break-word;
							}
							
						</${''}style>
						<${''}script src="https://cdn.tailwindcss.com"></${''}script>

						<${''}script>
							// 保存原始 fetch 方法
							const originalFetch = window.fetch;

							// 覆写 fetch 方法以捕获错误
							window.fetch = async function (...args) {
								try {
										const response = await originalFetch.apply(this, args);
										if (!response.ok) {
												if (response.url === 'https://esm.sh/transform' && response.status >= 400) {
													const msg = await response.json();
													const loadingContainer = document.getElementById('loading-message');
													if (loadingContainer) {
														loadingContainer.style.display = 'none';
													}
													const errorContainer = document.getElementById('error-message');
													if (errorContainer) {
														errorContainer.style.display = 'block';
														errorContainer.innerHTML = '<pre>' + msg.message + '</pre>';
													}
												}
										}
										return response;
								} catch (error) {
										console.error('Fetch error captured:', error);
										throw error; // 继续抛出错误
								}
							};
						</${''}script>

						<${''}script type="importmap">
							${JSON.stringify(importMap)}
						</${''}script>
						<${''}script type="module" src="https://esm.sh/tsx"></${''}script>
					</head>
					<body>
						<div id="root">
							<div id="loading-container">
								<div id="loading-message">
									<p>正在加载依赖库和编译代码，这可能需要一些时间...</p>
									<p style="font-size: 0.9em; margin-top: 8px;">如果加载时间过长，可能是模型出的代码有 bug</p>
								</div>
								<div id="error-message"></div>
							</div>
						</div>

						<${''}script type="text/babel">
							import { createRoot } from 'react-dom/client';
							import { ErrorBoundary } from 'react-error-boundary';

							function fallbackRender({ error, resetErrorBoundary }) {
								return (
									<div role="alert">
										<p>Something went wrong:</p>
										<pre style={{ color: "red" }}>{error.message}</pre>
									</div>
								);
							}

							${jsxContent}
							const rootElement = document.getElementById('root');
							createRoot(rootElement).render(
								<ErrorBoundary fallbackRender={fallbackRender}>
									${appComponent}
								</ErrorBoundary>
							);
						</${''}script>
					</body>
				</html>
		`;
};

/**
 * 检测是否是 iOS 微信浏览器
 * @returns 如果是 iOS 微信浏览器返回 true，否则返回 false
 */
export function isIosWechat(): boolean {
	const userAgent = navigator.userAgent.toLowerCase();
	
	// 检查是否是 iOS 设备
	const isIos = /iphone|ipad|ipod/.test(userAgent);
	
	// 检查是否是微信浏览器
	const isWechat = /micromessenger/.test(userAgent);
	
	return isIos && isWechat;
}

export const IsIosWechat = isIosWechat();

/**
 * 检测当前浏览器语言是否为中文
 * @returns 如果是中文语言返回 true，否则返回 false
 */
export function isChineseLanguage(): boolean {
	// 获取浏览器主要语言
	const primaryLanguage = navigator.language?.toLowerCase() || '';

	// 检查主要语言是否为中文
	if (primaryLanguage.startsWith('zh')) {
		return true;
	}
	
	
	return false
}

/**
 * 导出的中文语言判断变量
 */
export const IsChinese = isChineseLanguage();

