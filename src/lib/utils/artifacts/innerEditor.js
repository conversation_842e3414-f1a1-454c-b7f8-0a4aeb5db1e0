function initInnerEditor() {
	let currentEditElement = null;
	let smartInput = null;

	function enableEdit() {
		enableEditBlock();
	}

	// 鼠标进入事件
	function handleMouseOver(event) {
		event.stopPropagation();
		event.target.classList.add('editable-hover');
	}

	// 鼠标离开事件
	function handleMouseOut(event) {
		event.stopPropagation();
		event.target.classList.remove('editable-hover');
	}

	// 直接编辑元素的输入事件
	function handleInput(event) {
		const value = event.target.textContent;
		if (event.target.hasAttribute('data-z-edit-id')) {
			const editID = event.target.getAttribute('data-z-edit-id');
			window.parent.postMessage({
				type: 'manualEdit',
				data: {
					text: value,
					editID
				}
			});
		}
	}

	// 输入框失去焦点
	function handleElementBlur() {}

	// 响应esc退出编辑
	function handleElementKeydown(ev) {
		if (ev.key === 'Escape') {
			ev.preventDefault();
			disableCurrentEdit();
		}
	}

	// 在元素周围展示一个输入框，用于编辑
	function showSmartInput(element) {
		cancelSmartInput(); // 先关闭当前的

		const inputContainer = document.createElement('div');
		inputContainer.className = 'smart-edit-input';

		const rect = element.getBoundingClientRect();
		inputContainer.style.left = Math.min(rect.left, window.innerWidth - 420) + 'px';
		inputContainer.style.top = rect.bottom + 10 + 'px';

		// 如果输入框会超出底部，则显示在元素上方
		if (rect.bottom + 120 > window.innerHeight) {
			inputContainer.style.top = rect.top - 120 + 'px';
		}

		inputContainer.innerHTML = `
      <textarea id="smartEditTextarea" rows="1" placeholder="描述想修改的逻辑和样式"></textarea>
      <div class="smart-edit-buttons">
        <button id="smartSubmitButton" class="primary">Submit</button>
      </div>
    `;
		document.body.appendChild(inputContainer);
		smartInput = inputContainer;

		const textarea = inputContainer.querySelector('#smartEditTextarea');
		const submitButton = inputContainer.querySelector('#smartSubmitButton');
		if (!document.activeElement || document.activeElement.nodeName === 'BODY') {
			textarea?.focus();
		}

		inputContainer.addEventListener('keydown', (ev) => {
			if (ev.key === 'Escape') {
				ev.preventDefault();
				cancelSmartInput();
			}
		});

		submitButton?.addEventListener('click', () => {
			// 点击提交按钮，将文本内容应用到元素上
			if (currentEditElement) {
				window.parent.postMessage({
					type: 'editElement',
					data: {
						text: textarea?.value,
						startLine: currentEditElement.dataset.zStartLine,
						endLine: currentEditElement.dataset.zEndLine
					}
				});
				cancelSmartInput();
				disableCurrentEdit();
			}
		});
	}

	function cancelSmartInput() {
		if (smartInput) {
			document.body.removeChild(smartInput);
			smartInput = null;
		}
	}

	// 点击事件
	function handleClick(event) {
		event.preventDefault();
		event.stopPropagation();

		const element = event.target;
		if (currentEditElement === element) return;
		disableCurrentEdit();
		currentEditElement = element;
		element.setAttribute('contenteditable', 'true');
		// 如果element的子节点是文本节点,那么自动focus
		if (element.childNodes.length === 1 && element.childNodes[0].nodeType === Node.TEXT_NODE) {
			element.focus();
		}
		element.classList.remove('editable-hover');
		element.classList.add('element-editing');

		element.addEventListener('input', handleInput);
		element.addEventListener('blur', handleElementBlur);
		element.addEventListener('keydown', handleElementKeydown);

		showSmartInput(element);
	}

	// 编辑元素的点击事件
	function enableEditBlock() {
		// 获取所有块级和行内元素
		const elements = document.body.querySelectorAll('*');

		// 为每个元素添加鼠标事件监听
		elements.forEach((element) => {
			element.addEventListener('mouseover', handleMouseOver);
			element.addEventListener('mouseout', handleMouseOut);
			element.addEventListener('click', handleClick);
		});
	}

	function disableCurrentEdit() {
		if (currentEditElement) {
			currentEditElement.removeAttribute('contenteditable');
			currentEditElement.classList.remove('element-editing');
			currentEditElement.removeEventListener('input', handleInput);
			currentEditElement.removeEventListener('blur', handleElementBlur);
			currentEditElement.removeEventListener('keydown', handleElementKeydown);
			currentEditElement = null;
		}
	}

	enableEdit();
}

initInnerEditor();
