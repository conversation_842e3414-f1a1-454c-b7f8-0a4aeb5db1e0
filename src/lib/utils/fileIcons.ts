/**
 * File type icon mapping utilities
 * Maps file extensions to appropriate icons and colors
 */

import type { SupportedFileExtension } from './fileValidator';

export interface FileIconConfig {
	icon: string;
	color: string;
	bgColor: string;
	label: string;
	localIcon?: string; // Path to local SVG icon
}

// File type icon configurations
export const FILE_ICON_MAP: Record<SupportedFileExtension, FileIconConfig> = {
	// PDF
	pdf: {
		icon: '📄',
		color: '#DC2626',
		bgColor: '#FEE2E2',
		label: 'PDF',
		localIcon: '/icons/pdf.svg'
	},

	// Word Documents
	docx: {
		icon: '📝',
		color: '#2563EB',
		bgColor: '#DBEAFE',
		label: 'Word',
		localIcon: '/icons/word.svg'
	},
	doc: {
		icon: '📝',
		color: '#2563EB',
		bgColor: '#DBEAFE',
		label: 'Word',
		localIcon: '/icons/word.svg'
	},

	// Excel Spreadsheets
	xls: {
		icon: '📊',
		color: '#059669',
		bgColor: '#D1FAE5',
		label: 'Excel',
		localIcon: '/icons/excel.svg'
	},
	xlsx: {
		icon: '📊',
		color: '#059669',
		bgColor: '#D1FAE5',
		label: 'Excel',
		localIcon: '/icons/excel.svg'
	},

	// PowerPoint Presentations
	ppt: {
		icon: '📽️',
		color: '#DC2626',
		bgColor: '#FEE2E2',
		label: 'PowerPoint',
		localIcon: '/icons/ppt.svg'
	},
	pptx: {
		icon: '📽️',
		color: '#DC2626',
		bgColor: '#FEE2E2',
		label: 'PowerPoint',
		localIcon: '/icons/ppt.svg'
	},

	// Images (will use thumbnails instead of these icons)
	png: {
		icon: '🖼️',
		color: '#7C3AED',
		bgColor: '#EDE9FE',
		label: 'Image',
		localIcon: '/icons/image.svg'
	},
	jpg: {
		icon: '🖼️',
		color: '#7C3AED',
		bgColor: '#EDE9FE',
		label: 'Image',
		localIcon: '/icons/image.svg'
	},
	jpeg: {
		icon: '🖼️',
		color: '#7C3AED',
		bgColor: '#EDE9FE',
		label: 'Image',
		localIcon: '/icons/image.svg'
	},
	bmp: {
		icon: '🖼️',
		color: '#7C3AED',
		bgColor: '#EDE9FE',
		label: 'Image',
		localIcon: '/icons/image.svg'
	},
	gif: {
		icon: '🖼️',
		color: '#7C3AED',
		bgColor: '#EDE9FE',
		label: 'Image',
		localIcon: '/icons/image.svg'
	},

	// Text Files
	csv: {
		icon: '📋',
		color: '#059669',
		bgColor: '#D1FAE5',
		label: 'CSV',
		localIcon: '/icons/csv.svg'
	},
	py: {
		icon: '🐍',
		color: '#1D4ED8',
		bgColor: '#DBEAFE',
		label: 'Python',
		localIcon: '/icons/code.svg'
	},
	txt: {
		icon: '📄',
		color: '#6B7280',
		bgColor: '#F3F4F6',
		label: 'Text',
		localIcon: '/icons/txt.svg'
	},
	md: {
		icon: '📝',
		color: '#374151',
		bgColor: '#F3F4F6',
		label: 'Markdown',
		localIcon: '/icons/md.svg'
	}
};

/**
 * Get file icon configuration for a given extension
 */
export function getFileIconConfig(extension: string): FileIconConfig {
	const lowerExtension = extension.toLowerCase() as SupportedFileExtension;
	return FILE_ICON_MAP[lowerExtension] || {
		icon: '📄',
		color: '#6B7280',
		bgColor: '#F3F4F6',
		label: 'File',
		localIcon: '/icons/file.svg'
	};
}

/**
 * Get file type category for styling
 */
export function getFileTypeCategory(extension: string): 'document' | 'image' | 'text' | 'unknown' {
	const lowerExtension = extension.toLowerCase();

	if (['pdf', 'docx', 'doc', 'xls', 'xlsx', 'ppt', 'pptx'].includes(lowerExtension)) {
		return 'document';
	}

	if (['png', 'jpg', 'jpeg', 'bmp', 'gif'].includes(lowerExtension)) {
		return 'image';
	}

	if (['csv', 'py', 'txt', 'md'].includes(lowerExtension)) {
		return 'text';
	}

	return 'unknown';
}

/**
 * Check if file should display as thumbnail (images)
 */
export function shouldShowThumbnail(extension: string): boolean {
	return getFileTypeCategory(extension) === 'image';
}

/**
 * Generate CSS classes for file type styling
 */
export function getFileTypeClasses(extension: string): string {
	const category = getFileTypeCategory(extension);
	return `file-type-${category}`;
}

/**
 * Get inline styles for file icon
 */
export function getFileIconStyles(extension: string): { color: string; backgroundColor: string } {
	const config = getFileIconConfig(extension);
	return {
		color: config.color,
		backgroundColor: config.bgColor
	};
}
