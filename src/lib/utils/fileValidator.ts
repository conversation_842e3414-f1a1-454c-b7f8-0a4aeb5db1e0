/**
 * File validation utilities for the chat application
 * Supports Zhipu MaaS API file formats and size restrictions
 */

import { get } from 'svelte/store';
import { config } from '$lib/stores';
import { t } from 'i18next';

// Supported file extensions (Zhipu MaaS API supported formats)
export const SUPPORTED_FILE_EXTENSIONS = [
	'pdf',
	'docx', 
	'doc',
	'xls',
	'xlsx',
	'ppt',
	'pptx',
	'png',
	'jpg',
	'jpeg',
	'csv',
	'py',
	'txt',
	'md',
	'bmp',
	'gif'
] as const;

// Image file extensions for special handling
export const IMAGE_EXTENSIONS = [
	'png',
	'jpg', 
	'jpeg',
	'bmp',
	'gif'
] as const;

// Document file extensions
export const DOCUMENT_EXTENSIONS = [
	'pdf',
	'docx',
	'doc',
	'xls', 
	'xlsx',
	'ppt',
	'pptx'
] as const;

// Text file extensions
export const TEXT_EXTENSIONS = [
	'csv',
	'py',
	'txt',
	'md'
] as const;

export type SupportedFileExtension = typeof SUPPORTED_FILE_EXTENSIONS[number];
export type ImageExtension = typeof IMAGE_EXTENSIONS[number];
export type DocumentExtension = typeof DOCUMENT_EXTENSIONS[number];
export type TextExtension = typeof TEXT_EXTENSIONS[number];

export interface FileValidationResult {
	isValid: boolean;
	error?: string;
	extension?: string;
	isImage?: boolean;
	isDocument?: boolean;
	isText?: boolean;
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string | null {
	const extension = filename.split('.').pop()?.toLowerCase();
	return extension || null;
}

/**
 * Check if file extension is supported
 */
export function isSupportedExtension(extension: string): extension is SupportedFileExtension {
	return SUPPORTED_FILE_EXTENSIONS.includes(extension as SupportedFileExtension);
}

/**
 * Check if file is an image
 */
export function isImageFile(extension: string): extension is ImageExtension {
	return IMAGE_EXTENSIONS.includes(extension as ImageExtension);
}

/**
 * Check if file is a document
 */
export function isDocumentFile(extension: string): extension is DocumentExtension {
	return DOCUMENT_EXTENSIONS.includes(extension as DocumentExtension);
}

/**
 * Check if file is a text file
 */
export function isTextFile(extension: string): extension is TextExtension {
	return TEXT_EXTENSIONS.includes(extension as TextExtension);
}

/**
 * Get supported file types description for translation
 */
export function getSupportedFileTypesDescription(): string {
	return SUPPORTED_FILE_EXTENSIONS.map(ext => ext.toUpperCase()).join(', ');
}

/**
 * Validate file type
 */
export function validateFileType(file: File): FileValidationResult {
	const extension = getFileExtension(file.name);
	
	if (!extension) {
		return {
			isValid: false,
			error: t('File has no extension')
		};
	}

	if (!isSupportedExtension(extension)) {
		return {
			isValid: false,
			error: t('Unsupported file type: {{extension}}. Supported types: {{supportedTypes}}', {
				extension: extension.toUpperCase(),
				supportedTypes: getSupportedFileTypesDescription()
			}),
			extension
		};
	}

	return {
		isValid: true,
		extension,
		isImage: isImageFile(extension),
		isDocument: isDocumentFile(extension),
		isText: isTextFile(extension)
	};
}

/**
 * Validate file size
 */
export function validateFileSize(file: File): FileValidationResult {
	const configValue = get(config);
	const maxSizeMB = configValue?.file?.max_size;

	if (file.size === 0) {
		return {
			isValid: false,
			error: 'You cannot upload an empty file.'
		};
	}

	if (maxSizeMB !== null && maxSizeMB !== undefined && file.size > maxSizeMB * 1024 * 1024) {
		return {
			isValid: false,
			error: t('File size should not exceed {{maxSize}} MB.', {
				maxSize: maxSizeMB
			})
		};
	}

	return {
		isValid: true
	};
}

/**
 * Comprehensive file validation
 */
export function validateFile(file: File): FileValidationResult {
	// Check file size first
	const sizeValidation = validateFileSize(file);
	if (!sizeValidation.isValid) {
		return sizeValidation;
	}

	// Check file type
	const typeValidation = validateFileType(file);
	if (!typeValidation.isValid) {
		return typeValidation;
	}

	return {
		isValid: true,
		extension: typeValidation.extension,
		isImage: typeValidation.isImage,
		isDocument: typeValidation.isDocument,
		isText: typeValidation.isText
	};
}

/**
 * Validate multiple files
 */
export function validateFiles(files: File[]): { validFiles: File[]; errors: string[] } {
	const validFiles: File[] = [];
	const errors: string[] = [];

	files.forEach((file) => {
		const validation = validateFile(file);
		if (validation.isValid) {
			validFiles.push(file);
		} else {
			errors.push(`${file.name}: ${validation.error}`);
		}
	});

	return { validFiles, errors };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 Bytes';
	
	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Generate HTML accept attribute value for file input
 * This will filter files in the native file picker (Finder on macOS)
 */
export function getFileAcceptAttribute(): string {
	const extensions = SUPPORTED_FILE_EXTENSIONS.map(ext => `.${ext}`);
	return extensions.join(',');
}

/**
 * Generate HTML accept attribute for image files only
 */
export function getImageAcceptAttribute(): string {
	const extensions = IMAGE_EXTENSIONS.map(ext => `.${ext}`);
	return extensions.join(',');
}

/**
 * Generate HTML accept attribute for document files only
 */
export function getDocumentAcceptAttribute(): string {
	const extensions = DOCUMENT_EXTENSIONS.map(ext => `.${ext}`);
	return extensions.join(',');
}
