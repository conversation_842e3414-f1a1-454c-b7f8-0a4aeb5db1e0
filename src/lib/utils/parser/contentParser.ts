import fs from "fs"

const glmBlockRegex = new RegExp(`(\n\\s*)*<glm_block\\s+[^>]*>.*?<\\/glm_block>(\n\\s*)*`, 'gis')
const reasoningRegex = new RegExp(`(\n\\s*)*<details\\s+type="reasoning"[^>]*>.*?<\\/details>(\n\\s*)*`, 'gis')
const codeInterpreterRegex = new RegExp(`(\n\\s*)*<details\\s+type="code_interpreter"[^>]*>.*?<\\/details>(\n\\s*)*`, 'gis')

interface MatchResult {
  type: 'glm_block' | 'reasoning' | 'code_interpreter';
  content: any; // glm_block 返回 JSON 对象，reasoning 返回字符串，code_interpreter 返回 null
  index: number;
  matchText: string; // 完整的匹配文本
}

const matchFirst = (input: string): MatchResult | null => {
  const regexes = [
    { regex: glmBlockRegex, type: 'glm_block' as const },
    { regex: reasoningRegex, type: 'reasoning' as const },
    { regex: codeInterpreterRegex, type: 'code_interpreter' as const }
  ];

  let earliestMatch: MatchResult | null = null;
  let earliestIndex = Infinity;

  for (const { regex, type } of regexes) {
    // 重置正则的 lastIndex
    regex.lastIndex = 0;
    let currentType = type;

    const match = regex.exec(input);
    if (match && match.index < earliestIndex) {
      earliestIndex = match.index;

      let content: any = null;

      if (type === 'glm_block') {
        // 提取标签内的内容并解析为 JSON
        const innerContentMatch = match[0].match(/<glm_block[^>]*>(.*?)<\/glm_block>/is);
        if (innerContentMatch) {
          try {
            content = JSON.parse(innerContentMatch[1].trim());
            if (content?.type == "mcp") {
              content = content?.data?.metadata
              currentType = "mcp"
            }
          } catch (e) {
            // JSON 解析失败时返回原始文本
            content = innerContentMatch[1].trim();
          }
        }
      } else if (type === 'reasoning') {
        // 提取标签内的内容
        const innerContentMatch = match[0].match(/<details[^>]*>(.*?)<\/details>/is);
        if (innerContentMatch) {
          let reasoningContent = innerContentMatch[1].trim();

          // 去掉 summary 标签包裹的内容
          reasoningContent = reasoningContent.replace(/<summary[^>]*>.*?<\/summary>/gis, '');

          // 去掉每一行开头的 > 符号（markdown 引用格式）
          reasoningContent = reasoningContent
            .split('\n')
            .map(line => line.replace(/^\s*>\s?/, ''))
            .join('\n')
            .trim();

          content = "<think>" + reasoningContent + "</think>";
        }
      }
      // code_interpreter 不处理，content 保持 null

      earliestMatch = {
        type: currentType,
        content,
        index: match.index,
        matchText: match[0]
      };
    }
  }

  return earliestMatch;
}




interface ResultItem {
  content: any;
  type: any;
}

export const parseContent = (input: string) => {
  const results: ResultItem[] = []

  let position = 0;
  const maxCounter = 100;
  let counter = 0;
  while(counter < maxCounter) {
    counter += 1
    const 这次匹配的结果 = matchFirst(input.slice(position))
    if (!这次匹配的结果) {
      const 剩下的字符串 = input.slice(position).trim();
      if (剩下的字符串) {
        results.push({
          content: 剩下的字符串,
          type: 'text'
        })
      }
      break;
    }

    // 判断中间的字符串是不是 text，中间指的是这次匹配的文本，和上个 position 中间的空隙。
    const 中间的字符串 = input.slice(position, position + 这次匹配的结果.index).trim()

    if (中间的字符串) {
      results.push({
        content: 中间的字符串,
        type: 'text'
      })
    }

    // 判断各种类型
    if (这次匹配的结果.type != "code_interpreter") {
      results.push({
        content: 这次匹配的结果.content,
        type: 这次匹配的结果.type
      })
    }
    position += 这次匹配的结果.index + 这次匹配的结果.matchText.length;
    if (position >= input.length -1) {
      break;
    }
    // console.log("🤡", 这次匹配的结果, "最后的 position", position,"总共的length", input.length)
    
  }

  return results;
  // console.log("匹配的结果", results)
}


// const test = () => {
//   const input = fs.readFileSync("case0616_small", { encoding: "utf-8" })
//   const result = parseContent(input)
//   console.log("🤡", result)
// }

// test()