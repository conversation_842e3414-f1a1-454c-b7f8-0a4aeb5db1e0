import { writable } from 'svelte/store';

export interface CitationItem {
	title: string;
	url: string;
	text: string;
	index: number;
	favicon?: string;
}

export interface BrowserItem {
	search_result: CitationItem[];
	current_url?: string;
	page_title?: string;
}

export interface MCPServer {
	name: string;
}

export interface MCPMetadata {
	id: string;
	name: string;
	arguments: string; // 参数，固定存在的tab
	result: string; // 结果，固定存在的tab
	display_result?: string;
	duration: string;
	status?: 'executing' | 'completed' | 'error'; // 执行状态
	mcp_server?: MCPServer; // MCP服务器信息
}

export interface MCPData {
	metadata: MCPMetadata; // 元信息
	thought?: string; // 当前激活的tab
	ppt?: { name: ''; position: number; show: boolean; version: number }; // 存在则表示有ppt可以展示，此时调接口更新ppt数据
	browser?: BrowserItem; // 模拟浏览器工具
}

export const mcpData = writable<MCPData | null>();
export const showMCPSidePanel = writable<boolean>(false);


