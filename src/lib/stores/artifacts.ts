import { writable } from 'svelte/store';

type ArtifactsMessageID = string;
type ArtifactsMessage = {
	id: ArtifactsMessageID;
	lang: string;
	code: string; // source code
	mid: string; // message id
};

type ArtifactsMessageSource = Record<ArtifactsMessageID, ArtifactsMessage>;

// 每个codeBlock,message.done时，将能渲染的类型和源代码上报到这里
export const artifactsMessages = writable<ArtifactsMessageSource>({});
// artifactsMessages.subscribe((value) => {
// 	console.log('artifactsMessages', value);
// });
export const selectedArtifactsMessageID = writable<ArtifactsMessageID>('');
