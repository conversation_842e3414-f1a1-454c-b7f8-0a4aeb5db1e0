{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "만료 없음은 's', 'm', 'h', 'd', 'w' 아니면 '-1' 중 하나를 사용하세요.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(예: `sh webui.sh --api --api-auth 사용자이름_비밀번호`)", "(e.g. `sh webui.sh --api`)": "(예: `sh webui.sh --api`)", "(latest)": "(최근)", "(Ollama)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}의 채팅", "{{webUIName}} Backend Required": "{{webUIName}} 백엔드가 필요합니다.", "*Prompt node ID(s) are required for image generation": "사진 생성을 위해 프롬프트 노드 ID가 필요합니다", "A new version (v{{LATEST_VERSION}}) is now available.": "최신 버전 (v{{LATEST_VERSION}})이 가능합니다", "A task model is used when performing tasks such as generating titles for chats and web search queries": "작업 모델은 채팅 및 웹 검색 쿼리에 대한 제목 생성 등의 작업 수행 시 사용됩니다.", "a user": "사용자", "About": "정보", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "접근", "Access Control": "접근 제어", "Accessible to all users": "모든 사용자가 접근 가능", "Account": "계정", "Account Activation Pending": "계정 활성화 대기", "Accurate information": "정확한 정보", "Actions": "행동", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "채팅에서 \"{{COMMAND}}\"을 입력하여 이 명령을 활성화할 수 있습니다.", "Active Users": "활성 사용자", "Add": "추가", "Add a model ID": "모델 ID 추가", "Add a short description about what this model does": "모델의 기능에 대한 간단한 설명 추가", "Add a tag": "태그 추가", "Add Arena Model": "아레나 모델 추가", "Add Connection": "연결 추가", "Add Content": "내용 추가", "Add content here": "여기에 내용을 추가하세요", "Add custom prompt": "사용자 정의 프롬프트 추가", "Add Files": "파일 추가", "Add Group": "그룹 추가", "Add Memory": "메모리 추가", "Add Model": "모델 추가", "Add Reaction": "리액션 추가", "Add Tag": "태그 추가", "Add Tags": "태그 추가", "Add text content": "글 추가", "Add User": "사용자 추가", "Add User Group": "사용자 그룹 추가", "Adjusting these settings will apply changes universally to all users.": "위와 같이 설정시 모든 사용자에게 적용됩니다.", "admin": "관리자", "Admin": "관리자", "Admin Panel": "관리자 패널", "Admin Settings": "관리자 설정", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "관리자는 항상 모든 도구에 접근할 수 있지만, 사용자는 워크스페이스에서 모델마다 도구를 할당받아야 합니다.", "Advanced Parameters": "고급 매개변수", "Advanced Params": "고급 매개변수", "All": "", "All Documents": "모든 문서", "All models deleted successfully": "성공적으로 모든 모델이 삭제되었습니다", "Allow Chat Controls": "채팅 제어 허용", "Allow Chat Delete": "채팅 삭제 허용", "Allow Chat Deletion": "채팅 삭제 허용", "Allow Chat Edit": "채팅 수정 허용", "Allow File Upload": "파일 업로드 허용", "Allow non-local voices": "외부 음성 허용", "Allow Temporary Chat": "임시 채팅 허용", "Allow User Location": "사용자 위치 활용 허용", "Allow Voice Interruption in Call": "음성 기능에서 음성 방해 허용", "Allowed Endpoints": "", "Already have an account?": "이미 계정이 있으신가요?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Amazing": "놀라움", "an assistant": "어시스턴트", "Analyzed": "", "Analyzing...": "", "and": "그리고", "and {{COUNT}} more": "그리고 {{COUNT}} 더", "and create a new shared link.": "새로운 공유 링크를 생성합니다.", "API Base URL": "API 기본 URL", "API Key": "API 키", "API Key created.": "API 키가 생성되었습니다.", "API Key Endpoint Restrictions": "API 키 엔드포인트 제한", "API keys": "API 키", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "\"사용자\" 권한의 모든 사용자에게 적용됩니다", "April": "4월", "Archive": "보관", "Archive All Chats": "모든 채팅 보관", "Archived Chats": "보관된 채팅", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "정말 이 채널을 삭제하시겠습니까?", "Are you sure you want to delete this message?": "정말 이 메세지를 삭제하시겠습니까?", "Are you sure you want to unarchive all archived chats?": "정말 보관된 모든 채팅을 보관 해제하시겠습니까?", "Are you sure?": "확실합니까?", "Arena Models": "아레나 모델", "Artifacts": "아티팩트", "Ask": "", "Ask a question": "질문하기", "Assistant": "어시스턴트", "Attach file from knowledge": "", "Attention to detail": "세부 사항에 대한 주의", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "오디오", "August": "8월", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "응답을 클립보드에 자동 복사", "Auto-playback response": "응답 자동 재생", "Autocomplete Generation": "자동완성 생성", "Autocomplete Generation Input Max Length": "자동완성 생성 입력 최대 길이", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Automatic1111 API 인증 문자", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 기본 URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 기본 URL 설정이 필요합니다.", "Available list": "가능한 목록", "Available Tools": "", "available!": "사용 가능!", "Awful": "끔찍함", "Azure AI Speech": "Azure AI 음성", "Azure Region": "Azure 지역", "Back": "뒤로가기", "Bad Response": "잘못된 응답", "Banners": "배너", "Base Model (From)": "기본 모델(시작)", "Batch Size (num_batch)": "배치 크기 (num_batch)", "before": "이전", "Being lazy": "게으름 피우기", "Beta": "", "Bing Search V7 Endpoint": "Bing Search V7 엔드포인트", "Bing Search V7 Subscription Key": "Bing Search V7 구독 키", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Brave Search API Key": "Brave Search API 키", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass SSL verification for Websites": "웹 사이트에 대한 SSL 검증 무시: ", "Calendar": "", "Call": "음성 기능", "Call feature is not supported when using Web STT engine": "웹 STT 엔진 사용 시, 음성 기능은 지원되지 않습니다.", "Camera": "카메라", "Cancel": "취소", "Capabilities": "기능", "Capture": "", "Certificate Path": "", "Change Password": "비밀번호 변경", "Channel Name": "", "Channels": "채널", "Character": "캐릭터", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "채팅", "Chat Background Image": "채팅 배경 이미지", "Chat Bubble UI": "버블형 채팅 UI", "Chat Controls": "채팅 제어", "Chat direction": "채팅 방향", "Chat Overview": "채팅", "Chat Permissions": "채팅 권한", "Chat Tags Auto-Generation": "채팅 태그 자동생성", "Chats": "채팅", "Check Again": "다시 확인", "Check for updates": "업데이트 확인", "Checking for updates...": "업데이트 확인중...", "Choose a model before saving...": "저장하기 전에 모델을 선택하세요...", "Chunk Overlap": "Chunk 오버랩", "Chunk Size": "Chunk 크기", "Ciphers": "", "Citation": "인용", "Clear memory": "메모리 초기화", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "도움말을 보려면 여기를 클릭하세요.", "Click here to": "여기를 클릭하면", "Click here to download user import template file.": "사용자 삽입 템플렛 파일을 다운받으려면 여기를 클릭하세요", "Click here to learn more about faster-whisper and see the available models.": "빠른 속삭임에 대해 배우거나 가능한 모델을 보려면 여기를 클릭하세요", "Click here to see available models.": "", "Click here to select": "선택하려면 여기를 클릭하세요.", "Click here to select a csv file.": "csv 파일을 선택하려면 여기를 클릭하세요.", "Click here to select a py file.": "py 파일을 선택하려면 여기를 클릭하세요.", "Click here to upload a workflow.json file.": "workflow.json 파일을 업로드하려면 여기를 클릭하세요", "click here.": "여기를 클릭하세요.", "Click on the user role button to change a user's role.": "사용자 역할 버튼을 클릭하여 사용자의 역할을 변경하세요.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "클립보드 사용 권한이 거절되었습니다. 필요한 접근을 사용하기 위해 브라우져 설정을 확인 부탁드립니다.", "Clone": "복제", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "닫기", "Code execution": "코드 실행", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "성공적으로 코드가 생성되었습니다", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "컬렉션", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI 기본 URL", "ComfyUI Base URL is required.": "ComfyUI 기본 URL이 필요합니다.", "ComfyUI Workflow": "ComfyUI 워크플로", "ComfyUI Workflow Nodes": "ComfyUI 워크플로 노드", "Command": "명령", "Completions": "완성됨", "Concurrent Requests": "동시 요청 수", "Configure": "", "Confirm": "확인", "Confirm Password": "비밀번호 확인", "Confirm your action": "액션 확인", "Confirm your new password": "새로운 비밀번호를 한 번 더 입력해 주세요", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connections": "연결", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "WebUI 접속을 위해서는 관리자에게 연락에 연락하십시오", "Content": "내용", "Content Extraction Engine": "", "Context Length": "내용 길이", "Continue Response": "대화 계속", "Continue with {{provider}}": "{{provider}}로 계속", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTS 요청에 메시지가 어떻게 나뉘어지는지 제어하십시오. '문장 부호'는 문장으로 나뉘고, '문단'은 문단으로 나뉘고, '없음'은 메세지를 하나의 문자열로 인식합니다.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "제어", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "복사됨", "Copied shared chat URL to clipboard!": "채팅 공유 URL이 클립보드에 복사되었습니다!", "Copied to clipboard": "클립보드에 복사되었습니다", "Copy": "복사", "Copy last code block": "마지막 코드 블록 복사", "Copy last response": "마지막 응답 복사", "Copy Link": "링크 복사", "Copy to clipboard": "클립보드에 복사", "Copying to clipboard was successful!": "성공적으로 클립보드에 복사되었습니다!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "생성", "Create a knowledge base": "지식 기반 생성", "Create a model": "모델 생성", "Create Account": "계정 생성", "Create Admin Account": "관리자 계정 생성", "Create Channel": "채널 생성", "Create Group": "그룹 생성", "Create Knowledge": "지식 생성", "Create new key": "새로운 키 생성", "Create new secret key": "새로운 비밀 키 생성", "Created at": "생성일", "Created At": "생성일", "Created by": "생성자", "CSV Import": "CSV 가져오기", "Ctrl+Enter to Send": "", "Current Model": "현재 모델", "Current Password": "현재 비밀번호", "Custom": "사용자 정의", "Danger Zone": "", "Dark": "다크", "Database": "데이터베이스", "December": "12월", "Default": "기본값", "Default (Open AI)": "기본값 (Open AI)", "Default (SentenceTransformers)": "기본값 (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model’s built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "기본 모델", "Default model updated": "기본 모델이 업데이트되었습니다.", "Default Models": "기본 모델", "Default permissions": "기본 권한", "Default permissions updated successfully": "성공적으로 기본 권한이 수정되었습니다", "Default Prompt Suggestions": "기본 프롬프트 제안", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "기본 사용자 역할", "Delete": "삭제", "Delete a model": "모델 삭제", "Delete All Chats": "모든 채팅 삭제", "Delete All Models": "모든 모델 삭제", "Delete chat": "채팅 삭제", "Delete Chat": "채팅 삭제", "Delete chat?": "채팅을 삭제하겠습니까?", "Delete folder?": "폴더를 삭제하시겠습니까?", "Delete function?": "함수를 삭제하시겠습니까?", "Delete Message": "", "Delete message?": "", "Delete prompt?": "프롬프트를 삭제하시겠습니까?", "delete this link": "이 링크를 삭제합니다.", "Delete tool?": "도구를 삭제하시겠습니까?", "Delete User": "사용자 삭제", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} 삭제됨", "Deleted {{name}}": "{{name}}을(를) 삭제했습니다.", "Deleted User": "삭제된 사용자", "Describe your knowledge base and objectives": "지식 기반에 대한 설명과 목적을 입력하세요", "Description": "설명", "Didn't fully follow instructions": "완전히 지침을 따르지 않음", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Connections settings updated": "", "Direct Tool Servers": "", "Disabled": "제한됨", "Discover a function": "함수 검색", "Discover a model": "모델 검색", "Discover a prompt": "프롬프트 검색", "Discover a tool": "도구 검색", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "사용자 정의 함수 검색, 다운로드 및 탐색", "Discover, download, and explore custom prompts": "사용자 정의 프롬프트 검색, 다운로드 및 탐색", "Discover, download, and explore custom tools": "사용자 정의 도구 검색, 다운로드 및 탐색", "Discover, download, and explore model presets": "모델 사전 설정 검색, 다운로드 및 탐색", "Dismissible": "제외가능", "Display": "", "Display Emoji in Call": "음성기능에서 이모지 표시", "Display the username instead of You in the Chat": "채팅에서 '당신' 대신 사용자 이름 표시", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "불분명한 출처를 가진 함수를 설치하지마세요", "Do not install tools from sources you do not fully trust.": "불분명한 출처를 가진 도구를 설치하지마세요", "Docling": "", "Docling Server URL required.": "", "Document": "문서", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "문서 조사", "Documents": "문서", "does not make any external connections, and your data stays securely on your locally hosted server.": "외부와 어떠한 연결도 하지 않으며, 데이터는 로컬에서 호스팅되는 서버에 안전하게 유지됩니다.", "Domain Filter List": "", "Don't have an account?": "계정이 없으신가요?", "don't install random functions from sources you don't trust.": "불분명한 출처를 가진 임의의 함수를 설치하지마세요", "don't install random tools from sources you don't trust.": "불분명한 출처를 가진 임의의 도구를 설치하지마세요", "Don't like the style": "스타일이 마음에 안 드시나요?", "Done": "완료됨", "Download": "다운로드", "Download as SVG": "", "Download canceled": "다운로드 취소", "Download Database": "데이터베이스 다운로드", "Drag and drop a file to upload or select a file to view": "", "Draw": "그리기", "Drop any files here to add to the conversation": "대화에 추가할 파일을 여기에 드롭하세요.", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "예: '30초','10분'. 유효한 시간 단위는 '초', '분', '시'입니다.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "편집", "Edit Arena Model": "아레나 모델 편집", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "메모리 편집", "Edit User": "사용자 편집", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "이메일", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "임베딩 배치 크기", "Embedding Model": "임베딩 모델", "Embedding Model Engine": "임베딩 모델 엔진", "Embedding model set to \"{{embedding_model}}\"": "임베딩 모델을 \"{{embedding_model}}\"로 설정함", "Enable API Key": "API 키 활성화", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "커뮤니티 공유 활성화", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "메시지 평가 활성화", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "새 회원가입 활성화", "Enabled": "활성화됨", "Enforce Temporary Chat": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSV 파일에 이름, 이메일, 비밀번호, 역할 4개의 열이 순서대로 포함되어 있는지 확인하세요.", "Enter {{role}} message here": "여기에 {{role}} 메시지 입력", "Enter a detail about yourself for your LLMs to recall": "자신에 대한 세부사항을 입력하여 LLM들이 기억할 수 있도록 하세요.", "Enter api auth string (e.g. username:password)": "API 인증 문자 입력 (예: 사용자 이름:비밀번호)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "Bing Search V7 엔드포인트 입력", "Enter Bing Search V7 Subscription Key": "Bing Search V7 구독 키 입력", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Brave Search API Key 입력", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "CFG Scale 입력 (예: 7.0)", "Enter Chunk Overlap": "청크 오버랩 입력", "Enter Chunk Size": "청크 크기 입력", "Enter comma-seperated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter description": "설명 입력", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter Github Raw URL": "Github Raw URL 입력", "Enter Google PSE API Key": "Google PSE API 키 입력", "Enter Google PSE Engine Id": "Google PSE 엔진 ID 입력", "Enter Image Size (e.g. 512x512)": "이미지 크기 입력(예: 512x512)", "Enter Jina API Key": "Jina API 키 입력", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "Kagi Search API 키 입력", "Enter Key Behavior": "", "Enter language codes": "언어 코드 입력", "Enter Mistral API Key": "", "Enter Model ID": "모델 ID 입력", "Enter model tag (e.g. {{modelTag}})": "모델 태그 입력(예: {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search API 키 입력", "Enter Number of Steps (e.g. 50)": "단계 수 입력(예: 50)", "Enter Perplexity API Key": "", "Enter proxy URL (e.g. **************************:port)": "프록시 URL 입력(예: **************************:port)", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "샘플러 입력 (예: 오일러 a(<PERSON><PERSON><PERSON> a))", "Enter Scheduler (e.g. Karras)": "스케쥴러 입력 (예: 카라스(Ka<PERSON><PERSON>))", "Enter Score": "점수 입력", "Enter SearchApi API Key": "SearchApi API 키 입력", "Enter SearchApi Engine": "SearchApi 엔진 입력", "Enter Searxng Query URL": "Searxng 쿼리 URL 입력", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Serper API 키 입력", "Enter Serply API Key": "Serply API 키 입력", "Enter Serpstack API Key": "Serpstack API 키 입력", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "중지 시퀀스 입력", "Enter system prompt": "시스템 프롬프트 입력", "Enter system prompt here": "", "Enter Tavily API Key": "Tavily API 키 입력", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "WebUI의 공개 URL을 입력해 주세요. 이 URL은 알림에서 링크를 생성하는 데 사용합니다.", "Enter Tika Server URL": "Tika 서버 URL 입력", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Top K 입력", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL 입력(예: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL 입력(예: http://localhost:11434)", "Enter your current password": "현재 비밀번호를 입력해 주세요", "Enter Your Email": "이메일 입력", "Enter Your Full Name": "이름 입력", "Enter your message": "메세지 입력", "Enter your name": "", "Enter your new password": "새로운 비밀번호를 입력해 주세요", "Enter Your Password": "비밀번호 입력", "Enter Your Role": "역할 입력", "Enter Your Username": "", "Enter your webhook URL": "웹훅 URL을 입력해 주세요", "Error": "오류", "ERROR": "오류", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "평가", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "미포함", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "실험적", "Explain": "", "Explain this section to me in more detail": "", "Explore the cosmos": "", "Export": "내보내기", "Export All Archived Chats": "", "Export All Chats (All Users)": "모든 채팅 내보내기(모든 사용자)", "Export chat (.json)": "채팅 내보내기 (.json)", "Export Chats": "채팅 내보내기", "Export Config to JSON File": "Config를 JSON 파일로 내보내기", "Export Functions": "함수 내보내기", "Export Models": "모델 내보내기", "Export Presets": "프리셋 내보내기", "Export Prompts": "프롬프트 내보내기", "Export to CSV": "", "Export Tools": "도구 내보내기", "External": "", "External Models": "외부 모델", "Failed to add file.": "파일추가에 실패했습니다", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to create API Key.": "API 키 생성에 실패했습니다.", "Failed to fetch models": "", "Failed to read clipboard contents": "클립보드 내용 가져오기를 실패하였습니다.", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "설정 업데이트에 실패하였습니다.", "Failed to upload file.": "파일 업로드에 실패했습니다", "Features": "", "Features Permissions": "기능 권한", "February": "2월", "Feedback History": "피드백 기록", "Feedbacks": "피드백", "Feel free to add specific details": "자세한 내용을 자유롭게 추가하세요.", "File": "파일", "File added successfully.": "성공적으로 파일이 추가되었습니다", "File content updated successfully.": "성공적으로 내용이 업데이트되었습니다", "File Mode": "파일 모드", "File not found.": "파일을 찾을 수 없습니다.", "File removed successfully.": "성공적으로 파일이 제거되었습니다", "File size should not exceed {{maxSize}} MB.": "파일 사이즈가 {{maxSize}} MB를 초과하면 안됩니다.", "File uploaded successfully": "", "Files": "파일", "Filter is now globally disabled": "전반적으로 필터 비활성화됨", "Filter is now globally enabled": "전반적으로 필터 활성화됨", "Filters": "필터", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingerprint spoofing 감지: 이니셜을 아바타로 사용할 수 없습니다. 기본 프로필 이미지로 설정합니다.", "Fluidly stream large external response chunks": "대규모 외부 응답 청크를 유연하게 스트리밍", "Focus chat input": "채팅 입력창에 포커스", "Folder deleted successfully": "성공적으로 폴터가 생성되었습니다", "Folder name cannot be empty": "폴더 이름을 작성해주세요", "Folder name cannot be empty.": "폴더 이름을 작성해주세요", "Folder name updated successfully": "성공적으로 폴더 이름이 저장되었습니다", "Followed instructions perfectly": "명령을 완벽히 수행함", "Forge new paths": "", "Form": "폼", "Format your variables using brackets like this:": "변수를 다음과 같이 괄호를 사용하여 생성하세요", "Forwards system user session credentials to authenticate": "", "Frequency Penalty": "빈도 페널티", "Full Context Mode": "", "Function": "함수", "Function Calling": "", "Function created successfully": "성공적으로 함수가 생성되었습니다", "Function deleted successfully": "성공적으로 함수가 삭제되었습니다", "Function Description": "", "Function ID": "", "Function is now globally disabled": "전반적으로 함수 비활성화됨", "Function is now globally enabled": "전반적으로 함수 활성화됨", "Function Name": "", "Function updated successfully": "성공적으로 함수가 업데이트되었습니다", "Functions": "함수", "Functions allow arbitrary code execution": "함수로 임이의 코드 실행 허용하기", "Functions allow arbitrary code execution.": "함수가 임이의 코드를 실행하도록 허용하였습니다", "Functions imported successfully": "성공적으로 함수가 가져왔습니다", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "일반", "Generate an image": "", "Generate Image": "이미지 생성", "Generate prompt pair": "", "Generating search query": "검색 쿼리 생성", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "글로벌", "Good Response": "좋은 응답", "Google Drive": "", "Google PSE API Key": "Google PSE API 키", "Google PSE Engine Id": "Google PSE 엔진 ID", "Group created successfully": "성공적으로 그룹을 생성했습니다", "Group deleted successfully": "성공적으로 그룹을 삭제했습니다", "Group Description": "그룹 설명", "Group Name": "그룹 명", "Group updated successfully": "성공적으로 그룹을 수정했습니다", "Groups": "그룹", "Haptic Feedback": "햅틱 피드백", "has no conversations.": "대화가 없습니다.", "Hello, {{name}}": "안녕하세요, {{name}}", "Help": "도움말", "Help us create the best community leaderboard by sharing your feedback history!": "당신의 피드백 기록을 공유함으로서 최고의 커뮤니티 리더보드를 만드는데 도와주세요!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "숨기기", "Hide Model": "", "Home": "", "Host": "", "How can I help you today?": "오늘 어떻게 도와드릴까요?", "How would you rate this response?": "이 응답을 어떻게 평가하시겠어요?", "Hybrid Search": "하이브리드 검색", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "ID", "Ignite curiosity": "", "Image": "이미지", "Image Compression": "이미지 압축", "Image Generation": "이미지 생성", "Image Generation (Experimental)": "이미지 생성(실험적)", "Image Generation Engine": "이미지 생성 엔진", "Image Max Compression Size": "이미지 최대 압축 크기", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "이미지 설정", "Images": "이미지", "Import Chats": "채팅 가져오기", "Import Config from JSON File": "JSON 파일에서 Config 불러오기", "Import Functions": "함수 가져오기", "Import Models": "모델 가져오기", "Import Presets": "프리셋 가져오기", "Import Prompts": "프롬프트 가져오기", "Import Tools": "도구 가져오기", "Include": "포함", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webui를 실행 시 `--api-auth` 플래그를 포함하세요", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webui를 실행 시 `--api` 플래그를 포함하세요", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "정보", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "명령어 입력", "Install from Github URL": "G<PERSON><PERSON> URL에서 설치", "Instant Auto-Send After Voice Transcription": "음성 변환 후 즉시 자동 전송", "Integration": "", "Interface": "인터페이스", "Invalid file format.": "잘못된 파일 형식", "Invalid JSON schema": "", "Invalid Tag": "잘못된 태그", "is typing...": "", "January": "1월", "Jina API Key": "Jina API 키", "join our Discord for help.": "도움말을 보려면 Discord에 가입하세요.", "JSON": "JSON", "JSON Preview": "JSON 미리 보기", "July": "7월", "June": "6월", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT 만료", "JWT Token": "JWT 토큰", "Kagi Search API Key": "Kagi Search API 키", "Keep Alive": "계속 유지하기", "Key": "", "Keyboard shortcuts": "키보드 단축키", "Knowledge": "지식 기반", "Knowledge Access": "지식 접근", "Knowledge created successfully.": "성공적으로 지식 기반이 생성되었습니다", "Knowledge deleted successfully.": "성공적으로 지식 기반이 삭제되었습니다", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "성공적으로 지식 기반이 초기화되었습니다", "Knowledge updated successfully": "성공적으로 지식 기반이 업데이트되었습니다", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "랜딩페이지 모드", "Language": "언어", "Last Active": "최근 활동", "Last Modified": "마지막 수정", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "리더보드", "Learn more about OpenAPI tool servers.": "", "Leave empty for unlimited": "무제한을 위해 빈칸으로 남겨두세요", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "특정 모델을 선택하거나 모든 모델을 포함하고 싶으면 빈칸으로 남겨두세요", "Leave empty to use the default prompt, or enter a custom prompt": "기본 프롬프트를 사용하기 위해 빈칸으로 남겨두거나, 커스텀 프롬프트를 입력하세요", "Leave model field empty to use the default model.": "", "License": "", "Light": "라이트", "Listening...": "듣는 중...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM에 오류가 있을 수 있습니다. 중요한 정보는 확인이 필요합니다.", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Models": "로컬 모델", "Location access not allowed": "", "Logit Bias": "", "Lost": "패배", "LTR": "LTR", "Made by Open WebUI Community": "OpenWebUI 커뮤니티에 의해 개발됨", "Make sure to enclose them with": "꼭 다음으로 감싸세요:", "Make sure to export a workflow.json file as API format from ComfyUI.": "꼭 workflow.json 파일을 ComfyUI의 API 형식대로 내보내세요", "Manage": "관리", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "Ollama API 연결 관리", "Manage OpenAI API Connections": "OpenAI API 연결 관리", "Manage Pipelines": "파이프라인 관리", "Manage Tool Servers": "", "March": "3월", "Max Tokens (num_predict)": "최대 토큰(num_predict)", "Max Upload Count": "업로드 최대 수", "Max Upload Size": "업로드 최대 사이즈", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "최대 3개의 모델을 동시에 다운로드할 수 있습니다. 나중에 다시 시도하세요.", "May": "5월", "Memories accessible by LLMs will be shown here.": "LLM에서 접근할 수 있는 메모리는 여기에 표시됩니다.", "Memory": "메모리", "Memory added successfully": "성공적으로 메모리가 추가되었습니다", "Memory cleared successfully": "성공적으로 메모리가 정리되었습니다", "Memory deleted successfully": "성공적으로 메모리가 삭제되었습니다", "Memory updated successfully": "성공적으로 메모리가 업데이트되었습니다", "Merge Responses": "응답들 결합하기", "Message rating should be enabled to use this feature": "이 기능을 사용하려면 메시지 평가가 활성화되어야합니다", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "링크 생성 후에 보낸 메시지는 공유되지 않습니다. URL이 있는 사용자는 공유된 채팅을 볼 수 있습니다.", "Min P": "최소 P", "Minimum Score": "최소 점수", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "모델", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' 모델이 성공적으로 다운로드되었습니다.", "Model '{{modelTag}}' is already in queue for downloading.": "'{{modelTag}}' 모델은 이미 다운로드 대기열에 있습니다.", "Model {{modelId}} not found": "{{modelId}} 모델을 찾을 수 없습니다.", "Model {{modelName}} is not vision capable": "{{modelName}} 모델은 비전을 사용할 수 없습니다.", "Model {{name}} is now {{status}}": "{{name}} 모델은 이제 {{status}} 상태입니다.", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts image inputs": "모델이 이미지 삽입을 허용합니다", "Model created successfully!": "성공적으로 모델이 생성되었습니다", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "모델 파일 시스템 경로가 감지되었습니다. 업데이트하려면 모델 단축 이름이 필요하며 계속할 수 없습니다.", "Model Filtering": "", "Model ID": "모델 ID", "Model IDs": "", "Model Name": "모델 이름", "Model not selected": "모델이 선택되지 않았습니다.", "Model Params": "모델 파라미터", "Model Permissions": "", "Model updated successfully": "성공적으로 모델이 업데이트되었습니다", "Modelfile Content": "Modelfile 내용", "Models": "모델", "Models Access": "모델 접근", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "Mojeek Search API 키", "more": "더보기", "More": "더보기", "Name": "이름", "Name your knowledge base": "지식 기반 이름을 지정하세요", "Native": "", "New Chat": "새 채팅", "New Folder": "", "New Password": "새 비밀번호", "new-channel": "", "No content found": "내용을 찾을 수 없음", "No content to speak": "음성 출력할 내용을 찾을 수 없음", "No distance available": "거리 불가능", "No feedbacks found": "피드백 없음", "No file selected": "파일이 선택되지 않음", "No files found.": "파일 없음", "No groups with access, add a group to grant access": "접근 권한이 있는 그룹이 없습니다. 접근 권한을 부여하려면 그룹을 추가하세요.", "No HTML, CSS, or JavaScript content found.": "HTML, CSS, JavaScript이 발견되지 않음", "No inference engine with management support found": "", "No knowledge found": "지식 기반 없음", "No memories to clear": "", "No model IDs": "", "No models found": "모델 없음", "No models selected": "", "No results found": "결과 없음", "No search query generated": "검색어가 생성되지 않았습니다.", "No source available": "사용 가능한 소스 없음", "No users were found.": "", "No valves to update": "업데이트 할 변수 없음", "None": "없음", "Not factually correct": "사실상 맞지 않음", "Not helpful": "도움이 되지않음", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "참고: 최소 점수를 설정하면, 검색 결과로 최소 점수 이상의 점수를 가진 문서만 반환합니다.", "Notes": "노트", "Notification Sound": "알림 소리", "Notification Webhook": "알림 웹훅", "Notifications": "알림", "November": "11월", "num_gpu (Ollama)": "num_gpu (올라마(<PERSON><PERSON><PERSON>))", "num_thread (Ollama)": "num_thread (올라마(<PERSON><PERSON><PERSON>))", "OAuth ID": "OAuth ID", "October": "10월", "Off": "끄기", "Okay, Let's Go!": "좋아요, 시작합시다!", "OLED Dark": "OLED 다크", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Ollama 버전", "On": "켜기", "OneDrive": "", "Only alphanumeric characters and hyphens are allowed": "영문자, 숫자 및 하이픈(-)만 허용됨", "Only alphanumeric characters and hyphens are allowed in the command string.": "명령어 문자열에는 영문자, 숫자 및 하이픈(-)만 허용됩니다.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "가지고 있는 컬렉션만 수정 가능합니다, 새 지식 기반을 생성하여 문서를 수정 혹은 추가하십시오", "Only select users and groups with permission can access": "권한이 있는 사용자와 그룹만 접근 가능합니다", "Oops! Looks like the URL is invalid. Please double-check and try again.": "이런! URL이 잘못된 것 같습니다. 다시 한번 확인하고 다시 시도해주세요.", "Oops! There are files still uploading. Please wait for the upload to complete.": "이런! 파일이 계속 업로드중 입니다. 업로드가 완료될 때까지 잠시만 기다려주세요", "Oops! There was an error in the previous response.": "이런! 이전 응답에 에러가 있었던 것 같습니다", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "이런! 지원되지 않는 방식(프론트엔드만)을 사용하고 계십니다. 백엔드에서 WebUI를 제공해주세요.", "Open file": "파일 열기", "Open in full screen": "전체화면으로 열기", "Open new chat": "새 채팅 열기", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI는 내부적으로 패스트 위스퍼를 사용합니다.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI는 SpeechT5와 CMU Arctic 스피커 임베딩을 사용합니다.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "열린 WebUI 버젼(v{{OPEN_WEBUI_VERSION}})은 최소 버젼 (v{{REQUIRED_VERSION}})보다 낮습니다", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 설정", "OpenAI API Key is required.": "OpenAI API 키가 필요합니다.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/키가 필요합니다.", "openapi.json Path": "", "or": "또는", "Organize your users": "사용자를 ", "Other": "기타", "OUTPUT": "출력력", "Output format": "출력 형식", "Overview": "개요", "page": "페이지", "Password": "비밀번호", "Paste Large Text as File": "큰 텍스트를 파일로 붙여넣기", "PDF document (.pdf)": "PDF 문서(.pdf)", "PDF Extract Images (OCR)": "PDF 이미지 추출(OCR)", "pending": "보류 중", "Permission denied when accessing media devices": "미디어 장치 접근 권한이 거부되었습니다.", "Permission denied when accessing microphone": "마이크 접근 권한이 거부되었습니다.", "Permission denied when accessing microphone: {{error}}": "마이크 접근 권환이 거부되었습니다: {{error}}", "Permissions": "권한", "Perplexity API Key": "", "Personalization": "개인화", "Pin": "고정", "Pinned": "고정됨", "Pioneer insights": "", "Pipeline deleted successfully": "성공적으로 파이프라인이 삭제되었습니다", "Pipeline downloaded successfully": "성공적으로 파이프라인이 설치되었습니다", "Pipelines": "파이프라인", "Pipelines Not Detected": "파이프라인이 발견되지 않음", "Pipelines Valves": "파이프라인 밸브", "Plain text (.txt)": "일반 텍스트(.txt)", "Playground": "놀이터", "Please carefully review the following warnings:": "다음 주의를 조심히 확인해주십시오", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "프롬프트를 입력해주세요", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "모두 빈칸없이 채워주세요", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "이유를 선택해주세요", "Port": "포트", "Positive attitude": "긍정적인 자세", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Presence Penalty": "", "Previous 30 days": "이전 30일", "Previous 7 days": "이전 7일", "Private": "", "Profile Image": "프로필 이미지", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "프롬프트 (예: 로마 황제에 대해 재미있는 사실을 알려주세요)", "Prompt Autocompletion": "", "Prompt Content": "프롬프트 내용", "Prompt created successfully": "성공적으로 프롬프트를 생성했습니다", "Prompt suggestions": "프롬프트 제안", "Prompt updated successfully": "성공적으로 프롬프트를 수정했습니다", "Prompts": "프롬프트", "Prompts Access": "프롬프트 접근", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com에서 \"{{searchValue}}\" 가져오기", "Pull a model from Ollama.com": "Ollama.com에서 모델 가져오기(pull)", "Query Generation Prompt": "", "RAG Template": "RAG 템플릿", "Rating": "평가", "Re-rank models by topic similarity": "주제 유사성으로 모델을 재정렬하기", "Read": "", "Read Aloud": "읽어주기", "Reasoning Effort": "", "Record voice": "음성 녹음", "Redirecting you to Open WebUI Community": "OpenWebUI 커뮤니티로 리디렉션 중", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "스스로를 \"사용자\" 라고 지칭하세요. (예: \"사용자는 영어를 배우고 있습니다\")", "References from": "출처", "Refused when it shouldn't have": "허용되지 않았지만 허용되어야 합니다.", "Regenerate": "재생성", "Release Notes": "릴리스 노트", "Relevance": "관련도", "Remove": "삭제", "Remove Model": "모델 삭제", "Rename": "이름 변경", "Reorder Models": "모델 재정렬", "Repeat Last N": "마지막 N 반복", "Repeat Penalty (Ollama)": "", "Reply in Thread": "", "Request Mode": "요청 모드", "Reranking Model": "Reranking 모델", "Reranking model disabled": "Reranking 모델 비활성화", "Reranking model set to \"{{reranking_model}}\"": "Reranking 모델을 \"{{reranking_model}}\"로 설정", "Reset": "초기화", "Reset All Models": "모든 모델 초기화", "Reset Upload Directory": "업로드 디렉토리 초기화", "Reset Vector Storage/Knowledge": "벡터 저장 공간/지식 기반 초기화", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "웹사이트 권환과 같이 응답 알림이 활성화될 수 없습니다. 필요한 접근을 사용하기 위해 브라우져 설정을 확인 부탁드립니다.", "Response splitting": "응답 나누기", "Result": "결과", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "채팅을 위한 풍부한 텍스트(Rich Text) 입력", "RK": "RK", "Role": "역할", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "실행시키기", "Running": "실행 중", "Save": "저장", "Save & Create": "저장 및 생성", "Save & Update": "저장 및 업데이트", "Save As Copy": "다른 이름으로 저장", "Save Tag": "태그 저장", "Saved": "저장됨", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "브라우저의 저장소에 채팅 로그를 직접 저장하는 것은 더 이상 지원되지 않습니다. 아래 버튼을 클릭하여 채팅 로그를 다운로드하고 삭제하세요. 걱정 마세요. 백엔드를 통해 채팅 로그를 쉽게 다시 가져올 수 있습니다.", "Scroll to bottom when switching between branches": "브랜치 간 전환시 밑으로 스크롤 하세요", "Search": "검색", "Search a model": "모델 검색", "Search Base": "", "Search Chats": "채팅 검색", "Search Collection": "컬렉션 검색", "Search Filters": "필터 검색", "search for tags": "태그 검색", "Search Functions": "함수 검색", "Search Knowledge": "지식 기반 검색", "Search Models": "모델 검색", "Search options": "옵션 검색", "Search Prompts": "프롬프트 검색", "Search Result Count": "검색 결과 수", "Search the internet": "", "Search Tools": "검색 도구", "SearchApi API Key": "SearchApi API 키", "SearchApi Engine": "SearchApi 엔진", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" 검색 중", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\"위한 지식 기반 검색 중", "Searxng Query URL": "Searxng 쿼리 URL", "See readme.md for instructions": "설명은 readme.md를 참조하세요.", "See what's new": "새로운 기능 보기", "Seed": "시드", "Select a base model": "기본 모델 선택", "Select a engine": "엔진 선택", "Select a function": "함수 선택", "Select a group": "그룹 선택", "Select a model": "모델 선택", "Select a pipeline": "파이프라인 선택", "Select a pipeline url": "파이프라인 URL 선택", "Select a tool": "도구 선택", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "엔진 선택", "Select Knowledge": "지식 기반 선택", "Select only one model to call": "음성 기능을 위해서는 모델을 하나만 선택해야 합니다.", "Selected model(s) do not support image inputs": "선택한 모델은 이미지 입력을 지원하지 않습니다.", "Semantic distance to query": "쿼리까지 의미적 거리", "Send": "보내기", "Send a Message": "메시지 보내기", "Send message": "메시지 보내기", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "'stream_options: { include_usage: true }' 요청 보내기 \n지원되는 제공자가 토큰 사용 정보를 응답할 예정입니다", "September": "9월", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API 키", "Serply API Key": "Serply API 키", "Serpstack API Key": "Serpstack API 키", "Server connection verified": "서버 연결 확인됨", "Set as default": "기본값으로 설정", "Set CFG Scale": "CFG Scale 설정", "Set Default Model": "기본 모델 설정", "Set embedding model": "임베딩 모델 설정", "Set embedding model (e.g. {{model}})": "임베딩 모델 설정 (예: {{model}})", "Set Image Size": "이미지 크기 설정", "Set reranking model (e.g. {{model}})": "Reranking 모델 설정 (예: {{model}})", "Set Sampler": "샘플러 설정", "Set Scheduler": "스케쥴러 설정", "Set Steps": "단계 설정", "Set Task Model": "작업 모델 설정", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "음성 설정", "Set whisper model": "자막 생성기 모델 설정", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "중단 시퀀스를 설정합니다. 이 패턴이 발생하면 LLM은 텍스트 생성을 중단하고 반환합니다. 여러 중단 패턴은 모델 파일에서 여러 개의 별도 중단 매개변수를 지정하여 설정할 수 있습니다.", "Settings": "설정", "Settings saved successfully!": "설정이 성공적으로 저장되었습니다!", "Share": "공유", "Share Chat": "채팅 공유", "Share to Open WebUI Community": "OpenWebUI 커뮤니티에 공유", "Sharing Permissions": "", "Show": "보기", "Show \"What's New\" modal on login": "로그인시 \"새로운 기능\" 모달 보기", "Show Admin Details in Account Pending Overlay": "사용자용 계정 보류 설명창에, 관리자 상세 정보 노출", "Show Model": "", "Show shortcuts": "단축키 보기", "Show your support!": "당신의 응원을 보내주세요!", "Showcased creativity": "창의성 발휘", "Sign in": "로그인", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 로그인", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "로그아웃", "Sign up": "가입", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 가입", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}}로 가입중", "sk-1234": "", "Source": "출처", "Speech Playback Speed": "음성 재생 속도", "Speech recognition error: {{error}}": "음성 인식 오류: {{error}}", "Speech-to-Text Engine": "음성-텍스트 변환 엔진", "Stop": "정지", "Stop Sequence": "중지 시퀀스", "Stream Chat Response": "스트림 채팅 응답", "STT Model": "STT 모델", "STT Settings": "STT 설정", "Subtitle (e.g. about the Roman Empire)": "자막 (예: 로마 황제)", "Success": "성공", "Successfully updated.": "성공적으로 업데이트되었습니다.", "Suggested": "제안", "Support": "지원", "Support this plugin:": "플러그인 지원", "Sync directory": "디렉토리 연동", "System": "시스템", "System Instructions": "시스템 설명서", "System Prompt": "시스템 프롬프트", "Tags": "", "Tags Generation": "태그 생성", "Tags Generation Prompt": "태그 생성 프롬프트", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "탭하여 중단", "Tasks": "", "Tavily API Key": "Tavily API 키", "Tell us more:": "더 알려주세요:", "Temperature": "온도", "Template": "템플릿", "Temporary Chat": "임시 채팅", "Text Splitter": "텍스트 나누기", "Text-to-Speech Engine": "텍스트-음성 변환 엔진", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "피드백 감사합니다!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "이 플러그인 뒤에 있는 개발자는 커뮤니티에서 활동하는 단순한 열정적인 일반인들입니다. 만약 플러그인이 도움 되었다면, 플러그인 개발에 기여를 고려해주세요!", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "평가 리더보드는 Elo 평가 시스템을 기반으로 하고 실시간으로 업데이트됩니다", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "리더보드는 베타테스트중에 있습니다, 평가 기준이 알고리즘 수정과 함께 변할 수 있습니다", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "최대 파일 크기(MB). 만약 파일 크기가 한도를 초과할 시, 파일은 업로드되지 않습니다", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "하나의 채팅에서는 사용가능한 최대 파일 수가 있습니다. 만약 파일 수가 한도를 초과할 시, 파일은 업로드되지 않습니다.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "점수는 0.0(0%)에서 1.0(100%) 사이의 값이어야 합니다.", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "Theme": "테마", "Thinking...": "생각 중...", "This action cannot be undone. Do you wish to continue?": "이 액션은 되돌릴 수 없습니다. 계속 하시겠습니까?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "이렇게 하면 소중한 대화 내용이 백엔드 데이터베이스에 안전하게 저장됩니다. 감사합니다!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "이것은 실험적 기능으로, 예상대로 작동하지 않을 수 있으며 언제든지 변경될 수 있습니다.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "이 행동은 컬렉션에 존재하는 모든 파일을 삭제하고 새로 업로드된 파일들로 대체됩니다", "This response was generated by \"{{model}}\"": "\"{{model}}\"이 생성한 응답입니다", "This will delete": "이것은 다음을 삭제합니다.", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<strong>{{NAME}}</strong> 와 <strong>모든 내용</strong>을 삭제합니다.", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "지식 기반과 모든 파일 연동을 초기화합니다. 계속 하시겠습니까?", "Thorough explanation": "완전한 설명", "Thought for {{DURATION}}": "{{DURATION}} 동안 생각함", "Thought for {{DURATION}} seconds": "", "Tika": "티카(<PERSON><PERSON>)", "Tika Server URL required.": "티카 서버 URL이 필요합니다", "Tiktoken": "틱토큰 (Tiktoken)", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "팁: 각각의 교체 후 채팅 입력에서 탭 키를 눌러 여러 개의 변수 슬롯을 연속적으로 업데이트하세요.", "Title": "제목", "Title (e.g. Tell me a fun fact)": "제목 (예: 재미있는 사실을 알려주세요)", "Title Auto-Generation": "제목 자동 생성", "Title cannot be an empty string.": "제목은 빈 문자열일 수 없습니다.", "Title Generation": "", "Title Generation Prompt": "제목 생성 프롬프트", "TLS": "", "To access the available model names for downloading,": "다운로드 가능한 모델명을 확인하려면,", "To access the GGUF models available for downloading,": "다운로드 가능한 GGUF 모델을 확인하려면,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI에 접속하려면 관리자에게 문의하십시오. 관리자는 관리자 패널에서 사용자 상태를 관리할 수 있습니다.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "지식 기반을 여기에 첨부하려면. \"지식 기반\" 워크스페이스에 먼저 추가하세요", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "개인정보를 보호하기 위해, 당신의 채팅 로그는 비공개로 유지되고, 오직 당신의 피드백에서 평가, 모델 ID, 태그, 그리고 메타데이타만 공유됩니다", "To select actions here, add them to the \"Functions\" workspace first.": "여기서 행동을 선택하려면, \"함수\" 워크스페이스에 먼저 추가하세요", "To select filters here, add them to the \"Functions\" workspace first.": "여기서 필터를 선택하려면, \"함수\" 워크스페이스에 먼저 추가하세요", "To select toolkits here, add them to the \"Tools\" workspace first.": "여기서 도구를 선택하려면, \"도구\" 워크스페이스에 먼저 추가하세요.", "Toast notifications for new updates": "갓 나온 업데이트 알림", "Today": "오늘", "Toggle settings": "설정 전환", "Toggle sidebar": "사이드바 전환", "Token": "토큰", "Tokens To Keep On Context Refresh (num_keep)": "컨텍스트 새로 고침 시 유지할 토큰 수(num_keep)", "Too verbose": "말이 너무 많은", "Tool created successfully": "성공적으로 도구가 생성되었습니다", "Tool deleted successfully": "성공적으로 도구가 삭제되었습니다", "Tool Description": "도구 설명", "Tool ID": "도구 ID", "Tool imported successfully": "성공적으로 도구를 가져왔습니다", "Tool Name": "도구 이름", "Tool Servers": "", "Tool updated successfully": "성공적으로 도구가 업데이트되었습니다", "Tools": "도구", "Tools Access": "도구 접근", "Tools are a function calling system with arbitrary code execution": "도구는 임의 코드를 실행시키는 함수를 불러오는 시스템입니다", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution": "도구에 임의 코드 실행을 허용하는 함수가 포함되어 있습니다", "Tools have a function calling system that allows arbitrary code execution.": "도구에 임의 코드 실행을 허용하는 함수가 포함되어 있습니다.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "올라마(<PERSON><PERSON><PERSON>)에 접근하는 데 문제가 있나요?", "Trust Proxy Environment": "", "TTS Model": "TTS 모델", "TTS Settings": "TTS 설정", "TTS Voice": "TTS 음성", "Type": "입력", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (다운로드) URL 입력", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "모두 보관 해제", "Unarchive All Archived Chats": "보관된 모든 채팅을 보관 해제", "Unarchive Chat": "채팅 보관 해제", "Unlock mysteries": "", "Unpin": "고정 해제", "Unravel secrets": "", "Untagged": "태그 해제", "Update": "업데이트", "Update and Copy Link": "링크 업데이트 및 복사", "Update for the latest features and improvements.": "이번 업데이트의 새로운 기능과 개선", "Update password": "비밀번호 업데이트", "Updated": "업데이트됨", "Updated at": "다음에 업데이트됨", "Updated At": "다음에 업데이트됨됨", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "업로드", "Upload a GGUF model": "GGUF 모델 업로드", "Upload directory": "디렉토리 업로드", "Upload files": "파일 업로드", "Upload Files": "파일 업로드", "Upload Pipeline": "업로드 파이프라인", "Upload Progress": "업로드 진행 상황", "URL": "", "URL Mode": "URL 모드", "Use '#' in the prompt input to load and include your knowledge.": "프롬프트 입력에서 '#'를 사용하여 지식 기반을 불러오고 포함하세요.", "Use Gravatar": "Gravatar 사용", "Use groups to group your users and assign permissions.": "그룹을 사용하여 사용자를 그룹화하고 권한을 할당하세요.", "Use Initials": "초성 사용", "use_mlock (Ollama)": "use_mlock (올라마)", "use_mmap (Ollama)": "use_mmap (올라마)", "user": "사용자", "User": "사용자", "User location successfully retrieved.": "성공적으로 사용자의 위치를 불러왔습니다", "User Webhooks": "", "Username": "", "Users": "사용자", "Using the default arena model with all models. Click the plus button to add custom models.": "모든 모델은 기본 아레나 모델을 사용중입니다. 플러스 버튼을 눌러 커스텀 모델을 추가하세요", "Utilize": "활용", "Valid time units:": "유효 시간 단위:", "Valves": "밸브", "Valves updated": "밸브 업데이트됨", "Valves updated successfully": "성공적으로 밸브가 업데이트되었습니다", "variable": "변수", "variable to have them replaced with clipboard content.": "변수를 사용하여 클립보드 내용으로 바꾸세요.", "Verify Connection": "", "Version": "버전", "Version {{selectedVersion}} of {{totalVersions}}": "버전 {{totalVersions}}의 {{selectedVersion}}", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "공개 범위", "Voice": "음성", "Voice Input": "음성 입력", "Warning": "경고", "Warning:": "주의:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "주의: 이 기능을 활성화하면 사용자가 서버에 임의 코드를 업로드할 수 있습니다.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "주의: 기존 임베딩 모델을 변경 또는 업데이트하는 경우, 모든 문서를 다시 가져와야 합니다.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "웹", "Web API": "웹 API", "Web Search": "웹 검색", "Web Search Engine": "웹 검색 엔진", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "웹훅 URL", "WebUI Settings": "WebUI 설정", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI가 \"{{url}}/api/chat\"로 요청을 보냅니다", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI가 \"{{url}}/chat/completions\"로 요청을 보냅니다", "What are you trying to achieve?": "무엇을 성취하고 싶으신가요?", "What are you working on?": "어떤 작업을 하고 계신가요?", "What’s New in": "새로운 기능:", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "활성화하면 모델이 각 채팅 메시지에 실시간으로 응답하여 사용자가 메시지를 보내는 즉시 응답을 생성합니다. 이 모드는 실시간 채팅 애플리케이션에 유용하지만, 느린 하드웨어에서는 성능에 영향을 미칠 수 있습니다.", "wherever you are": "", "Whisper (Local)": "<PERSON>his<PERSON> (로컬)", "Why?": "이유는?", "Widescreen Mode": "와이드스크린 모드", "Won": "승리", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "워크스페이스", "Workspace Permissions": "워크스페이스 권한", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "프롬프트 제안 작성 (예: 당신은 누구인가요?)", "Write a summary in 50 words that summarizes [topic or keyword].": "[주제 또는 키워드]에 대한 50단어 요약문 작성.", "Write something...": "아무거나 쓰세요...", "Write your model template content here": "여기에 모델 템플릿 내용을 입력하세요", "Yesterday": "어제", "You": "당신", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "동시에 최대 {{maxCount}} 파일과만 대화할 수 있습니다 ", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "아래 '관리' 버튼으로 메모리를 추가하여 LLM들과의 상호작용을 개인화할 수 있습니다. 이를 통해 더 유용하고 맞춤화된 경험을 제공합니다.", "You cannot upload an empty file.": "빈 파일을 업로드 할 수 없습니다", "You do not have permission to upload files": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "채팅을 보관한 적이 없습니다.", "You have shared this chat": "이 채팅을 공유했습니다.", "You're a helpful assistant.": "당신은 유용한 어시스턴트입니다.", "You're now logged in.": "로그인되었습니다.", "Your account status is currently pending activation.": "현재 계정은 아직 활성화되지 않았습니다.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "당신의 모든 기여는 곧바로 플러그인 개발자에게 갑니다; Open WebUI는 일절 가져가지 않습니다 하지만, 선택한 후원 플랫폼은 수수료를 가져갈 수 있습니다.", "Youtube": "유튜브", "Youtube Language": "", "Youtube Proxy URL": ""}