{"-1 for no limit, or a positive integer for a specific limit": "-1 pour aucune limite, ou un entier positif pour une limite spécifique", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": " 's', 'm', 'h', 'd', 'w' ou '-1' pour une durée illimitée.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(par ex. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(par exemple `sh webui.sh --api`)", "(latest)": "(dernière version)", "(Ollama)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "{{COUNT}} réponses", "{{user}}'s Chats": "Conversations de {{user}}", "{{webUIName}} Backend Required": "Backend {{webUIName}} requis", "*Prompt node ID(s) are required for image generation": "*Les ID de noeud du prompt sont nécessaires pour la génération d’images", "A new version (v{{LATEST_VERSION}}) is now available.": "Une nouvelle version (v{{LATEST_VERSION}}) est disponible.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Un modèle de tâche est utilisé lors de l’exécution de tâches telles que la génération de titres pour les conversations et les requêtes de recherche sur le web.", "a user": "un utilisateur", "About": "À propos", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Accès", "Access Control": "Contrôle d'accès", "Accessible to all users": "Accessible à tous les utilisateurs", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "Activation du compte en attente", "Accurate information": "Information exacte", "Actions": "Actions", "Activate": "Activer", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Activez cette commande en tapant \"/{{COMMAND}}\" dans l'entrée de chat.", "Active Users": "Utilisateurs connectés", "Add": "Ajouter", "Add a model ID": "Ajouter un identifiant de modèle", "Add a short description about what this model does": "Ajoutez une brève description de ce que fait ce modèle.", "Add a tag": "Ajouter un tag", "Add Arena Model": "Ajouter un modèle d'arène", "Add Connection": "Ajouter une connexion", "Add Content": "A<PERSON>ter du contenu", "Add content here": "Ajoutez du contenu ici", "Add custom prompt": "Ajouter un prompt personnalisé", "Add Files": "Ajouter des fichiers", "Add Group": "Ajouter un groupe", "Add Memory": "Ajouter un souvenir", "Add Model": "Ajouter un modèle", "Add Reaction": "Ajouter une réaction", "Add Tag": "Ajouter un tag", "Add Tags": "Ajouter des tags", "Add text content": "Ajouter du contenu textuel", "Add User": "Ajouter un utilisateur", "Add User Group": "Ajouter un groupe d'utilisateurs", "Adjusting these settings will apply changes universally to all users.": "L'ajustement de ces paramètres appliquera universellement les changements à tous les utilisateurs.", "admin": "administrateur", "Admin": "Administrateur", "Admin Panel": "Panneau d'administration", "Admin Settings": "Paramètres admin.", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Les administrateurs ont accès à tous les outils en permanence ; les utilisateurs doivent se voir attribuer des outils pour chaque modèle dans l’espace de travail.", "Advanced Parameters": "Paramètres avancés", "Advanced Params": "Paramètres avancés", "All": "", "All Documents": "Tous les documents", "All models deleted successfully": "Tous les modèles ont été supprimés avec succès", "Allow Chat Controls": "Autoriser les contrôles de chat", "Allow Chat Delete": "Autoriser la suppression de la conversation", "Allow Chat Deletion": "Autoriser la suppression de l'historique de chat", "Allow Chat Edit": "Autoriser la modification de la conversation", "Allow File Upload": "Autoriser le téléchargement de fichiers", "Allow non-local voices": "Autoriser les voix non locales", "Allow Temporary Chat": "Autoriser le chat éphé<PERSON>ère", "Allow User Location": "Autoriser l'emplacement de l'utilisateur", "Allow Voice Interruption in Call": "Autoriser l'interruption vocale pendant un appel", "Allowed Endpoints": "Points de terminaison autorisés", "Already have an account?": "<PERSON><PERSON>-vous déjà un compte ?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Amazing": "Incroyable", "an assistant": "un assistant", "Analyzed": "", "Analyzing...": "", "and": "et", "and {{COUNT}} more": "et {{COUNT}} autres", "and create a new shared link.": "et créer un nouveau lien partagé.", "API Base URL": "URL de base de l'API", "API Key": "Clé d'API", "API Key created.": "Clé d'API générée.", "API Key Endpoint Restrictions": "Restrictions des points de terminaison de la clé API", "API keys": "Clés d'API", "Application DN": "DN de l'application", "Application DN Password": "Mot de passe DN de l'application", "applies to all users with the \"user\" role": "s'applique à tous les utilisateurs ayant le rôle « utilisateur »", "April": "Avril", "Archive": "Archiver", "Archive All Chats": "Archiver toutes les conversations", "Archived Chats": "Conversations archivées", "archived-chat-export": "exportation de conversation archivée", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "Êtes-vous sûr de vouloir supprimer ce canal ?", "Are you sure you want to delete this message?": "Êtes-vous sûr de vouloir supprimer ce message ?", "Are you sure you want to unarchive all archived chats?": "Êtes-vous sûr de vouloir désarchiver toutes les conversations archivées?", "Are you sure?": "Êtes-vous certain ?", "Arena Models": "<PERSON><PERSON><PERSON><PERSON>", "Artifacts": "Artéfacts", "Ask": "", "Ask a question": "Posez votre question", "Assistant": "Assistant", "Attach file from knowledge": "", "Attention to detail": "Attention aux détails", "Attribute for Mail": "Attribut pour l'e-mail", "Attribute for Username": "Attribut pour le nom d'utilisateur", "Audio": "Audio", "August": "Août", "Auth": "", "Authenticate": "Authentifier", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Copie automatique de la réponse vers le presse-papiers", "Auto-playback response": "Lire automatiquement la réponse", "Autocomplete Generation": "Génération des suggestions", "Autocomplete Generation Input Max Length": "Longueur maximale pour la génération des suggestions", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Chaîne d'authentification de l'API", "AUTOMATIC1111 Base URL": "URL de base AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "L'URL de base {AUTOMATIC1111} est requise.", "Available list": "Liste disponible", "Available Tools": "", "available!": "disponible !", "Awful": "Horrible", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Région Azure", "Back": "Retour en arrière", "Bad Response": "Mauvaise réponse", "Banners": "Bannières", "Base Model (From)": "Mod<PERSON>le de base (à partir de)", "Batch Size (num_batch)": "<PERSON><PERSON> (num_batch)", "before": "avant", "Being lazy": "Être fainéant", "Beta": "<PERSON><PERSON><PERSON>", "Bing Search V7 Endpoint": "Point de terminaison Bing Search V7", "Bing Search V7 Subscription Key": "Clé d'abonnement Bing Search V7", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Brave Search API Key": "Clé API Brave Search", "By {{name}}": "Par {{name}}", "Bypass Embedding and Retrieval": "", "Bypass SSL verification for Websites": "Bypasser la vérification SSL pour les sites web", "Calendar": "", "Call": "<PERSON><PERSON><PERSON>", "Call feature is not supported when using Web STT engine": "La fonction d'appel n'est pas prise en charge lors de l'utilisation du moteur Web STT", "Camera": "Appareil photo", "Cancel": "Annuler", "Capabilities": "Capacités", "Capture": "Prise de vue", "Certificate Path": "Chemin du certificat", "Change Password": "Changer le mot de passe", "Channel Name": "Nom du canal", "Channels": "Canaux", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "Limite de caractères pour la génération des suggestions", "Chart new frontiers": "Tracer de nouvelles frontières", "Chat": "Cha<PERSON>", "Chat Background Image": "Image d'arrière-plan de la fenêtre de chat", "Chat Bubble UI": "<PERSON>es de <PERSON>", "Chat Controls": "Contr<PERSON><PERSON> du chat", "Chat direction": "Direction du chat", "Chat Overview": "Aperçu du chat", "Chat Permissions": "Autorisations de chat", "Chat Tags Auto-Generation": "Génération automatique des tags", "Chats": "Conversations", "Check Again": "Vérifiez à nouveau.", "Check for updates": "Vérifier les mises à jour disponibles", "Checking for updates...": "Recherche de mises à jour...", "Choose a model before saving...": "Choisissez un modèle avant de sauvegarder...", "Chunk Overlap": "Chevauchement des chunks", "Chunk Size": "Taille des chunks", "Ciphers": "<PERSON><PERSON><PERSON>", "Citation": "Citation", "Clear memory": "Effacer la mémoire", "Clear Memory": "", "click here": "cliquez ici", "Click here for filter guides.": "Cliquez ici pour les guides de filtrage.", "Click here for help.": "Cliquez ici pour obtenir de l'aide.", "Click here to": "Cliquez ici pour", "Click here to download user import template file.": "Cliquez ici pour télécharger le fichier modèle d'importation des utilisateurs.", "Click here to learn more about faster-whisper and see the available models.": "Cliquez ici pour en savoir plus sur faster-whisper et voir les modèles disponibles.", "Click here to see available models.": "", "Click here to select": "Cliquez ici pour sélectionner", "Click here to select a csv file.": "Cliquez ici pour sélectionner un fichier .csv.", "Click here to select a py file.": "Cliquez ici pour sélectionner un fichier .py.", "Click here to upload a workflow.json file.": "Cliquez ici pour télécharger un fichier workflow.json.", "click here.": "cliquez ici.", "Click on the user role button to change a user's role.": "Cliquez sur le bouton de rôle d'utilisateur pour modifier son rôle.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "L'autorisation d'écriture du presse-papier a été refusée. Veuillez vérifier les paramètres de votre navigateur pour accorder l'accès nécessaire.", "Clone": "<PERSON><PERSON><PERSON>", "Clone Chat": "<PERSON><PERSON><PERSON><PERSON>", "Clone of {{TITLE}}": "", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "Exécution de code", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Le code a été formaté avec succès", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "Collection", "Color": "<PERSON><PERSON><PERSON>", "ComfyUI": "ComfyUI", "ComfyUI API Key": "Clé API ComfyUI", "ComfyUI Base URL": "URL de base ComfyUI", "ComfyUI Base URL is required.": "L'URL de base ComfyUI est requise.", "ComfyUI Workflow": "Flux de travaux de ComfyUI", "ComfyUI Workflow Nodes": "Noeud du flux de travaux de ComfyUI", "Command": "Commande", "Completions": "Complétions", "Concurrent Requests": "Demandes concurrentes", "Configure": "Configurer", "Confirm": "Confirmer", "Confirm Password": "Confirmer le mot de passe", "Confirm your action": "Confirmer votre action", "Confirm your new password": "Confirmer votre nouveau mot de passe", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connections": "Connexions", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Contacter l'administrateur pour obtenir l'accès à WebUI", "Content": "Contenu", "Content Extraction Engine": "", "Context Length": "<PERSON><PERSON><PERSON> du contexte", "Continue Response": "Continuer la réponse", "Continue with {{provider}}": "Continuer avec {{provider}}", "Continue with Email": "Continuer avec l'email", "Continue with LDAP": "Continuer avec LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Contrôle la façon dont le texte des messages est divisé pour les demandes de Text-to-Speech. « ponctuation » divise en phrases, « paragraphes » divise en paragraphes et « aucun » garde le message en tant que chaîne de texte unique.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "<PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "URL du chat copié dans le presse-papiers !", "Copied to clipboard": "Copié dans le presse-papiers", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "<PERSON><PERSON>r le dernier bloc de code", "Copy last response": "Copier la dernière réponse", "Copy Link": "Copier le lien", "Copy to clipboard": "Copier dans le presse-papiers", "Copying to clipboard was successful!": "La copie dans le presse-papiers a réussi !", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "<PERSON><PERSON><PERSON>", "Create a knowledge base": "<PERSON><PERSON>er une base de connaissances", "Create a model": "<PERSON><PERSON><PERSON> un modèle", "Create Account": "<PERSON><PERSON><PERSON> un compte", "Create Admin Account": "<PERSON><PERSON>er un compte administrateur", "Create Channel": "Créer un canal", "Create Group": "Créer un groupe", "Create Knowledge": "Créer une connaissance", "Create new key": "<PERSON><PERSON>er une nouvelle clé", "Create new secret key": "<PERSON><PERSON>er une nouvelle clé secrète", "Created at": "<PERSON><PERSON><PERSON>", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "C<PERSON><PERSON> par", "CSV Import": "Import CSV", "Ctrl+Enter to Send": "", "Current Model": "<PERSON><PERSON><PERSON><PERSON> actuel", "Current Password": "Mot de passe actuel", "Custom": "Sur mesure", "Danger Zone": "", "Dark": "Sombre", "Database": "Base de données", "December": "Décembre", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON> (OpenAI)", "Default (SentenceTransformers)": "<PERSON><PERSON> (Sentence Transformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model’s built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Modèle standard", "Default model updated": "Modèle par défaut mis à jour", "Default Models": "Modèles par défaut", "Default permissions": "Autorisations par défaut", "Default permissions updated successfully": "Autorisations par défaut mises à jour avec succès", "Default Prompt Suggestions": "Suggestions de prompts par défaut", "Default to 389 or 636 if TLS is enabled": "Par dé<PERSON>ut à 389 ou 636 si TLS est activé", "Default to ALL": "Par défaut à TOUS", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Rôle utilisateur par défaut", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete a model": "Supprimer un modèle", "Delete All Chats": "Supp<PERSON><PERSON> toutes les conversations", "Delete All Models": "Supprimer tous les modèles", "Delete chat": "Supprimer la conversation", "Delete Chat": "Supprimer la Conversation", "Delete chat?": "Supprimer la conversation ?", "Delete folder?": "Supprimer le dossier ?", "Delete function?": "Supprimer la fonction ?", "Delete Message": "Supprimer le message", "Delete message?": "", "Delete prompt?": "Supprimer le prompt ?", "delete this link": "supprimer ce lien", "Delete tool?": "Effacer l'outil ?", "Delete User": "Supprimer le compte d'utilisateur", "Deleted {{deleteModelTag}}": "Supprimé {{deleteModelTag}}", "Deleted {{name}}": "Supprimé {{name}}", "Deleted User": "Utilisateur supprimé", "Describe your knowledge base and objectives": "Décrivez votre base de connaissances et vos objectifs", "Description": "Description", "Didn't fully follow instructions": "N'a pas entièrement respecté les instructions", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Connections settings updated": "", "Direct Tool Servers": "", "Disabled": "Désactivé", "Discover a function": "Trouvez une fonction", "Discover a model": "<PERSON>rou<PERSON> un modèle", "Discover a prompt": "<PERSON><PERSON><PERSON> un prompt", "Discover a tool": "<PERSON><PERSON><PERSON> un outil", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "Découvrir des merveilles", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, téléchargez et explorez des fonctions personnalisées", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, té<PERSON>chargez et explorez des prompts personnalisés", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, téléchargez et explorez des outils personnalisés", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, télécharger et explorer des préréglages de modèles", "Dismissible": "Fermeture", "Display": "<PERSON><PERSON><PERSON><PERSON>", "Display Emoji in Call": "Afficher les emojis pendant l'appel", "Display the username instead of You in the Chat": "Afficher le nom d'utilisateur à la place de \"Vous\" dans le chat", "Displays citations in the response": "Affiche les citations dans la réponse", "Dive into knowledge": "Plonger dans les connaissances", "Do not install functions from sources you do not fully trust.": "N'installez pas de fonctions provenant de sources auxquelles vous ne faites pas entièrement confiance.", "Do not install tools from sources you do not fully trust.": "N'installez pas d'outils provenant de sources auxquelles vous ne faites pas entièrement confiance.", "Docling": "", "Docling Server URL required.": "", "Document": "Document", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Documentation", "Documents": "Documents", "does not make any external connections, and your data stays securely on your locally hosted server.": "n'établit aucune connexion externe et garde vos données en sécurité sur votre serveur local.", "Domain Filter List": "", "Don't have an account?": "Vous n'avez pas de compte ?", "don't install random functions from sources you don't trust.": "n'installez pas de fonctions aléatoires provenant de sources auxquelles vous ne faites pas confiance.", "don't install random tools from sources you don't trust.": "n'installez pas d'outils aléatoires provenant de sources auxquelles vous ne faites pas confiance.", "Don't like the style": "N'apprécie pas le style", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Télécharger", "Download as SVG": "", "Download canceled": "Téléchargement annulé", "Download Database": "Télécharger la base de données", "Drag and drop a file to upload or select a file to view": "G<PERSON><PERSON><PERSON> et déposez un fichier pour le télécharger ou sélectionnez un fichier à visualiser", "Draw": "Match nul", "Drop any files here to add to the conversation": "Déposez des fichiers ici pour les ajouter à la conversation", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "par ex. '30s', '10 min'. Les unités de temps valides sont 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "par ex. un filtre pour retirer les vulgarités du texte", "e.g. My Filter": "par ex. Mon Filtre", "e.g. My Tools": "par ex. <PERSON><PERSON>", "e.g. my_filter": "par ex. mon_filtre", "e.g. my_tools": "par ex. mes_outils", "e.g. Tools for performing various operations": "par ex. Outils pour effectuer diverses opérations", "Edit": "Modifier", "Edit Arena Model": "Modifier le modèle d'arène", "Edit Channel": "Modifier le canal", "Edit Connection": "Modifier la connexion", "Edit Default Permissions": "Modifier les autorisations par défaut", "Edit Memory": "Modifier la mémoire", "Edit User": "Modifier l'utilisateur", "Edit User Group": "Modifier le groupe d'utilisateurs", "ElevenLabs": "ElevenLabs", "Email": "E-mail", "Embark on adventures": "Embarquez pour des aventures", "Embedding": "", "Embedding Batch Size": "<PERSON><PERSON> du lot d'embedding", "Embedding Model": "<PERSON><PERSON><PERSON><PERSON> d'embedding", "Embedding Model Engine": "Mo<PERSON>ur de modèle d'embedding", "Embedding model set to \"{{embedding_model}}\"": "Mod<PERSON><PERSON> d'embedding d<PERSON><PERSON><PERSON> sur « {{embedding_model}} »", "Enable API Key": "Activer la clé API", "Enable autocomplete generation for chat messages": "Activer la génération des suggestions pour les messages", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "Activer le partage communautaire", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Activer le verrouillage de la mémoire (mlock) pour empêcher les données du modèle d'être échangées de la RAM. Cette option verrouille l'ensemble de pages de travail du modèle en RAM, garantissant qu'elles ne seront pas échangées vers le disque. Cela peut aider à maintenir les performances en évitant les défauts de page et en assurant un accès rapide aux données.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Activer le mappage de la mémoire (mmap) pour charger les données du modèle. Cette option permet au système d'utiliser le stockage disque comme une extension de la RAM en traitant les fichiers disque comme s'ils étaient en RAM. Cela peut améliorer les performances du modèle en permettant un accès plus rapide aux données. Cependant, cela peut ne pas fonctionner correctement avec tous les systèmes et peut consommer une quantité significative d'espace disque.", "Enable Message Rating": "Activer l'évaluation des messages", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Activer les nouvelles inscriptions", "Enabled": "Activé", "Enforce Temporary Chat": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Vérifiez que votre fichier CSV comprenne les 4 colonnes dans cet ordre : Name, Email, Password, Role.", "Enter {{role}} message here": "Entrez le message {{role}} ici", "Enter a detail about yourself for your LLMs to recall": "Saisissez un détail sur vous-même que vos LLMs pourront se rappeler", "Enter api auth string (e.g. username:password)": "Entrez la chaîne d'authentification de l'API (par ex. nom d'utilisateur:mot de passe)", "Enter Application DN": "Entrez le DN de l'application", "Enter Application DN Password": "Entrez le mot de passe DN de l'application", "Enter Bing Search V7 Endpoint": "Entrez le point de terminaison Bing Search V7", "Enter Bing Search V7 Subscription Key": "Entrez la clé d'abonnement Bing Search V7", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Entrez la clé API Brave Search", "Enter certificate path": "Entrez le chemin du certificat", "Enter CFG Scale (e.g. 7.0)": "Entrez l'échelle CFG (par ex. 7.0)", "Enter Chunk Overlap": "Entrez le chevauchement des chunks", "Enter Chunk Size": "Entrez la taille des chunks", "Enter comma-seperated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter description": "Entrez la description", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter Github Raw URL": "Entrez l'URL brute de GitHub", "Enter Google PSE API Key": "Entrez la clé API Google PSE", "Enter Google PSE Engine Id": "Entrez l'identifiant du moteur Google PSE", "Enter Image Size (e.g. 512x512)": "Entrez la taille de l'image (par ex. 512x512)", "Enter Jina API Key": "Entrez la clé API Jina", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "Entrez la clé API Kagi Search", "Enter Key Behavior": "", "Enter language codes": "Entrez les codes de langue", "Enter Mistral API Key": "", "Enter Model ID": "Entrez l'ID du modèle", "Enter model tag (e.g. {{modelTag}})": "Entrez le tag du modèle (par ex. {{modelTag}})", "Enter Mojeek Search API Key": "Entrez la clé API Mojeek", "Enter Number of Steps (e.g. 50)": "Entrez le nombre d'étapes (par ex. 50)", "Enter Perplexity API Key": "", "Enter proxy URL (e.g. **************************:port)": "Entrez l'URL du proxy (par ex. *************************:port)", "Enter reasoning effort": "Entrez l'effort de raisonnement", "Enter Sampler (e.g. Euler a)": "Entrez le sampler (par ex. Euler a)", "Enter Scheduler (e.g. Karras)": "En<PERSON>z le planificateur (par ex. <PERSON>)", "Enter Score": "Entrez votre score", "Enter SearchApi API Key": "Entrez la clé API SearchApi", "Enter SearchApi Engine": "Entrez le moteur de recherche SearchApi", "Enter Searxng Query URL": "Entrez l'URL de la requête Searxng", "Enter Seed": "<PERSON><PERSON><PERSON>", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Entrez la clé API Serper", "Enter Serply API Key": "Entrez la clé API Serply", "Enter Serpstack API Key": "Entrez la clé API Serpstack", "Enter server host": "Entrez l'hôte du serveur", "Enter server label": "Entrez l'étiquette du serveur", "Enter server port": "Entrez le port du serveur", "Enter stop sequence": "Entrez la séquence d'arrê<PERSON>", "Enter system prompt": "Entrez le prompt système", "Enter system prompt here": "", "Enter Tavily API Key": "Entrez la clé API Tavily", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "Entrez l'URL publique de votre WebUI. Cette URL sera utilisée pour générer des liens dans les notifications.", "Enter Tika Server URL": "Entrez l'URL du serveur Tika", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Entrez les Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Entrez l'URL (par ex. {http://127.0.0.1:7860/})", "Enter URL (e.g. http://localhost:11434)": "Entrez l'URL (par ex. http://localhost:11434)", "Enter your current password": "Entrez votre mot de passe actuel", "Enter Your Email": "Entrez votre adresse e-mail", "Enter Your Full Name": "Entrez votre nom complet", "Enter your message": "Entrez votre message", "Enter your name": "", "Enter your new password": "Entrez votre nouveau mot de passe", "Enter Your Password": "Entrez votre mot de passe", "Enter Your Role": "Entrez votre rôle", "Enter Your Username": "Entrez votre nom d'utilisateur", "Enter your webhook URL": "Entrez l'URL de votre webhook", "Error": "<PERSON><PERSON><PERSON>", "ERROR": "ERREUR", "Error accessing Google Drive: {{error}}": "<PERSON><PERSON><PERSON> d'accès à Google Drive : {{error}}", "Error uploading file: {{error}}": "<PERSON><PERSON><PERSON> de téléversement du fichier : {{error}}", "Evaluations": "Évaluations", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Exemple: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Exemple: TOUS", "Example: mail": "Exemple: mail", "Example: ou=users,dc=foo,dc=example": "Exemple: ou=utilisateurs,dc=foo,dc=exemple", "Example: sAMAccountName or uid or userPrincipalName": "Exemple: sAMAccountName ou uid ou userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Exclure", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Expérimental", "Explain": "", "Explain this section to me in more detail": "", "Explore the cosmos": "Explorer le cosmos", "Export": "Exportation", "Export All Archived Chats": "Exporter toutes les conversations archivées", "Export All Chats (All Users)": "Exporter toutes les conversations (de tous les utilisateurs)", "Export chat (.json)": "Exporter la conversation (.json)", "Export Chats": "Exporter les conversations", "Export Config to JSON File": "Exporter la configuration vers un fichier JSON", "Export Functions": "Exporter des fonctions", "Export Models": "Exporter des modèles", "Export Presets": "Exporter les préréglages", "Export Prompts": "Exporter des prompts", "Export to CSV": "Exporter en CSV", "Export Tools": "Exporter des outils", "External": "", "External Models": "<PERSON><PERSON><PERSON><PERSON> externes", "Failed to add file.": "Échec de l'ajout du fichier.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to create API Key.": "Échec de la création de la clé API.", "Failed to fetch models": "Échec de la récupération des modèles", "Failed to read clipboard contents": "Échec de la lecture du contenu du presse-papiers", "Failed to save connections": "", "Failed to save models configuration": "Échec de la sauvegarde de la configuration des modèles", "Failed to update settings": "Échec de la mise à jour des paramètres", "Failed to upload file.": "Échec du téléchargement du fichier.", "Features": "", "Features Permissions": "Autorisations des fonctionnalités", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback History": "Historique des avis", "Feedbacks": "<PERSON><PERSON>", "Feel free to add specific details": "N'hésitez pas à ajouter des détails spécifiques", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON>er ajouté avec succès.", "File content updated successfully.": "Contenu du fichier mis à jour avec succès.", "File Mode": "<PERSON> fichier", "File not found.": "Fichier introuvable.", "File removed successfully.": "Fichier supprimé avec succès.", "File size should not exceed {{maxSize}} MB.": "La taille du fichier ne doit pas dépasser {{maxSize}} Mo.", "File uploaded successfully": "<PERSON><PERSON>er téléversé avec succès", "Files": "Fichiers", "Filter is now globally disabled": "Le filtre est maintenant désactivé globalement", "Filter is now globally enabled": "Le filtre est désormais activé globalement", "Filters": "Filtres", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Spoofing détecté : impossible d'utiliser les initiales comme avatar. Retour à l'image de profil par défaut.", "Fluidly stream large external response chunks": "Streaming fluide de gros chunks de réponses externes", "Focus chat input": "Mettre le focus sur le champ de chat", "Folder deleted successfully": "Dossier supprimé avec succès", "Folder name cannot be empty": "Le nom du dossier ne peut pas être vide", "Folder name cannot be empty.": "Le nom du dossier ne peut pas être vide.", "Folder name updated successfully": "Le nom du dossier a été mis à jour avec succès", "Followed instructions perfectly": "A parfaitement suivi les instructions", "Forge new paths": "<PERSON><PERSON>er de nouveaux chemins", "Form": "Formulaire", "Format your variables using brackets like this:": "Formatez vos variables en utilisant des parenthèses comme ceci :", "Forwards system user session credentials to authenticate": "", "Frequency Penalty": "Pénalité de fréquence", "Full Context Mode": "", "Function": "Fonction", "Function Calling": "", "Function created successfully": "La fonction a été créée avec succès", "Function deleted successfully": "Fonction supprimée avec succès", "Function Description": "Description de la fonction", "Function ID": "ID de la fonction", "Function is now globally disabled": "La fonction est désormais globalement désactivée", "Function is now globally enabled": "La fonction est désormais globalement activée", "Function Name": "Nom de la fonction", "Function updated successfully": "La fonction a été mise à jour avec succès", "Functions": "Fonctions", "Functions allow arbitrary code execution": "Les fonctions permettent l'exécution de code arbitraire", "Functions allow arbitrary code execution.": "Les fonctions permettent l'exécution de code arbitraire.", "Functions imported successfully": "Fonctions importées avec succès", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Général", "Generate an image": "", "Generate Image": "<PERSON><PERSON><PERSON>rer une image", "Generate prompt pair": "", "Generating search query": "Génération d'une requête de recherche", "Get started": "<PERSON><PERSON><PERSON><PERSON>", "Get started with {{WEBUI_NAME}}": "<PERSON>é<PERSON>rez avec {{WEBUI_NAME}}", "Global": "Globale", "Good Response": "Bonne réponse", "Google Drive": "Google Drive", "Google PSE API Key": "Clé API Google PSE", "Google PSE Engine Id": "ID du moteur de recherche PSE de Google", "Group created successfully": "Groupe créé avec succès", "Group deleted successfully": "Groupe supprimé avec succès", "Group Description": "Description du groupe", "Group Name": "Nom du groupe", "Group updated successfully": "Groupe mis à jour avec succès", "Groups": "Groupes", "Haptic Feedback": "Retour haptique", "has no conversations.": "n'a aucune conversation.", "Hello, {{name}}": "<PERSON><PERSON><PERSON>, {{name}}.", "Help": "Aide", "Help us create the best community leaderboard by sharing your feedback history!": "Aidez-nous à créer le meilleur classement communautaire en partageant votre historique des avis !", "Hex Color": "<PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "Couleur Hex - Laissez vide pour la couleur par défaut", "Hide": "<PERSON><PERSON>", "Hide Model": "", "Home": "", "Host": "<PERSON><PERSON><PERSON>", "How can I help you today?": "Comment puis-je vous aider aujou<PERSON>'hui ?", "How would you rate this response?": "Comment évalueriez-vous cette réponse ?", "Hybrid Search": "Recherche hybride", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Je reconnais avoir lu et compris les implications de mes actions. Je suis conscient des risques associés à l'exécution d'un code arbitraire et j'ai vérifié la fiabilité de la source.", "ID": "ID", "Ignite curiosity": "Éveiller la curiosité", "Image": "Image", "Image Compression": "Compression d'image", "Image Generation": "Génération d'images", "Image Generation (Experimental)": "Génération d'images (expérimental)", "Image Generation Engine": "Moteur de génération d'images", "Image Max Compression Size": "Taille maximale de compression d'image", "Image Prompt Generation": "Génération de prompts d'images", "Image Prompt Generation Prompt": "Prompt de génération de prompts d'images", "Image Settings": "Paramètres de génération d'images", "Images": "Images", "Import Chats": "Importer les conversations", "Import Config from JSON File": "Importer la configuration depuis un fichier JSON", "Import Functions": "Importer des fonctions", "Import Models": "Importer des modèles", "Import Presets": "Importer les préréglages", "Import Prompts": "Importer des prompts", "Import Tools": "Importer des outils", "Include": "Inclure", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> le drapeau `--api-auth` lors de l'exécution de stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> le drapeau `--api` lorsque vous exécutez stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Info", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Commandes d'entrée", "Install from Github URL": "Installer depuis une URL GitHub", "Instant Auto-Send After Voice Transcription": "Envoi automatique après la transcription", "Integration": "", "Interface": "Interface utilisateur", "Invalid file format.": "Format de fichier non valide.", "Invalid JSON schema": "", "Invalid Tag": "Tag non valide", "is typing...": "est en train d'écrire...", "January": "<PERSON><PERSON>", "Jina API Key": "Clé API Jina", "join our Discord for help.": "<PERSON><PERSON><PERSON><PERSON> notre Discord pour obtenir de l'aide.", "JSON": "JSON", "JSON Preview": "Aperçu JSON", "July": "<PERSON><PERSON><PERSON>", "June": "Juin", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Expiration du token JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "Clé API Kagi Search", "Keep Alive": "Temps de maintien connecté", "Key": "Clé", "Keyboard shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "Knowledge": "Connaissances", "Knowledge Access": "Accès aux connaissances", "Knowledge created successfully.": "Connaissance créée avec succès.", "Knowledge deleted successfully.": "Connaissance supprimée avec succès.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Connaissance réinitialisée avec succès.", "Knowledge updated successfully": "Connaissance mise à jour avec succès", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Étiquette", "Landing Page Mode": "Mode de la page d'accueil", "Language": "<PERSON><PERSON>", "Last Active": "Dernière activité", "Last Modified": "Dernière modification", "Last reply": "Déernière réponse", "LDAP": "LDAP", "LDAP server updated": "Serveur LDAP mis à jour", "Leaderboard": "Classement", "Learn more about OpenAPI tool servers.": "", "Leave empty for unlimited": "Laissez vide pour illimité", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Laissez vide pour inclure tous les modèles ou sélectionnez des modèles spécifiques", "Leave empty to use the default prompt, or enter a custom prompt": "Laissez vide pour utiliser le prompt par défaut, ou entrez un prompt personnalisé", "Leave model field empty to use the default model.": "", "License": "", "Light": "<PERSON>", "Listening...": "Écoute en cours...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "Les LLM peuvent faire des erreurs. Vérifiez les informations importantes.", "Loader": "", "Loading Kokoro.js...": "", "Local": "Local", "Local Models": "<PERSON><PERSON><PERSON><PERSON>", "Location access not allowed": "", "Logit Bias": "", "Lost": "Perdu", "LTR": "LTR", "Made by Open WebUI Community": "Réalisé par la communauté OpenWebUI", "Make sure to enclose them with": "Assurez-vous de les inclure dans", "Make sure to export a workflow.json file as API format from ComfyUI.": "Veillez à exporter un fichier workflow.json au format API depuis ComfyUI.", "Manage": "<PERSON><PERSON><PERSON>", "Manage Direct Connections": "", "Manage Models": "<PERSON><PERSON><PERSON> les modèles", "Manage Ollama": "<PERSON><PERSON><PERSON>", "Manage Ollama API Connections": "Gérer les connexions API Ollama", "Manage OpenAI API Connections": "Gérer les connexions API OpenAI", "Manage Pipelines": "Gérer les pipelines", "Manage Tool Servers": "", "March": "Mars", "Max Tokens (num_predict)": "Nb max de tokens (num_predict)", "Max Upload Count": "Nombre maximal de téléversements", "Max Upload Size": "Limite de taille de téléversement", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Un maximum de 3 modèles peut être téléchargé en même temps. Veuillez réessayer ultérieurement.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Les souvenirs accessibles par les LLMs seront affichées ici.", "Memory": "M<PERSON><PERSON><PERSON>", "Memory added successfully": "Souvenir ajoutée avec succès", "Memory cleared successfully": "La mémoire a été effacée avec succès", "Memory deleted successfully": "Le souvenir a été supprimé avec succès", "Memory updated successfully": "Le souvenir a été mis à jour avec succès", "Merge Responses": "Fusionner les réponses", "Message rating should be enabled to use this feature": "L'évaluation des messages doit être activée pour pouvoir utiliser cette fonctionnalité", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Les messages que vous envoyez après avoir créé votre lien ne seront pas partagés. Les utilisateurs disposant de l'URL pourront voir la conversation partagée.", "Min P": "P min", "Minimum Score": "Score minimal", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON><PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Le modèle '{{modelName}}' a été téléchargé avec succès.", "Model '{{modelTag}}' is already in queue for downloading.": "Le modèle '{{modelTag}}' est déjà dans la file d'attente pour le téléchargement.", "Model {{modelId}} not found": "<PERSON>d<PERSON><PERSON> {{modelId}} introuvable", "Model {{modelName}} is not vision capable": "Le modèle {{modelName}} n'a pas de capacités visuelles", "Model {{name}} is now {{status}}": "Le modèle {{name}} est désormais {{status}}.", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts image inputs": "Le modèle accepte les images en entrée", "Model created successfully!": "Le modèle a été créé avec succès !", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Chemin du système de fichiers de modèle détecté. Le nom court du modèle est requis pour la mise à jour, l'opération ne peut pas être poursuivie.", "Model Filtering": "Filtrage de modèle", "Model ID": "ID du modèle", "Model IDs": "ID des modèles", "Model Name": "Nom du modèle", "Model not selected": "Modèle non sélectionné", "Model Params": "Paramètres du modèle", "Model Permissions": "Autorisations du modèle", "Model updated successfully": "Le modèle a été mis à jour avec succès", "Modelfile Content": "Contenu du Fichier de Modèle", "Models": "<PERSON><PERSON><PERSON><PERSON>", "Models Access": "Accès aux modèles", "Models configuration saved successfully": "Configuration des modèles enregistrée avec succès", "Models Public Sharing": "", "Mojeek Search API Key": "Clé API Mojeek", "more": "plus", "More": "Plus", "Name": "Nom d'utilisateur", "Name your knowledge base": "Nommez votre base de connaissances", "Native": "<PERSON><PERSON>", "New Chat": "Nouvelle conversation", "New Folder": "Nouveau dossier", "New Password": "Nouveau mot de passe", "new-channel": "nouveau-canal", "No content found": "Aucun contenu trouvé", "No content to speak": "<PERSON><PERSON>er", "No distance available": "Aucune distance disponible", "No feedbacks found": "<PERSON><PERSON><PERSON> avis trouvé", "No file selected": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "No files found.": "<PERSON><PERSON><PERSON> fichier trouvé.", "No groups with access, add a group to grant access": "Aucun groupe n'a accès, ajoutez un groupe pour accorder l'accès", "No HTML, CSS, or JavaScript content found.": "Aucun contenu HTML, CSS ou JavaScript trouvé.", "No inference engine with management support found": "Aucun moteur d'inférence avec support trouvé", "No knowledge found": "Aucune connaissance trouvée", "No memories to clear": "Aucun souvenir à effacer", "No model IDs": "Aucun ID de modèle", "No models found": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "No models selected": "Aucun modèle s<PERSON>", "No results found": "Aucun résultat trouvé", "No search query generated": "Aucune requête de recherche générée", "No source available": "Aucune source n'est disponible", "No users were found.": "Aucun utilisateur trouvé.", "No valves to update": "<PERSON><PERSON><PERSON> vanne à mettre à jour", "None": "Aucun", "Not factually correct": "Non factuellement correct", "Not helpful": "Pas utile", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Note : Si vous définissez un score minimum, seuls les documents ayant un score supérieur ou égal à ce score minimum seront retournés par la recherche.", "Notes": "Notes", "Notification Sound": "Son de notification", "Notification Webhook": "Webhook de notification", "Notifications": "Notifications", "November": "Novembre", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "ID OAuth", "October": "Octobre", "Off": "Désactivé", "Okay, Let's Go!": "D'accord, allons-y !", "OLED Dark": "Noir OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "Paramètres de l'API Ollama mis à jour", "Ollama Version": "Version d'Ollama", "On": "Activé", "OneDrive": "OneDrive", "Only alphanumeric characters and hyphens are allowed": "Seuls les caractères alphanumériques et les tirets sont autorisés", "Only alphanumeric characters and hyphens are allowed in the command string.": "Seuls les caractères alphanumériques et les tirets sont autorisés dans la chaîne de commande.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Seules les collections peuvent être modifiées, créez une nouvelle base de connaissance pour modifier/ajouter des documents.", "Only select users and groups with permission can access": "Seuls les utilisateurs et groupes autorisés peuvent accéder", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Oups ! Il semble que l'URL soit invalide. Veuillez vérifier à nouveau et réessayer.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Oups ! Des fichiers sont encore en cours de téléversement. Veuillez patienter jusqu'à la fin du téléversement.", "Oops! There was an error in the previous response.": "Oups ! Il y a eu une erreur dans la réponse précédente.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Oups ! Vous utilisez une méthode non prise en charge (frontend uniquement). Veuillez servir l'interface Web à partir du backend.", "Open file": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>er", "Open in full screen": "Ou<PERSON><PERSON>r en plein écran", "Open new chat": "Ouvrir une nouvelle conversation", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI utilise faster-whisper en interne.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI utilise SpeechT5 et les embeddings de locuteur CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "La version Open WebUI (v{{OPEN_WEBUI_VERSION}}) est inférieure à la version requise (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API compatibles OpenAI", "OpenAI API Config": "Configuration de l'API OpenAI", "OpenAI API Key is required.": "Une clé API OpenAI est requise.", "OpenAI API settings updated": "Paramètres de l'API OpenAI mis à jour", "OpenAI URL/Key required.": "URL/Clé OpenAI requise.", "openapi.json Path": "", "or": "ou", "Organize your users": "Organisez vos utilisateurs", "Other": "<PERSON><PERSON>", "OUTPUT": "SORTIE", "Output format": "Format de sortie", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "page": "page", "Password": "Mot de passe", "Paste Large Text as File": "Coller un texte volumineux comme fichier", "PDF document (.pdf)": "Document au format PDF (.pdf)", "PDF Extract Images (OCR)": "Extraction d'images PDF (OCR)", "pending": "en attente", "Permission denied when accessing media devices": "Accès aux appareils multimédias refusé", "Permission denied when accessing microphone": "Accès au microphone refusé", "Permission denied when accessing microphone: {{error}}": "Accès au microphone refusé : {{error}}", "Permissions": "Permissions", "Perplexity API Key": "", "Personalization": "Personnalisation", "Pin": "<PERSON><PERSON><PERSON>", "Pinned": "<PERSON><PERSON><PERSON>", "Pioneer insights": "Explorer de nouvelles perspectives", "Pipeline deleted successfully": "Le pipeline a été supprimé avec succès", "Pipeline downloaded successfully": "Le pipeline a été téléchargé avec succès", "Pipelines": "Pipelines", "Pipelines Not Detected": "Aucun pipelines détecté", "Pipelines Valves": "Vannes de pipelines", "Plain text (.txt)": "Texte (.txt)", "Playground": "Playground", "Please carefully review the following warnings:": "Veuillez lire attentivement les avertissements suivants :", "Please do not close the settings page while loading the model.": "Veuillez ne pas fermer les paramètres pendant le chargement du modèle.", "Please enter a prompt": "Veuillez saisir un prompt", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "<PERSON><PERSON><PERSON>z remplir tous les champs.", "Please select a model first.": "Veuillez d'abord sélectionner un modèle.", "Please select a model.": "Veuillez sélectionner un modèle.", "Please select a reason": "<PERSON>euillez sélectionner une raison", "Port": "Port", "Positive attitude": "Attitude positive", "Prefix ID": "ID de préfixe", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Le préfixe ID est utilisé pour éviter les conflits avec d'autres connexions en ajoutant un préfixe aux ID de modèle - laissez vide pour désactiver", "Presence Penalty": "Pénalité de présence", "Previous 30 days": "30 derniers jours", "Previous 7 days": "7 derniers jours", "Private": "", "Profile Image": "Image de profil", "Prompt": "Prompt", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (par ex. Di<PERSON>-moi un fait amusant à propos de l'Empire romain)", "Prompt Autocompletion": "", "Prompt Content": "Contenu du prompt", "Prompt created successfully": "Prompt créé avec succès", "Prompt suggestions": "Suggestions pour le prompt", "Prompt updated successfully": "Prompt mis à jour avec succès", "Prompts": "Prompts", "Prompts Access": "Accès aux prompts", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ré<PERSON><PERSON>rer « {{searchValue}} » depuis Ollama.com", "Pull a model from Ollama.com": "Télécharger un modèle depuis Ollama.com", "Query Generation Prompt": "Prompt de génération de requête", "RAG Template": "Modèle RAG", "Rating": "Note", "Re-rank models by topic similarity": "Re<PERSON><PERSON>er les modèles par similarité de sujet", "Read": "<PERSON><PERSON>", "Read Aloud": "Lire à haute voix", "Reasoning Effort": "E<PERSON>ort de raisonnement", "Record voice": "Enregistrer la voix", "Redirecting you to Open WebUI Community": "Redirection vers la communauté OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Désignez-vous comme « Utilisateur » (par ex. « L'utilisateur apprend l'espagnol »)", "References from": "Références de", "Refused when it shouldn't have": "<PERSON><PERSON><PERSON><PERSON> alors qu'il n'aurait pas dû l'être", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Release Notes": "Notes de mise à jour", "Relevance": "Pertinence", "Remove": "<PERSON><PERSON><PERSON>", "Remove Model": "<PERSON><PERSON><PERSON> le modèle", "Rename": "<PERSON>mmer", "Reorder Models": "Réorganiser les modèles", "Repeat Last N": "Répéter les N derniers", "Repeat Penalty (Ollama)": "Pénalité de répétition (Ollama)", "Reply in Thread": "Répondre dans le fil de discussion", "Request Mode": "Mode de requête", "Reranking Model": "Mo<PERSON><PERSON><PERSON> de ré-ranking", "Reranking model disabled": "Mod<PERSON>le de ré-ranking désactivé", "Reranking model set to \"{{reranking_model}}\"": "<PERSON><PERSON><PERSON><PERSON> de ré-ranking défini sur « {{reranking_model}} »", "Reset": "Réinitialiser", "Reset All Models": "Réinitialiser tous les modèles", "Reset Upload Directory": "Réinitialiser le répertoire de téléchargement", "Reset Vector Storage/Knowledge": "Réinitialiser le stockage vectoriel/connaissances", "Reset view": "Réinitialiser la vue", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Les notifications de réponse ne peuvent pas être activées car les autorisations du site web ont été refusées. Veuillez vérifier les paramètres de votre navigateur pour accorder l'accès nécessaire.", "Response splitting": "Fractionnement de la réponse", "Result": "Résultat", "Retrieval": "Récupération", "Retrieval Query Generation": "Génération de requête de RAG", "Rich Text Input for Chat": "<PERSON>sie de texte enrichi pour le chat", "RK": "<PERSON>ng", "Role": "R<PERSON><PERSON>", "Rosé Pine": "<PERSON>n r<PERSON>", "Rosé Pine Dawn": "Aube de Pin <PERSON>", "RTL": "RTL", "Run": "Exécuter", "Running": "Exécution", "Save": "Enregistrer", "Save & Create": "Enregistrer & Créer", "Save & Update": "Enregistrer & Mettre à jour", "Save As Copy": "Enregistrer comme copie", "Save Tag": "Enregistrer le tag", "Saved": "Enregistré", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "La sauvegarde des journaux de conversation directement dans le stockage de votre navigateur n'est plus prise en charge. V<PERSON><PERSON>z prendre un instant pour télécharger et supprimer vos journaux de conversation en cliquant sur le bouton ci-dessous. Ne vous inquiétez pas, vous pouvez facilement réimporter vos journaux de conversation dans le backend via", "Scroll to bottom when switching between branches": "Dé<PERSON>ler vers le bas lors du passage d'une branche à l'autre", "Search": "Recherche", "Search a model": "Rechercher un modèle", "Search Base": "Base de recherche", "Search Chats": "Rechercher des conversations", "Search Collection": "Rechercher une collection", "Search Filters": "Filtres de recherche", "search for tags": "Rechercher des tags", "Search Functions": "Rechercher des fonctions", "Search Knowledge": "Rechercher des connaissances", "Search Models": "Rechercher des modèles", "Search options": "Options de recherche", "Search Prompts": "Rechercher des prompts", "Search Result Count": "Nombre de résultats de recherche", "Search the internet": "", "Search Tools": "Rechercher des outils", "SearchApi API Key": "Clé API SearchApi", "SearchApi Engine": "Moteur de recherche SearchApi", "Searched {{count}} sites": "{{count}} sites recherchés", "Searching \"{{searchQuery}}\"": "Recherche de « {{searchQuery}} »", "Searching Knowledge for \"{{searchQuery}}\"": "Recherche des connaissances pour « {{searchQuery}} »", "Searxng Query URL": "URL de recherche Searxng", "See readme.md for instructions": "Voir le fichier readme.md pour les instructions", "See what's new": "Découvrez les nouvelles fonctionnalités", "Seed": "Seed", "Select a base model": "Sélectionnez un modèle de base", "Select a engine": "Sélectionnez un moteur", "Select a function": "Sélectionnez une fonction", "Select a group": "Sélectionner un groupe", "Select a model": "Sélectionnez un modèle", "Select a pipeline": "Sélectionnez un pipeline", "Select a pipeline url": "Sélectionnez l'URL du pipeline", "Select a tool": "Sélectionnez un outil", "Select an auth method": "Veuillez sélectionner une méthode de connexion", "Select an Ollama instance": "Sélectionnez une instance Ollama", "Select Engine": "Sélectionnez le moteur", "Select Knowledge": "Sélectionnez une connaissance", "Select only one model to call": "Sélectionnez seulement un modèle pour appeler", "Selected model(s) do not support image inputs": "Les modèle(s) sélectionné(s) ne prennent pas en charge les entrées d'images", "Semantic distance to query": "Distance sémantique à la requête", "Send": "Envoyer", "Send a Message": "Envoyer un message", "Send message": "Envoyer un message", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Envoie `stream_options: { include_usage: true }` dans la requête.\nLes fournisseurs pris en charge renverront des informations sur l'utilisation des tokens dans la réponse lorsque cette option est activée.", "September": "Septembre", "SerpApi API Key": "Clé d'API SerpAPI", "SerpApi Engine": "Moteur SerpAPI", "Serper API Key": "Clé API Serper", "Serply API Key": "Clé API Serply", "Serpstack API Key": "Clé API Serpstack", "Server connection verified": "Connexion au serveur vérifiée", "Set as default": "Définir comme valeur par défaut", "Set CFG Scale": "Définir la CFG", "Set Default Model": "Définir le modèle par défaut", "Set embedding model": "<PERSON>é<PERSON><PERSON> le modèle d'embedding", "Set embedding model (e.g. {{model}})": "Définir le modèle d'embedding (par ex. {{model}})", "Set Image Size": "Définir la taille de l'image", "Set reranking model (e.g. {{model}})": "Dé<PERSON><PERSON> le modèle de ré-ranking (par ex. {{model}})", "Set Sampler": "Définir le sampler", "Set Scheduler": "Définir le planificateur", "Set Steps": "Définir le nombre d'étapes", "Set Task Model": "Définir le modèle de tâche", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Définir le nombre de couches qui seront déchargées sur le GPU. Augmenter cette valeur peut améliorer considérablement les performances pour les modèles optimisés pour l'accélération GPU, mais peut également consommer plus d'énergie et de ressources GPU.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Définir le nombre de threads de travail utilisés pour le calcul. Cette option contrôle combien de threads sont utilisés pour traiter les demandes entrantes simultanément. L'augmentation de cette valeur peut améliorer les performances sous de fortes charges de travail concurrentes mais peut également consommer plus de ressources CPU.", "Set Voice": "Choisir la voix", "Set whisper model": "<PERSON><PERSON> le modèle <PERSON>per", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Définit les séquences d'arrêt à utiliser. Lorsque ce motif est rencontré, le LLM cessera de générer du texte et retournera. Plusieurs motifs d'arrêt peuvent être définis en spécifiant plusieurs paramètres d'arrêt distincts dans un fichier modèle.", "Settings": "Paramètres", "Settings saved successfully!": "Paramètres enregistrés avec succès !", "Share": "Partager", "Share Chat": "Partage de conversation", "Share to Open WebUI Community": "Partager avec la communauté OpenWebUI", "Sharing Permissions": "", "Show": "<PERSON><PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "Afficher la fenêtre modale \"Quoi de neuf\" lors de la connexion", "Show Admin Details in Account Pending Overlay": "Afficher les coordonnées de l'administrateur aux comptes en attente", "Show Model": "", "Show shortcuts": "Aff<PERSON>r les raccourcis", "Show your support!": "<PERSON><PERSON> votre soutien !", "Showcased creativity": "Créativité mise en avant", "Sign in": "Connexion", "Sign in to {{WEBUI_NAME}}": "Connectez-vous à {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Connectez-vous à {{WEBUI_NAME}} avec LDAP", "Sign Out": "Déconnexion", "Sign up": "Inscrivez-vous", "Sign up to {{WEBUI_NAME}}": "Inscrivez-vous à {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Connexion à {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Source": "Source", "Speech Playback Speed": "Vitesse de lecture de la parole", "Speech recognition error: {{error}}": "E<PERSON>ur de reconnaissance vocale : {{error}}", "Speech-to-Text Engine": "Moteur de reconnaissance vocale", "Stop": "Stop", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Stream Chat Response": "Streamer la réponse de la conversation", "STT Model": "<PERSON><PERSON><PERSON><PERSON> de Speech-to-Text", "STT Settings": "Paramètres de Speech-to-Text", "Subtitle (e.g. about the Roman Empire)": "Sous-titres (par ex. sur l'Empire romain)", "Success": "Réussite", "Successfully updated.": "Mise à jour réussie.", "Suggested": "<PERSON><PERSON><PERSON><PERSON>", "Support": "Supporter", "Support this plugin:": "Supporter ce module", "Sync directory": "Synchroniser le répertoire", "System": "Système", "System Instructions": "Instructions système", "System Prompt": "Prompt système", "Tags": "", "Tags Generation": "Génération de tags", "Tags Generation Prompt": "Prompt de génération de tags", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "<PERSON><PERSON><PERSON> au modèle", "Tap to interrupt": "Appuyez pour interrompre", "Tasks": "Tâches", "Tavily API Key": "Clé API Tavily", "Tell us more:": "Dites-nous en plus à ce sujet : ", "Temperature": "Température", "Template": "Template", "Temporary Chat": "<PERSON><PERSON>", "Text Splitter": "Text Splitter", "Text-to-Speech Engine": "<PERSON><PERSON><PERSON> de Text-to-Speech", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Merci pour vos commentaires !", "The Application Account DN you bind with for search": "Le DN du compte de l'application avec lequel vous vous liez pour la recherche", "The base to search for users": "La base pour rechercher des utilisateurs", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Les développeurs de ce plugin sont des bénévoles passionnés issus de la communauté. Si vous trouvez ce plugin utile, merci de contribuer à son développement.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Le classement d'évaluation est basé sur le système de notation Elo et est mis à jour en temps réel.", "The LDAP attribute that maps to the mail that users use to sign in.": "L'attribut LDAP qui correspond à l'adresse e-mail que les utilisateurs utilisent pour se connecter.", "The LDAP attribute that maps to the username that users use to sign in.": "L'attribut LDAP qui correspond au nom d'utilisateur que les utilisateurs utilisent pour se connecter.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Le classement est actuellement en version bêta et nous pouvons ajuster les calculs de notation à mesure que nous peaufinons l'algorithme.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "La taille maximale du fichier en Mo. Si la taille du fichier dépasse cette limite, le fichier ne sera pas téléchargé.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Le nombre maximal de fichiers pouvant être utilisés en même temps dans la conversation. Si le nombre de fichiers dépasse cette limite, les fichiers ne seront pas téléchargés.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Le score doit être une valeur comprise entre 0,0 (0%) et 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "Theme": "Thème", "Thinking...": "En train de réfléchir...", "This action cannot be undone. Do you wish to continue?": "Cette action ne peut pas être annulée. Souhaitez-vous continuer ?", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON><PERSON> garan<PERSON>t que vos conversations précieuses soient sauvegardées en toute sécurité dans votre base de données backend. Merci !", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Il s'agit d'une fonctionnalité expérimentale, elle peut ne pas fonctionner comme prévu et est sujette à modification à tout moment.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Cette option supprimera tous les fichiers existants dans la collection et les remplacera par les fichiers nouvellement téléchargés.", "This response was generated by \"{{model}}\"": "<PERSON>tte réponse a été générée par \"{{model}}\"", "This will delete": "<PERSON><PERSON> supprimera", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON> supprimera <strong>{{NAME}}</strong> et <strong>tout son contenu</strong>.", "This will delete all models including custom models": "<PERSON>la supprimera tous les modèles, y compris les modèles personnalisés", "This will delete all models including custom models and cannot be undone.": "<PERSON>la supprimera tous les modèles, y compris les modèles personnalisés, et ne peut pas être annulé.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "<PERSON>la réinitialisera la base de connaissances et synchronisera tous les fichiers. Souhaitez-vous continuer ?", "Thorough explanation": "Explication approfondie", "Thought for {{DURATION}}": "Réflexion de {{DURATION}}", "Thought for {{DURATION}} seconds": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL du serveur Tika requise.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Conseil: mettez à jour plusieurs emplacements de variables consécutivement en appuyant sur la touche Tab dans l’entrée de chat après chaque remplacement.", "Title": "Titre", "Title (e.g. Tell me a fun fact)": "Titre (par ex. raconte-moi un fait amusant)", "Title Auto-Generation": "Génération automatique des titres", "Title cannot be an empty string.": "Le titre ne peut pas être une chaîne de caractères vide.", "Title Generation": "Génération du Titre", "Title Generation Prompt": "Prompt de génération de titre", "TLS": "TLS", "To access the available model names for downloading,": "Pour accéder aux noms des modèles disponibles,", "To access the GGUF models available for downloading,": "Pour accéder aux modèles GGUF disponibles,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Pour accéder à l'interface Web, veuillez contacter l'administrateur. Les administrateurs peuvent gérer les statuts des utilisateurs depuis le panneau d'administration.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Pour attacher une base de connaissances ici, ajoutez-les d'abord à l'espace de travail « Connaissances ».", "To learn more about available endpoints, visit our documentation.": "Pour en savoir plus sur les points de terminaison disponibles, consultez notre documentation.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Pour protéger votre confidentialité, seules les notes, les identifiants de modèle, les tags et les métadonnées de vos commentaires sont partagés. Vos journaux de discussion restent privés et ne sont pas inclus.", "To select actions here, add them to the \"Functions\" workspace first.": "Pour sélectionner des actions ici, ajoutez-les d'abord à l'espace de travail « Fonctions ».", "To select filters here, add them to the \"Functions\" workspace first.": "Pour sélectionner des filtres ici, ajoutez-les d'abord à l'espace de travail « Fonctions ». ", "To select toolkits here, add them to the \"Tools\" workspace first.": "Pour sélectionner des outils ici, ajoutez-les d'abord à l'espace de travail « Outils ». ", "Toast notifications for new updates": "Notifications toast pour les nouvelles mises à jour", "Today": "<PERSON><PERSON><PERSON>'hui", "Toggle settings": "Afficher/masquer les paramètres", "Toggle sidebar": "Afficher/masquer la barre latérale", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Tokens à conserver lors du rafraîchissement du contexte (num_keep)", "Too verbose": "Trop détaillé", "Tool created successfully": "L'outil a été créé avec succès", "Tool deleted successfully": "Outil supprimé avec succès", "Tool Description": "Description de l'outil", "Tool ID": "ID de l'outil", "Tool imported successfully": "Outil importé avec succès", "Tool Name": "Nom de l'outil", "Tool Servers": "", "Tool updated successfully": "L'outil a été mis à jour avec succès", "Tools": "Outils", "Tools Access": "Accès aux outils", "Tools are a function calling system with arbitrary code execution": "Les outils sont un système d'appel de fonction avec exécution de code arbitraire", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution": "Les outils ont un système d'appel de fonction qui permet l'exécution de code arbitraire", "Tools have a function calling system that allows arbitrary code execution.": "Les outils ont un système d'appel de fonction qui permet l'exécution de code arbitraire.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Top P": "Top P", "Transformers": "Transformers", "Trouble accessing Ollama?": "Problèmes d'accès à Ollama ?", "Trust Proxy Environment": "Faire confiance au proxy de l'environement", "TTS Model": "<PERSON><PERSON><PERSON><PERSON> de Text-to-Speech", "TTS Settings": "Paramètres de Text-to-Speech", "TTS Voice": "Voix de Text-to-Speech", "Type": "Type", "Type Hugging Face Resolve (Download) URL": "Entrez l'URL de Téléchargement Hugging Face Resolve", "Uh-oh! There was an issue with the response.": "Oh! Un problème est survenu avec la réponse.", "UI": "UI", "Unarchive All": "Désarchiver tout", "Unarchive All Archived Chats": "Désarchiver toutes les conversations archivées", "Unarchive Chat": "Désarchiver la conversation", "Unlock mysteries": "Déverrouiller les mystères", "Unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unravel secrets": "Dévoiler les secrets", "Untagged": "<PERSON><PERSON> de <PERSON>", "Update": "Mise à jour", "Update and Copy Link": "Mettre à jour et copier le lien", "Update for the latest features and improvements.": "Mettez à jour pour bénéficier des dernières fonctionnalités et améliorations.", "Update password": "Mettre à jour le mot de passe", "Updated": "Mis à jour", "Updated at": "Mise à jour le", "Updated At": "Mise à jour le", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "<PERSON><PERSON>z la mise à niveau vers le plan payant pour bénéficier de fonctionnalités améliorées, notamment les thèmes et le branding personnalisé, ainsi qu'un support dédié.", "Upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload a GGUF model": "Téléverser un modèle GGUF", "Upload directory": "<PERSON>éléverser un dossier", "Upload files": "Téléverser des fichiers", "Upload Files": "Téléverser des fichiers", "Upload Pipeline": "Pipeline de téléchargement", "Upload Progress": "Progression de l'envoi", "URL": "URL", "URL Mode": "Mode d'URL", "Use '#' in the prompt input to load and include your knowledge.": "Utilisez '#' dans la zone de saisie du prompt pour charger et inclure vos connaissances.", "Use Gravatar": "Util<PERSON> Gravatar", "Use groups to group your users and assign permissions.": "Utilisez des groupes pour regrouper vos utilisateurs et attribuer des permissions.", "Use Initials": "Utiliser les initiales", "use_mlock (Ollama)": "Utiliser mlock (Ollama)", "use_mmap (Ollama)": "Utiliser mmap (Ollama)", "user": "utilisateur", "User": "Utilisa<PERSON>ur", "User location successfully retrieved.": "L'emplacement de l'utilisateur a été récupéré avec succès.", "User Webhooks": "", "Username": "Nom d'utilisateur", "Users": "Utilisateurs", "Using the default arena model with all models. Click the plus button to add custom models.": "Utilisation du modèle d'arène par défaut avec tous les modèles. Cliquez sur le bouton plus pour ajouter des modèles personnalisés.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Unités de temps valides :", "Valves": "<PERSON><PERSON>", "Valves updated": "<PERSON><PERSON> mises à jour", "Valves updated successfully": "Les vannes ont été mises à jour avec succès", "variable": "variable", "variable to have them replaced with clipboard content.": "variable pour qu'elles soient remplacées par le contenu du presse-papiers.", "Verify Connection": "", "Version": "Version:", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} de {{totalVersions}}", "View Replies": "Voir les réponses", "View Result from **{{NAME}}**": "", "Visibility": "Visibilité", "Voice": "Voix", "Voice Input": "<PERSON><PERSON> vocale", "Warning": "Avertissement", "Warning:": "Avertissement :", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Avertissement : Activer cette option permettra aux utilisateurs de télécharger du code arbitraire sur le serveur.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Avertissement : Si vous mettez à jour ou modifiez votre modèle d'embedding, vous devrez réimporter tous les documents.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "API Web", "Web Search": "Recherche Web", "Web Search Engine": "Moteur de recherche Web", "Web Search in Chat": "Recherche web depuis le chat", "Web Search Query Generation": "Génération de requête de recherche Web", "Webhook URL": "URL du webhook", "WebUI Settings": "Paramètres de WebUI", "WebUI URL": "URL de WebUI", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI fera des requêtes à \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI fera des requêtes à \"{{url}}/chat/completions\"", "What are you trying to achieve?": "Que cherchez-vous à accomplir ?", "What are you working on?": "Sur quoi travaillez-vous ?", "What’s New in": "Quoi de neuf dans", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Lorsq<PERSON>'il est activé, le modèle répondra à chaque message de chat en temps réel, g<PERSON><PERSON>nt une réponse dès que l'utilisateur envoie un message. Ce mode est utile pour les applications de chat en direct, mais peut affecter les performances sur un matériel plus lent.", "wherever you are": "où que vous soyez", "Whisper (Local)": "<PERSON><PERSON><PERSON> (local)", "Why?": "Pourquoi ?", "Widescreen Mode": "Mode grand écran", "Won": "Victoires", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "Espace de travail", "Workspace Permissions": "Autorisations de l'espace de travail", "Write": "<PERSON><PERSON><PERSON><PERSON>", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON>z une suggestion de prompt (par exemple : Qui êtes-vous ?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Rédigez un résumé de 50 mots qui résume [sujet ou mot-clé].", "Write something...": "<PERSON><PERSON><PERSON><PERSON> quelque chose...", "Write your model template content here": "Écrivez ici le contenu de votre modèle", "Yesterday": "<PERSON>er", "You": "Vous", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Vous ne pouvez discuter qu'avec un maximum de {{maxCount}} fichier(s) à la fois.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Vous pouvez personnaliser vos interactions avec les LLM en ajoutant des mémoires à l'aide du bouton « Gérer » ci-dessous, ce qui les rendra plus utiles et mieux adaptées à vos besoins.", "You cannot upload an empty file.": "Vous ne pouvez pas envoyer un fichier vide.", "You do not have permission to upload files": "", "You do not have permission to upload files.": "Vous n'avez pas la permission de télécharger des fichiers.", "You have no archived conversations.": "Vous n'avez aucune conversation archivée.", "You have shared this chat": "Vous avez partagé cette conversation.", "You're a helpful assistant.": "Vous êtes un assistant efficace.", "You're now logged in.": "Vous êtes désormais connecté.", "Your account status is currently pending activation.": "Votre statut de compte est actuellement en attente d'activation.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "L'intégralité de votre contribution ira directement au développeur du plugin ; Open WebUI ne prend aucun pourcentage. Cependant, la plateforme de financement choisie peut avoir ses propres frais.", "Youtube": "YouTube", "Youtube Language": "Langue de Youtube", "Youtube Proxy URL": "URL du proxy YouTube"}