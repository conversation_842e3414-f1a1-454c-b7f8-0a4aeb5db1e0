{"-1 for no limit, or a positive integer for a specific limit": "-1 ནི་ཚད་མེད་པའི་ཆེད་དམ། ཡང་ན་ཧྲིལ་གྲངས་དགོས་ངེས་ཤིག་ཚད་བཀག་ངེས་ཅན་ཞིག་གི་ཆེད་དུ།", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' ཡང་ན་ '-1' དུས་ཚོད་རྫོགས་མི་དགོས་པའི་ཆེད་དུ།", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(དཔེར་ན། `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(དཔེར་ན། `sh webui.sh --api`)", "(latest)": "(ཆེས་གསར།)", "(Ollama)": "(<PERSON><PERSON><PERSON>)", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "ཡིག་ཕྲེང་ {{COUNT}} སྦས་ཡོད།", "{{COUNT}} Replies": "ལན་ {{COUNT}}", "{{user}}'s Chats": "{{user}} ཡི་ཁ་བརྡ།", "{{webUIName}} Backend Required": "{{webUIName}} རྒྱབ་སྣེ་དགོས།", "*Prompt node ID(s) are required for image generation": "*པར་བཟོའི་ཆེད་དུ་འགུལ་སློང་མདུད་ཚེག་གི་ ID(s) དགོས།", "A new version (v{{LATEST_VERSION}}) is now available.": "པར་གཞི་གསར་པ། (v{{LATEST_VERSION}}) ད་ལྟ་ཡོད།", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ལས་ཀའི་དཔེ་དབྱིབས་ནི་ཁ་བརྡའི་ཁ་བྱང་བཟོ་བ་དང་དྲ་བའི་འཚོལ་བཤེར་འདྲི་བ་ལྟ་བུའི་ལས་འགན་སྒྲུབ་སྐབས་སྤྱོད་ཀྱི་ཡོད།", "a user": "བེད་སྤྱོད་མཁན་ཞིག", "About": "སྐོར་ལོ།", "Accept autocomplete generation / Jump to prompt variable": "རང་འཚང་བཟོ་བསྐྲུན་དང་ལེན་བྱེད་པ། / འགུལ་སློང་འགྱུར་ཚད་ལ་མཆོངས་པ།", "Access": "འཛུལ་སྤྱོད།", "Access Control": "འཛུལ་སྤྱོད་ཚོད་འཛིན།", "Accessible to all users": "བེད་སྤྱོད་མཁན་ཡོངས་ལ་འཛུལ་སྤྱོད་ཆོག་པ།", "Account": "རྩིས་ཁྲ།", "Account Activation Pending": "རྩིས་ཁྲ་སྒུལ་བསྐྱོད་སྒུག་བཞིན་པ།", "Accurate information": "གནས་ཚུལ་ཡང་དག", "Actions": "བྱ་སྤྱོད།", "Activate": "སྒུལ་བསྐྱོད།", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "ཁ་བརྡའི་ནང་འཇུག་ཏུ་ \"/{{COMMAND}}\" མནན་ནས་བཀའ་བརྡ་འདི་སྒུལ་བསྐྱོད་བྱེད་པ།", "Active Users": "བེད་སྤྱོད་མཁན་ལས་བྱེད་བཞིན་པ།", "Add": "སྣོན་པ།", "Add a model ID": "དཔེ་དབྱིབས་ཀྱི་ ID ཞིག་སྣོན་པ།", "Add a short description about what this model does": "དཔེ་དབྱིབས་འདིས་ཅི་ཞིག་བྱེད་མིན་སྐོར་གྱི་འགྲེལ་བཤད་ཐུང་ངུ་ཞིག་སྣོན་པ།", "Add a tag": "རྟགས་ཤིག་སྣོན་པ།", "Add Arena Model": "Arena དཔེ་དབྱིབས་སྣོན་པ།", "Add Connection": "སྦྲེལ་མཐུད་སྣོན་པ།", "Add Content": "ནང་དོན་སྣོན་པ།", "Add content here": "ནང་དོན་འདིར་སྣོན་པ།", "Add custom prompt": "སྲོལ་བཟོས་འགུལ་སློང་སྣོན་པ།", "Add Files": "ཡིག་ཆ་སྣོན་པ།", "Add Group": "ཚོགས་པ་སྣོན་པ།", "Add Memory": "དྲན་ཤེས་སྣོན་པ།", "Add Model": "དཔེ་དབྱིབས་སྣོན་པ།", "Add Reaction": "ཡ་ལན་སྣོན་པ།", "Add Tag": "རྟགས་སྣོན་པ།", "Add Tags": "རྟགས་ཚུ་སྣོན་པ།", "Add text content": "ཡིག་རྐྱང་ནང་དོན་སྣོན་པ།", "Add User": "བེད་སྤྱོད་མཁན་སྣོན་པ།", "Add User Group": "བེད་སྤྱོད་མཁན་ཚོགས་པ་སྣོན་པ།", "Adjusting these settings will apply changes universally to all users.": "སྒྲིག་འགོད་འདི་དག་ལེགས་སྒྲིག་བྱས་ན་བེད་སྤྱོད་མཁན་ཡོངས་ལ་འགྱུར་བ་དེ་བཀོལ་སྤྱོད་བྱེད་ངེས།", "admin": "དོ་དམ་པ།", "Admin": "དོ་དམ་པ།", "Admin Panel": "དོ་དམ་པའི་ལྟ་སྟེགས།", "Admin Settings": "དོ་དམ་པའི་སྒྲིག་འགོད།", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "དོ་དམ་པས་དུས་རྟག་ཏུ་ལག་ཆ་ཡོངས་ལ་འཛུལ་སྤྱོད་བྱེད་ཆོག བེད་སྤྱོད་མཁན་གྱིས་ལས་ཡུལ་ནང་དཔེ་དབྱིབས་རེ་རེར་བཀོད་པའི་ལག་ཆ་དགོས་མཁོ་ཡོད།", "Advanced Parameters": "མཐོ་རིམ་ཞུགས་གྲངས།", "Advanced Params": "མཐོ་རིམ་ཞུགས།", "All": "ཡོངས།", "All Documents": "ཡིག་ཆ་ཡོངས།", "All models deleted successfully": "དཔེ་དབྱིབས་ཡོངས་རྫོགས་ལེགས་པར་བསུབས་ཟིན།", "Allow Chat Controls": "ཁ་བརྡའི་ཚོད་འཛིན་ལ་གནང་བ་སྤྲོད་པ།", "Allow Chat Delete": "ཁ་བརྡ་བསུབ་པར་གནང་བ་སྤྲོད་པ།", "Allow Chat Deletion": "ཁ་བརྡ་བསུབ་པར་གནང་བ་སྤྲོད་པ།", "Allow Chat Edit": "ཁ་བརྡ་ཞུ་དག་ལ་གནང་བ་སྤྲོད་པ།", "Allow File Upload": "ཡིག་ཆ་སྤར་བར་གནང་བ་སྤྲོད་པ།", "Allow non-local voices": "ས་གནས་མིན་པའི་སྐད་གདངས་ལ་གནང་བ་སྤྲོད་པ།", "Allow Temporary Chat": "གནས་སྐབས་ཁ་བརྡར་གནང་བ་སྤྲོད་པ།", "Allow User Location": "བེད་སྤྱོད་མཁན་གནས་ཡུལ་ལ་གནང་བ་སྤྲོད་པ།", "Allow Voice Interruption in Call": "སྐད་འབོད་ནང་གི་སྐད་ཆའི་བར་ཆད་ལ་གནང་བ་སྤྲོད་པ།", "Allowed Endpoints": "གནང་བ་ཐོབ་པའི་མཇུག་མཐུད།", "Already have an account?": "རྩིས་ཁྲ་ཡོད་ཟིན་ནམ།", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "top_p ཡི་ཚབ་བྱེད། སྤུས་ཚད་དང་སྣ་ཚོགས་ཀྱི་དོ་མཉམ་འགན་ལེན་བྱ་རྒྱུར་དམིགས་པ། ཞུགས་གྲངས་ p ཡིས་མཚོན་བྱེད་ནི། ཆེས་འབྱུང་སྲིད་པའི་ཊོཀ་ཀེན་གྱི་ཆགས་ཚུལ་དང་བསྡུར་ན། བསམ་ཞིབ་བྱེད་དགོས་པའི་ཊོཀ་ཀེན་གྱི་ཆགས་ཚུལ་ཉུང་ཤོས་ཡིན། དཔེར་ན། p=0.05 དང་ཆེས་འབྱུང་སྲིད་པའི་ཊོཀ་ཀེན་གྱི་ཆགས་ཚུལ་ 0.9 ཡིན་ན། 0.045 ལས་ཆུང་བའི་རིན་ཐང་ཅན་གྱི་ལོ་ཇི་ཁེ་སི་དག་ཕྱིར་འཚག་བྱེད་ངེས།", "Always": "རྟག་ཏུ།", "Always Collapse Code Blocks": "རྟག་ཏུ་ཀོཌ་གཏོགས་ཁོངས་བསྐུམ་པ།", "Always Expand Details": "རྟག་ཏུ་ཞིབ་ཕྲ་རྒྱ་བསྐྱེད་པ།", "Amazing": "ངོ་མཚར་ཆེན།", "an assistant": "ལག་རོགས་པ།", "Analyzed": "དབྱེ་ཞིབ་བྱས་པ།", "Analyzing...": "དབྱེ་ཞིབ་བྱེད་བཞིན་པ།...", "and": "དང་།", "and {{COUNT}} more": "ད་དུང་ {{COUNT}}", "and create a new shared link.": "དང་མཉམ་སྤྱོད་སྦྲེལ་ཐག་གསར་པ་ཞིག་བཟོ་བ།", "API Base URL": "API གཞི་རྩའི་ URL", "API Key": "API ལྡེ་མིག", "API Key created.": "API ལྡེ་མིག་བཟོས་ཟིན།", "API Key Endpoint Restrictions": "API ལྡེ་མིག་མཇུག་མཐུད་ཚད་བཀག", "API keys": "API ལྡེ་མིག", "Application DN": "Application DN", "Application DN Password": "Application DN གསང་གྲངས།", "applies to all users with the \"user\" role": "\"བེད་སྤྱོད་མཁན་\" གྱི་གནས་ཚད་ཡོད་པའི་བེད་སྤྱོད་མཁན་ཡོངས་ལ་འཕྲོད་པ།", "April": "ཟླ་བཞི་པ།", "Archive": "ཡིག་མཛོད།", "Archive All Chats": "ཁ་བརྡ་ཡོངས་རྫོགས་ཡིག་མཛོད་དུ་འཇུག་པ།", "Archived Chats": "ཡིག་མཛོད་དུ་བཞག་པའི་ཁ་བརྡ།", "archived-chat-export": "ཡིག་མཛོད་ཁ་བརྡ་ཕྱིར་གཏོང་།", "Are you sure you want to clear all memories? This action cannot be undone.": "དྲན་ཤེས་ཡོངས་རྫོགས་བསུབ་འདོད་ཡོད་དམ། བྱ་སྤྱོད་འདི་ཕྱིར་ལྡོག་བྱེད་མི་ཐུབ།", "Are you sure you want to delete this channel?": "ཁྱེད་ཀྱིས་བགྲོ་གླེང་འདི་བསུབ་འདོད་ངེས་ཡིན་ནམ།", "Are you sure you want to delete this message?": "འཕྲིན་འདི་བསུབ་འདོད་ངེས་ཡིན་ནམ།", "Are you sure you want to unarchive all archived chats?": "ཁྱེད་ཀྱིས་ཡིག་མཛོད་དུ་བཞག་པའི་ཁ་བརྡ་ཡོངས་རྫོགས་ཕྱིར་འདོན་འདོད་ངེས་ཡིན་ནམ།", "Are you sure?": "ཁྱོད་ངེས་པ་ཡིན་ནམ།", "Arena Models": "Arena དཔེ་དབྱིབས།", "Artifacts": "རྫས་རྟེན།", "Ask": "འདྲི་བ།", "Ask a question": "དྲི་བ་ཞིག་འདྲི་བ།", "Assistant": "ལག་རོགས་པ།", "Attach file from knowledge": "ཤེས་བྱའི་ནང་ནས་ཡིག་ཆ་འཇོག་པ།", "Attention to detail": "ཞིབ་ཕྲར་དོ་སྣང་།", "Attribute for Mail": "ཡིག་ཟམ་གྱི་ཁྱད་ཆོས།", "Attribute for Username": "བེད་སྤྱོད་མིང་གི་ཁྱད་ཆོས།", "Audio": "སྒྲ།", "August": "ཟླ་བརྒྱད་པ།", "Auth": "", "Authenticate": "དངོས་ར་སྤྲོད་པ།", "Authentication": "དངོས་ར་སྤྲོད་པ།", "Auto": "", "Auto-Copy Response to Clipboard": "ལན་རང་འགུལ་གྱིས་སྦྱར་སྡེར་དུ་འདྲ་བཤུས་བྱེད་པ།", "Auto-playback response": "ལན་རང་འགུལ་གྱིས་གཏོང་བ།", "Autocomplete Generation": "རང་འཚང་བཟོ་སྐྲུན།", "Autocomplete Generation Input Max Length": "རང་འཚང་བཟོ་སྐྲུན་ནང་འཇུག་གི་རིང་ཚད་ཆེ་ཤོས།", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth ཡིག་ཕྲེང་།", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 གཞི་རྩའི་ URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 གཞི་རྩའི་ URL ངེས་པར་དུ་དགོས།", "Available list": "ཡོད་པའི་ཐོ་གཞུང་།", "Available Tools": "", "available!": "ཡོད།", "Awful": "ཧ་ཅང་སྡུག", "Azure AI Speech": "Azure AI སྐད་ཆ།", "Azure Region": "Azure ས་ཁུལ།", "Back": "རྒྱབ།", "Bad Response": "ལན་ངན་པ།", "Banners": "དར་ཆ།", "Base Model (From)": "གཞི་རྩའི་དཔེ་དབྱིབས། (ནས།)", "Batch Size (num_batch)": "ཚན་ཆུང་ཆེ་ཆུང་། (num_batch)", "before": "སྔོན།", "Being lazy": "ལེ་ལོ་བྱེད་པ།", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing Search V7 མཇུག་མཐུད།", "Bing Search V7 Subscription Key": "Bing Search V7 མངགས་ཉོ་ལྡེ་མིག", "Bocha Search API Key": "Bocha Search API ལྡེ་མིག", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "ཚད་བཀག་ལན་གྱི་ཆེད་དུ་ཊོཀ་ཀེན་ངེས་ཅན་ལ་ཤུགས་སྣོན་ནམ་ཉེས་ཆད་གཏོང་བ། ཕྱོགས་ཞེན་གྱི་རིན་ཐང་ -100 ནས་ 100 བར་བཙིར་ངེས། (ཚུད་པ།) (སྔོན་སྒྲིག་མེད།)", "Brave Search API Key": "Brave Search API ལྡེ་མིག", "By {{name}}": "{{name}} ཡིས།", "Bypass Embedding and Retrieval": "ཚུད་འཇུག་དང་ལེན་ཚུར་སྒྲུབ་ལས་བརྒལ་བ།", "Bypass SSL verification for Websites": "དྲ་ཚིགས་ཀྱི་ SSL ར་སྤྲོད་བརྒལ་བ།", "Calendar": "ལོ་ཐོ།", "Call": "སྐད་འབོད།", "Call feature is not supported when using Web STT engine": "Web STT མ་ལག་སྤྱོད་སྐབས་སྐད་འབོད་ཀྱི་ཁྱད་ཆོས་ལ་རྒྱབ་སྐྱོར་མེད།", "Camera": "པར་ཆས།", "Cancel": "རྩིས་མེད།", "Capabilities": "ནུས་པ།", "Capture": "འཛིན་པ།", "Certificate Path": "ལག་ཁྱེར་གྱི་ལམ་བུ།", "Change Password": "གསང་གྲངས་བརྗེ་བ།", "Channel Name": "བགྲོ་གླེང་གི་མིང་།", "Channels": "བགྲོ་གླེང་།", "Character": "ཡིག་འབྲུ།", "Character limit for autocomplete generation input": "རང་འཚང་བཟོ་སྐྲུན་ནང་འཇུག་གི་ཡིག་འབྲུ་ཚད་བཀག", "Chart new frontiers": "ས་མཚམས་གསར་པ་འགོད་པ།", "Chat": "ཁ་བརྡ།", "Chat Background Image": "ཁ་བརྡའི་རྒྱབ་ལྗོངས་པར།", "Chat Bubble UI": "ཁ་བརྡའི་ལྦུ་བའི་ UI", "Chat Controls": "ཁ་བརྡའི་ཚོད་འཛིན།", "Chat direction": "ཁ་བརྡའི་ཁ་ཕྱོགས།", "Chat Overview": "ཁ་བརྡའི་སྤྱི་མཐོང་།", "Chat Permissions": "ཁ་བརྡའི་དབང་ཚད།", "Chat Tags Auto-Generation": "ཁ་བརྡའི་རྟགས་རང་འགུལ་བཟོ་སྐྲུན།", "Chats": "ཁ་བརྡ།", "Check Again": "ཡང་བསྐྱར་ཞིབ་དཔྱད།", "Check for updates": "གསར་སྒྱུར་འཚོལ་ཞིབ།", "Checking for updates...": "གསར་སྒྱུར་འཚོལ་བཞིན་པ།...", "Choose a model before saving...": "ཉར་ཚགས་མ་བྱས་སྔོན་དུ་དཔེ་དབྱིབས་ཤིག་གདམ་པ།...", "Chunk Overlap": "དུམ་བུ་བསྣོལ་བ།", "Chunk Size": "དུམ་བུའི་ཆེ་ཆུང་།", "Ciphers": "གསང་སྦྱོར།", "Citation": "ལུང་འདྲེན།", "Clear memory": "དྲན་ཤེས་གཙང་སེལ།", "Clear Memory": "དྲན་ཤེས་གཙང་སེལ།", "click here": "འདིར་མནན་པ།", "Click here for filter guides.": "འཚག་མ་ལམ་སྟོན་གྱི་ཆེད་དུ་འདིར་མནན་པ།", "Click here for help.": "རོགས་རམ་ཆེད་དུ་འདིར་མནན་པ།", "Click here to": "འདིར་མནན་ནས།", "Click here to download user import template file.": "བེད་སྤྱོད་མཁན་ནང་འདྲེན་མ་དཔེ་ཡིག་ཆ་ཕབ་ལེན་བྱེད་པར་འདིར་མནན་པ།", "Click here to learn more about faster-whisper and see the available models.": "faster-whisper སྐོར་མང་ཙམ་ཤེས་པ་དང་ཡོད་པའི་དཔེ་དབྱིབས་ལྟ་བར་འདིར་མནན་པ།", "Click here to see available models.": "ཡོད་པའི་དཔེ་དབྱིབས་ལྟ་བར་འདིར་མནན་པ།", "Click here to select": "གདམ་ག་བྱེད་པར་འདིར་མནན་པ།", "Click here to select a csv file.": "csv ཡིག་ཆ་ཞིག་གདམ་ག་བྱེད་པར་འདིར་མནན་པ།", "Click here to select a py file.": "py ཡིག་ཆ་ཞིག་གདམ་ག་བྱེད་པར་འདིར་མནན་པ།", "Click here to upload a workflow.json file.": "workflow.json ཡིག་ཆ་ཞིག་སྤར་བར་འདིར་མནན་པ།", "click here.": "འདིར་མནན་པ།", "Click on the user role button to change a user's role.": "བེད་སྤྱོད་མཁན་གྱི་གནས་ཚད་མཐེབ་གནོན་ལ་མནན་ནས་བེད་སྤྱོད་མཁན་གྱི་གནས་ཚད་བརྗེ་བ།", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "སྦྱར་སྡེར་འབྲི་བའི་དབང་ཚད་ཁས་མ་བླངས། དགོས་ངེས་ཀྱི་འཛུལ་སྤྱོད་སྤྲོད་པར་ཁྱེད་ཀྱི་བརྡ་འཚོལ་ཆས་ཀྱི་སྒྲིག་འགོད་ཞིབ་དཔྱད་བྱེད་རོགས།", "Clone": "འདྲ་བཟོ།", "Clone Chat": "ཁ་བརྡ་འདྲ་བཟོ།", "Clone of {{TITLE}}": "{{TITLE}} ཡི་འདྲ་བཟོ།", "Close": "ཁ་རྒྱག་པ།", "Code execution": "ཀོཌ་ལག་བསྟར།", "Code Execution": "ཀོཌ་ལག་བསྟར།", "Code Execution Engine": "ཀོཌ་ལག་བསྟར་འཕྲུལ་འཁོར།", "Code Execution Timeout": "ཀོཌ་ལག་བསྟར་དུས་ཚོད་བཀག་པ།", "Code formatted successfully": "ཀོཌ་བཀོད་པ་ལེགས་པར་བཟོས་ཟིན།", "Code Interpreter": "ཀོཌ་འགྲེལ་བཤད།", "Code Interpreter Engine": "ཀོཌ་འགྲེལ་བཤད་འཕྲུལ་འཁོར།", "Code Interpreter Prompt Template": "ཀོཌ་འགྲེལ་བཤད་འགུལ་སློང་མ་དཔེ།", "Collapse": "བསྐུམ་པ།", "Collection": "བསྡུ་གསོག", "Color": "ཚོན་མདོག", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API ལྡེ་མིག", "ComfyUI Base URL": "ComfyUI གཞི་རྩའི་ URL", "ComfyUI Base URL is required.": "ComfyUI གཞི་རྩའི་ URL དགོས་ངེས།", "ComfyUI Workflow": "ComfyUI ལས་ཀའི་རྒྱུན་རིམ།", "ComfyUI Workflow Nodes": "ComfyUI ལས་ཀའི་རྒྱུན་རིམ་མདུད་ཚེག", "Command": "བཀའ་བརྡ།", "Completions": "འགྲུབ་པ།", "Concurrent Requests": "མཉམ་ལས་རེ་ཞུ།", "Configure": "སྒྲིག་འགོད།", "Confirm": "གཏན་འཁེལ།", "Confirm Password": "གསང་གྲངས་གཏན་འཁེལ།", "Confirm your action": "ཁྱེད་ཀྱི་བྱ་སྤྱོད་གཏན་འཁེལ།", "Confirm your new password": "ཁྱེད་ཀྱི་གསང་གྲངས་གསར་པ་གཏན་འཁེལ།", "Connect to your own OpenAI compatible API endpoints.": "ཁྱེད་རང་གི་ OpenAI དང་མཐུན་པའི་ API མཇུག་མཐུད་ལ་སྦྲེལ་བ།", "Connect to your own OpenAPI compatible external tool servers.": "ཁྱེད་རང་གི་ OpenAPI དང་མཐུན་པའི་ཕྱི་རོལ་ལག་ཆའི་སར་བར་ལ་སྦྲེལ་བ།", "Connection failed": "", "Connection successful": "", "Connections": "སྦྲེལ་མཐུད།", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "རྒྱུ་མཚན་འདྲེན་པའི་དཔེ་དབྱིབས་ཀྱི་རྒྱུ་མཚན་འདྲེན་པའི་འབད་བརྩོན་ལ་ཚད་བཀག་བྱེད་པ། རྒྱུ་མཚན་འདྲེན་པའི་འབད་བརྩོན་ལ་རྒྱབ་སྐྱོར་བྱེད་པའི་མཁོ་སྤྲོད་པ་ངེས་ཅན་གྱི་རྒྱུ་མཚན་འདྲེན་པའི་དཔེ་དབྱིབས་ལ་ཁོ་ན་འཕྲོད་པ།", "Contact Admin for WebUI Access": "WebUI འཛུལ་སྤྱོད་ཆེད་དུ་དོ་དམ་པ་དང་འབྲེལ་གཏུག་བྱེད་པ།", "Content": "ནང་དོན།", "Content Extraction Engine": "ནང་དོན་འདོན་སྤེལ་འཕྲུལ་འཁོར།", "Context Length": "ནང་དོན་གྱི་རིང་ཚད།", "Continue Response": "ལན་མུ་མཐུད་པ།", "Continue with {{provider}}": "{{provider}} དང་མཉམ་དུ་མུ་མཐུད་པ།", "Continue with Email": "ཡིག་ཟམ་དང་མཉམ་དུ་མུ་མཐུད་པ།", "Continue with LDAP": "LDAP དང་མཉམ་དུ་མུ་མཐུད་པ།", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTS རེ་ཞུའི་ཆེད་དུ་འཕྲིན་ཡིག་གི་ཡིག་རྐྱང་ཇི་ལྟར་བགོ་དགོས་ཚོད་འཛིན་བྱེད་པ། 'ཚེག་ཤད་' ཀྱིས་རረབ་ལུ་བགོ། 'ཚན་པ་' ཀྱིས་ཚན་པར་བགོ། 'མེད་པ་' ཡིས་འཕྲིན་ཡིག་དེ་ཡིག་ཕྲེང་གཅིག་ཏུ་ཉར་ཚགས་བྱེད་པ།", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "བཟོས་པའི་ཡིག་རྐྱང་ནང་གི་ཊོཀ་ཀེན་གྱི་རིམ་པའི་བསྐྱར་ཟློས་ཚོད་འཛིན་བྱེད་པ། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། 1.5) ཡིས་བསྐྱར་ཟློས་ལ་ཆད་པ་དྲག་པོ་གཏོང་ངེས། དེ་བཞིན་དུ་རིན་ཐང་དམའ་བ་ (དཔེར་ན། 1.1) ཡིས་གུ་ཡངས་ཆེ་བ་ཡོང་ངེས། 1 ལ་སླེབས་དུས། དེ་ནུས་མེད་བཏང་ཡོད།", "Controls": "ཚོད་འཛིན།", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "ཐོན་འབྲས་ཀྱི་འབྲེལ་ཆགས་རང་བཞིན་དང་སྣ་ཚོགས་རང་བཞིན་བར་གྱི་དོ་མཉམ་ཚོད་འཛིན་བྱེད་པ། རིན་ཐང་དམའ་བས་ཡིག་རྐྱང་གི་དམིགས་ཚད་དང་འབྲེལ་ཆགས་རང་བཞིན་སྔར་ལས་ལྷག་པར་བྱེད་ངེས།", "Copied": "འདྲ་བཤུས་བྱས་པ།", "Copied shared chat URL to clipboard!": "མཉམ་སྤྱོད་ཁ་བརྡའི་ URL སྦྱར་སྡེར་དུ་འདྲ་བཤུས་བྱས་ཟིན།", "Copied to clipboard": "སྦྱར་སྡེར་དུ་འདྲ་བཤུས་བྱས་པ།", "Copy": "འདྲ་བཤུས།", "Copy last code block": "ཀོཌ་གཏོགས་ཁོངས་མཐའ་མ་འདྲ་བཤུས།", "Copy last response": "ལན་མཐའ་མ་འདྲ་བཤུས།", "Copy Link": "སྦྲེལ་ཐག་འདྲ་བཤུས།", "Copy to clipboard": "སྦྱར་སྡེར་དུ་འདྲ་བཤུས།", "Copying to clipboard was successful!": "སྦྱར་སྡེར་དུ་འདྲ་བཤུས་བྱེད་པ་ལེགས་འགྲུབ་བྱུང་།", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "Open WebUI ནས་རེ་ཞུ་གཏོང་བར་གནང་བ་སྤྲོད་ཆེད། CORS ངེས་པར་དུ་མཁོ་སྤྲོད་པས་འགྲིག་པོར་སྒྲིག་འགོད་བྱེད་དགོས།", "Create": "གསར་བཟོ།", "Create a knowledge base": "ཤེས་བྱའི་རྟེན་གཞི་ཞིག་གསར་བཟོ་བྱེད་པ།", "Create a model": "དཔེ་དབྱིབས་ཤིག་གསར་བཟོ་བྱེད་པ།", "Create Account": "རྩིས་ཁྲ་གསར་བཟོ།", "Create Admin Account": "དོ་དམ་པའི་རྩིས་ཁྲ་གསར་བཟོ།", "Create Channel": "བགྲོ་གླེང་གསར་བཟོ།", "Create Group": "ཚོགས་པ་གསར་བཟོ།", "Create Knowledge": "ཤེས་བྱ་གསར་བཟོ།", "Create new key": "ལྡེ་མིག་གསར་པ་བཟོ་བ།", "Create new secret key": "གསང་བའི་ལྡེ་མིག་གསར་པ་བཟོ་བ།", "Created at": "གསར་བཟོ་བྱེད་དུས།", "Created At": "གསར་བཟོ་བྱེད་དུས།", "Created by": "གསར་བཟོ་བྱེད་མཁན།", "CSV Import": "CSV ནང་འདྲེན།", "Ctrl+Enter to Send": "Ctrl+Enter གཏོང་བ།", "Current Model": "ད་ལྟའི་དཔེ་དབྱིབས།", "Current Password": "ད་ལྟའི་གསང་གྲངས།", "Custom": "སྲོལ་བཟོས།", "Danger Zone": "ཉེན་ཁའི་ས་ཁུལ།", "Dark": "ནག་པོ།", "Database": "གནས་ཚུལ་མཛོད།", "December": "ཟླ་བ་བཅུ་གཉིས་པ།", "Default": "སྔོན་སྒྲིག", "Default (Open AI)": "སྔོན་སྒྲིག (Open AI)", "Default (SentenceTransformers)": "སྔོན་སྒྲིག (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model’s built-in tool-calling capabilities, but requires the model to inherently support this feature.": "སྔོན་སྒྲིག་མ་དཔེ་ནི་ལག་བསྟར་མ་བྱས་སྔོན་དུ་ལག་ཆ་ཐེངས་གཅིག་འབོད་ནས་དཔེ་དབྱིབས་རྒྱ་ཆེ་བའི་ཁྱབ་ཁོངས་དང་མཉམ་ལས་བྱེད་ཐུབ། ས་སྐྱེས་མ་དཔེ་ཡིས་དཔེ་དབྱིབས་ཀྱི་ནང་འདྲེས་ལག་ཆ་འབོད་པའི་ནུས་པ་སྤྱོད་ཀྱི་ཡོད་མོད། འོན་ཀྱང་དཔེ་དབྱིབས་དེས་ཁྱད་ཆོས་འདི་ལ་ངོ་བོའི་ཐོག་ནས་རྒྱབ་སྐྱོར་བྱེད་དགོས།", "Default Model": "སྔོན་སྒྲིག་དཔེ་དབྱིབས།", "Default model updated": "སྔོན་སྒྲིག་དཔེ་དབྱིབས་གསར་སྒྱུར་བྱས།", "Default Models": "སྔོན་སྒྲིག་དཔེ་དབྱིབས།", "Default permissions": "སྔོན་སྒྲིག་དབང་ཚད།", "Default permissions updated successfully": "སྔོན་སྒྲིག་དབང་ཚད་ལེགས་པར་གསར་སྒྱུར་བྱས།", "Default Prompt Suggestions": "སྔོན་སྒྲིག་འགུལ་སློང་གྲོས་གཞི།", "Default to 389 or 636 if TLS is enabled": "TLS སྒུལ་བསྐྱོད་བྱས་ན་སྔོན་སྒྲིག་ཏུ་ ༣༨༩ ཡང་ན་ ༦༣༦།", "Default to ALL": "སྔོན་སྒྲིག་ཏུ་ཡོངས་རྫོགས།", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "དམིགས་ཚད་དང་འབྲེལ་ཡོད་ནང་དོན་འདོན་སྤེལ་གྱི་ཆེད་དུ་སྔོན་སྒྲིག་ཏུ་དུམ་བུ་ལེན་ཚུར་སྒྲུབ་བྱེད་པ། འདི་ནི་གནས་སྟངས་མང་ཆེ་བའི་ཆེད་དུ་འོས་སྦྱོར་བྱེད།", "Default User Role": "སྔོན་སྒྲིག་བེད་སྤྱོད་མཁན་གྱི་གནས་ཚད།", "Delete": "བསུབ་པ།", "Delete a model": "དཔེ་དབྱིབས་ཤིག་བསུབ་པ།", "Delete All Chats": "ཁ་བརྡ་ཡོངས་རྫོགས་བསུབ་པ།", "Delete All Models": "དཔེ་དབྱིབས་ཡོངས་རྫོགས་བསུབ་པ།", "Delete chat": "ཁ་བརྡ་བསུབ་པ།", "Delete Chat": "ཁ་བརྡ་བསུབ་པ།", "Delete chat?": "ཁ་བརྡ་བསུབ་པ།?", "Delete folder?": "ཡིག་སྣོད་བསུབ་པ།?", "Delete function?": "ལས་འགན་བསུབ་པ།?", "Delete Message": "འཕྲིན་བསུབ་པ།", "Delete message?": "འཕྲིན་བསུབ་པ།?", "Delete prompt?": "འགུལ་སློང་བསུབ་པ།?", "delete this link": "སྦྲེལ་ཐག་འདི་བསུབ་པ།", "Delete tool?": "ལག་ཆ་བསུབ་པ།?", "Delete User": "བེད་སྤྱོད་མཁན་བསུབ་པ།", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} བསུབས་ཟིན།", "Deleted {{name}}": "{{name}} བསུབས་ཟིན།", "Deleted User": "བེད་སྤྱོད་མཁན་བསུབས་ཟིན།", "Describe your knowledge base and objectives": "ཁྱེད་ཀྱི་ཤེས་བྱའི་རྟེན་གཞི་དང་དམིགས་ཡུལ་འགྲེལ་བཤད་བྱེད་པ།", "Description": "འགྲེལ་བཤད།", "Didn't fully follow instructions": "ལམ་སྟོན་ཡོངས་སུ་མ་བསྒྲུབས།", "Direct": "ཐད་ཀར།", "Direct Connections": "ཐད་ཀར་སྦྲེལ་མཐུད།", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "ཐད་ཀར་སྦྲེལ་མཐུད་ཀྱིས་བེད་སྤྱོད་མཁན་ཚོར་ཁོ་ཚོའི་རང་གི་ OpenAI དང་མཐུན་པའི་ API མཇུག་མཐུད་ལ་སྦྲེལ་བར་གནང་བ་སྤྲོད།", "Direct Connections settings updated": "ཐད་ཀར་སྦྲེལ་མཐུད་ཀྱི་སྒྲིག་འགོད་གསར་སྒྱུར་བྱས།", "Direct Tool Servers": "", "Disabled": "ནུས་མེད།", "Discover a function": "ལས་འགན་ཞིག་རྙེད་པ།", "Discover a model": "དཔེ་དབྱིབས་ཤིག་རྙེད་པ།", "Discover a prompt": "འགུལ་སློང་ཞིག་རྙེད་པ།", "Discover a tool": "ལག་ཆ་ཞིག་རྙེད་པ།", "Discover how to use Open WebUI and seek support from the community.": "Open WebUI ཇི་ལྟར་བེད་སྤྱོད་གཏོང་ཚུལ་རྙེད་པ་དང་སྤྱི་ཚོགས་ནས་རྒྱབ་སྐྱོར་འཚོལ་བ།", "Discover wonders": "ངོ་མཚར་རྙེད་པ།", "Discover, download, and explore custom functions": "སྲོལ་བཟོས་ལས་འགན་རྙེད་པ། ཕབ་ལེན་བྱེད་པ། དང་བརྟག་ཞིབ་བྱེད་པ།", "Discover, download, and explore custom prompts": "སྲོལ་བཟོས་འགུལ་སློང་རྙེད་པ། ཕབ་ལེན་བྱེད་པ། དང་བརྟག་ཞིབ་བྱེད་པ།", "Discover, download, and explore custom tools": "སྲོལ་བཟོས་ལག་ཆ་རྙེད་པ། ཕབ་ལེན་བྱེད་པ། དང་བརྟག་ཞིབ་བྱེད་པ།", "Discover, download, and explore model presets": "དཔེ་དབྱིབས་སྔོན་སྒྲིག་རྙེད་པ། ཕབ་ལེན་བྱེད་པ། དང་བརྟག་ཞིབ་བྱེད་པ།", "Dismissible": "ཕྱིར་འཐེན་རུང་བ།", "Display": "འཆར་སྟོན།", "Display Emoji in Call": "སྐད་འབོད་ནང་ Emoji འཆར་སྟོན་བྱེད་པ།", "Display the username instead of You in the Chat": "ཁ་བརྡའི་ནང་ 'ཁྱེད་' ཀྱི་ཚབ་ཏུ་བེད་སྤྱོད་མིང་འཆར་སྟོན་བྱེད་པ།", "Displays citations in the response": "ལན་ནང་ལུང་འདྲེན་འཆར་སྟོན་བྱེད་པ།", "Dive into knowledge": "ཤེས་བྱའི་ནང་འཛུལ་བ།", "Do not install functions from sources you do not fully trust.": "ཁྱེད་ཀྱིས་ཡིད་ཆེས་ཆ་ཚང་མེད་པའི་འབྱུང་ཁུངས་ནས་ལས་འགན་སྒྲིག་སྦྱོར་མ་བྱེད།", "Do not install tools from sources you do not fully trust.": "ཁྱེད་ཀྱིས་ཡིད་ཆེས་ཆ་ཚང་མེད་པའི་འབྱུང་ཁུངས་ནས་ལག་ཆ་སྒྲིག་སྦྱོར་མ་བྱེད།", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "Docling སར་བར་གྱི་ URL དགོས་ངེས།", "Document": "ཡིག་ཆ།", "Document Intelligence": "ཡིག་ཆའི་རིག་ནུས།", "Document Intelligence endpoint and key required.": "ཡིག་ཆའི་རིག་ནུས་མཇུག་མཐུད་དང་ལྡེ་མིག་དགོས་ངེས།", "Documentation": "ཡིག་ཆ།", "Documents": "ཡིག་ཆ།", "does not make any external connections, and your data stays securely on your locally hosted server.": "ཕྱི་རོལ་གྱི་སྦྲེལ་མཐུད་གང་ཡང་མི་བྱེད། དེ་མིན་ཁྱེད་ཀྱི་གནས་ཚུལ་དེ་ཁྱེད་ཀྱི་ས་གནས་སུ་བཀོད་སྒྲིག་བྱས་པའི་སར་བར་སྟེང་བདེ་འཇགས་ངང་གནས་ངེས།", "Domain Filter List": "ཁྱབ་ཁོངས་འཚག་མའི་ཐོ་གཞུང་།", "Don't have an account?": "རྩིས་ཁྲ་མེད་དམ།", "don't install random functions from sources you don't trust.": "ཁྱེད་ཀྱིས་ཡིད་ཆེས་མེད་པའི་འབྱུང་ཁུངས་ནས་གང་བྱུང་གི་ལས་འགན་སྒྲིག་སྦྱོར་མ་བྱེད།", "don't install random tools from sources you don't trust.": "ཁྱེད་ཀྱིས་ཡིད་ཆེས་མེད་པའི་འབྱུང་ཁུངས་ནས་གང་བྱུང་གི་ལག་ཆ་སྒྲིག་སྦྱོར་མ་བྱེད།", "Don't like the style": "བཟོ་ལྟ་ལ་མི་དགའ།", "Done": "ཚར་སོང་།", "Download": "ཕབ་ལེན།", "Download as SVG": "SVG ཐོག་ཕབ་ལེན།", "Download canceled": "ཕབ་ལེན་རྩིས་མེད་བཏང་།", "Download Database": "གནས་ཚུལ་མཛོད་ཕབ་ལེན།", "Drag and drop a file to upload or select a file to view": "ཡིག་ཆ་ཞིག་འདྲུད་ནས་འཇོག་སྟེ་སྤར་བའམ། ཡང་ན་ཡིག་ཆ་ཞིག་གདམ་ག་བྱས་ནས་ལྟ་བ།", "Draw": "རི་མོ་འབྲི་བ།", "Drop any files here to add to the conversation": "ཡིག་ཆ་གང་རུང་འདིར་བཞག་ནས་ཁ་བརྡར་སྣོན་པ།", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "དཔེར་ན། '30s','10m'. ནུས་ལྡན་དུས་ཚོད་ཀྱི་ཚན་པ་ནི། 's', 'm', 'h' ཡིན།", "e.g. \"json\" or a JSON schema": "དཔེར་ན། \"json\" ཡང་ན་ JSON གི་ schema", "e.g. 60": "དཔེར་ན། ༦༠", "e.g. A filter to remove profanity from text": "དཔེར་ན། ཡིག་རྐྱང་ནས་ཚིག་རྩུབ་འདོར་བྱེད་ཀྱི་འཚག་མ།", "e.g. My Filter": "དཔེར་ན། ངའི་འཚག་མ།", "e.g. My Tools": "དཔེར་ན། ངའི་ལག་ཆ།", "e.g. my_filter": "དཔེར་ན། my_filter", "e.g. my_tools": "དཔེར་ན། my_tools", "e.g. Tools for performing various operations": "དཔེར་ན། ལས་ཀ་སྣ་ཚོགས་སྒྲུབ་བྱེད་ཀྱི་ལག་ཆ།", "Edit": "ཞུ་དག", "Edit Arena Model": "Arena དཔེ་དབྱིབས་ཞུ་དག", "Edit Channel": "བགྲོ་གླེང་ཞུ་དག", "Edit Connection": "སྦྲེལ་མཐུད་ཞུ་དག", "Edit Default Permissions": "སྔོན་སྒྲིག་དབང་ཚད་ཞུ་དག", "Edit Memory": "དྲན་ཤེས་ཞུ་དག", "Edit User": "བེད་སྤྱོད་མཁན་ཞུ་དག", "Edit User Group": "བེད་སྤྱོད་མཁན་ཚོགས་པ་ཞུ་དག", "ElevenLabs": "ElevenLabs", "Email": "ཡིག་ཟམ།", "Embark on adventures": "ཉེན་བརྟུལ་གྱི་འགྲུལ་བཞུད་ལ་འཇུག་པ།", "Embedding": "ཚུད་འཇུག", "Embedding Batch Size": "ཚུད་འཇུག་ཚན་ཆུང་གི་ཆེ་ཆུང་།", "Embedding Model": "ཚུད་འཇུག་དཔེ་དབྱིབས།", "Embedding Model Engine": "ཚུད་འཇུག་དཔེ་དབྱིབས་འཕྲུལ་འཁོར།", "Embedding model set to \"{{embedding_model}}\"": "ཚུད་འཇུག་དཔེ་དབྱིབས་ \"{{embedding_model}}\" ལ་བཀོད་སྒྲིག་བྱས།", "Enable API Key": "API ལྡེ་མིག་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable autocomplete generation for chat messages": "ཁ་བརྡའི་འཕྲིན་ཡིག་གི་ཆེད་དུ་རང་འཚང་བཟོ་སྐྲུན་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable Code Execution": "ཀོཌ་ལག་བསྟར་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable Code Interpreter": "ཀོཌ་འགྲེལ་བཤད་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable Community Sharing": "སྤྱི་ཚོགས་མཉམ་སྤྱོད་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "དཔེ་དབྱིབས་ཀྱི་གནས་ཚུལ་ RAM ནས་ཕྱིར་བརྗེ་བར་སྔོན་འགོག་བྱེད་པའི་ཆེད་དུ་དྲན་ཤེས་ཟྭ་རྒྱག་ (mlock) སྒུལ་བསྐྱོད་བྱེད་པ། འདེམས་ཀ་འདིས་དཔེ་དབྱིབས་ཀྱི་ལས་ཀའི་ཤོག་ངོས་ཚོགས་སྡེ་ RAM ནང་ཟྭ་རྒྱག་སྟེ། དེ་དག་ཌིསཀ་ལ་ཕྱིར་བརྗེ་མི་འགྲོ་བ་འགན་ལེན་བྱེད། འདིས་ཤོག་ངོས་ནོར་འཁྲུལ་ལས་གཡོལ་བ་དང་གནས་ཚུལ་མྱུར་པོར་འཛུལ་སྤྱོད་ཐུབ་པའི་འགན་ལེན་བྱས་ནས་ལས་ཆོད་རྒྱུན་སྲུང་བྱེད་པར་རོགས་པ་བྱེད་ཐུབ།", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "དཔེ་དབྱིབས་ཀྱི་གནས་ཚུལ་ནང་འཇུག་བྱེད་པའི་ཆེད་དུ་དྲན་ཤེས་ས་ཁྲ་འགོད་པ་ (mmap) སྒུལ་བསྐྱོད་བྱེད་པ། འདེམས་ཀ་འདིས་མ་ལག་ལ་ཌིསཀ་ཡིག་ཆ་ RAM ནང་ཡོད་པ་ལྟར་བརྩིས་ནས། ཌིསཀ་གསོག་ཆས་ RAM གྱི་རྒྱ་བསྐྱེད་དུ་བེད་སྤྱོད་གཏོང་བའི་གནང་བ་སྤྲོད། འདིས་གནས་ཚུལ་མྱུར་པོར་འཛུལ་སྤྱོད་ཆོག་པར་བཏང་ནས་དཔེ་དབྱིབས་ཀྱི་ལས་ཆོད་ལེགས་སུ་གཏོང་ཐུབ། འོན་ཀྱང་། འདི་མ་ལག་ཡོངས་ལ་ཡང་དག་པར་ལས་ཀ་བྱེད་མི་སྲིད། དེ་མིན་ཌིསཀ་གི་བར་སྟོང་མང་པོ་ཟ་སྲིད།", "Enable Message Rating": "འཕྲིན་ལ་སྐར་མ་སྤྲོད་པ་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable Mirostat sampling for controlling perplexity.": "རྙོག་འཛིང་ཚད་ཚོད་འཛིན་གྱི་ཆེད་དུ་ Mirostat མ་དཔེ་འདེམས་པ་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enable New Sign Ups": "ཐོ་འགོད་གསར་པ་སྒུལ་བསྐྱོད་བྱེད་པ།", "Enabled": "སྒུལ་བསྐྱོད་བྱས་ཡོད།", "Enforce Temporary Chat": "གནས་སྐབས་ཁ་བརྡ་བཙན་བཀོལ་བྱེད་པ།", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "ཁྱེད་ཀྱི་ CSV ཡིག་ཆར་གོ་རིམ་འདི་ལྟར། མིང་། ཡིག་ཟམ། གསང་གྲངས། གནས་ཚད། སྟར་པ་ ༤ ཚུད་ཡོད་པ་ཁག་ཐེག་བྱེད་རོགས།", "Enter {{role}} message here": "{{role}} ཡི་འཕྲིན་འདིར་འཇུག་པ།", "Enter a detail about yourself for your LLMs to recall": "ཁྱེད་ཀྱི་ LLMs ཡིས་ཕྱིར་དྲན་ཆེད་དུ་ཁྱེད་རང་གི་སྐོར་གྱི་ཞིབ་ཕྲ་ཞིག་འཇུག་པ།", "Enter api auth string (e.g. username:password)": "api auth ཡིག་ཕྲེང་འཇུག་པ། (དཔེར་ན། username:password)", "Enter Application DN": "Application DN འཇུག་པ།", "Enter Application DN Password": "Application DN གསང་གྲངས་འཇུག་པ།", "Enter Bing Search V7 Endpoint": "Bing Search V7 མཇུག་མཐུད་འཇུག་པ།", "Enter Bing Search V7 Subscription Key": "Bing Search V7 མངགས་ཉོ་ལྡེ་མིག་འཇུག་པ།", "Enter Bocha Search API Key": "Bocha Search API ལྡེ་མིག་འཇུག་པ།", "Enter Brave Search API Key": "Brave Search API ལྡེ་མིག་འཇུག་པ།", "Enter certificate path": "ལག་ཁྱེར་གྱི་ལམ་བུ་འཇུག་པ།", "Enter CFG Scale (e.g. 7.0)": "CFG ཆེ་ཆུང་འཇུག་པ། (དཔེར་ན། 7.0)", "Enter Chunk Overlap": "དུམ་བུ་བསྣོལ་བ་འཇུག་པ།", "Enter Chunk Size": "དུམ་བུའི་ཆེ་ཆུང་འཇུག་པ།", "Enter comma-seperated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "ཚེག་བསྐུངས་ཀྱིས་ལོགས་སུ་བཀར་བའི་ \"ཊོཀ་ཀེན།:ཕྱོགས་ཞེན་རིན་ཐང་།\" ཆ་འཇུག་པ། (དཔེར། 5432:100, 413:-100)", "Enter description": "འགྲེལ་བཤད་འཇུག་པ།", "Enter Docling Server URL": "Docling སར་བར་གྱི་ URL འཇུག་པ།", "Enter Document Intelligence Endpoint": "ཡིག་ཆའི་རིག་ནུས་མཇུག་མཐུད་འཇུག་པ།", "Enter Document Intelligence Key": "ཡིག་ཆའི་རིག་ནུས་ལྡེ་མིག་འཇུག་པ།", "Enter domains separated by commas (e.g., example.com,site.org)": "ཚེག་བསྐུངས་ཀྱིས་ལོགས་སུ་བཀར་བའི་ཁྱབ་ཁོངས་འཇུག་པ། (དཔེར་ན། example.com,site.org)", "Enter Exa API Key": "Exa API ལྡེ་མིག་འཇུག་པ།", "Enter Github Raw URL": "Github Raw URL འཇུག་པ།", "Enter Google PSE API Key": "Google PSE API ལྡེ་མིག་འཇུག་པ།", "Enter Google PSE Engine Id": "Google PSE Engine Id འཇུག་པ།", "Enter Image Size (e.g. 512x512)": "པར་གྱི་ཆེ་ཆུང་འཇུག་པ། (དཔེར་ན། 512x512)", "Enter Jina API Key": "Jina API ལྡེ་མིག་འཇུག་པ།", "Enter Jupyter Password": "Jupyter གསང་གྲངས་འཇུག་པ།", "Enter Jupyter Token": "<PERSON><PERSON><PERSON>ken འཇུག་པ།", "Enter Jupyter URL": "Jupyter URL འཇུག་པ།", "Enter Kagi Search API Key": "Kagi Search API ལྡེ་མིག་འཇུག་པ།", "Enter Key Behavior": "ལྡེ་མིག་གི་བྱེད་སྟངས་འཇུག་པ།", "Enter language codes": "སྐད་ཡིག་གི་ཨང་རྟགས་འཇུག་པ།", "Enter Mistral API Key": "", "Enter Model ID": "དཔེ་དབྱིབས་ཀྱི་ ID འཇུག་པ།", "Enter model tag (e.g. {{modelTag}})": "དཔེ་དབྱིབས་ཀྱི་རྟགས་འཇུག་པ། (དཔེར་ན། {{modelTag}})", "Enter Mojeek Search API Key": "Mojeek Search API ལྡེ་མིག་འཇུག་པ།", "Enter Number of Steps (e.g. 50)": "གོམ་གྲངས་འཇུག་པ། (དཔེར་ན། ༥༠)", "Enter Perplexity API Key": "Perplexity API ལྡེ་མིག་འཇུག་པ།", "Enter proxy URL (e.g. **************************:port)": "Proxy URL འཇུག་པ། (དཔེར་ན། **************************:port)", "Enter reasoning effort": "རྒྱུ་མཚན་འདྲེན་པའི་འབད་བརྩོན་འཇུག་པ།", "Enter Sampler (e.g. Euler a)": "Sampler འཇུག་པ། (དཔེར་ན། Euler a)", "Enter Scheduler (e.g. Karras)": "Scheduler འཇུག་པ། (དཔེར་ན། Karras)", "Enter Score": "སྐར་མ་འཇུག་པ།", "Enter SearchApi API Key": "SearchApi API ལྡེ་མིག་འཇུག་པ།", "Enter SearchApi Engine": "SearchApi Engine འཇུག་པ།", "Enter Searxng Query URL": "Searxng Query URL འཇུག་པ།", "Enter Seed": "Seed འཇུག་པ།", "Enter SerpApi API Key": "SerpApi API ལྡེ་མིག་འཇུག་པ།", "Enter SerpApi Engine": "SerpApi Engine འཇུག་པ།", "Enter Serper API Key": "Serper API ལྡེ་མིག་འཇུག་པ།", "Enter Serply API Key": "Serply API ལྡེ་མིག་འཇུག་པ།", "Enter Serpstack API Key": "Serpstack API ལྡེ་མིག་འཇུག་པ།", "Enter server host": "སར་བར་གྱི་ Host འཇུག་པ།", "Enter server label": "སར་བར་གྱི་བྱང་རྟགས་འཇུག་པ།", "Enter server port": "སར་བར་གྱི་ Port འཇུག་པ།", "Enter stop sequence": "མཚམས་འཇོག་རིམ་པ་འཇུག་པ།", "Enter system prompt": "མ་ལག་གི་འགུལ་སློང་འཇུག་པ།", "Enter system prompt here": "", "Enter Tavily API Key": "Tavily API ལྡེ་མིག་འཇུག་པ།", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "ཁྱེད་ཀྱི་ WebUI ཡི་སྤྱི་སྤྱོད་ URL འཇུག་པ། URL འདི་བརྡ་ཁྱབ་ནང་སྦྲེལ་ཐག་བཟོ་བར་བེད་སྤྱོད་བྱེད་ངེས།", "Enter Tika Server URL": "Tika Server URL འཇུག་པ།", "Enter timeout in seconds": "སྐར་ཆའི་ནང་དུས་ཚོད་བཀག་པ་འཇུག་པ།", "Enter to Send": "Enter གཏོང་བ།", "Enter Top K": "Top K འཇུག་པ།", "Enter Top K Reranker": "Top K Reranker འཇུག་པ།", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL འཇུག་པ། (དཔེར་ན། http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL འཇུག་པ། (དཔེར་ན། http://localhost:11434)", "Enter your current password": "ཁྱེད་ཀྱི་ད་ལྟའི་གསང་གྲངས་འཇུག་པ།", "Enter Your Email": "ཁྱེད་ཀྱི་ཡིག་ཟམ་འཇུག་པ།", "Enter Your Full Name": "ཁྱེད་ཀྱི་མིང་ཆ་ཚང་འཇུག་པ།", "Enter your message": "ཁྱེད་ཀྱི་འཕྲིན་འཇུག་པ།", "Enter your name": "", "Enter your new password": "ཁྱེད་ཀྱི་གསང་གྲངས་གསར་པ་འཇུག་པ།", "Enter Your Password": "ཁྱེད་ཀྱི་གསང་གྲངས་འཇུག་པ།", "Enter Your Role": "ཁྱེད་ཀྱི་གནས་ཚད་འཇུག་པ།", "Enter Your Username": "ཁྱེད་ཀྱི་བེད་སྤྱོད་མིང་འཇུག་པ།", "Enter your webhook URL": "ཁྱེད་ཀྱི་ Webhook URL འཇུག་པ།", "Error": "ནོར་འཁྲུལ།", "ERROR": "ནོར་འཁྲུལ།", "Error accessing Google Drive: {{error}}": "Google Drive འཛུལ་སྤྱོད་སྐབས་ནོར་འཁྲུལ།: {{error}}", "Error uploading file: {{error}}": "ཡིག་ཆ་སྤར་སྐབས་ནོར་འཁྲུལ།: {{error}}", "Evaluations": "གདེང་འཇོག", "Exa API Key": "Exa API ལྡེ་མིག", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "དཔེར་ན། (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "དཔེར་ན། ALL", "Example: mail": "དཔེར་ན། mail", "Example: ou=users,dc=foo,dc=example": "དཔེར་ན། ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "དཔེར་ན། sAMAccountName ཡང་ན་ uid ཡང་ན་ userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "ཁྱེད་ཀྱི་ཆོག་མཆན་ནང་གི་སྟེགས་གྲངས་ལས་བརྒལ་སོང་། སྟེགས་གྲངས་མང་དུ་གཏོང་བར་རོགས་སྐྱོར་དང་འབྲེལ་གཏུག་བྱེད་རོགས།", "Exclude": "ཕུད་པ།", "Execute code for analysis": "དབྱེ་ཞིབ་ཆེད་དུ་ཀོཌ་ལག་བསྟར་བྱེད་པ།", "Executing **{{NAME}}**...": "", "Expand": "རྒྱ་བསྐྱེད་པ།", "Experimental": "ཚོད་ལྟའི་རང་བཞིན།", "Explain": "འགྲེལ་བཤད།", "Explain this section to me in more detail": "ས་བཅད་འདི་ང་ལ་ཞིབ་ཕྲ་འགྲེལ་བཤད་བྱེད་རོགས།", "Explore the cosmos": "འཇིག་རྟེན་བརྟག་ཞིབ་བྱེད་པ།", "Export": "ཕྱིར་གཏོང་།", "Export All Archived Chats": "ཡིག་མཛོད་དུ་བཞག་པའི་ཁ་བརྡ་ཡོངས་རྫོགས་ཕྱིར་གཏོང་།", "Export All Chats (All Users)": "ཁ་བརྡ་ཡོངས་རྫོགས་ཕྱིར་གཏོང་། (བེད་སྤྱོད་མཁན་ཡོངས་)", "Export chat (.json)": "ཁ་བརྡ་ཕྱིར་གཏོང་ (.json)", "Export Chats": "ཁ་བརྡ་ཕྱིར་གཏོང་།", "Export Config to JSON File": "སྒྲིག་འགོད་ JSON ཡིག་ཆར་ཕྱིར་གཏོང་།", "Export Functions": "ལས་འགན་ཕྱིར་གཏོང་།", "Export Models": "དཔེ་དབྱིབས་ཕྱིར་གཏོང་།", "Export Presets": "སྔོན་སྒྲིག་ཕྱིར་གཏོང་།", "Export Prompts": "འགུལ་སློང་ཕྱིར་གཏོང་།", "Export to CSV": "CSV ལ་ཕྱིར་གཏོང་།", "Export Tools": "ལག་ཆ་ཕྱིར་གཏོང་།", "External": "ཕྱི་རོལ།", "External Models": "ཕྱི་རོལ་གྱི་དཔེ་དབྱིབས།", "Failed to add file.": "ཡིག་ཆ་སྣོན་པར་མ་ཐུབ།", "Failed to connect to {{URL}} OpenAPI tool server": "{{URL}} OpenAPI ལག་ཆའི་སར་བར་ལ་སྦྲེལ་མཐུད་བྱེད་མ་ཐུབ།", "Failed to create API Key.": "API ལྡེ་མིག་བཟོ་མ་ཐུབ།", "Failed to fetch models": "དཔེ་དབྱིབས་ལེན་པར་མ་ཐུབ།", "Failed to read clipboard contents": "སྦྱར་སྡེར་གྱི་ནང་དོན་ཀློག་མ་ཐུབ།", "Failed to save connections": "", "Failed to save models configuration": "དཔེ་དབྱིབས་སྒྲིག་འགོད་ཉར་ཚགས་བྱེད་མ་ཐུབ།", "Failed to update settings": "སྒྲིག་འགོད་གསར་སྒྱུར་བྱེད་མ་ཐུབ།", "Failed to upload file.": "ཡིག་ཆ་སྤར་མ་ཐུབ།", "Features": "ཁྱད་ཆོས།", "Features Permissions": "ཁྱད་ཆོས་ཀྱི་དབང་ཚད།", "February": "ཟླ་བ་གཉིས་པ།", "Feedback History": "བསམ་འཆར་གྱི་ལོ་རྒྱུས།", "Feedbacks": "བསམ་འཆར།", "Feel free to add specific details": "ཞིབ་ཕྲ་ངེས་ཅན་སྣོན་པར་སེམས་ཁྲལ་མེད།", "File": "ཡིག་ཆ།", "File added successfully.": "ཡིག་ཆ་ལེགས་པར་བསྣན་ཟིན།", "File content updated successfully.": "ཡིག་ཆའི་ནང་དོན་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "File Mode": "ཡིག་ཆའི་མ་དཔེ།", "File not found.": "ཡིག་ཆ་མ་རྙེད།", "File removed successfully.": "ཡིག་ཆ་ལེགས་པར་བསུབས་ཟིན།", "File size should not exceed {{maxSize}} MB.": "ཡིག་ཆའི་ཆེ་ཆུང་ {{maxSize}} MB ལས་མི་བརྒལ་དགོས།", "File uploaded successfully": "ཡིག་ཆ་ལེགས་པར་སྤར་ཟིན།", "Files": "ཡིག་ཆ།", "Filter is now globally disabled": "འཚག་མ་དེ་ད་ལྟ་འཛམ་གླིང་ཡོངས་ནས་ནུས་མེད་བཏང་ཡོད།", "Filter is now globally enabled": "འཚག་མ་དེ་ད་ལྟ་འཛམ་གླིང་ཡོངས་ནས་སྒུལ་བསྐྱོད་བྱས་ཡོད།", "Filters": "འཚག་མ།", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "མཛུབ་ཐེལ་རྫུན་བཟོ་རྙེད་སོང་།: མིང་གི་ཡིག་འབྲུ་མགོ་མ་སྐུ་ཚབ་ཏུ་བེད་སྤྱོད་གཏོང་མི་ཐུབ། སྔོན་སྒྲིག་ཕྱི་ཐག་པར་རིས་ལ་སྔོན་སྒྲིག་བྱེད་བཞིན་པ།", "Fluidly stream large external response chunks": "ཕྱི་རོལ་གྱི་ལན་གྱི་དུམ་བུ་ཆེན་པོ་རྒྱུན་བཞིན་རྒྱུག་པ།", "Focus chat input": "ཁ་བརྡའི་ནང་འཇུག་ལ་དམིགས་པ།", "Folder deleted successfully": "ཡིག་སྣོད་ལེགས་པར་བསུབས་ཟིན།", "Folder name cannot be empty": "ཡིག་སྣོད་ཀྱི་མིང་སྟོང་པ་ཡིན་མི་ཆོག", "Folder name cannot be empty.": "ཡིག་སྣོད་ཀྱི་མིང་སྟོང་པ་ཡིན་མི་ཆོག", "Folder name updated successfully": "ཡིག་སྣོད་ཀྱི་མིང་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "Followed instructions perfectly": "ལམ་སྟོན་ཡང་དག་པར་བསྒྲུབས།", "Forge new paths": "ལམ་བུ་གསར་པ་བཟོ་བ།", "Form": "རེའུ་མིག", "Format your variables using brackets like this:": "ཁྱེད་ཀྱི་འགྱུར་ཚད་དེ་འདི་ལྟར་གུག་རྟགས་བེད་སྤྱོད་ནས་བཀོད་སྒྲིག་བྱེད་པ།:", "Forwards system user session credentials to authenticate": "", "Frequency Penalty": "ཡང་ཟློས་ཀྱི་ཆད་པ།", "Full Context Mode": "ནང་དོན་ཆ་ཚང་མ་དཔེ།", "Function": "ལས་འགན།", "Function Calling": "ལས་འགན་འབོད་པ།", "Function created successfully": "ལས་འགན་ལེགས་པར་བཟོས་ཟིན།", "Function deleted successfully": "ལས་འགན་ལེགས་པར་བསུབས་ཟིན།", "Function Description": "ལས་འགན་གྱི་འགྲེལ་བཤད།", "Function ID": "ལས་འགན་གྱི་ ID", "Function is now globally disabled": "ལས་འགན་དེ་ད་ལྟ་འཛམ་གླིང་ཡོངས་ནས་ནུས་མེད་བཏང་ཡོད།", "Function is now globally enabled": "ལས་འགན་དེ་ད་ལྟ་འཛམ་གླིང་ཡོངས་ནས་སྒུལ་བསྐྱོད་བྱས་ཡོད།", "Function Name": "ལས་འགན་གྱི་མིང་།", "Function updated successfully": "ལས་འགན་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "Functions": "ལས་འགན།", "Functions allow arbitrary code execution": "ལས་འགན་གྱིས་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་ལ་གནང་བ་སྤྲོད།", "Functions allow arbitrary code execution.": "ལས་འགན་གྱིས་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་ལ་གནང་བ་སྤྲོད།", "Functions imported successfully": "ལས་འགན་ལེགས་པར་ནང་འདྲེན་བྱས།", "Gemini": "Gemini", "Gemini API Config": "Gemini API Config", "Gemini API Key is required.": "Gemini API ལྡེ་མིག་དགོས་ངེས།", "General": "སྤྱིར་བཏང་།", "Generate an image": "པར་ཞིག་བཟོ་བ།", "Generate Image": "པར་བཟོ་བ།", "Generate prompt pair": "འགུལ་སློང་ཆ་ཞིག་བཟོ་བ།", "Generating search query": "འཚོལ་བཤེར་འདྲི་བ་བཟོ་བཞིན་པ།", "Get started": "འགོ་འཛུགས།", "Get started with {{WEBUI_NAME}}": "{{WEBUI_NAME}} དང་མཉམ་དུ་འགོ་འཛུགས་པ།", "Global": "འཛམ་གླིང་ཡོངས་ཀྱི་", "Good Response": "ལན་ཡག་པོ།", "Google Drive": "Google Drive", "Google PSE API Key": "Google PSE API ལྡེ་མིག", "Google PSE Engine Id": "Google PSE Engine Id", "Group created successfully": "ཚོགས་པ་ལེགས་པར་བཟོས་ཟིན།", "Group deleted successfully": "ཚོགས་པ་ལེགས་པར་བསུབས་ཟིན།", "Group Description": "ཚོགས་པའི་འགྲེལ་བཤད།", "Group Name": "ཚོགས་པའི་མིང་།", "Group updated successfully": "ཚོགས་པ་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "Groups": "ཚོགས་པ།", "Haptic Feedback": "འདར་འཕྲུལ་གྱི་བསམ་འཆར།", "has no conversations.": "ལ་ཁ་བརྡ་མེད།", "Hello, {{name}}": "བཀྲ་ཤིས་བདེ་ལེགས། {{name}}", "Help": "རོགས་རམ།", "Help us create the best community leaderboard by sharing your feedback history!": "ཁྱེད་ཀྱི་བསམ་འཆར་ལོ་རྒྱུས་མཉམ་སྤྱོད་བྱས་ནས་ང་ཚོས་སྤྱི་ཚོགས་ཀྱི་འགྲན་རེས་རེའུ་མིག་ཡག་ཤོས་བཟོ་བར་རོགས་པ་བྱེད་རོགས།", "Hex Color": "Hex ཚོན་མདོག", "Hex Color - Leave empty for default color": "Hex ཚོན་མདོག - སྔོན་སྒྲིག་ཚོན་མདོག་གི་ཆེད་དུ་སྟོང་པ་བཞག་པ།", "Hide": "སྦ་བ།", "Hide Model": "དཔེ་དབྱིབས་སྦ་བ།", "Home": "གཙོ་ངོས།", "Host": "Host", "How can I help you today?": "དེ་རིང་ངས་ཁྱེད་ལ་རོགས་པ་ཅི་ཞིག་བྱེད་ཐུབ་བམ།", "How would you rate this response?": "ལན་འདི་ལ་ཁྱེད་ཀྱིས་སྐར་མ་ག་ཚོད་སྤྲོད་འདོད་དམ།", "Hybrid Search": "Hybrid འཚོལ་བཤེར།", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "ངས་ངའི་བྱ་སྤྱོད་ཀྱི་ཤུགས་རྐྱེན་ཀློག་པ་དང་གོ་རྟོགས་སྤྲད་ཡོད་པ་ཁས་ལེན་བྱེད། ངས་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་དང་འབྲེལ་བའི་ཉེན་ཁ་ཤེས་ཀྱི་ཡོད། དེ་མིན་ངས་འབྱུང་ཁུངས་ཀྱི་ཡིད་རྟོན་རུང་བའི་རང་བཞིན་ར་སྤྲོད་བྱས་ཡོད།", "ID": "ID", "Ignite curiosity": "ཤེས་འདོད་སློང་བ།", "Image": "པར།", "Image Compression": "པར་བསྡུ་སྐུམ།", "Image Generation": "པར་བཟོ།", "Image Generation (Experimental)": "པར་བཟོ། (ཚོད་ལྟའི་རང་བཞིན།)", "Image Generation Engine": "པར་བཟོ་འཕྲུལ་འཁོར།", "Image Max Compression Size": "པར་གྱི་བསྡུ་སྐུམ་ཆེ་ཤོས།", "Image Prompt Generation": "པར་འགུལ་སློང་བཟོ་སྐྲུན།", "Image Prompt Generation Prompt": "པར་འགུལ་སློང་བཟོ་སྐྲུན་གྱི་འགུལ་སློང་།", "Image Settings": "པར་གྱི་སྒྲིག་འགོད།", "Images": "པར།", "Import Chats": "ཁ་བརྡ་ནང་འདྲེན།", "Import Config from JSON File": "JSON ཡིག་ཆ་ནས་སྒྲིག་འགོད་ནང་འདྲེན།", "Import Functions": "ལས་འགན་ནང་འདྲེན།", "Import Models": "དཔེ་དབྱིབས་ནང་འདྲེན།", "Import Presets": "སྔོན་སྒྲིག་ནང་འདྲེན།", "Import Prompts": "འགུལ་སློང་ནང་འདྲེན།", "Import Tools": "ལག་ཆ་ནང་འདྲེན།", "Include": "ཚུད་པ།", "Include `--api-auth` flag when running stable-diffusion-webui": "stable-diffusion-webui ལག་བསྟར་བྱེད་སྐབས་ `--api-auth` དར་ཆ་ཚུད་པ།", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webui ལག་བསྟར་བྱེད་སྐབས་ `--api` དར་ཆ་ཚུད་པ།", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "བཟོས་པའི་ཡིག་རྐྱང་ནས་ཐོན་པའི་བསམ་འཆར་ལ་ཨང་རྩིས་ཀྱིས་ཇི་ཙམ་མགྱོགས་པོར་ལན་འདེབས་བྱེད་པར་ཤུགས་རྐྱེན་ཐེབས་པ། སྦྱོང་ཚད་དམའ་བས་ལེགས་སྒྲིག་དལ་བ་ཡོང་ངེས། དེ་བཞིན་དུ་སྦྱོང་ཚད་མཐོ་བས་ཨང་རྩིས་དེ་ལན་འདེབས་ཆེ་བ་བཟོ་ངེས།", "Info": "གནས་ཚུལ།", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "ནང་དོན་ཆ་ཚང་དེ་ནང་དོན་དུ་བཅུག་ནས་སྒྲུབ་རིམ་ཆ་ཚང་བྱེད་པ། འདི་ནི་འདྲི་བ་རྙོག་འཛིང་ཅན་གྱི་ཆེད་དུ་འོས་སྦྱོར་བྱེད།", "Input commands": "ནང་འཇུག་བཀའ་བརྡ།", "Install from Github URL": "Github URL ནས་སྒྲིག་སྦྱོར་བྱེད་པ།", "Instant Auto-Send After Voice Transcription": "སྐད་ཆ་ཡིག་འབེབས་བྱས་རྗེས་ལམ་སང་རང་འགུལ་གཏོང་བ།", "Integration": "མཉམ་འདྲེས།", "Interface": "ངོས་འཛིན།", "Invalid file format.": "ཡིག་ཆའི་བཀོད་པ་ནུས་མེད།", "Invalid JSON schema": "JSON schema ནུས་མེད།", "Invalid Tag": "རྟགས་ནུས་མེད།", "is typing...": "ཡིག་འབྲུ་རྒྱག་བཞིན་པ།...", "January": "ཟླ་བ་དང་པོ།", "Jina API Key": "Jina API ལྡེ་མིག", "join our Discord for help.": "རོགས་རམ་ཆེད་དུ་ང་ཚོའི་ Discord ལ་མཉམ་ཞུགས་བྱེད་པ།", "JSON": "JSON", "JSON Preview": "JSON སྔོན་ལྟ།", "July": "ཟླ་བ་བདུན་པ།", "June": "ཟླ་བ་དྲུག་པ།", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT དུས་ཚོད་རྫོགས་པ།", "JWT Token": "JWT Token", "Kagi Search API Key": "Kagi Search API ལྡེ་མིག", "Keep Alive": "གསོན་པོར་གནས་པ།", "Key": "ལྡེ་མིག", "Keyboard shortcuts": "མཐེབ་གནོན་མྱུར་ལམ།", "Knowledge": "ཤེས་བྱ།", "Knowledge Access": "ཤེས་བྱར་འཛུལ་སྤྱོད།", "Knowledge created successfully.": "ཤེས་བྱ་ལེགས་པར་བཟོས་ཟིན།", "Knowledge deleted successfully.": "ཤེས་བྱ་ལེགས་པར་བསུབས་ཟིན།", "Knowledge Public Sharing": "ཤེས་བྱ་སྤྱི་སྤྱོད་མཉམ་སྤྱོད།", "Knowledge reset successfully.": "ཤེས་བྱ་ལེགས་པར་སླར་སྒྲིག་བྱས་ཟིན།", "Knowledge updated successfully": "ཤེས་བྱ་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "Kokoro.js (Browser)": "Kokoro.js (བརྡ་འཚོལ་ཆས།)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "བྱང་རྟགས།", "Landing Page Mode": "འབབ་ཤོག་མ་དཔེ།", "Language": "སྐད་ཡིག", "Last Active": "མཐའ་མའི་ལས་བྱེད།", "Last Modified": "མཐའ་མའི་བཟོ་བཅོས།", "Last reply": "ལན་མཐའ་མ།", "LDAP": "LDAP", "LDAP server updated": "LDAP སར་བར་གསར་སྒྱུར་བྱས།", "Leaderboard": "འགྲན་རེས་རེའུ་མིག", "Learn more about OpenAPI tool servers.": "", "Leave empty for unlimited": "ཚད་མེད་པའི་ཆེད་དུ་སྟོང་པ་བཞག་པ།", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "\"{{url}}/api/tags\" མཇུག་མཐུད་ནས་དཔེ་དབྱིབས་ཡོངས་རྫོགས་ཚུད་པར་སྟོང་པ་བཞག་པ།", "Leave empty to include all models from \"{{url}}/models\" endpoint": "\"{{url}}/models\" མཇུག་མཐུད་ནས་དཔེ་དབྱིབས་ཡོངས་རྫོགས་ཚུད་པར་སྟོང་པ་བཞག་པ།", "Leave empty to include all models or select specific models": "དཔེ་དབྱིབས་ཡོངས་རྫོགས་ཚུད་པར་སྟོང་པ་བཞག་པའམ། ཡང་ན་དཔེ་དབྱིབས་ངེས་ཅན་གདམ་ག་བྱེད་པ།", "Leave empty to use the default prompt, or enter a custom prompt": "སྔོན་སྒྲིག་འགུལ་སློང་བེད་སྤྱོད་གཏོང་བར་སྟོང་པ་བཞག་པའམ། ཡང་ན་སྲོལ་བཟོས་འགུལ་སློང་འཇུག་པ།", "Leave model field empty to use the default model.": "སྔོན་སྒྲིག་དཔེ་དབྱིབས་བེད་སྤྱོད་གཏོང་བར་དཔེ་དབྱིབས་ཀྱི་ཁོངས་སྟོང་པ་བཞག་པ།", "License": "ཆོག་མཆན།", "Light": "དཀར་པོ།", "Listening...": "ཉན་བཞིན་པ།...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "LLMs ལ་ནོར་འཁྲུལ་ཡོང་སྲིད། གནས་ཚུལ་གལ་ཆེན་ར་སྤྲོད་བྱེད་རོགས།", "Loader": "ནང་འཇུག་བྱེད་པོ།", "Loading Kokoro.js...": "Kokoro.js ནང་འཇུག་བྱེད་བཞིན་པ།...", "Local": "ས་གནས།", "Local Models": "ས་གནས་ཀྱི་དཔེ་དབྱིབས།", "Location access not allowed": "གནས་ཡུལ་འཛུལ་སྤྱོད་ལ་གནང་བ་མ་སྤྲད།", "Logit Bias": "Logit ཕྱོགས་ཞེན།", "Lost": "བརླགས།", "LTR": "LTR", "Made by Open WebUI Community": "Open WebUI སྤྱི་ཚོགས་ཀྱིས་བཟོས།", "Make sure to enclose them with": "དེ་དག་འདིས་བསྐོར་བ་ཁག་ཐེག་བྱེད་པ།", "Make sure to export a workflow.json file as API format from ComfyUI.": "ComfyUI ནས་ workflow.json ཡིག་ཆ་ API བཀོད་པའི་ཐོག་ཕྱིར་གཏོང་བྱེད་པ་ཁག་ཐེག་བྱེད་པ།", "Manage": "དོ་དམ།", "Manage Direct Connections": "ཐད་ཀར་སྦྲེལ་མཐུད་དོ་དམ།", "Manage Models": "དཔེ་དབྱིབས་དོ་དམ།", "Manage Ollama": "Ollama དོ་དམ།", "Manage Ollama API Connections": "Ollama API སྦྲེལ་མཐུད་དོ་དམ།", "Manage OpenAI API Connections": "OpenAI API སྦྲེལ་མཐུད་དོ་དམ།", "Manage Pipelines": "རྒྱུ་ལམ་དོ་དམ།", "Manage Tool Servers": "ལག་ཆའི་སར་བར་དོ་དམ།", "March": "ཟླ་བ་གསུམ་པ།", "Max Tokens (num_predict)": "ཊོཀ་ཀེན་མང་ཤོས། (num_predict)", "Max Upload Count": "སྤར་བའི་གྲངས་མང་ཤོས།", "Max Upload Size": "སྤར་བའི་ཆེ་ཆུང་མང་ཤོས།", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "དཔེ་དབྱིབས་ ༣ ལས་མང་བ་མཉམ་དུ་ཕབ་ལེན་བྱེད་མི་ཐུབ། རྗེས་སུ་ཡང་བསྐྱར་ཚོད་ལྟ་བྱེད་རོགས།", "May": "ཟླ་བ་ལྔ་པ།", "Memories accessible by LLMs will be shown here.": "LLMs ཀྱིས་འཛུལ་སྤྱོད་ཐུབ་པའི་དྲན་ཤེས་དག་འདིར་སྟོན་ངེས།", "Memory": "དྲན་ཤེས།", "Memory added successfully": "དྲན་ཤེས་ལེགས་པར་བསྣན་ཟིན།", "Memory cleared successfully": "དྲན་ཤེས་ལེགས་པར་གཙང་སེལ་བྱས་ཟིན།", "Memory deleted successfully": "དྲན་ཤེས་ལེགས་པར་བསུབས་ཟིན།", "Memory updated successfully": "དྲན་ཤེས་ལེགས་པར་གསར་སྒྱུར་བྱས་ཟིན།", "Merge Responses": "ལན་ཟླ་སྒྲིལ།", "Message rating should be enabled to use this feature": "ཁྱད་ཆོས་འདི་བེད་སྤྱོད་གཏོང་བར་འཕྲིན་ལ་སྐར་མ་སྤྲོད་པ་སྒུལ་བསྐྱོད་བྱེད་དགོས།", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "ཁྱེད་ཀྱི་སྦྲེལ་ཐག་བཟོས་རྗེས་ཁྱེད་ཀྱིས་བསྐུར་བའི་འཕྲིན་དག་མཉམ་སྤྱོད་བྱེད་མི་འགྱུར། URL ཡོད་པའི་བེད་སྤྱོད་མཁན་ཚོས་མཉམ་སྤྱོད་ཁ་བརྡ་ལྟ་ཐུབ་ངེས།", "Min P": "P ཉུང་ཤོས།", "Minimum Score": "སྐར་མ་ཉུང་ཤོས།", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "དཔེ་དབྱིབས།", "Model '{{modelName}}' has been successfully downloaded.": "དཔེ་དབྱིབས། '{{modelName}}' ལེགས་པར་ཕབ་ལེན་བྱས་ཟིན།", "Model '{{modelTag}}' is already in queue for downloading.": "དཔེ་དབྱིབས། '{{modelTag}}' ཕབ་ལེན་གྱི་སྒུག་ཐོ་ནང་ཡོད་ཟིན།", "Model {{modelId}} not found": "དཔེ་དབྱིབས། {{modelId}} མ་རྙེད།", "Model {{modelName}} is not vision capable": "དཔེ་དབྱིབས། {{modelName}} ལ་མཐོང་ནུས་མེད།", "Model {{name}} is now {{status}}": "དཔེ་དབྱིབས། {{name}} ད་ལྟ་ {{status}} ཡིན།", "Model {{name}} is now hidden": "དཔེ་དབྱིབས། {{name}} ད་ལྟ་སྦས་ཡོད།", "Model {{name}} is now visible": "དཔེ་དབྱིབས། {{name}} ད་ལྟ་མཐོང་ཐུབ།", "Model accepts image inputs": "དཔེ་དབྱིབས་ཀྱིས་པར་གྱི་ནང་འཇུག་དང་ལེན་བྱེད།", "Model created successfully!": "དཔེ་དབྱིབས་ལེགས་པར་བཟོས་ཟིན།", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "དཔེ་དབྱིབས་ཀྱི་ཡིག་ཆ་མ་ལག་ལམ་བུ་རྙེད་སོང་། གསར་སྒྱུར་གྱི་ཆེད་དུ་དཔེ་དབྱིབས་ཀྱི་མིང་ཐུང་ངུ་དགོས། མུ་མཐུད་མི་ཐུབ།", "Model Filtering": "དཔེ་དབྱིབས་འཚག་མ།", "Model ID": "དཔེ་དབྱིབས་ཀྱི་ ID", "Model IDs": "དཔེ་དབྱིབས་ཀྱི་ IDs", "Model Name": "དཔེ་དབྱིབས་ཀྱི་མིང་།", "Model not selected": "དཔེ་དབྱིབས་གདམ་ག་མ་བྱས།", "Model Params": "དཔེ་དབྱིབས་ཀྱི་ཞུགས་གྲངས།", "Model Permissions": "དཔེ་དབྱིབས་ཀྱི་དབང་ཚད།", "Model updated successfully": "དཔེ་དབྱིབས་ལེགས་པར་གསར་སྒྱུར་བྱས།", "Modelfile Content": "Modelfile ནང་དོན།", "Models": "དཔེ་དབྱིབས།", "Models Access": "དཔེ་དབྱིབས་འཛུལ་སྤྱོད།", "Models configuration saved successfully": "དཔེ་དབྱིབས་སྒྲིག་འགོད་ལེགས་པར་ཉར་ཚགས་བྱས།", "Models Public Sharing": "དཔེ་དབྱིབས་སྤྱི་སྤྱོད་མཉམ་སྤྱོད།", "Mojeek Search API Key": "Mojeek Search API ལྡེ་མིག", "more": "མང་བ།", "More": "མང་བ།", "Name": "མིང་།", "Name your knowledge base": "ཁྱེད་ཀྱི་ཤེས་བྱའི་རྟེན་གཞི་ལ་མིང་ཐོགས།", "Native": "ས་སྐྱེས།", "New Chat": "ཁ་བརྡ་གསར་པ།", "New Folder": "ཡིག་སྣོད་གསར་པ།", "New Password": "གསང་གྲངས་གསར་པ།", "new-channel": "བགྲོ་གླེང་གསར་པ།", "No content found": "ནང་དོན་མ་རྙེད།", "No content to speak": "བཤད་རྒྱུའི་ནང་དོན་མེད།", "No distance available": "ཐག་རིང་ཚད་མེད།", "No feedbacks found": "བསམ་འཆར་མ་རྙེད།", "No file selected": "ཡིག་ཆ་གདམ་ག་མ་བྱས།", "No files found.": "ཡིག་ཆ་མ་རྙེད།", "No groups with access, add a group to grant access": "འཛུལ་སྤྱོད་ཡོད་པའི་ཚོགས་པ་མེད། འཛུལ་སྤྱོད་སྤྲོད་པར་ཚོགས་པ་ཞིག་སྣོན་པ།", "No HTML, CSS, or JavaScript content found.": "HTML, CSS, ཡང་ན་ JavaScript གི་ནང་དོན་མ་རྙེད།", "No inference engine with management support found": "དོ་དམ་རྒྱབ་སྐྱོར་ཡོད་པའི་དཔོག་རྩིས་འཕྲུལ་འཁོར་མ་རྙེད།", "No knowledge found": "ཤེས་བྱ་མ་རྙེད།", "No memories to clear": "གཙང་སེལ་བྱེད་རྒྱུའི་དྲན་ཤེས་མེད།", "No model IDs": "དཔེ་དབྱིབས་ཀྱི་ ID མེད།", "No models found": "དཔེ་དབྱིབས་མ་རྙེད།", "No models selected": "དཔེ་དབྱིབས་གདམ་ག་མ་བྱས།", "No results found": "འབྲས་བུ་མ་རྙེད།", "No search query generated": "འཚོལ་བཤེར་འདྲི་བ་བཟོས་མེད།", "No source available": "འབྱུང་ཁུངས་མེད།", "No users were found.": "བེད་སྤྱོད་མཁན་མ་རྙེད།", "No valves to update": "གསར་སྒྱུར་བྱེད་རྒྱུའི་ Valve མེད།", "None": "གཅིག་ཀྱང་མེད།", "Not factually correct": "དོན་དངོས་དང་མི་མཐུན།", "Not helpful": "ཕན་ཐོགས་མེད།", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "དོ་སྣང་།: གལ་ཏེ་ཁྱེད་ཀྱིས་སྐར་མ་ཉུང་ཤོས་ཤིག་བཀོད་སྒྲིག་བྱས་ན། འཚོལ་བཤེར་གྱིས་སྐར་མ་ཉུང་ཤོས་དེ་དང་མཉམ་པའམ་དེ་ལས་ཆེ་བའི་ཡིག་ཆ་ཁོ་ན་ཕྱིར་སློག་བྱེད་ངེས།", "Notes": "མཆན་བུ།", "Notification Sound": "བརྡ་ཁྱབ་ཀྱི་སྒྲ།", "Notification Webhook": "བརྡ་ཁྱབ་ཀྱི་ Webhook", "Notifications": "བརྡ་ཁྱབ།", "November": "ཟླ་བ་བཅུ་གཅིག་པ།", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "ཟླ་བ་བཅུ་པ།", "Off": "ཁ་རྒྱག་པ།", "Okay, Let's Go!": "འགྲིག་སོང་། འགྲོ།", "OLED Dark": "OLED ནག་པོ།", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API སྒྲིག་འགོད་གསར་སྒྱུར་བྱས།", "Ollama Version": "Ollama པར་གཞི།", "On": "ཁ་ཕྱེ་བ།", "OneDrive": "OneDrive", "Only alphanumeric characters and hyphens are allowed": "ཨང་ཀི་དང་དབྱིན་ཡིག་གི་ཡིག་འབྲུ་དང་སྦྲེལ་རྟགས་ཁོ་ན་ཆོག་པ།", "Only alphanumeric characters and hyphens are allowed in the command string.": "བཀའ་བརྡའི་ཡིག་ཕྲེང་ནང་ཨང་ཀི་དང་དབྱིན་ཡིག་གི་ཡིག་འབྲུ་དང་སྦྲེལ་རྟགས་ཁོ་ན་ཆོག་པ།", "Only collections can be edited, create a new knowledge base to edit/add documents.": "བསྡུ་གསོག་ཁོ་ན་ཞུ་དག་བྱེད་ཐུབ། ཡིག་ཆ་ཞུ་དག་/སྣོན་པར་ཤེས་བྱའི་རྟེན་གཞི་གསར་པ་ཞིག་བཟོ་བ།", "Only select users and groups with permission can access": "དབང་ཚད་ཡོད་པའི་བེད་སྤྱོད་མཁན་དང་ཚོགས་པ་གདམ་ག་བྱས་པ་ཁོ་ན་འཛུལ་སྤྱོད་ཐུབ།", "Oops! Looks like the URL is invalid. Please double-check and try again.": "ཨོའོ། URL དེ་ནུས་མེད་ཡིན་པ་འདྲ། ཡང་བསྐྱར་ཞིབ་དཔྱད་བྱས་ནས་ཚོད་ལྟ་བྱེད་རོགས།", "Oops! There are files still uploading. Please wait for the upload to complete.": "ཨོའོ། ད་དུང་སྤར་བཞིན་པའི་ཡིག་ཆ་ཡོད། སྤར་ཚར་བར་སྒུག་རོགས།", "Oops! There was an error in the previous response.": "ཨོའོ། ལན་སྔ་མར་ནོར་འཁྲུལ་ཞིག་བྱུང་སོང་།", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "ཨོའོ། ཁྱེད་ཀྱིས་རྒྱབ་སྐྱོར་མེད་པའི་ཐབས་ལམ་ཞིག་ (མདུན་ངོས་ཁོ་ན།) བེད་སྤྱོད་གཏོང་བཞིན་འདུག རྒྱབ་སྣེ་ནས་ WebUI མཁོ་སྤྲོད་བྱེད་རོགས།", "Open file": "ཡིག་ཆ་ཁ་ཕྱེ་བ།", "Open in full screen": "ཡོངས་གནས་ངོས་སུ་ཁ་ཕྱེ་བ།", "Open new chat": "ཁ་བརྡ་གསར་པ་ཁ་ཕྱེ་བ།", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Open WebUI ཡིས་ནང་ཁུལ་དུ་ faster-whisper བེད་སྤྱོད་བྱེད།", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI ཡིས་ SpeechT5 དང་ CMU Arctic གཏམ་བཤད་པའི་ཚུད་འཇུག་བེད་སྤྱོད་བྱེད།", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI པར་གཞི། (v{{OPEN_WEBUI_VERSION}}) དེ་དགོས་ངེས་ཀྱི་པར་གཞི་ (v{{REQUIRED_VERSION}}) ལས་དམའ་བ།", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API Config", "OpenAI API Key is required.": "OpenAI API ལྡེ་མིག་དགོས་ངེས།", "OpenAI API settings updated": "OpenAI API སྒྲིག་འགོད་གསར་སྒྱུར་བྱས།", "OpenAI URL/Key required.": "OpenAI URL/ལྡེ་མིག་དགོས་ངེས།", "openapi.json Path": "", "or": "ཡང་ན།", "Organize your users": "ཁྱེད་ཀྱི་བེད་སྤྱོད་མཁན་སྒྲིག་འཛུགས།", "Other": "གཞན།", "OUTPUT": "ཐོན་འབྲས།", "Output format": "ཐོན་འབྲས་ཀྱི་བཀོད་པ།", "Overview": "སྤྱི་མཐོང་།", "page": "ཤོག་ངོས།", "Password": "གསང་གྲངས།", "Paste Large Text as File": "ཡིག་རྐྱང་ཆེན་པོ་ཡིག་ཆ་ལྟར་སྦྱོར་བ།", "PDF document (.pdf)": "PDF ཡིག་ཆ། (.pdf)", "PDF Extract Images (OCR)": "PDF པར་འདོན་སྤེལ། (OCR)", "pending": "སྒུག་བཞིན་པ།", "Permission denied when accessing media devices": "བརྒྱུད་ལམ་སྒྲིག་ཆས་འཛུལ་སྤྱོད་སྐབས་དབང་ཚད་ཁས་མ་བླངས།", "Permission denied when accessing microphone": "སྐད་སྒྲ་འཛིན་ཆས་འཛུལ་སྤྱོད་སྐབས་དབང་ཚད་ཁས་མ་བླངས།", "Permission denied when accessing microphone: {{error}}": "སྐད་སྒྲ་འཛིན་ཆས་འཛུལ་སྤྱོད་སྐབས་དབང་ཚད་ཁས་མ་བླངས།: {{error}}", "Permissions": "དབང་ཚད།", "Perplexity API Key": "Perplexity API ལྡེ་མིག", "Personalization": "སྒེར་སྤྱོད་ཅན།", "Pin": "གདབ་པ།", "Pinned": "གདབ་ཟིན།", "Pioneer insights": "སྔོན་དཔག་རིག་ནུས།", "Pipeline deleted successfully": "རྒྱུ་ལམ་ལེགས་པར་བསུབས་ཟིན།", "Pipeline downloaded successfully": "རྒྱུ་ལམ་ལེགས་པར་ཕབ་ལེན་བྱས་ཟིན།", "Pipelines": "རྒྱུ་ལམ།", "Pipelines Not Detected": "རྒྱུ་ལམ་མ་རྙེད།", "Pipelines Valves": "རྒྱུ་ལམ་གྱི་ Valve", "Plain text (.txt)": "ཡིག་རྐྱང་རྐྱང་པ། (.txt)", "Playground": "རྩེད་ཐང་།", "Please carefully review the following warnings:": "གཤམ་གསལ་ཉེན་བརྡ་དག་ལ་ཞིབ་ཚགས་ངང་བལྟ་ཞིབ་བྱེད་རོགས།:", "Please do not close the settings page while loading the model.": "དཔེ་དབྱིབས་ནང་འཇུག་བྱེད་སྐབས་སྒྲིག་འགོད་ཤོག་ངོས་ཁ་མ་རྒྱག་རོགས།", "Please enter a prompt": "འགུལ་སློང་ཞིག་འཇུག་རོགས།", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "ཁོངས་ཡོངས་རྫོགས་ཁ་སྐོང་རོགས།", "Please select a model first.": "ཐོག་མར་དཔེ་དབྱིབས་ཤིག་གདམ་ག་བྱེད་རོགས།", "Please select a model.": "དཔེ་དབྱིབས་ཤིག་གདམ་ག་བྱེད་རོགས།", "Please select a reason": "རྒྱུ་མཚན་ཞིག་གདམ་ག་བྱེད་རོགས།", "Port": "Port", "Positive attitude": "ལྟ་སྟངས་དགེ་མཚན།", "Prefix ID": "སྔོན་སྦྱོར་ ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "སྔོན་སྦྱོར་ ID ནི་དཔེ་དབྱིབས་ཀྱི་ IDs ལ་སྔོན་སྦྱོར་ཞིག་སྣོན་ནས་སྦྲེལ་མཐུད་གཞན་དང་གདོང་ཐུག་ལས་གཡོལ་བར་བེད་སྤྱོད་བྱེད། - ནུས་མེད་བཏང་བར་སྟོང་པ་བཞག་པ།", "Presence Penalty": "ད་ཡོད་ཀྱི་ཆད་པ།", "Previous 30 days": "ཉིན་ ༣༠ སྔོན་མ།", "Previous 7 days": "ཉིན་ ༧ སྔོན་མ།", "Private": "སྒེར།", "Profile Image": "སྤྱི་ཐག་པར།", "Prompt": "འགུལ་སློང་།", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "འགུལ་སློང་ (དཔེར་ན། རོམ་མའི་གོང་མའི་རྒྱལ་ཁབ་སྐོར་གྱི་དགོད་བྲོ་བའི་དོན་དངོས་ཤིག་ང་ལ་ཤོད་པ།)", "Prompt Autocompletion": "འགུལ་སློང་རང་འཚང་།", "Prompt Content": "འགུལ་སློང་ནང་དོན།", "Prompt created successfully": "འགུལ་སློང་ལེགས་པར་བཟོས་ཟིན།", "Prompt suggestions": "འགུལ་སློང་གྲོས་གཞི།", "Prompt updated successfully": "འགུལ་སློང་ལེགས་པར་གསར་སྒྱུར་བྱས།", "Prompts": "འགུལ་སློང་།", "Prompts Access": "འགུལ་སློང་འཛུལ་སྤྱོད།", "Prompts Public Sharing": "འགུལ་སློང་སྤྱི་སྤྱོད་མཉམ་སྤྱོད།", "Public": "སྤྱི་སྤྱོད།", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com ནས་ \"{{searchValue}}\" འཐེན་པ།", "Pull a model from Ollama.com": "Ollama.com ནས་དཔེ་དབྱིབས་ཤིག་འཐེན་པ།", "Query Generation Prompt": "འདྲི་བ་བཟོ་སྐྲུན་གྱི་འགུལ་སློང་།", "RAG Template": "RAG མ་དཔེ།", "Rating": "སྐར་མ།", "Re-rank models by topic similarity": "བརྗོད་གཞི་འདྲ་མཚུངས་ལྟར་དཔེ་དབྱིབས་བསྐྱར་སྒྲིག་བྱེད་པ།", "Read": "ཀློག་པ།", "Read Aloud": "སྐད་གསལ་པོས་ཀློག་པ།", "Reasoning Effort": "རྒྱུ་མཚན་འདྲེན་པའི་འབད་བརྩོན།", "Record voice": "སྐད་སྒྲ་ཕབ་པ།", "Redirecting you to Open WebUI Community": "ཁྱེད་ Open WebUI སྤྱི་ཚོགས་ལ་ཁ་ཕྱོགས་སྒྱུར་བཞིན་པ།", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "དོན་མེད་བཟོ་བའི་ཆགས་ཚུལ་ཉུང་དུ་གཏོང་བ། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། ༡༠༠) ཡིས་ལན་སྣ་ཚོགས་ཆེ་བ་སྤྲོད་ངེས། དེ་བཞིན་དུ་རིན་ཐང་དམའ་བ་ (དཔེར་ན། ༡༠) ཡིས་སྲུང་འཛིན་ཆེ་བ་ཡོང་ངེས།", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "ཁྱེད་རང་ལ་ \"བེད་སྤྱོད་མཁན་\" ཞེས་འབོད་པ། (དཔེར་ན། \"བེད་སྤྱོད་མཁན་གྱིས་སི་པན་གྱི་སྐད་ཡིག་སྦྱོང་བཞིན་པ།\")", "References from": "ནས་ལུང་འདྲེན།", "Refused when it shouldn't have": "མི་དགོས་དུས་ཁས་མ་བླངས།", "Regenerate": "བསྐྱར་བཟོ།", "Release Notes": "འགྲེམས་སྤེལ་མཆན་བུ།", "Relevance": "འབྲེལ་ཡོད་རང་བཞིན།", "Remove": "འདོར་བ།", "Remove Model": "དཔེ་དབྱིབས་འདོར་བ།", "Rename": "མིང་བསྐྱར་འདོགས།", "Reorder Models": "དཔེ་དབྱིབས་བསྐྱར་སྒྲིག", "Repeat Last N": "N མཐའ་མ་བསྐྱར་ཟློས།", "Repeat Penalty (Ollama)": "བསྐྱར་ཟློས་ཀྱི་ཆད་པ། (Ollama)", "Reply in Thread": "བརྗོད་གཞིའི་ནང་ལན་འདེབས།", "Request Mode": "རེ་ཞུའི་མ་དཔེ།", "Reranking Model": "བསྐྱར་སྒྲིག་དཔེ་དབྱིབས།", "Reranking model disabled": "བསྐྱར་སྒྲིག་དཔེ་དབྱིབས་ནུས་མེད་བཏང་།", "Reranking model set to \"{{reranking_model}}\"": "བསྐྱར་སྒྲིག་དཔེ་དབྱིབས་ \"{{reranking_model}}\" ལ་བཀོད་སྒྲིག་བྱས།", "Reset": "སླར་སྒྲིག", "Reset All Models": "དཔེ་དབྱིབས་ཡོངས་རྫོགས་སླར་སྒྲིག", "Reset Upload Directory": "སྤར་བའི་ཐོ་འཚོལ་སླར་སྒྲིག", "Reset Vector Storage/Knowledge": "ཚད་བརྡའི་གསོག་ཆས།/ཤེས་བྱ་སླར་སྒྲིག", "Reset view": "མཐོང་སྣང་སླར་སྒྲིག", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "དྲ་ཚིགས་ཀྱི་དབང་ཚད་ཁས་མ་བླངས་པས་ལན་གྱི་བརྡ་ཁྱབ་སྒུལ་བསྐྱོད་བྱེད་མི་ཐུབ། དགོས་ངེས་ཀྱི་འཛུལ་སྤྱོད་སྤྲོད་པར་ཁྱེད་ཀྱི་བརྡ་འཚོལ་ཆས་ཀྱི་སྒྲིག་འགོད་ལ་ལྟ་རོགས།", "Response splitting": "ལན་བགོ་བ།", "Result": "འབྲས་བུ།", "Retrieval": "ལེན་ཚུར་སྒྲུབ།", "Retrieval Query Generation": "ལེན་ཚུར་སྒྲུབ་འདྲི་བ་བཟོ་སྐྲུན།", "Rich Text Input for Chat": "ཁ་བརྡའི་ཆེད་དུ་ཡིག་རྐྱང་ཕུན་སུམ་ཚོགས་པའི་ནང་འཇུག", "RK": "RK", "Role": "གནས་ཚད།", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "ལག་བསྟར།", "Running": "ལག་བསྟར་བྱེད་བཞིན་པ།", "Save": "ཉར་ཚགས།", "Save & Create": "ཉར་ཚགས་ & གསར་བཟོ།", "Save & Update": "ཉར་ཚགས་ & གསར་སྒྱུར།", "Save As Copy": "འདྲ་བཤུས་ཐོག་ཉར་ཚགས།", "Save Tag": "རྟགས་ཉར་ཚགས།", "Saved": "ཉར་ཚགས་བྱས།", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "ཁ་བརྡའི་ཟིན་ཐོ་ཐད་ཀར་ཁྱེད་ཀྱི་བརྡ་འཚོལ་ཆས་ཀྱི་གསོག་ཆས་སུ་ཉར་ཚགས་བྱེད་པར་ད་ནས་བཟུང་རྒྱབ་སྐྱོར་མེད། གཤམ་གྱི་མཐེབ་གནོན་མནན་ནས་ཁྱེད་ཀྱི་ཁ་བརྡའི་ཟིན་ཐོ་ཕབ་ལེན་དང་བསུབ་པར་དུས་ཚོད་ཅུང་ཟད་བླང་རོགས། སེམས་ཁྲལ་མེད། ཁྱེད་ཀྱིས་སྟབས་བདེ་པོར་ཁྱེད་ཀྱི་ཁ་བརྡའི་ཟིན་ཐོ་རྒྱབ་སྣེ་ལ་བསྐྱར་དུ་ནང་འདྲེན་བྱེད་ཐུབ།", "Scroll to bottom when switching between branches": "ཡན་ལག་བརྗེ་སྐབས་མཐིལ་དུ་འགྲིལ་བ།", "Search": "འཚོལ་བཤེར།", "Search a model": "དཔེ་དབྱིབས་ཤིག་འཚོལ་བ།", "Search Base": "འཚོལ་བཤེར་གཞི་རྩ།", "Search Chats": "ཁ་བརྡ་འཚོལ་བཤེར།", "Search Collection": "བསྡུ་གསོག་འཚོལ་བཤེར།", "Search Filters": "འཚོལ་བཤེར་འཚག་མ།", "search for tags": "རྟགས་འཚོལ་བ།", "Search Functions": "ལས་འགན་འཚོལ་བཤེར།", "Search Knowledge": "ཤེས་བྱ་འཚོལ་བཤེར།", "Search Models": "དཔེ་དབྱིབས་འཚོལ་བཤེར།", "Search options": "འཚོལ་བཤེར་འདེམས་ཀ", "Search Prompts": "འགུལ་སློང་འཚོལ་བཤེར།", "Search Result Count": "འཚོལ་བཤེར་འབྲས་བུའི་གྲངས།", "Search the internet": "དྲ་རྒྱ་འཚོལ་བཤེར།", "Search Tools": "ལག་ཆ་འཚོལ་བཤེར།", "SearchApi API Key": "SearchApi API ལྡེ་མིག", "SearchApi Engine": "SearchApi Engine", "Searched {{count}} sites": "དྲ་ཚིགས་ {{count}} འཚོལ་བཤེར་བྱས།", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" འཚོལ་བཞིན་པ།", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\" ཆེད་དུ་ཤེས་བྱ་འཚོལ་བཞིན་པ།", "Searxng Query URL": "Searxng Query URL", "See readme.md for instructions": "ལམ་སྟོན་ཆེད་དུ་ readme.md ལ་ལྟ་བ།", "See what's new": "གསར་པ་ཅི་ཡོད་ལྟ་བ།", "Seed": "Seed", "Select a base model": "གཞི་རྩའི་དཔེ་དབྱིབས་ཤིག་གདམ་པ།", "Select a engine": "འཕྲུལ་འཁོར་ཞིག་གདམ་པ།", "Select a function": "ལས་འགན་ཞིག་གདམ་པ།", "Select a group": "ཚོགས་པ་ཞིག་གདམ་པ།", "Select a model": "དཔེ་དབྱིབས་ཤིག་གདམ་པ།", "Select a pipeline": "རྒྱུ་ལམ་ཞིག་གདམ་པ།", "Select a pipeline url": "རྒྱུ་ལམ་གྱི་ url ཞིག་གདམ་པ།", "Select a tool": "ལག་ཆ་ཞིག་གདམ་པ།", "Select an auth method": "auth ཐབས་ལམ་ཞིག་གདམ་པ།", "Select an Ollama instance": "Ollama དཔེ་མཚོན་ཞིག་གདམ་པ།", "Select Engine": "འཕྲུལ་འཁོར་གདམ་པ།", "Select Knowledge": "ཤེས་བྱ་གདམ་པ།", "Select only one model to call": "འབོད་པར་དཔེ་དབྱིབས་གཅིག་ཁོ་ན་གདམ་པ།", "Selected model(s) do not support image inputs": "གདམ་ཟིན་པའི་དཔེ་དབྱིབས་(ཚོ)ས་པར་གྱི་ནང་འཇུག་ལ་རྒྱབ་སྐྱོར་མི་བྱེད།", "Semantic distance to query": "འདྲི་བའི་དོན་གྱི་ཐག་རིང་ཚད།", "Send": "གཏོང་བ།", "Send a Message": "འཕྲིན་ཞིག་གཏོང་བ།", "Send message": "འཕྲིན་གཏོང་བ།", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "རེ་ཞུའི་ནང་ `stream_options: { include_usage: true }` གཏོང་བ།\nབཀོད་སྒྲིག་བྱས་ཚེ། རྒྱབ་སྐྱོར་ཡོད་པའི་མཁོ་སྤྲོད་པས་ལན་ནང་ཊོཀ་ཀེན་བེད་སྤྱོད་ཀྱི་གནས་ཚུལ་ཕྱིར་སློག་བྱེད་ངེས།", "September": "ཟླ་བ་དགུ་པ།", "SerpApi API Key": "SerpApi API ལྡེ་མིག", "SerpApi Engine": "SerpApi Engine", "Serper API Key": "Serper API ལྡེ་མིག", "Serply API Key": "Serply API ལྡེ་མིག", "Serpstack API Key": "Serpstack API ལྡེ་མིག", "Server connection verified": "སར་བར་སྦྲེལ་མཐུད་ར་སྤྲོད་བྱས།", "Set as default": "སྔོན་སྒྲིག་ཏུ་འཇོག་པ།", "Set CFG Scale": "CFG ཆེ་ཆུང་འཇོག་པ།", "Set Default Model": "སྔོན་སྒྲིག་དཔེ་དབྱིབས་འཇོག་པ།", "Set embedding model": "ཚུད་འཇུག་དཔེ་དབྱིབས་འཇོག་པ།", "Set embedding model (e.g. {{model}})": "ཚུད་འཇུག་དཔེ་དབྱིབས་འཇོག་པ། (དཔེར་ན། {{model}})", "Set Image Size": "པར་གྱི་ཆེ་ཆུང་འཇོག་པ།", "Set reranking model (e.g. {{model}})": "བསྐྱར་སྒྲིག་དཔེ་དབྱིབས་འཇོག་པ། (དཔེར་ན། {{model}})", "Set Sampler": "Sampler འཇོག་པ།", "Set Scheduler": "Scheduler འཇོག་པ།", "Set Steps": "གོམ་གྲངས་འཇོག་པ།", "Set Task Model": "ལས་ཀའི་དཔེ་དབྱིབས་འཇོག་པ།", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "GPU ལ་ཕྱིར་འགོད་བྱེད་རྒྱུའི་བརྩེགས་གྲངས་འཇོག་པ། རིན་ཐང་འདི་མང་དུ་བཏང་ན་ GPU མགྱོགས་སྣོན་གྱི་ཆེད་དུ་ལེགས་སྒྱུར་བྱས་པའི་དཔེ་དབྱིབས་ཀྱི་ལས་ཆོད་མངོན་གསལ་དོད་པོས་ལེགས་སུ་གཏོང་ཐུབ། འོན་ཀྱང་དེས་གློག་ཤུགས་དང་ GPU ཡི་ཐོན་ཁུངས་མང་བ་ཟ་སྲིད།", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "རྩིས་རྒྱག་ལ་བེད་སྤྱོད་གཏོང་བའི་ལས་ཀའི་སྐུད་གྲངས་འཇོག་པ། འདེམས་ཀ་འདིས་ནང་ཡོང་རེ་ཞུ་དུས་མཉམ་དུ་སྒྲུབ་པར་སྐུད་གྲངས་ག་ཚོད་བེད་སྤྱོད་གཏོང་དགོས་ཚོད་འཛིན་བྱེད། རིན་ཐང་འདི་མང་དུ་བཏང་ན་མཉམ་ལས་མཐོ་བའི་ལས་འགན་འོག་ལས་ཆོད་ལེགས་སུ་གཏོང་ཐུབ། འོན་ཀྱང་དེས་ CPU ཡི་ཐོན་ཁུངས་མང་བ་ཟ་སྲིད།", "Set Voice": "སྐད་འཇོག་པ།", "Set whisper model": "whisper དཔེ་དབྱིབས་འཇོག་པ།", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "ཉུང་མཐར་ཐེངས་གཅིག་བྱུང་བའི་ཊོཀ་ཀེན་ལ་ངོས་མཉམ་ཕྱོགས་ཞེན་ཞིག་འཇོག་པ། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། 1.5) ཡིས་བསྐྱར་ཟློས་ལ་ཆད་པ་དྲག་པོ་གཏོང་ངེས། དེ་བཞིན་དུ་རིན་ཐང་དམའ་བ་ (དཔེར་ན། 0.9) ཡིས་གུ་ཡངས་ཆེ་བ་ཡོང་ངེས། 0 ལ་སླེབས་དུས། དེ་ནུས་མེད་བཏང་ཡོད།", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "ཊོཀ་ཀེན་དེ་ཐེངས་ག་ཚོད་བྱུང་ཡོད་པར་གཞིགས་ནས་བསྐྱར་ཟློས་ལ་ཆད་པ་གཏོང་བར་ཊོཀ་ཀེན་ལ་ཆེ་ཆུང་འཇོག་པའི་ཕྱོགས་ཞེན་ཞིག་འཇོག་པ། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། 1.5) ཡིས་བསྐྱར་ཟློས་ལ་ཆད་པ་དྲག་པོ་གཏོང་ངེས། དེ་བཞིན་དུ་རིན་ཐང་དམའ་བ་ (དཔེར་ན། 0.9) ཡིས་གུ་ཡངས་ཆེ་བ་ཡོང་ངེས། 0 ལ་སླེབས་དུས། དེ་ནུས་མེད་བཏང་ཡོད།", "Sets how far back for the model to look back to prevent repetition.": "བསྐྱར་ཟློས་སྔོན་འགོག་བྱེད་པའི་ཆེད་དུ་དཔེ་དབྱིབས་ཀྱིས་ཕྱིར་ག་ཚོད་ལྟ་དགོས་འཇོག་པ།", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "བཟོ་སྐྲུན་ལ་བེད་སྤྱོད་གཏོང་རྒྱུའི་གང་བྱུང་གྲངས་ཀྱི་ Seed འཇོག་པ། འདི་གྲངས་ངེས་ཅན་ཞིག་ལ་བཀོད་སྒྲིག་བྱས་ན་དཔེ་དབྱིབས་ཀྱིས་འགུལ་སློང་གཅིག་པར་ཡིག་རྐྱང་གཅིག་པ་བཟོ་ངེས།", "Sets the size of the context window used to generate the next token.": "ཊོཀ་ཀེན་རྗེས་མ་བཟོ་བར་བེད་སྤྱོད་གཏོང་བའི་ནང་དོན་སྒེའུ་ཁུང་གི་ཆེ་ཆུང་འཇོག་པ།", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "བེད་སྤྱོད་གཏོང་རྒྱུའི་མཚམས་འཇོག་རིམ་པ་འཇོག་པ། བཟོ་ལྟ་འདི་འཕྲད་དུས། LLM ཡིས་ཡིག་རྐྱང་བཟོ་བ་མཚམས་འཇོག་ནས་ཕྱིར་ལོག་བྱེད་ངེས། Modelfile ནང་མཚམས་འཇོག་ཞུགས་གྲངས་ལོགས་སུ་མང་པོ་གསལ་བཀོད་བྱས་ནས་མཚམས་འཇོག་བཟོ་ལྟ་མང་པོ་འཇོག་ཐུབ།", "Settings": "སྒྲིག་འགོད།", "Settings saved successfully!": "སྒྲིག་འགོད་ལེགས་པར་ཉར་ཚགས་བྱས།", "Share": "མཉམ་སྤྱོད།", "Share Chat": "ཁ་བརྡ་མཉམ་སྤྱོད།", "Share to Open WebUI Community": "Open WebUI སྤྱི་ཚོགས་ལ་མཉམ་སྤྱོད།", "Sharing Permissions": "མཉམ་སྤྱོད་དབང་ཚད།", "Show": "སྟོན་པ།", "Show \"What's New\" modal on login": "ནང་འཛུལ་སྐབས་ \"གསར་པ་ཅི་ཡོད\" modal སྟོན་པ།", "Show Admin Details in Account Pending Overlay": "རྩིས་ཁྲ་སྒུག་བཞིན་པའི་གཏོགས་ངོས་སུ་དོ་དམ་པའི་ཞིབ་ཕྲ་སྟོན་པ།", "Show Model": "དཔེ་དབྱིབས་སྟོན་པ།", "Show shortcuts": "མྱུར་ལམ་སྟོན་པ།", "Show your support!": "ཁྱེད་ཀྱི་རྒྱབ་སྐྱོར་སྟོན་པ།", "Showcased creativity": "གསར་གཏོད་ནུས་པ་ངོམ་པ།", "Sign in": "ནང་འཛུལ།", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} ལ་ནང་འཛུལ།", "Sign in to {{WEBUI_NAME}} with LDAP": "LDAP དང་མཉམ་དུ་ {{WEBUI_NAME}} ལ་ནང་འཛུལ།", "Sign Out": "ཕྱིར་ཐོན།", "Sign up": "ཐོ་འགོད།", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}} ལ་ཐོ་འགོད།", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} ལ་ནང་འཛུལ་བྱེད་བཞིན་པ།", "sk-1234": "sk-༡༢༣༤", "Source": "འབྱུང་ཁུངས།", "Speech Playback Speed": "གཏམ་བཤད་ཕྱིར་གཏོང་གི་མྱུར་ཚད།", "Speech recognition error: {{error}}": "གཏམ་བཤད་ངོས་འཛིན་ནོར་འཁྲུལ།: {{error}}", "Speech-to-Text Engine": "གཏམ་བཤད་ནས་ཡིག་རྐྱང་གི་འཕྲུལ་འཁོར།", "Stop": "མཚམས་འཇོག", "Stop Sequence": "མཚམས་འཇོག་རིམ་པ།", "Stream Chat Response": "ཁ་བརྡའི་ལན་རྒྱུག་པ།", "STT Model": "STT དཔེ་དབྱིབས།", "STT Settings": "STT སྒྲིག་འགོད།", "Subtitle (e.g. about the Roman Empire)": "ཁ་བྱང་ཕལ་པ། (དཔེར་ན། རོམ་མའི་གོང་མའི་རྒྱལ་ཁབ་སྐོར།)", "Success": "ལེགས་འགྲུབ།", "Successfully updated.": "ལེགས་པར་གསར་སྒྱུར་བྱས།", "Suggested": "གྲོས་གཞི།", "Support": "རྒྱབ་སྐྱོར།", "Support this plugin:": "plugin འདི་ལ་རྒྱབ་སྐྱོར་བྱེད་པ།:", "Sync directory": "ཐོ་འཚོལ་མཉམ་སྡེབ།", "System": "མ་ལག", "System Instructions": "མ་ལག་གི་ལམ་སྟོན།", "System Prompt": "མ་ལག་གི་འགུལ་སློང་།", "Tags": "རྟགས།", "Tags Generation": "རྟགས་བཟོ་སྐྲུན།", "Tags Generation Prompt": "རྟགས་བཟོ་སྐྲུན་གྱི་འགུལ་སློང་།", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "མཇུག་མ་རང་དབང་མ་དཔེ་འདེམས་པ་ནི་ཐོན་འབྲས་ནས་ཆགས་ཚུལ་དམའ་བའི་ཊོཀ་ཀེན་གྱི་ཤུགས་རྐྱེན་ཉུང་དུ་གཏོང་བར་བེད་སྤྱོད་བྱེད། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། 2.0) ཡིས་ཤུགས་རྐྱེན་དེ་སྔར་ལས་ཉུང་དུ་གཏོང་ངེས། དེ་བཞིན་དུ་རིན་ཐང་ 1.0 ཡིས་སྒྲིག་འགོད་འདི་ནུས་མེད་བཏང་ངེས།", "Talk to model": "དཔེ་དབྱིབས་ལ་སྐད་ཆ་ཤོད།", "Tap to interrupt": "བར་ཆད་བྱེད་པར་མནན་པ།", "Tasks": "ལས་འགན།", "Tavily API Key": "Tavily API ལྡེ་མིག", "Tell us more:": "ང་ཚོ་ལ་མང་ཙམ་ཤོད།:", "Temperature": "དྲོད་ཚད།", "Template": "མ་དཔེ།", "Temporary Chat": "གནས་སྐབས་ཁ་བརྡ།", "Text Splitter": "ཡིག་རྐྱང་བགོ་བྱེད།", "Text-to-Speech Engine": "ཡིག་རྐྱང་ནས་གཏམ་བཤད་ཀྱི་འཕྲུལ་འཁོར།", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "ཁྱེད་ཀྱི་བསམ་འཆར་ལ་ཐུགས་རྗེ་ཆེ།", "The Application Account DN you bind with for search": "ཁྱེད་ཀྱིས་འཚོལ་བཤེར་གྱི་ཆེད་དུ་སྦྲེལ་བའི་ Application Account DN", "The base to search for users": "བེད་སྤྱོད་མཁན་འཚོལ་བའི་གཞི་རྩ།", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "ཚན་ཆུང་གི་ཆེ་ཆུང་གིས་ཡིག་རྐྱང་རེ་ཞུ་ག་ཚོད་མཉམ་དུ་ཐེངས་གཅིག་ལ་སྒྲུབ་དགོས་གཏན་འཁེལ་བྱེད། ཚན་ཆུང་ཆེ་བ་ཡིས་དཔེ་དབྱིབས་ཀྱི་ལས་ཆོད་དང་མྱུར་ཚད་མང་དུ་གཏོང་ཐུབ། འོན་ཀྱང་དེས་དྲན་ཤེས་མང་བ་དགོས།", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "plugin འདིའི་རྒྱབ་ཀྱི་གསར་སྤེལ་བ་དག་ནི་སྤྱི་ཚོགས་ནས་ཡིན་པའི་སེམས་ཤུགས་ཅན་གྱི་དང་བླངས་པ་ཡིན། གལ་ཏེ་ཁྱེད་ཀྱིས་ plugin འདི་ཕན་ཐོགས་ཡོད་པ་མཐོང་ན། དེའི་གསར་སྤེལ་ལ་ཞལ་འདེབས་གནང་བར་བསམ་ཞིབ་གནང་རོགས།", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "གདེང་འཇོག་འགྲན་རེས་རེའུ་མིག་དེ་ Elo སྐར་མ་སྤྲོད་པའི་མ་ལག་ལ་གཞི་བཅོལ་ཡོད། དེ་མིན་དུས་ཐོག་ཏུ་གསར་སྒྱུར་བྱེད་ཀྱི་ཡོད།", "The LDAP attribute that maps to the mail that users use to sign in.": "བེད་སྤྱོད་མཁན་ཚོས་ནང་འཛུལ་བྱེད་སྐབས་བེད་སྤྱོད་གཏོང་བའི་ཡིག་ཟམ་ལ་སྦྲེལ་བའི་ LDAP ཁྱད་ཆོས།", "The LDAP attribute that maps to the username that users use to sign in.": "བེད་སྤྱོད་མཁན་ཚོས་ནང་འཛུལ་བྱེད་སྐབས་བེད་སྤྱོད་གཏོང་བའི་བེད་སྤྱོད་མིང་ལ་སྦྲེལ་བའི་ LDAP ཁྱད་ཆོས།", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "འགྲན་རེས་རེའུ་མིག་དེ་ད་ལྟ་ Beta པར་གཞི་ཡིན། ང་ཚོས་ཨང་རྩིས་དེ་ཞིབ་ཚགས་སུ་གཏོང་སྐབས་སྐར་མའི་རྩིས་རྒྱག་ལེགས་སྒྲིག་བྱེད་སྲིད།", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "MB ནང་གི་ཡིག་ཆའི་ཆེ་ཆུང་མང་ཤོས། གལ་ཏེ་ཡིག་ཆའི་ཆེ་ཆུང་ཚད་བཀག་འདི་ལས་བརྒལ་ན། ཡིག་ཆ་དེ་སྤར་མི་འགྱུར།", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "ཁ་བརྡའི་ནང་ཐེངས་གཅིག་ལ་བེད་སྤྱོད་གཏོང་ཐུབ་པའི་ཡིག་ཆའི་གྲངས་མང་ཤོས། གལ་ཏེ་ཡིག་ཆའི་གྲངས་ཚད་བཀག་འདི་ལས་བརྒལ་ན། ཡིག་ཆ་དག་སྤར་མི་འགྱུར།", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "སྐར་མ་དེ་ 0.0 (0%) ནས་ 1.0 (100%) བར་གྱི་རིན་ཐང་ཞིག་ཡིན་དགོས།", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "དཔེ་དབྱིབས་ཀྱི་དྲོད་ཚད། དྲོད་ཚད་མཐོ་རུ་བཏང་ན་དཔེ་དབྱིབས་ཀྱིས་ལན་གསར་གཏོད་ཆེ་བ་སྤྲོད་ངེས།", "Theme": "བརྗོད་གཞི།", "Thinking...": "བསམ་བཞིན་པ།...", "This action cannot be undone. Do you wish to continue?": "བྱ་སྤྱོད་འདི་ཕྱིར་ལྡོག་བྱེད་མི་ཐུབ། ཁྱེད་མུ་མཐུད་འདོད་ཡོད་དམ།", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "བགྲོ་གླེང་འདི་ {{createdAt}} ལ་བཟོས་པ། འདི་ནི་ {{channelName}} བགྲོ་གླེང་གི་ཐོག་མ་རང་ཡིན།", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "འདིས་ཁྱེད་ཀྱི་རྩ་ཆེའི་ཁ་བརྡ་དག་བདེ་འཇགས་ངང་ཁྱེད་ཀྱི་རྒྱབ་སྣེ་གནས་ཚུལ་མཛོད་དུ་ཉར་ཚགས་བྱེད་པ་ཁག་ཐེག་བྱེད། ཐུགས་རྗེ་ཆེ།", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "འདི་ནི་ཚོད་ལྟའི་རང་བཞིན་གྱི་ཁྱད་ཆོས་ཤིག་ཡིན། དེ་རེ་སྒུག་ལྟར་ལས་ཀ་བྱེད་མི་སྲིད། དེ་མིན་དུས་ཚོད་གང་རུང་ལ་འགྱུར་བ་འགྲོ་སྲིད།", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "འདེམས་ཀ་འདིས་ནང་དོན་གསར་སྒྱུར་བྱེད་སྐབས་ཊོཀ་ཀེན་ག་ཚོད་ཉར་ཚགས་བྱེད་དགོས་ཚོད་འཛིན་བྱེད། དཔེར་ན། གལ་ཏེ་ ༢ ལ་བཀོད་སྒྲིག་བྱས་ན། ཁ་བརྡའི་ནང་དོན་གྱི་ཊོཀ་ཀེན་མཐའ་མ་ ༢ ཉར་ཚགས་བྱེད་ངེས། ནང་དོན་ཉར་ཚགས་བྱས་ན་ཁ་བརྡའི་རྒྱུན་མཐུད་རང་བཞིན་རྒྱུན་སྲུང་བྱེད་པར་རོགས་པ་བྱེད་ཐུབ། འོན་ཀྱང་དེས་བརྗོད་གཞི་གསར་པར་ལན་འདེབས་བྱེད་པའི་ནུས་པ་ཉུང་དུ་གཏོང་སྲིད།", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "འདེམས་ཀ་འདིས་དཔེ་དབྱིབས་ཀྱིས་དེའི་ལན་ནང་བཟོ་ཐུབ་པའི་ཊོཀ་ཀེན་གྱི་གྲངས་མང་ཤོས་འཇོག་པ། ཚད་བཀག་འདི་མང་དུ་བཏང་ན་དཔེ་དབྱིབས་ཀྱིས་ལན་རིང་བ་སྤྲོད་པར་གནང་བ་སྤྲོད། འོན་ཀྱང་དེས་ཕན་ཐོགས་མེད་པའམ་འབྲེལ་མེད་ཀྱི་ནང་དོན་བཟོ་བའི་ཆགས་ཚུལ་མང་དུ་གཏོང་སྲིད།", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "འདེམས་ཀ་འདིས་བསྡུ་གསོག་ནང་གི་ཡོད་པའི་ཡིག་ཆ་ཡོངས་རྫོགས་བསུབ་ནས་དེ་དག་གསར་དུ་སྤར་བའི་ཡིག་ཆས་ཚབ་བྱེད་ངེས།", "This response was generated by \"{{model}}\"": "ལན་འདི་ \"{{model}}\" ཡིས་བཟོས་པ།", "This will delete": "འདིས་བསུབ་ངེས།", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "འདིས་ <strong>{{NAME}}</strong> དང་ <strong>དེའི་ནང་དོན་ཡོངས་རྫོགས་</strong> བསུབ་ངེས།", "This will delete all models including custom models": "འདིས་སྲོལ་བཟོས་དཔེ་དབྱིབས་ཚུད་པའི་དཔེ་དབྱིབས་ཡོངས་རྫོགས་བསུབ་ངེས།", "This will delete all models including custom models and cannot be undone.": "འདིས་སྲོལ་བཟོས་དཔེ་དབྱིབས་ཚུད་པའི་དཔེ་དབྱིབས་ཡོངས་རྫོགས་བསུབ་ངེས་པ་དང་ཕྱིར་ལྡོག་བྱེད་མི་ཐུབ།", "This will reset the knowledge base and sync all files. Do you wish to continue?": "འདིས་ཤེས་བྱའི་རྟེན་གཞི་སླར་སྒྲིག་བྱས་ནས་ཡིག་ཆ་ཡོངས་རྫོགས་མཉམ་སྡེབ་བྱེད་ངེས། ཁྱེད་མུ་མཐུད་འདོད་ཡོད་དམ།", "Thorough explanation": "འགྲེལ་བཤད་ཞིབ་ཚགས།", "Thought for {{DURATION}}": "{{DURATION}} རིང་བསམས།", "Thought for {{DURATION}} seconds": "སྐར་ཆ་ {{DURATION}} རིང་བསམས།", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika Server URL དགོས་ངེས།", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "བསམ་འཆར།: ཚབ་བྱེད་རེ་རེའི་རྗེས་སུ་ཁ་བརྡའི་ནང་འཇུག་ཏུ་ tab མཐེབ་གནོན་མནན་ནས་འགྱུར་ཚད་ཀྱི་གནས་མང་པོ་རྒྱུན་མཐུད་དུ་གསར་སྒྱུར་བྱེད་པ།", "Title": "ཁ་བྱང་།", "Title (e.g. Tell me a fun fact)": "ཁ་བྱང་ (དཔེར་ན། དགོད་བྲོ་བའི་དོན་དངོས་ཤིག་ང་ལ་ཤོད།)", "Title Auto-Generation": "ཁ་བྱང་རང་འགུལ་བཟོ་སྐྲུན།", "Title cannot be an empty string.": "ཁ་བྱང་ཡིག་ཕྲེང་སྟོང་པ་ཡིན་མི་ཆོག", "Title Generation": "ཁ་བྱང་བཟོ་སྐྲུན།", "Title Generation Prompt": "ཁ་བྱང་བཟོ་སྐྲུན་གྱི་འགུལ་སློང་།", "TLS": "TLS", "To access the available model names for downloading,": "ཕབ་ལེན་གྱི་ཆེད་དུ་ཡོད་པའི་དཔེ་དབྱིབས་ཀྱི་མིང་ལ་འཛུལ་སྤྱོད་བྱེད་པར།:", "To access the GGUF models available for downloading,": "ཕབ་ལེན་གྱི་ཆེད་དུ་ཡོད་པའི་ GGUF དཔེ་དབྱིབས་ལ་འཛུལ་སྤྱོད་བྱེད་པར།:", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "WebUI ལ་འཛུལ་སྤྱོད་བྱེད་པར། དོ་དམ་པ་དང་འབྲེལ་གཏུག་བྱེད་རོགས། དོ་དམ་པས་དོ་དམ་པའི་ལྟ་སྟེགས་ནས་བེད་སྤྱོད་མཁན་གྱི་གནས་སྟངས་དོ་དམ་བྱེད་ཐུབ།", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "ཤེས་བྱའི་རྟེན་གཞི་འདིར་འཇོག་པར། ཐོག་མར་དེ་དག་ \"ཤེས་བྱའི་\" ལས་ཡུལ་དུ་སྣོན་པ།", "To learn more about available endpoints, visit our documentation.": "ཡོད་པའི་མཇུག་མཐུད་སྐོར་མང་ཙམ་ཤེས་པར། ང་ཚོའི་ཡིག་ཆ་ལ་ལྟ་བ།", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "ཁྱེད་ཀྱི་སྒེར་དོན་སྲུང་སྐྱོབ་བྱེད་པར། ཁྱེད་ཀྱི་བསམ་འཆར་ནས་སྐར་མ། དཔེ་དབྱིབས་ཀྱི་ IDs། རྟགས། དང་ metadata ཁོ་ན་མཉམ་སྤྱོད་བྱེད།—ཁྱེད་ཀྱི་ཁ་བརྡའི་ཟིན་ཐོ་སྒེར་དོན་དུ་གནས་པ་དང་ཚུད་མེད།", "To select actions here, add them to the \"Functions\" workspace first.": "བྱ་སྤྱོད་འདིར་གདམ་ག་བྱེད་པར། ཐོག་མར་དེ་དག་ \"ལས་འགན་གྱི་\" ལས་ཡུལ་དུ་སྣོན་པ།", "To select filters here, add them to the \"Functions\" workspace first.": "འཚག་མ་འདིར་གདམ་ག་བྱེད་པར། ཐོག་མར་དེ་དག་ \"ལས་འགན་གྱི་\" ལས་ཡུལ་དུ་སྣོན་པ།", "To select toolkits here, add them to the \"Tools\" workspace first.": "ལག་ཆའི་ཚོགས་སྡེ་འདིར་གདམ་ག་བྱེད་པར། ཐོག་མར་དེ་དག་ \"ལག་ཆའི་\" ལས་ཡུལ་དུ་སྣོན་པ།", "Toast notifications for new updates": "གསར་སྒྱུར་གསར་པའི་ཆེད་དུ་ Toast བརྡ་ཁྱབ།", "Today": "དེ་རིང་།", "Toggle settings": "སྒྲིག་འགོད་བརྗེ་བ།", "Toggle sidebar": "ཟུར་ངོས་བརྗེ་བ།", "Token": "ཊོཀ་ཀེན།", "Tokens To Keep On Context Refresh (num_keep)": "ནང་དོན་གསར་སྒྱུར་སྐབས་ཉར་ཚགས་བྱེད་རྒྱུའི་ཊོཀ་ཀེན། (num_keep)", "Too verbose": "རིང་དྲགས།", "Tool created successfully": "ལག་ཆ་ལེགས་པར་བཟོས་ཟིན།", "Tool deleted successfully": "ལག་ཆ་ལེགས་པར་བསུབས་ཟིན།", "Tool Description": "ལག་ཆའི་འགྲེལ་བཤད།", "Tool ID": "ལག་ཆའི་ ID", "Tool imported successfully": "ལག་ཆ་ལེགས་པར་ནང་འདྲེན་བྱས།", "Tool Name": "ལག་ཆའི་མིང་།", "Tool Servers": "", "Tool updated successfully": "ལག་ཆ་ལེགས་པར་གསར་སྒྱུར་བྱས།", "Tools": "ལག་ཆ།", "Tools Access": "ལག་ཆར་འཛུལ་སྤྱོད།", "Tools are a function calling system with arbitrary code execution": "ལག་ཆ་ནི་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་ཡོད་པའི་ལས་འགན་འབོད་པའི་མ་ལག་ཅིག་ཡིན།", "Tools Function Calling Prompt": "ལག་ཆ་ལས་འགན་འབོད་པའི་འགུལ་སློང་།", "Tools have a function calling system that allows arbitrary code execution": "ལག་ཆར་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་ལ་གནང་བ་སྤྲོད་པའི་ལས་འགན་འབོད་པའི་མ་ལག་ཡོད།", "Tools have a function calling system that allows arbitrary code execution.": "ལག་ཆར་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་ལ་གནང་བ་སྤྲོད་པའི་ལས་འགན་འབོད་པའི་མ་ལག་ཡོད།", "Tools Public Sharing": "ལག་ཆ་སྤྱི་སྤྱོད་མཉམ་སྤྱོད།", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Top P": "Top P", "Transformers": "Transformers", "Trouble accessing Ollama?": "Ollama འཛུལ་སྤྱོད་སྐབས་དཀའ་ངལ་འཕྲད་དམ།", "Trust Proxy Environment": "Proxy ཁོར་ཡུག་ལ་ཡིད་ཆེས།", "TTS Model": "TTS དཔེ་དབྱིབས།", "TTS Settings": "TTS སྒྲིག་འགོད།", "TTS Voice": "TTS སྐད།", "Type": "རིགས།", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (ཕབ་ལེན།) URL མནན་པ།", "Uh-oh! There was an issue with the response.": "ཨོའོ། ལན་ལ་དཀའ་ངལ་ཞིག་བྱུང་སོང་།", "UI": "UI", "Unarchive All": "ཡོངས་རྫོགས་ཕྱིར་འདོན།", "Unarchive All Archived Chats": "ཡིག་མཛོད་དུ་བཞག་པའི་ཁ་བརྡ་ཡོངས་རྫོགས་ཕྱིར་འདོན།", "Unarchive Chat": "ཁ་བརྡ་ཕྱིར་འདོན།", "Unlock mysteries": "གསང་བ་གྲོལ་བ།", "Unpin": "ཕྱིར་འདོན།", "Unravel secrets": "གསང་བ་གྲོལ་བ།", "Untagged": "རྟགས་མེད།", "Update": "གསར་སྒྱུར།", "Update and Copy Link": "གསར་སྒྱུར་དང་སྦྲེལ་ཐག་འདྲ་བཤུས།", "Update for the latest features and improvements.": "ཁྱད་ཆོས་དང་ལེགས་བཅོས་གསར་ཤོས་ཀྱི་ཆེད་དུ་གསར་སྒྱུར་བྱེད་པ།", "Update password": "གསང་གྲངས་གསར་སྒྱུར།", "Updated": "གསར་སྒྱུར་བྱས།", "Updated at": "གསར་སྒྱུར་བྱེད་དུས།", "Updated At": "གསར་སྒྱུར་བྱེད་དུས།", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "ནུས་པ་ཤུགས་ཆེ་བ། སྲོལ་བཟོས་བརྗོད་གཞི་དང་ཚོང་རྟགས། དམིགས་བསལ་རྒྱབ་སྐྱོར་ཚུད་པའི་ཆོག་མཆན་ཡོད་པའི་འཆར་གཞི་ལ་རིམ་སྤོར་བྱེད་པ།", "Upload": "སྤར་བ།", "Upload a GGUF model": "GGUF དཔེ་དབྱིབས་ཤིག་སྤར་བ།", "Upload directory": "སྤར་བའི་ཐོ་འཚོལ།", "Upload files": "ཡིག་ཆ་སྤར་བ།", "Upload Files": "ཡིག་ཆ་སྤར་བ།", "Upload Pipeline": "རྒྱུ་ལམ་སྤར་བ།", "Upload Progress": "སྤར་བའི་འཕེལ་རིམ།", "URL": "URL", "URL Mode": "URL མ་དཔེ།", "Use '#' in the prompt input to load and include your knowledge.": "འགུལ་སློང་ནང་འཇུག་ཏུ་ '#' བེད་སྤྱོད་ནས་ཁྱེད་ཀྱི་ཤེས་བྱ་ནང་འཇུག་དང་ཚུད་པ།", "Use Gravatar": "Gravatar བེད་སྤྱོད།", "Use groups to group your users and assign permissions.": "ཁྱེད་ཀྱི་བེད་སྤྱོད་མཁན་ཚོགས་པ་བཟོ་བ་དང་དབང་ཚད་སྤྲོད་པར་ཚོགས་པ་བེད་སྤྱོད་གཏོང་བ།", "Use Initials": "མིང་གི་ཡིག་འབྲུ་མགོ་མ་བེད་སྤྱོད།", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "བེད་སྤྱོད་མཁན།", "User": "བེད་སྤྱོད་མཁན།", "User location successfully retrieved.": "བེད་སྤྱོད་མཁན་གནས་ཡུལ་ལེགས་པར་ལེན་ཚུར་སྒྲུབ་བྱས།", "User Webhooks": "བེད་སྤྱོད་མཁན་གྱི་ Webhooks", "Username": "བེད་སྤྱོད་མིང་།", "Users": "བེད་སྤྱོད་མཁན།", "Using the default arena model with all models. Click the plus button to add custom models.": "དཔེ་དབྱིབས་ཡོངས་རྫོགས་དང་མཉམ་དུ་སྔོན་སྒྲིག་ arena དཔེ་དབྱིབས་བེད་སྤྱོད་གཏོང་བཞིན་པ། སྲོལ་བཟོས་དཔེ་དབྱིབས་སྣོན་པར་བསྣན་རྟགས་མཐེབ་གནོན་ལ་མནན་པ།", "Utilize": "བེད་སྤྱོད།", "Valid time units:": "ནུས་ལྡན་དུས་ཚོད་ཀྱི་ཚན་པ།:", "Valves": "Valves", "Valves updated": "Valves གསར་སྒྱུར་བྱས།", "Valves updated successfully": "Valves ལེགས་པར་གསར་སྒྱུར་བྱས།", "variable": "འགྱུར་ཚད།", "variable to have them replaced with clipboard content.": "འགྱུར་ཚད་དེ་དག་སྦྱར་སྡེར་གྱི་ནང་དོན་གྱིས་ཚབ་བྱེད་པར་ཡོད་པ།", "Verify Connection": "སྦྲེལ་མཐུད་ར་སྤྲོད།", "Version": "པར་གཞི།", "Version {{selectedVersion}} of {{totalVersions}}": "པར་གཞི་ {{selectedVersion}} ། {{totalVersions}} ནས།", "View Replies": "ལན་ལྟ་བ།", "View Result from **{{NAME}}**": "", "Visibility": "མཐོང་ཐུབ་རང་བཞིན།", "Voice": "སྐད།", "Voice Input": "སྐད་ཀྱི་ནང་འཇུག", "Warning": "ཉེན་བརྡ།", "Warning:": "ཉེན་བརྡ།:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "ཉེན་བརྡ།: འདི་སྒུལ་བསྐྱོད་བྱས་ན་བེད་སྤྱོད་མཁན་ཚོས་སར་བར་སྟེང་གང་འདོད་ཀྱི་ཀོཌ་སྤར་བར་གནང་བ་སྤྲོད་ངེས།", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "ཉེན་བརྡ།: གལ་ཏེ་ཁྱེད་ཀྱིས་ཁྱེད་ཀྱི་ཚུད་འཇུག་དཔེ་དབྱིབས་གསར་སྒྱུར་རམ་བརྗེ་བ་ཡིན་ན། ཁྱེད་ཀྱིས་ཡིག་ཆ་ཡོངས་རྫོགས་བསྐྱར་དུ་ནང་འདྲེན་བྱེད་དགོས་ངེས།", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "ཉེན་བརྡ།: Jupyter ལག་བསྟར་གྱིས་གང་འདོད་ཀྱི་ཀོཌ་ལག་བསྟར་སྒུལ་བསྐྱོད་བྱས་ནས། བདེ་འཇགས་ཀྱི་ཉེན་ཁ་ཚབས་ཆེན་བཟོ་གི་ཡོད།—ཧ་ཅང་གཟབ་ནན་གྱིས་སྔོན་སྐྱོད་བྱེད་རོགས།", "Web": "དྲ་བ།", "Web API": "Web API", "Web Search": "དྲ་བའི་འཚོལ་བཤེར།", "Web Search Engine": "དྲ་བའི་འཚོལ་བཤེར་འཕྲུལ་འཁོར།", "Web Search in Chat": "ཁ་བརྡའི་ནང་དྲ་བའི་འཚོལ་བཤེར།", "Web Search Query Generation": "དྲ་བའི་འཚོལ་བཤེར་འདྲི་བ་བཟོ་སྐྲུན།", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI སྒྲིག་འགོད།", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI ཡིས་ \"{{url}}/api/chat\" ལ་རེ་ཞུ་གཏོང་ངེས།", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI ཡིས་ \"{{url}}/chat/completions\" ལ་རེ་ཞུ་གཏོང་ངེས།", "What are you trying to achieve?": "ཁྱེད་ཀྱིས་ཅི་ཞིག་འགྲུབ་ཐབས་བྱེད་བཞིན་ཡོད།", "What are you working on?": "ཁྱེད་ཀྱིས་ཅི་ཞིག་ལས་ཀ་བྱེད་བཞིན་ཡོད།", "What’s New in": "གསར་པ་ཅི་ཡོད།", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "སྒུལ་བསྐྱོད་བྱས་ཚེ། དཔེ་དབྱིབས་ཀྱིས་ཁ་བརྡའི་འཕྲིན་རེ་རེར་དུས་ཐོག་ཏུ་ལན་འདེབས་བྱེད་ངེས། བེད་སྤྱོད་མཁན་གྱིས་འཕྲིན་བཏང་མ་ཐག་ལན་ཞིག་བཟོ་ངེས། མ་དཔེ་འདི་ཐད་གཏོང་ཁ་བརྡའི་བཀོལ་ཆས་ལ་ཕན་ཐོགས་ཡོད། འོན་ཀྱང་དེས་མཁྲེགས་ཆས་དལ་བའི་སྟེང་ལས་ཆོད་ལ་ཤུགས་རྐྱེན་ཐེབས་སྲིད།", "wherever you are": "ཁྱེད་གང་དུ་ཡོད་ཀྱང་།", "Whisper (Local)": "<PERSON><PERSON><PERSON> (ས་གནས།)", "Why?": "ཅིའི་ཕྱིར།", "Widescreen Mode": "ཡངས་གནས་ངོས་མ་དཔེ།", "Won": "ཐོབ།", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "top-k དང་མཉམ་ལས་བྱེད། རིན་ཐང་མཐོ་བ་ (དཔེར་ན། 0.95) ཡིས་ཡིག་རྐྱང་སྣ་ཚོགས་ཆེ་བ་ཡོང་ངེས། དེ་བཞིན་དུ་རིན་ཐང་དམའ་བ་ (དཔེར་ན། 0.5) ཡིས་ཡིག་རྐྱང་དམིགས་ཚད་དང་སྲུང་འཛིན་ཆེ་བ་བཟོ་ངེས།", "Workspace": "ལས་ཡུལ།", "Workspace Permissions": "ལས་ཡུལ་གྱི་དབང་ཚད།", "Write": "འབྲི་བ།", "Write a prompt suggestion (e.g. Who are you?)": "འགུལ་སློང་གྲོས་གཞི་ཞིག་འབྲི་བ། (དཔེར་ན། ཁྱེད་སུ་ཡིན།)", "Write a summary in 50 words that summarizes [topic or keyword].": "[བརྗོད་གཞི་ཡང་ན་གནད་ཚིག] ཕྱོགས་སྡོམ་བྱེད་པའི་ཚིག་ ༥༠ ནང་གི་སྙིང་བསྡུས་ཤིག་འབྲི་བ།", "Write something...": "ཅི་ཞིག་འབྲི་བ།...", "Write your model template content here": "ཁྱེད་ཀྱི་དཔེ་དབྱིབས་མ་དཔེའི་ནང་དོན་འདིར་འབྲི་བ།", "Yesterday": "ཁ་ས།", "You": "ཁྱེད།", "You are currently using a trial license. Please contact support to upgrade your license.": "ཁྱེད་ཀྱིས་ད་ལྟ་ཚོད་ལྟའི་ཆོག་མཆན་ཞིག་བེད་སྤྱོད་གཏོང་བཞིན་འདུག ཁྱེད་ཀྱི་ཆོག་མཆན་རིམ་སྤོར་བྱེད་པར་རོགས་སྐྱོར་དང་འབྲེལ་གཏུག་བྱེད་རོགས།", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "ཁྱེད་ཀྱིས་ཐེངས་གཅིག་ལ་ཡིག་ཆ་ {{maxCount}} ལས་མང་བ་དང་ཁ་བརྡ་བྱེད་མི་ཐུབ།", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "ཁྱེད་ཀྱིས་གཤམ་གྱི་ 'དོ་དམ་' མཐེབ་གནོན་བརྒྱུད་དྲན་ཤེས་སྣོན་ནས་ LLMs དང་མཉམ་དུ་འབྲེལ་འདྲིས་བྱེད་པ་སྒེར་སྤྱོད་ཅན་བཟོ་ཐུབ། དེ་དག་ཁྱེད་ལ་སྔར་ལས་ཕན་ཐོགས་པ་དང་འཚམ་པོ་བཟོ་ཐུབ།", "You cannot upload an empty file.": "ཁྱེད་ཀྱིས་ཡིག་ཆ་སྟོང་པ་སྤར་མི་ཐུབ།", "You do not have permission to upload files": "ཁྱེད་ལ་ཡིག་ཆ་སྤར་བའི་དབང་ཚད་མེད།", "You do not have permission to upload files.": "ཁྱེད་ལ་ཡིག་ཆ་སྤར་བའི་དབང་ཚད་མེད།", "You have no archived conversations.": "ཁྱེད་ལ་ཡིག་མཛོད་དུ་བཞག་པའི་ཁ་བརྡ་མེད།", "You have shared this chat": "ཁྱེད་ཀྱིས་ཁ་བརྡ་འདི་མཉམ་སྤྱོད་བྱས་ཡོད།", "You're a helpful assistant.": "ཁྱེད་ནི་ཕན་ཐོགས་པའི་ལག་རོགས་པ་ཞིག་ཡིན།", "You're now logged in.": "ཁྱེད་ད་ལྟ་ནང་འཛུལ་བྱས་ཟིན།", "Your account status is currently pending activation.": "ཁྱེད་ཀྱི་རྩིས་ཁྲའི་གནས་སྟངས་ད་ལྟ་སྒུལ་བསྐྱོད་སྒུག་བཞིན་པ།", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "ཁྱེད་ཀྱི་ཞལ་འདེབས་ཆ་ཚང་ཐད་ཀར་ plugin གསར་སྤེལ་བ་ལ་འགྲོ་ངེས། Open WebUI ཡིས་བརྒྱ་ཆ་གང་ཡང་མི་ལེན། འོན་ཀྱང་། གདམ་ཟིན་པའི་མ་དངུལ་གཏོང་བའི་སྟེགས་བུ་ལ་དེའི་རང་གི་འགྲོ་གྲོན་ཡོད་སྲིད།", "Youtube": "Youtube", "Youtube Language": "Youtube སྐད་ཡིག", "Youtube Proxy URL": "Youtube Proxy URL"}