import type { i18n } from 'i18next';
import type { Writable } from 'svelte/store';

export type Banner = {
	id: string;
	type: string;
	title?: string;
	content: string;
	url?: string;
	dismissible?: boolean;
	timestamp: number;
};

export enum TTS_RESPONSE_SPLIT {
	PUNCTUATION = 'punctuation',
	PARAGRAPHS = 'paragraphs',
	NONE = 'none'
}

export type PPTPages = {
	title: string;
	slide_name: string;
	page_num: number;
	width: number;
	height: number;
	desc: string;
	pages: string[];
};

export type GLMBlockGroup = 'start' | 'middle' | 'end' | undefined;

export type I18nType = Writable<i18n>;

export type VibeInfo = {
	vibeMode?: "ppt" | "artifacts" | "",
	vibeReference?: {
		line: number,
		column: number,
		code: string,
		filename?: string,
		pptIndex?: number
	}
}