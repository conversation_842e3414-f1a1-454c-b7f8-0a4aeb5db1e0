<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="16"
	height="16"
	viewBox="0 0 16 16"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M13.6666 2.2002H2.33325V11.5335H13.6666V2.2002Z"
		stroke="currentColor"
		stroke-linejoin="round"
	/>
	<path
		d="M5.33325 14.1999L7.99992 11.5332L10.6666 14.1999"
		stroke="currentColor"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<path
		d="M4.64136 8.58802L6.52219 6.75209L8.00282 8.20039L11.3224 4.87402"
		stroke="currentColor"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<path
		d="M1.33325 2.2002H14.6666"
		stroke="currentColor"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>
