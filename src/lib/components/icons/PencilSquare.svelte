<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	width="24"
	height="24"
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	stroke-width={strokeWidth}
	stroke="currentColor"
>
	<g opacity="0.8">
		<path
			d="M21 13V17C21 19.2091 19.2091 21 17 21H7C4.79086 21 3 19.2132 3 17.0041C3 14.0012 3 9.96446 3 6.99654C3 4.7874 4.79083 3 6.99997 3C9.00004 3 11 3 11 3"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M8.58494 12.0343C8.21038 12.4093 8 12.9177 8 13.4477V16H10.5686C11.0992 16 11.608 15.7892 11.9831 15.4139L19.5867 7.80695C20.3674 7.02597 20.3674 5.76012 19.5867 4.97914L19.0235 4.41568C18.2422 3.63401 16.9749 3.63425 16.1939 4.41622L8.58494 12.0343Z"
			stroke="currentColor"
			stroke-linejoin="round"
		/>
	</g>
</svg>
