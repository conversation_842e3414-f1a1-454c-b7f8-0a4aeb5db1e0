<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g id="&#230;&#136;&#145;&#231;&#154;&#132;_me 1" clip-path="url(#clip0_34_1451)">
		<path
			id="Vector"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M9.99996 18.3337C14.6023 18.3337 18.3333 14.6027 18.3333 10.0003C18.3333 5.39795 14.6023 1.66699 9.99996 1.66699C5.39758 1.66699 1.66663 5.39795 1.66663 10.0003C1.66663 14.6027 5.39758 18.3337 9.99996 18.3337Z"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_2"
			d="M9.99996 9.58366C11.1505 9.58366 12.0833 8.65091 12.0833 7.50033C12.0833 6.34974 11.1505 5.41699 9.99996 5.41699C8.84938 5.41699 7.91663 6.34974 7.91663 7.50033C7.91663 8.65091 8.84938 9.58366 9.99996 9.58366Z"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_3"
			d="M4.1759 15.9713C4.31911 13.7999 6.12574 12.083 8.3334 12.083H11.6667C13.8715 12.083 15.6763 13.7954 15.8237 15.9627"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
	<defs>
		<clipPath id="clip0_34_1451">
			<rect width="20" height="20" fill="white" />
		</clipPath>
	</defs>
</svg>
