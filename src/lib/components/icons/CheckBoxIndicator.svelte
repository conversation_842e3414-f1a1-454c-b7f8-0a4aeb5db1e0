<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="12"
	height="10"
	viewBox="0 0 12 10"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="Vector 2661">
		<path
			d="M1 5.27474L4.10257 8.08751C4.39662 8.3541 4.85162 8.32983 5.11565 8.03347L11 1.42859"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M1 5.27474L4.10257 8.08751C4.39662 8.3541 4.85162 8.32983 5.11565 8.03347L11 1.42859"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
</svg>
