<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	viewBox="0 0 1024 1024"
	version="1.1"
	xmlns="http://www.w3.org/2000/svg"
	width="32"
	height="32"
	stroke-width={strokeWidth}
	><path
		d="M938.666667 512a384 384 0 0 1-384 384 379.306667 379.306667 0 0 1-220.16-69.546667 21.76 21.76 0 0 1-8.96-15.786666 21.333333 21.333333 0 0 1 5.973333-16.64l30.72-31.146667a21.333333 21.333333 0 0 1 26.88-2.56A294.826667 294.826667 0 0 0 554.666667 810.666667a298.666667 298.666667 0 1 0-298.666667-298.666667h100.693333a20.906667 20.906667 0 0 1 15.36 6.4l8.533334 8.533333a21.333333 21.333333 0 0 1 0 30.293334L229.973333 708.266667a21.76 21.76 0 0 1-30.293333 0l-150.613333-151.04a21.333333 21.333333 0 0 1 0-30.293334l8.533333-8.533333a20.906667 20.906667 0 0 1 15.36-6.4H170.666667a384 384 0 0 1 768 0z m-367.786667-213.333333h-32.426667a21.333333 21.333333 0 0 0-21.333333 21.333333v198.826667a22.613333 22.613333 0 0 0 6.4 14.933333l140.373333 140.373333a21.333333 21.333333 0 0 0 30.293334 0l22.613333-22.613333a21.333333 21.333333 0 0 0 0-30.293333l-124.586667-124.586667V320a21.333333 21.333333 0 0 0-21.333333-21.333333z"
		fill="currentColor"
	></path></svg
>
