<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke="currentColor"
	stroke-width={strokeWidth}
	width="16"
	height="16"
	viewBox="0 0 16 16"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="&#229;&#136;&#134;&#228;&#186;&#171;2_share-two 1" opacity="0.8">
		<path
			id="Vector"
			d="M8.66667 2.40397C8.66667 1.79298 9.42013 1.50385 9.82886 1.958L13.843 6.41819C14.3081 6.93497 14.2981 7.7224 13.8199 8.22713L9.8173 12.4521C9.40282 12.8896 8.66667 12.5963 8.66667 11.9936V9.33333C4 9.33333 2 14.3333 2 14.3333C2 8.66667 3.66667 5 8.66667 5V2.40397Z"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
</svg>
