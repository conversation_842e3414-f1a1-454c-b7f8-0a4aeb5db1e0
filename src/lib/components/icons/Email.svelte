<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g opacity="0.8">
		<path
			d="M1.66664 14.25C1.66664 15.3546 2.56207 16.25 3.66664 16.25H16.3333C17.4379 16.25 18.3333 15.3546 18.3333 14.25V10V5.75C18.3333 4.64543 17.4379 3.75 16.3333 3.75H9.99997H3.66664C2.56207 3.75 1.66664 4.64543 1.66664 5.75V10V14.25Z"
			stroke-linejoin="round"
		/>
		<path d="M3.00002 4.5L9.99998 10L17 4.5" stroke-linecap="round" stroke-linejoin="round" />
	</g>
</svg>
