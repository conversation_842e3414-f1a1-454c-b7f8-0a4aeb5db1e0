<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 24 24"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g id="&#230;&#156;&#136;&#228;&#186;&#174;_moon 1" clip-path="url(#clip0_34_865)">
		<path
			id="Vector"
			d="M11.6886 1.83794C9.40949 2.43214 7.72728 4.5045 7.72728 6.96979C7.72728 9.89858 10.1015 12.2728 13.0303 12.2728C15.4956 12.2728 17.5679 10.5906 18.1621 8.31146C18.2744 8.85675 18.3333 9.42154 18.3333 10.0001C18.3333 14.6025 14.6024 18.3334 9.99999 18.3334C5.39761 18.3334 1.66666 14.6025 1.66666 10.0001C1.66666 5.39771 5.39761 1.66675 9.99999 1.66675C10.5785 1.66675 11.1433 1.7257 11.6886 1.83794Z"
			stroke-linejoin="round"
		/>
	</g>
	<defs>
		<clipPath id="clip0_34_865">
			<rect width="20" height="20" fill="transparent" />
		</clipPath>
	</defs>
</svg>
