<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g opacity="0.8">
		<path
			d="M17.5 16.3334V11.1667C17.5 10.0621 16.6064 9.16669 15.5018 9.16669H4.49558C3.39101 9.16669 2.5 10.0621 2.5 11.1667V16.3334C2.5 17.4379 3.39543 18.3334 4.5 18.3334H15.5C16.6046 18.3334 17.5 17.4379 17.5 16.3334Z"
			stroke-linejoin="round"
		/>
		<path
			d="M5.83336 9.16669V5.83335C5.83336 3.53217 7.69886 1.66669 10 1.66669C12.3012 1.66669 14.1667 3.53217 14.1667 5.83335V9.16669"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path d="M10 12.5V15" stroke-linecap="round" stroke-linejoin="round" />
	</g>
</svg>
