<script lang="ts">
	export let className = 'size-5';
	export let strokeWidth = '2';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 24 24"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g opacity="0.8">
		<path
			d="M3 17.0048V6.99666C3 4.78752 4.7865 3 6.99563 3H17.0037C19.2129 3 21 4.78757 21 6.99671V17.0048C21 19.214 19.2129 21 17.0037 21H6.99563C4.7865 21 3 19.2139 3 17.0048Z"
			stroke-linejoin="round"
		/>
		<path d="M15 3V21" stroke-linecap="round" stroke-linejoin="round" />
		<path d="M8 10L10 12L8 14" stroke-linecap="round" stroke-linejoin="round" />
	</g>
</svg>
