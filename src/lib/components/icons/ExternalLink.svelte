<script lang="ts">
	export let className = 'size-5';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	viewBox="0 0 1024 1024"
	version="1.1"
	xmlns="http://www.w3.org/2000/svg"
	xmlns:xlink="http://www.w3.org/1999/xlink"
>
	<path
		d="M480.9984 241.3056a25.6 25.6 0 0 1-2.6112 51.1488 255.232 255.232 0 0 0-194.176 74.624c-99.968 99.9936-99.968 262.0672 0 362.0608 99.968 99.968 262.0672 99.968 362.0352 0a255.232 255.232 0 0 0 74.6752-193.5104 25.6 25.6 0 1 1 51.1488-2.4832 306.432 306.432 0 0 1-89.6 232.192c-119.9872 119.9616-314.496 119.9616-434.4576 0C128 645.3504 128 450.8672 248.0128 330.9056a306.432 306.432 0 0 1 232.96-89.6z"
		fill="currentColor"
	/>
	<path
		d="M758.144 255.7184v153.6a25.6 25.6 0 0 1-51.2 0V342.528l-274.8928 274.944a25.6 25.6 0 0 1-36.1984-36.224l275.5584-275.5584-68.0448 0.0256a25.6 25.6 0 0 1 0-51.2h153.6v1.152h1.1776z"
		fill="currentColor"
	/>
</svg>
