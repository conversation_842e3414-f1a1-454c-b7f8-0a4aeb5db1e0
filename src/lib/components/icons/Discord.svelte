<script lang="ts">
	export let className = ' size-4';
	export let theme = 'light';
	export let fill = 'none';
</script>

<svg
	class={className}
	width="24"
	height="18"
	viewBox="0 0 24 18"
	{fill}
	xmlns="http://www.w3.org/2000/svg"
>
	<g clip-path="url(#clip0_311_2416)">
		<mask
			id="mask0_311_2416"
			style="mask-type:luminance"
			maskUnits="userSpaceOnUse"
			x="0"
			y="-1"
			width="24"
			height="20"
		>
			<path d="M23.8556 -0.136292H0.240845V18.1572H23.8556V-0.136292Z" fill="white" />
		</mask>
		<g mask="url(#mask0_311_2416)">
			<path
				d="M15.9785 12.2783C14.8149 12.2783 13.8556 11.2104 13.8556 9.8982C13.8556 8.58599 14.7955 7.51811 15.9785 7.51811C17.1614 7.51811 18.1194 8.59633 18.1013 9.8982C18.0832 11.2001 17.1704 12.2783 15.9785 12.2783ZM8.13101 12.2783C6.96747 12.2783 6.00819 11.2104 6.00819 9.8982C6.00819 8.58599 6.94808 7.51811 8.13101 7.51811C9.31394 7.51811 10.2719 8.59633 10.2538 9.8982C10.2357 11.2001 9.31394 12.2783 8.1323 12.2783H8.13101ZM20.2306 1.49226C18.6995 0.78957 17.0838 0.288511 15.4238 0.00162936C15.4087 -0.0013667 15.3931 0.000525122 15.3791 0.00703455C15.3652 0.013544 15.3537 0.024332 15.3463 0.0378283C15.1262 0.437329 14.9264 0.847674 14.7477 1.2673C12.9583 0.995548 11.1382 0.995548 9.34885 1.2673C9.16907 0.846616 8.96622 0.43617 8.74122 0.0378283C8.7334 0.0247275 8.72182 0.0142729 8.708 0.00781968C8.69417 0.0013665 8.67872 -0.000788362 8.66365 0.00162936C7.00354 0.287913 5.38774 0.788996 3.85693 1.49226C3.84405 1.49791 3.83321 1.50738 3.8259 1.5194C0.764492 6.09342 -0.0745515 10.555 0.335274 14.9648C0.336448 14.9757 0.339803 14.9862 0.345134 14.9957C0.350464 15.0053 0.357665 15.0137 0.366305 15.0204C2.14879 16.3409 4.14269 17.349 6.26288 18.0016C6.27779 18.0061 6.29371 18.0059 6.3085 18.001C6.32329 17.9962 6.33625 17.9869 6.34562 17.9745C6.80093 17.3546 7.20436 16.6982 7.55182 16.012C7.55645 16.0025 7.55905 15.9923 7.55945 15.9818C7.55985 15.9713 7.55805 15.9608 7.55415 15.9511C7.55025 15.9413 7.54434 15.9325 7.53682 15.9252C7.52929 15.9178 7.52031 15.9122 7.51045 15.9085C6.87414 15.6651 6.25812 15.3715 5.66818 15.0307C5.65678 15.0245 5.64715 15.0156 5.64019 15.0046C5.63323 14.9937 5.62917 14.9812 5.62839 14.9682C5.62761 14.9553 5.63015 14.9424 5.63575 14.9307C5.64136 14.919 5.64985 14.9089 5.66042 14.9014C5.7897 14.8084 5.90864 14.7127 6.02629 14.6144C6.03675 14.6057 6.04945 14.6001 6.06294 14.5983C6.07643 14.5965 6.09017 14.5985 6.10257 14.6041C9.96682 16.3688 14.1517 16.3688 17.9707 14.6041C17.9831 14.598 17.9971 14.5957 18.0108 14.5973C18.0246 14.5989 18.0376 14.6044 18.0483 14.6131C18.1659 14.7101 18.29 14.8084 18.4154 14.9001C18.4261 14.9075 18.4346 14.9175 18.4404 14.9291C18.4461 14.9408 18.4487 14.9536 18.4481 14.9666C18.4474 14.9795 18.4435 14.9921 18.4366 15.0031C18.4298 15.0141 18.4203 15.0231 18.409 15.0294C17.8226 15.3713 17.2085 15.6632 16.5732 15.9021C16.5632 15.9058 16.5542 15.9116 16.5467 15.9191C16.5392 15.9266 16.5334 15.9355 16.5296 15.9454C16.5258 15.9553 16.5242 15.9659 16.5248 15.9765C16.5254 15.9871 16.5282 15.9974 16.5331 16.0068C16.8858 16.6895 17.2881 17.3454 17.7367 17.9693C17.7458 17.9821 17.7586 17.9917 17.7735 17.9968C17.7883 18.0019 17.8044 18.0023 17.8194 17.9978C19.9433 17.3472 21.9405 16.339 23.7251 15.0165C23.7338 15.0102 23.7411 15.002 23.7464 14.9927C23.7517 14.9833 23.755 14.9729 23.7561 14.9622C24.2487 9.86846 22.9313 5.44313 20.2655 1.51682C20.2575 1.50493 20.2456 1.49623 20.2318 1.49226H20.2306Z"
				fill={theme === 'dark' ? 'white' : fill}
			/>
		</g>
	</g>
	<defs>
		<clipPath id="clip0_311_2416">
			<rect width="23.6147" height="18" fill="white" transform="translate(0.241089)" />
		</clipPath>
	</defs>
</svg>
