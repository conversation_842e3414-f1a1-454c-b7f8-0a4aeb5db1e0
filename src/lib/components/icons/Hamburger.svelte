<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="16"
	height="17"
	viewBox="0 0 16 17"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g
		id="&#230;&#177;&#137;&#229;&#160;&#161;&#229;&#155;&#190;&#230;&#160;&#135;_hamburger-button (4) 1"
	>
		<path
			id="Vector"
			d="M2.6499 4.48322H13.3166"
			stroke="currentColor"
			stroke-width="1.33333"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_2"
			d="M2.6499 8.48322H13.3166"
			stroke="currentColor"
			stroke-width="1.33333"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_3"
			d="M2.6499 12.4832H13.3166"
			stroke="currentColor"
			stroke-width="1.33333"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
</svg>
