<script lang="ts">
	export let className = 'size-4';
	export let theme = 'light';
</script>

<svg
	width="20"
	height="20"
	viewBox="0 0 20 20"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={className}
>
	<g opacity={theme === 'light' ? '0.4' : '0.4'}>
		<path
			d="M2.5 9.00346V16.5H17.5V9"
			stroke="currentColor"
			stroke-width="1.66667"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M13.75 8.5835L10 12.3335L6.25 8.5835"
			stroke="currentColor"
			stroke-width="1.66667"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			d="M9.99634 1.5V12.3333"
			stroke="currentColor"
			stroke-width="1.66667"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
</svg>
