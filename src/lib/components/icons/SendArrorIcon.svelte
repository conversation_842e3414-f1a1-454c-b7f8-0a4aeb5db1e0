<script>
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="16"
	height="16"
	viewBox="0 0 16 16"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="&#229;&#143;&#145;&#233;&#128;&#129;1_send-one 1" clip-path="url(#clip0_1243_3321)">
		<path
			id="Vector"
			d="M7.99946 1.50005L2.29635 13.5283L7.82816 9.72781L13.4678 13.5283L7.99946 1.50005Z"
			stroke="currentColor"
			stroke-linejoin="round"
		/>
	</g>
	<defs>
		<clipPath id="clip0_1243_3321">
			<rect width="16" height="16" fill="currentColor" />
		</clipPath>
	</defs>
</svg>
