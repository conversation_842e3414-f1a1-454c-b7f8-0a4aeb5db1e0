<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="20"
	height="20"
	viewBox="0 0 20 20"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="&#233;&#166;&#150;&#233;&#161;&#181;_home-two 1">
		<path
			id="Vector"
			d="M6.28749 18.3334H2.66602C2.11373 18.3334 1.66602 17.8857 1.66602 17.3334V9.29463C1.66602 8.68707 1.94219 8.11244 2.41663 7.7329L9.37465 2.16647C9.73987 1.8743 10.2588 1.8743 10.624 2.16647L17.5821 7.7329C18.0565 8.11244 18.3327 8.68707 18.3327 9.29463V17.3334C18.3327 17.8857 17.885 18.3334 17.3327 18.3334H14M6.28749 18.3334V18.3334C6.49655 18.3334 6.66602 18.1639 6.66602 17.9549V11.8334C6.66602 11.2811 7.11373 10.8334 7.66602 10.8334H12.3327C12.885 10.8334 13.3327 11.2811 13.3327 11.8334V17.6661C13.3327 18.0346 13.6315 18.3334 14 18.3334V18.3334M6.28749 18.3334H14"
			stroke="currentColor"
			stroke-linejoin="round"
		/>
	</g>
</svg>
