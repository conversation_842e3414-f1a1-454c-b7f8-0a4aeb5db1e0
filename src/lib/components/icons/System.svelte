<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g
		id="&#229;&#157;&#144;&#230;&#160;&#135;&#231;&#179;&#187;&#231;&#187;&#159;_coordinate-system (1) 1"
	>
		<path
			id="Vector"
			d="M10 5L15.8333 8.33333V15L10 18.3333L4.16667 15V8.33333L10 5Z"
			stroke-linejoin="round"
		/>
		<path id="Vector_2" d="M10 2.5V5" stroke-linecap="round" stroke-linejoin="round" />
		<path
			id="Vector_3"
			d="M4.16667 8.33325L10 11.6666L15.8333 8.33325"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_4"
			d="M15.8333 15L18.3333 16.25"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_5"
			d="M1.66667 16.25L4.16667 15"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path id="Vector_6" d="M10 11.6667V18.3334" stroke-linecap="round" stroke-linejoin="round" />
		<path
			id="Vector_7"
			d="M12.9167 6.66675L15.8333 8.33341V11.6667M7.08333 6.66675L4.16667 8.33341V11.6667M7.08333 16.6667L10 18.3334L12.9167 16.6667"
			stroke-linejoin="round"
		/>
	</g>
</svg>
