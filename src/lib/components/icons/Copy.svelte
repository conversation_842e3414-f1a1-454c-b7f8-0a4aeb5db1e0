<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 18 18"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g id="&#229;&#164;&#141;&#229;&#136;&#182;_copy (6) 1">
		<path
			id="Vector"
			d="M4.875 4.66161V2.92944C4.875 2.34696 5.3472 1.87476 5.92969 1.87476H15.0703C15.6528 1.87476 16.125 2.34696 16.125 2.92944V12.0701C16.125 12.6526 15.6528 13.1248 15.0703 13.1248H13.3186"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_2"
			d="M12.0703 4.87476H2.92969C2.3472 4.87476 1.875 5.34696 1.875 5.92944V15.0701C1.875 15.6526 2.3472 16.1248 2.92969 16.1248H12.0703C12.6528 16.1248 13.125 15.6526 13.125 15.0701V5.92944C13.125 5.34696 12.6528 4.87476 12.0703 4.87476Z"
			stroke-linejoin="round"
		/>
	</g>
</svg>
