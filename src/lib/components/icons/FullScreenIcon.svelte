<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="24"
	height="24"
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="&#229;&#133;&#168;&#229;&#177;&#128;&#230;&#148;&#190;&#229;&#164;&#167;_full-screen 1">
		<path
			id="Vector"
			d="M16.5 3H21V7.5"
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke="currentColor"
		/>
		<path
			id="Vector_2"
			d="M21 16.5V21H16.5"
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke="currentColor"
		/>
		<path
			id="Vector_3"
			d="M7.5 21H3V16.5"
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke="currentColor"
		/>
		<path
			id="Vector_4"
			d="M3 7.5V3H7.5"
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke="currentColor"
		/>
	</g>
</svg>
