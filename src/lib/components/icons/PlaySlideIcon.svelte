<script>
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	width="20"
	height="20"
	viewBox="0 0 20 20"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="&#229;&#185;&#187;&#231;&#129;&#175;&#231;&#137;&#135;_ppt 1">
		<path
			id="Vector"
			d="M1.66602 3.33333H18.3327"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_2"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M3.33398 3.33333H16.6673V14.1667H3.33398V3.33333Z"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_3"
			d="M9.16602 6.66666L11.2493 8.75L9.16602 10.8333"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_4"
			d="M6.66602 17.5L9.99935 14.1667L13.3327 17.5"
			stroke="currentColor"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</g>
</svg>
