<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g opacity="0.8">
		<g id="Group 16059">
			<path
				id="Vector"
				d="M18.3334 5.80005C18.3334 4.14319 16.9903 2.80005 15.3334 2.80005H4.66675C3.0099 2.80005 1.66675 4.14319 1.66675 5.80005V12.3C1.66675 13.9569 3.00989 15.3 4.66675 15.3H10.4167L14.5834 17.3834V15.3H15.3334C16.9903 15.3 18.3334 13.9569 18.3334 12.3V5.80005Z"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
			<path
				id="Vector_2"
				d="M5.41675 9.8833C5.41675 9.8833 7.08341 11.55 10.0001 11.55C12.9167 11.55 14.5834 9.8833 14.5834 9.8833"
				stroke-linecap="round"
				stroke-linejoin="round"
			/>
		</g>
	</g>
</svg>
