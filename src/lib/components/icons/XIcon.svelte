<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
	export let theme = 'light';
</script>

<svg
	class={className}
	width="19"
	height="18"
	viewBox="0 0 19 18"
	stroke-width={strokeWidth}
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g opacity={0.8}>
		<path
			d="M14.4443 0.875H17.201L11.1785 7.75833L18.2635 17.125H12.716L8.37101 11.4442L3.39934 17.125H0.641011L7.08268 9.7625L0.286011 0.875H5.97434L9.90184 6.0675L14.4443 0.875ZM13.4768 15.475H15.0043L5.14434 2.43833H3.50518L13.4768 15.475Z"
			fill="currentColor"
		/>
	</g>
</svg>
