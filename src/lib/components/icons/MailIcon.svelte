<script lang="ts">
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
	export let theme = 'light';
</script>

<svg
	class={className}
	stroke-width={strokeWidth}
	viewBox="0 0 1024 1024"
	version="1.1"
	xmlns="http://www.w3.org/2000/svg"
	width="16"
	height="16"
	><path
		d="M853.333333 341.333333 512 554.666667 170.666667 341.333333 170.666667 256 512 469.333333 853.333333 256M853.333333 170.666667 170.666667 170.666667C123.306667 170.666667 85.333333 208.64 85.333333 256L85.333333 768C85.333333 814.933333 123.733333 853.333333 170.666667 853.333333L853.333333 853.333333C900.266667 853.333333 938.666667 814.933333 938.666667 768L938.666667 256C938.666667 208.64 900.266667 170.666667 853.333333 170.666667Z"
		fill="currentColor"
	></path></svg
>
