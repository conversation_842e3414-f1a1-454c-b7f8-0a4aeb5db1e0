<script lang="ts">
	export let className = 'size-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 20 20"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className}
>
	<g id="&#228;&#191;&#161;&#230;&#129;&#175;_info (1) 1" clip-path="url(#clip0_34_1458)">
		<path
			id="Vector"
			d="M9.99996 18.3337C12.3011 18.3337 14.3845 17.4009 15.8925 15.8929C17.4005 14.3848 18.3333 12.3015 18.3333 10.0003C18.3333 7.69916 17.4005 5.61583 15.8925 4.10777C14.3845 2.59973 12.3011 1.66699 9.99996 1.66699C7.69879 1.66699 5.61546 2.59973 4.1074 4.10777C2.59937 5.61583 1.66663 7.69916 1.66663 10.0003C1.66663 12.3015 2.59937 14.3848 4.1074 15.8929C5.61546 17.4009 7.69879 18.3337 9.99996 18.3337Z"
			stroke-linejoin="round"
		/>
		<path
			id="Vector_2"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M10 4.58301C10.5753 4.58301 11.0417 5.04938 11.0417 5.62467C11.0417 6.19997 10.5753 6.66634 10 6.66634C9.42475 6.66634 8.95837 6.19997 8.95837 5.62467C8.95837 5.04938 9.42475 4.58301 10 4.58301Z"
			fill="transparent"
		/>
		<path
			id="Vector_3"
			d="M10.2083 14.1663V8.33301H9.79167H9.375"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
		<path id="Vector_4" d="M8.75 14.167H11.6667" stroke-linecap="round" stroke-linejoin="round" />
	</g>
	<defs>
		<clipPath id="clip0_34_1458">
			<rect width="20" height="20" fill="transparent" />
		</clipPath>
	</defs>
</svg>
