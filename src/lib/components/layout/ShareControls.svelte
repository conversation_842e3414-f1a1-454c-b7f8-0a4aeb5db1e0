<script lang="ts">
	import { SvelteFlowProvider } from '@xyflow/svelte';
	import { Pane, PaneResizer } from 'paneforge';

	import { onDestroy, onMount } from 'svelte';
	import { showControls } from '$lib/stores';

	import Drawer from '../common/Drawer.svelte';
	import EllipsisVertical from '../icons/EllipsisVertical.svelte';
	import Artifacts from '../chat/Artifacts.svelte';
	import McpSidePanel from '../chat/MCPSidePanel.svelte';
	import SearchResultPanel from '../chat/SearchResultPanel.svelte';
	import SharePpt from '../share/SharePPT.svelte';

	export let history;

	export let chatId = null;
	export let pane;

	let mediaQuery;
	let largeScreen = false;
	let dragged = false;
	let dragging = false;

	let minSize = 0;

	export const openPane = () => {
		if (parseInt(localStorage?.chatControlsSize)) {
			pane.resize(parseInt(localStorage?.chatControlsSize));
		} else {
			pane.resize(50); // 默认大小
		}
	};

	const handleMediaQuery = async (e) => {
		if (e.matches) {
			largeScreen = true;
		} else {
			largeScreen = false;
			pane = null;
		}
	};

	const onMouseDown = (event) => {
		dragged = true;
	};

	const onMouseUp = (event) => {
		dragged = false;
	};

	onMount(() => {
		// listen to resize 1024px
		mediaQuery = window.matchMedia('(min-width: 1024px)');

		mediaQuery.addEventListener('change', handleMediaQuery);
		handleMediaQuery(mediaQuery);

		// Select the container element you want to observe
		const container = document.getElementById('share-container');
		if (!container) return;

		// initialize the minSize based on the container width
		minSize = Math.floor((350 / container.clientWidth) * 100);

		// Create a new ResizeObserver instance
		const resizeObserver = new ResizeObserver((entries) => {
			for (let entry of entries) {
				const width = entry.contentRect.width;
				// calculate the percentage of 200px
				const percentage = (350 / width) * 100;
				// set the minSize to the percentage, must be an integer
				minSize = Math.floor(percentage);

				if ($showControls) {
					if (pane && pane.isExpanded() && pane.getSize() < minSize) {
						pane.resize(minSize);
					}
				}
			}
		});

		// Start observing the container's size changes
		resizeObserver.observe(container);

		document.addEventListener('mousedown', onMouseDown);
		document.addEventListener('mouseup', onMouseUp);
	});

	onDestroy(() => {
		showControls.set('');
		mediaQuery.removeEventListener('change', handleMediaQuery);
		document.removeEventListener('mousedown', onMouseDown);
		document.removeEventListener('mouseup', onMouseUp);
	});

	const closeHandler = () => {
		showControls.set('');
	};

	$: if (!chatId) {
		closeHandler();
	}
</script>

<SvelteFlowProvider>
	{#if !largeScreen}
		{#if $showControls}
			<Drawer className="rounded-none" show={Boolean($showControls)} on:close={() => {}}>
				<div class=" {$showControls ? ' h-screen  w-full' : 'px-6 py-4'} h-full">
					{#if $showControls === 'artifacts'}
						<Artifacts {history} showDeploy={false} />
					{:else if $showControls === 'mcp'}
						<McpSidePanel {history} />
					{:else if $showControls === 'ppt'}
						<SharePpt />
					{:else if $showControls === 'search'}
						<SearchResultPanel />
					{:else}
						{(() => {
							showControls.set('');
						})()}
					{/if}
				</div>
			</Drawer>
		{/if}
	{:else}
		<!-- if $showControls -->

		{#if $showControls}
			<PaneResizer
				onDraggingChange={(v) => {
					dragging = v;
				}}
				class="relative flex w-2 items-center justify-center bg-background group"
			>
				<div class="z-10 flex h-7 w-5 items-center justify-center rounded-xl">
					<EllipsisVertical
						className="size-4 animate-pulse hover:animate-[pulse_0.2s_ease-in-out_infinite]"
					/>
				</div>
			</PaneResizer>
		{/if}

		<Pane
			bind:pane
			defaultSize={0}
			onResize={(size) => {
				// console.log('size', size, minSize);

				if ($showControls && pane.isExpanded()) {
					if (size < minSize) {
						pane.resize(minSize);
					}

					if (size < minSize) {
						localStorage.chatControlsSize = 0;
					} else {
						localStorage.chatControlsSize = size;
					}
				}
			}}
			onCollapse={() => {
				showControls.set('');
			}}
			collapsible={true}
			class="shadow-[0_0_16px_0_#0000000A]"
		>
			{#if $showControls}
				<div class="flex max-h-full min-h-full relative">
					<div
						class="w-full {$showControls
							? ''
							: 'px-4 py-4 bg-white dark:shadow-lg dark:bg-gray-850'}   z-40 pointer-events-auto overflow-y-auto scrollbar-hidden"
					>
						{#if $showControls === 'artifacts'}
							<Artifacts {history} overlay={dragged} showDeploy={false} />
						{:else if $showControls === 'mcp'}
							<McpSidePanel {history} />
						{:else if $showControls === 'ppt'}
							<SharePpt />
						{:else if $showControls === 'search'}
							<SearchResultPanel />
						{:else}
							{(() => {
								showControls.set('');
							})()}
						{/if}
					</div>
					{#if dragging}
						<div
							class="draggingOverlay absolute top-0 left-0 z-999 h-full w-full pointer-events-auto"
						/>
					{/if}
				</div>
			{/if}
		</Pane>
	{/if}
</SvelteFlowProvider>
