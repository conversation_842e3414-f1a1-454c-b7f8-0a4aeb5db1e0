<script lang="ts">
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import {
		showNotifySignInModal,
		NotifySignInModelType,
		SignInModalType,
		chatId,
		chatTitle,
		showCallOverlay,
		showOverview,
		showArtifacts,
		SignInModalSource
	} from '$lib/stores';
	import Modal from '../common/Modal.svelte';
	import { userSignOut } from '$lib/apis/auths';
	import { goto } from '$app/navigation';
	import XMark from '../icons/XMark.svelte';

	const i18n = getContext<Writable<i18nType>>('i18n');

	const headerContentMap = {
		[SignInModalType.NewChat]: 'Clear Current Chat?',
		[SignInModalType.History]: 'Access Your Chat History.',
		[SignInModalType.Share]: 'Ready To Share?',
		[SignInModalType.FileUpload]: 'Unlock Your Insights?'
	};

	const subContentMap = {
		[SignInModalType.NewChat]:
			'To start a new chat, your current conversation will be discarded. Sign in to save chats',
		[SignInModalType.History]:
			'Sign in to unlock chat history and revisit past conversations anytime',
		[SignInModalType.Share]: 'Sign in to share your conversation and enjoy the full experience',
		[SignInModalType.FileUpload]:
			'Sign in to analyze your files, helping you discover key insights and truly understand your content'
	};

	const redirect = async () => {
		location.replace('/auth');
	};

	const signUp = () => {
		$showNotifySignInModal = false;
		location.replace('/auth?action=signup');
	};

	const stayLoggedOut = () => {
		$showNotifySignInModal = false;
		if ($NotifySignInModelType[0] === SignInModalType.NewChat) {
			chatId.set('');
			chatTitle.set('');
			showCallOverlay.set(false);
			showOverview.set(false);
			showArtifacts.set(false);

			goto('/');
		}
	};

	const keepInPlace = () => {
		$showNotifySignInModal = false;
	};
</script>

<Modal bind:show={$showNotifySignInModal} size="sm">
	<div class="flex flex-col justify-center p-8 md:p-10 relative">
		<button
			on:click={keepInPlace}
			class="absolute top-7 right-7 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
			aria-label="Close"
		>
			<XMark className="size-6" />
		</button>

		<div
			class="text-center text-4xl font-bold text-black dark:text-white text-gradient mb-2"
			style="line-height: 40px"
		>
			{$i18n.t(headerContentMap[$NotifySignInModelType[0]])}
		</div>
		<div>
			<div
				class="text-center text-black dark:text-white text-sm font-normal mb-6 desc"
				style="line-height: 24px; opacity: 0.6"
			>
				{$i18n.t(subContentMap[$NotifySignInModelType[0]])}
			</div>
		</div>
		<div class="flex flex-col gap-3">
			<button
				on:click={redirect}
				class="w-full py-2 text-center button-gradient text-white hover:bg-primary-hover transition rounded-lg signin-btn-primary h-10 md:h-12"
			>
				{$i18n.t('Sign in')}
			</button>
			<button
				on:click={stayLoggedOut}
				class="flex justify-center button-secondary items-center dark:text-gray-300 dark:hover:text-white transition w-full rounded-lg font-medium text-sm py-2.5 h-10 md:h-12"
			>
				{$NotifySignInModelType[1] === SignInModalSource.Chat
					? $i18n.t('Clear chat')
					: $i18n.t('Stay logged out')}
			</button>
		</div>
	</div>
</Modal>

<style>
	.text-gradient {
		background: linear-gradient(160.13deg, #191a1d -3.97%, #747689 107.25%, #191a1d 218.47%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.button-secondary {
		border: 1px solid rgba(0, 0, 0, 0.1);
		color: rgba(0, 0, 0, 1);
		opacity: 0.8;
	}
	:global(.dark) .button-secondary {
		border-color: rgba(255, 255, 255, 0.1);
		color: rgba(255, 255, 255, 1);
	}

	:global(.dark) .signin-btn-primary {
		background-color: rgba(72, 74, 88, 1);
	}

	:global(.dark) .text-gradient {
		background: linear-gradient(136.4deg, #ffffff -13.32%, #acaebd 58.94%, #ffffff 150.59%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.desc {
		font-weight: 400;
		font-size: 14px;
		line-height: 24px;
		letter-spacing: 0%;
		text-align: center;
		vertical-align: bottom;
		/* font-family: MiSans; */
		padding-left: 8px;
		padding-right: 8px;
	}
	:global(.dark) .desc {
		color: rgba(255, 255, 255, 1);
	}
</style>
