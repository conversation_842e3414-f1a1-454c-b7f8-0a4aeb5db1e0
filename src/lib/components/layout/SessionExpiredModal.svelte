<script lang="ts">
	import { getContext } from 'svelte';
	import { showSessionExpiredModal, user, userTheme } from '$lib/stores';
	import Modal from '../common/Modal.svelte';
	import { userSignOut } from '$lib/apis/auths';
	
	const i18n = getContext('i18n');
	
	let second = 5;
	let timer: number | null = null;
	$: if ($showSessionExpiredModal && !timer) {
		timer = window.setInterval(() => {
			second--;
			if (second === 0) {
				redirect();
			}
		}, 1000);
	}
	
	const redirect = async () => {
		await userSignOut();
		showSessionExpiredModal.set(false);
		timer && window.clearInterval(timer);
		timer = null;
		second = 5;
		user.set(undefined);
		localStorage.removeItem('token');
		location.replace('/auth');
	};
</script>

<Modal bind:show={$showSessionExpiredModal} size="sm">
	<div class=" flex flex-col gap-6 justify-center p-6">
		<div class=" self-center">
			<img
				class=" size-[79px]"
				src="/static/{$userTheme === 'light' ? 'loginLight.png' : 'loginDark.png'}"
				alt="logo"
			/>
		</div>
		<div>
			<div class="text-center text-black dark:text-white">
				{$i18n.t('Login session expired. Please sign in again.')}
			</div>
		</div>
		<div class="flex flex-col gap-3">
			<button
				on:click={redirect}
				class="w-full py-2 text-center button-gradient text-white hover:bg-primary-hover transition rounded-lg"
			>
				{$i18n.t('Redirecting in {{second}} seconds...', { second })}
			</button>
			<!-- <a
				on:click={() => ($showSessionExpiredModal = false)}
				href="/auth?action=signup"
				class="w-full py-2 text-center text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition rounded-xl"
			>
				{$i18n.t('Sign up')}
			</a> -->
			<button
				on:click={() => {
					timer && window.clearInterval(timer);
					timer = null;
					second = 5;
					$showSessionExpiredModal = false;
				}}
				class="flex justify-center items-center dark:text-gray-300 dark:hover:text-white transition w-full rounded-lg border-1 border-black/10 dark:border-white/10 font-medium text-sm py-2.5"
			>
				{$i18n.t('Skip for now')}
			</button>
		</div>
	</div>
</Modal>
