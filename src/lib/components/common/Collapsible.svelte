<script lang="ts">
	import { decode } from 'html-entities';
	import { v4 as uuidv4 } from 'uuid';

	import { getContext, createEventDispatcher } from 'svelte';
	const i18n = getContext('i18n');

	import dayjs from '$lib/dayjs';
	import duration from 'dayjs/plugin/duration';
	import relativeTime from 'dayjs/plugin/relativeTime';

	dayjs.extend(duration);
	dayjs.extend(relativeTime);

	async function loadLocale(locales) {
		for (const locale of locales) {
			try {
				dayjs.locale(locale);
				break; // Stop after successfully loading the first available locale
			} catch (error) {
				console.error(`Could not load locale '${locale}':`, error);
			}
		}
	}

	// Assuming $i18n.languages is an array of language codes
	$: loadLocale($i18n.languages);

	const dispatch = createEventDispatcher();
	$: dispatch('change', open);

	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	import ChevronUp from '../icons/ChevronUp.svelte';
	import ChevronDown from '../icons/ChevronDown.svelte';
	import Spinner from './Spinner.svelte';
	import CodeBlock from '../chat/Messages/CodeBlock.svelte';
	import Markdown from '../chat/Messages/Markdown.svelte';
	import Image from './Image.svelte';
	import { userTheme } from '$lib/stores';

	export let className = '';
	export let buttonClassName =
		' text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition';

	export let id = '';
	export let title = null;
	export let attributes = null;
	export let open = false;
	// $: if (attributes?.done == undefined) {
	// 	open = true;
	// }

	export let chevron = false;
	export let grow = false;

	export let disabled = false;
	export let hide = false;
	export let messageDone: boolean = false;

	const collapsibleId = uuidv4();

	function parseJSONString(str) {
		try {
			return parseJSONString(JSON.parse(str));
		} catch (e) {
			return str;
		}
	}

	function formatJSONString(str) {
		try {
			const parsed = parseJSONString(str);
			// If parsed is an object/array, then it's valid JSON
			if (typeof parsed === 'object') {
				return JSON.stringify(parsed, null, 2);
			} else {
				// It's a primitive value like a number, boolean, etc.
				return String(parsed);
			}
		} catch (e) {
			// Not valid JSON, return as-is
			return str;
		}
	}
</script>

<div {id} class={className}>
	{#if title !== null}
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div
			class="{buttonClassName} cursor-pointer"
			on:pointerup={() => {
				if (!disabled) {
					open = !open;
				}
			}}
		>
			<div class=" text-sm -ml-1.5 flex items-center gap-2">
				{#if !messageDone && attributes?.done && attributes?.done !== 'true'}
					<div>
						<Spinner className="size-4" />
					</div>
				{:else}
					<div>
						<img
							class="size-4"
							src={$userTheme === 'light'
								? '/static/thinkingLight.svg'
								: '/static/thinkingDark.svg'}
							alt="thinking"
						/>
					</div>
				{/if}

				<div
					class=" flex gap-2 item-center rounded-lg py-2 px-3 bg-white dark:bg-[#26282A] text-black dark:text-white"
				>
					{#if attributes?.type === 'reasoning'}
						{#if attributes?.done === 'true' && attributes?.duration}
							{#if Number(attributes.duration) < 60}
								{$i18n.t('Thought for {{DURATION}} seconds', {
									DURATION: Number(attributes.duration) === 0 ? 1 : Number(attributes.duration)
								})}
							{:else}
								{$i18n.t('Thought for {{DURATION}}', {
									DURATION: dayjs
										.duration(
											Number(attributes.duration) === 0 ? 1 : Number(attributes.duration),
											'seconds'
										)
										.humanize()
								})}
							{/if}
						{:else if messageDone}
							{$i18n.t('Thinking stopped')}
						{:else}
							<span class="shimmer">
								{$i18n.t('Thinking...')}
							</span>
						{/if}
					{:else if attributes?.type === 'code_interpreter'}
						{#if attributes?.done === 'true'}
							{$i18n.t('Analyzed')}
						{:else}
							{$i18n.t('Analyzing...')}
						{/if}
					{:else if attributes?.type === 'tool_calls'}
						{#if attributes?.done === 'true'}
							<Markdown
								{messageDone}
								id={`${collapsibleId}-tool-calls-${attributes?.id}`}
								content={$i18n.t('View Result from **{{NAME}}** - {{DURATION}}', {
									NAME: attributes.name,
									DURATION: attributes.duration
								})}
							/>
						{:else}
							<Markdown
								{messageDone}
								id={`${collapsibleId}-tool-calls-${attributes?.id}-executing`}
								content={$i18n.t('Executing **{{NAME}}** {{DURATION}}...', {
									NAME: attributes.name,
									DURATION: attributes.duration
								})}
							/>
						{/if}
					{:else}
						{title}
					{/if}
					<div class="flex self-center translate-y-[1px]">
						{#if open}
							<ChevronUp strokeWidth="3" className="size-3" />
						{:else}
							<ChevronDown strokeWidth="3" className="size-3" />
						{/if}
					</div>
				</div>
			</div>
		</div>
	{:else}
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div
			class="{buttonClassName} cursor-pointer"
			on:pointerup={() => {
				if (!disabled) {
					open = !open;
				}
			}}
		>
			<div>
				<div class="flex items-start justify-between">
					<slot />

					{#if chevron}
						<div class="flex self-start translate-y-1">
							{#if open}
								<ChevronUp strokeWidth="3.5" className="size-3.5" />
							{:else}
								<ChevronDown strokeWidth="3.5" className="size-3.5" />
							{/if}
						</div>
					{/if}
				</div>

				{#if grow}
					{#if open && !hide}
						<div
							on:pointerup={(e) => {
								e.stopPropagation();
							}}
						>
							<slot name="content" />
						</div>
					{/if}
				{/if}
			</div>
		</div>
	{/if}

	{#if attributes?.type === 'tool_calls'}
		{@const args = decode(attributes?.arguments)}
		{@const result = decode(attributes?.result ?? '')}
		{@const files = parseJSONString(decode(attributes?.files ?? ''))}

		{#if !grow}
			{#if open && !hide}
				<div>
					{#if attributes?.done === 'true'}
						<!-- FIXME: formatJSONString 有 bug，reuslt 序列化结果有重复字段 -->
						<!-- FIXME: result quote 格式问题 + 区分 result 是不是 json，决定不同渲染样式 @zichen -->
						<Markdown
							{messageDone}
							id={`${collapsibleId}-tool-calls-${attributes?.id}-result`}
							content={`> **Reqeust**
> \`\`\`json
> ${formatJSONString(args)}
> \`\`\`
> **Response**
> \`\`\`json
${formatJSONString(result)
	.split('\n')
	.map((l) => '> ' + l)
	.join('\n')}
> \`\`\``}
						/>
					{:else}
						<Markdown
							{messageDone}
							id={`${collapsibleId}-tool-calls-${attributes?.id}-result`}
							content={`> **Request**
> \`\`\`json
> ${formatJSONString(args)}
> \`\`\``}
						/>
					{/if}
				</div>
			{/if}

			{#if attributes?.done === 'true'}
				{#if typeof files === 'object'}
					{#each files ?? [] as file, idx}
						{#if file.startsWith('data:image/')}
							<Image
								id={`${collapsibleId}-tool-calls-${attributes?.id}-result-${idx}`}
								src={file}
								alt="Image"
							/>
						{/if}
					{/each}
				{/if}
			{/if}
		{/if}
	{:else if !grow}
		{#if open && !hide}
			<div>
				<slot name="content" />
			</div>
		{/if}
	{/if}
</div>
