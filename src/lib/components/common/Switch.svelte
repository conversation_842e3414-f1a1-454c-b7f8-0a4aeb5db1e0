<script lang="ts">
	import { createEventDispatcher, tick } from 'svelte';
	import { Switch } from 'bits-ui';
	export let state = true;

	const dispatch = createEventDispatcher();

	$: dispatch('change', state);
</script>

<Switch.Root
	bind:checked={state}
	class="flex h-5.5 min-h-5 w-11 shrink-0 cursor-pointer items-center rounded-full px-[3px] mx-[1px] transition  {state
		? ' bg-black/80 dark:bg-white/60'
		: 'bg-black/10 dark:bg-white/15'} outline outline-gray-100 dark:outline-gray-800"
>
	<Switch.Thumb
		class="pointer-events-none block size-4 shrink-0 rounded-full bg-white transition-transform data-[state=checked]:translate-x-5.5 data-[state=unchecked]:translate-x-0 data-[state=unchecked]:shadow-mini "
	/>
</Switch.Root>
