<script lang="ts">
	import { Tabs } from 'bits-ui';
	import { twMerge } from 'tailwind-merge';

	type TabItem = {
		value: string;
		label: string;
	};

	type ClassNames = {
		root?: string;
		trigger?: string;
		list?: string;
	};

	export let classNames: ClassNames = {
		root: '',
		trigger: '',
		list: ''
	};
	export let value = '';
	export let items: TabItem[] = [];
	export let onValueChange: (value: string | undefined) => void;

	$: _classNames = {
		root: '',
		trigger: '',
		list: '',
		...classNames
	};
</script>

<div>
	<Tabs.Root bind:value {onValueChange} class={twMerge('rounded-full', _classNames.root)}>
		<Tabs.List
			class={twMerge(
				'flex gap-1 rounded-full bg-white p-0.5 dark:bg-[#16181A] text-nowrap',
				_classNames.list
			)}
		>
			{#each items as item, key (item.value)}
				<Tabs.Trigger
					value={item.value}
					class={twMerge(
						' rounded-full min-w-16 bg-transparent px-4 py-1.5 data-[state=active]:bg-black dark:data-[state=active]:bg-white data-[state=active]:text-white dark:data-[state=active]:text-black font-semibold hover:bg-black/5 dark:hover:bg-white/5 transition text-sm text-black/40 dark:text-white/40',
						_classNames.trigger
					)}>{item.label}</Tabs.Trigger
				>
			{/each}
		</Tabs.List>
	</Tabs.Root>
</div>
