<script lang="ts">
	import fileSaver from 'file-saver';
	const { saveAs } = fileSaver;

	import { toast } from 'svelte-sonner';

	import panzoom, { type PanZoom } from 'panzoom';
	import DOMPurify from 'dompurify';

	import { onMount, getContext } from 'svelte';
	const i18n = getContext('i18n');

	import { copyToClipboard } from '$lib/utils';

	import DocumentDuplicate from '../icons/DocumentDuplicate.svelte';
	import Tooltip from './Tooltip.svelte';
	import Clipboard from '../icons/Clipboard.svelte';
	import Reset from '../icons/Reset.svelte';
	import ArrowDownTray from '../icons/ArrowDownTray.svelte';

	export let className = '';
	export let svg = '';
	export let content = '';

	let instance: PanZoom;

	let sceneParentElement: HTMLElement;
	let sceneElement: HTMLElement;
	let svgElement: SVGElement | null;

	// 在组件挂载后自动缩放SVG
	onMount(() => {
		// 使用setTimeout确保DOM元素已完全渲染
		setTimeout(() => {
			fitSvgToContainer();
		}, 200);
	});

	// 初始化panzoom实例
	$: if (sceneElement) {
		instance = panzoom(sceneElement, {
			bounds: true,
			boundsPadding: 0.1,
			zoomSpeed: 0.065,
			smoothScroll: false,
			initialZoom: 1
		});
	}

	// 自动缩放SVG以适应容器大小
	const fitSvgToContainer = () => {
		if (!sceneElement || !sceneParentElement || !instance) return;

		try {
			// 获取SVG元素 - 确保正确选择
			svgElement = sceneElement.querySelector('svg');
			if (!svgElement) return;

			// 修复SVG视图框
			if (
				!svgElement.getAttribute('viewBox') &&
				svgElement.getAttribute('width') &&
				svgElement.getAttribute('height')
			) {
				const width = svgElement.getAttribute('width');
				const height = svgElement.getAttribute('height');
				svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
			}

			// 设置SVG样式确保可见性
			svgElement.style.maxWidth = '100%';
			svgElement.style.height = 'auto';
			svgElement.style.display = 'block';

			// 重置transform以获取准确的尺寸
			instance.moveTo(0, 0);
			instance.zoomAbs(0, 0, 1);

			// 等待重绘
			setTimeout(() => {
				// 获取容器和SVG的尺寸
				const containerWidth = sceneParentElement.clientWidth;
				const containerHeight = sceneParentElement.clientHeight;

				// 添加空值检查
				if (!svgElement) return;

				const svgRect = svgElement.getBoundingClientRect();
				const svgWidth = svgRect.width;
				const svgHeight = svgRect.height;

				console.log('原始尺寸', {
					svgWidth,
					svgHeight,
					containerWidth,
					containerHeight
				});

				// 计算缩放比例
				let scale = 1;
				if (svgWidth > containerWidth * 0.95) {
					// 如果SVG宽度接近或超过容器宽度
					scale = (containerWidth * 0.95) / svgWidth;
				}

				// 计算水平居中位置
				// const translateX = Math.max(0, (containerWidth - (svgWidth * scale)) / 2) / scale;
				const translateX = instance.getTransform().x;

				// 计算垂直居中位置 - 不再固定在顶部，而是居中
				const translateY = Math.max(0, (containerHeight - svgHeight * scale) / 2) / scale;

				// 应用缩放和位置
				instance.moveTo(0, 0); // 先重置位置
				instance.zoomAbs(0, 0, scale);
				instance.moveTo(translateX, translateY);

				console.log('自动缩放SVG居中', {
					containerWidth,
					containerHeight,
					svgWidth,
					svgHeight,
					scale,
					translateX,
					translateY
				});
			}, 50);
		} catch (error) {
			console.error('自动缩放SVG时出错:', error);
		}
	};

	const resetPanZoomViewport = () => {
		fitSvgToContainer();
	};

	const downloadAsSVG = () => {
		const svgBlob = new Blob([svg], { type: 'image/svg+xml' });
		saveAs(svgBlob, `diagram.svg`);
	};
</script>

<div bind:this={sceneParentElement} class="relative flex-1 overflow-hidden {className}">
	<div bind:this={sceneElement} class="svgCanvas justify-items-center-safe overflow-visible">
		{@html svg}
	</div>

	{#if content}
		<div class="absolute top-1 right-1">
			<div class="flex gap-1">
				<Tooltip content={$i18n.t('Download as SVG')}>
					<button
						class="p-1.5 rounded-lg border border-gray-100 dark:border-none dark:bg-gray-850 hover:bg-gray-50 dark:hover:bg-gray-800 transition"
						on:click={() => {
							downloadAsSVG();
						}}
					>
						<ArrowDownTray className="size-4" />
					</button>
				</Tooltip>

				<Tooltip content={$i18n.t('Reset view')}>
					<button
						class="p-1.5 rounded-lg border border-gray-100 dark:border-none dark:bg-gray-850 hover:bg-gray-50 dark:hover:bg-gray-800 transition"
						on:click={() => {
							resetPanZoomViewport();
						}}
					>
						<Reset className="size-4" />
					</button>
				</Tooltip>

				<Tooltip content={$i18n.t('Copy to clipboard')}>
					<button
						class="p-1.5 rounded-lg border border-gray-100 dark:border-none dark:bg-gray-850 hover:bg-gray-50 dark:hover:bg-gray-800 transition"
						on:click={() => {
							copyToClipboard(content);
							toast.success($i18n.t('Copied to clipboard'));
						}}
					>
						<Clipboard className="size-4" strokeWidth="1.5" />
					</button>
				</Tooltip>
			</div>
		</div>
	{/if}
</div>
