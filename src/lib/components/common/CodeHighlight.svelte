<script lang="ts">
	import { userTheme } from '$lib/stores';
	import Highlight, { LineNumbers, HighlightAuto } from 'svelte-highlight';
	import * as supportedLangs from 'svelte-highlight/languages';
	import OneDark from 'svelte-highlight/styles/atom-one-dark';
	import LightTheme from 'svelte-highlight/styles/solar-flare-light';

	export let language: string = '';
	export let code: string = '';
	export let autoScroll: boolean = false;

	let _code = code;
	let containerRef: HTMLElement;
	$: codeUpdateEffect(code);
	function codeUpdateEffect(code: string) {
		if (code !== _code) {
			_code = code;
			// 滚动到最底部
			containerRef && autoScroll && (containerRef.scrollTop = containerRef.scrollHeight);
		}
	}
</script>

<svelte:head>
	{#if $userTheme === 'dark'}
		{@html OneDark}
	{:else}
		{@html LightTheme}
	{/if}
</svelte:head>

<div
	class=" highlight overflow-y-auto text-nowrap text-sm bg-[#F5F7FA] dark:bg-[#282C34] w-full h-full"
	bind:this={containerRef}
>
	<HighlightAuto {code} let:highlighted>
		<LineNumbers {highlighted} wrapLines={false} />
	</HighlightAuto>
</div>
