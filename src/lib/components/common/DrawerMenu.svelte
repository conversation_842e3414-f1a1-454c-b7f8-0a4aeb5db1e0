<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';

	const rounded = {
		top: 'rounded-b-2xl',
		bottom: 'rounded-t-2xl',
		left: '',
		right: ''
	};

	export let open = false;
	export let position: 'left' | 'right' | 'top' | 'bottom' = 'bottom';
	export let onShow: (value: boolean) => void = () => {};
	export let height = '73%';

	let drawer: HTMLElement;
	let portal: HTMLElement;

	// 处理遮罩层点击
	function handleOverlayClick() {
		onShow(false);
	}

	// 根据position设置抽屉样式
	$: drawerStyle = {
		left: position === 'left' ? '0' : 'auto',
		right: position === 'right' ? '0' : 'auto',
		top: ['top', 'left', 'right'].includes(position) ? '0' : 'auto',
		bottom: ['bottom', 'left', 'right'].includes(position) ? '0' : 'auto',
		width: position === 'left' || position === 'right' ? '75%' : '100vw',
		height: position === 'top' || position === 'bottom' ? height : '100vh',
		transform: open ? 'none' : getTransform(position)
	};

	// 获取transform样式
	function getTransform(pos: string): string {
		switch (pos) {
			case 'left':
				return 'translateX(-150%)';
			case 'right':
				return 'translateX(150%)';
			case 'top':
				return 'translateY(-150%)';
			case 'bottom':
				return 'translateY(150%)';
			default:
				return '';
		}
	}

	// 挂载时创建portal
	onMount(() => {
		portal = document.createElement('div');
		portal.className = 'drawer-portal';
		document.body.appendChild(portal);
		portal.appendChild(drawer);

		return () => {
			portal.remove();
		};
	});
</script>

{#if open}
	<div
		class="fixed inset-0 z-999 bg-black/50 transition-opacity"
		on:click={handleOverlayClick}
		transition:fade={{ duration: 200 }}
	/>
{/if}

<div
	bind:this={drawer}
	class="fixed z-9999 bg-white dark:bg-gray-850 shadow-lg transition-transform duration-300 ease-in-out {rounded[
		position
	]} "
	style={Object.entries(drawerStyle)
		.map(([key, value]) => `${key}: ${value}`)
		.join(';')}
>
	{#if open}
		<slot />
	{/if}
</div>

<style>
	:global(.drawer-portal) {
		position: relative;
		z-index: 999;
	}
</style>
