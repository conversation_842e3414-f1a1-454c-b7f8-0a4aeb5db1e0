<script lang="ts">
	import { decode } from 'html-entities';
	import { v4 as uuidv4 } from 'uuid';

	import { getContext, createEventDispatcher, onMount, onDestroy } from 'svelte';
	const i18n: Writable<i18n> = getContext('i18n');

	import dayjs from '$lib/dayjs';
	import duration from 'dayjs/plugin/duration';
	import relativeTime from 'dayjs/plugin/relativeTime';

	dayjs.extend(duration);
	dayjs.extend(relativeTime);

	async function loadLocale(locales) {
		for (const locale of locales) {
			try {
				dayjs.locale(locale);
				break; // Stop after successfully loading the first available locale
			} catch (error) {
				console.error(`Could not load locale '${locale}':`, error);
			}
		}
	}

	// Assuming $i18n.languages is an array of language codes
	$: loadLocale($i18n.languages);

	const dispatch = createEventDispatcher();
	$: dispatch('change', open);

	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	import ChevronUp from '../icons/ChevronUp.svelte';
	import ChevronDown from '../icons/ChevronDown.svelte';
	import Spinner from './Spinner.svelte';
	import CodeBlock from '../chat/Messages/CodeBlock.svelte';
	import Markdown from '../chat/Messages/Markdown.svelte';
	import Image from './Image.svelte';
	import { userTheme } from '$lib/stores';
	import { twMerge } from 'tailwind-merge';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import { markdownToText } from '$lib/utils';
	import TimerIcon from '../icons/TimerIcon.svelte';

	export let className = '';
	export let buttonClassName = 'transition';

	export let id = '';
	export let title = null;
	export let attributes = null;
	export let open = false;
	export let chevron = false;
	export let grow = false;
	export let text: string = '';

	export let disabled = false;
	export let hide = false;
	export let messageDone: boolean = false;
	export let group: boolean = false;
	const collapsibleId = uuidv4();

	// 用于DOM变化监听的MutationObserver
	let detailsObserver: MutationObserver | null;
	let detailsContainer: HTMLElement;
	// 控制渐变阴影的显示
	let showHeadShadow = false;
	let showTailShadow = true;
	let userOpen = false;

	$: done = attributes?.done;
	$: if (done === 'true') {
		open = false;
		// 当done为true时，注销MutationObserver
		if (detailsObserver) {
			detailsObserver.disconnect();
			detailsObserver = null;
		}
	} else {
		open = true;
	}

	// 当open状态变化时，处理观察器
	$: if (open && !hide && done !== 'true') {
		// 在下一个tick中设置观察器，确保DOM已更新
		setTimeout(() => {
			setupObserver();
		}, 0);
	} else if (done === 'true') {
		// 关闭时注销观察器
		detailsObserver?.disconnect();
		detailsObserver = null;
	}

	// 设置MutationObserver来监听内容变化
	function setupObserver() {
		if (!detailsContainer || detailsObserver || done === 'true') return;

		detailsObserver = new MutationObserver(() => {
			// 内容变化时滚动到底部
			if (detailsContainer) {
				// detailsContainer.scrollTop = detailsContainer.scrollHeight;
				detailsContainer.scrollTo({ top: detailsContainer.scrollHeight, behavior: 'smooth' });
				// 更新阴影状态
				updateShadows();
			}
		});

		// 配置观察选项
		// characterData 触发太频繁，先干掉
		const config = { childList: true, subtree: true };
		// 开始观察
		detailsObserver.observe(detailsContainer, config);
	}

	// 更新阴影显示状态
	function updateShadows() {
		if (!detailsContainer) return;

		// 检查是否滚动到顶部
		showHeadShadow = detailsContainer.scrollTop > 5;

		// 检查是否滚动到底部
		const isAtBottom =
			Math.abs(
				detailsContainer.scrollHeight - detailsContainer.scrollTop - detailsContainer.clientHeight
			) < 5;
		showTailShadow = !isAtBottom;
	}

	// 组件销毁时清理观察器和事件监听
	onDestroy(() => {
		if (detailsObserver) {
			detailsObserver.disconnect();
			detailsObserver = null;
		}

		if (detailsContainer) {
			detailsContainer.removeEventListener('scroll', updateShadows);
		}
	});

	function parseJSONString(str) {
		try {
			return parseJSONString(JSON.parse(str));
		} catch (e) {
			return str;
		}
	}

	function formatJSONString(str) {
		try {
			const parsed = parseJSONString(str);
			// If parsed is an object/array, then it's valid JSON
			if (typeof parsed === 'object') {
				return JSON.stringify(parsed, null, 2);
			} else {
				// It's a primitive value like a number, boolean, etc.
				return String(parsed);
			}
		} catch (e) {
			// Not valid JSON, return as-is
			return str;
		}
	}
</script>

<div
	{id}
	class={twMerge(
		'w-full flex flex-col px-4 py-3 bg-white/60 dark:bg-black/10 border-1 border-black/10 dark:border-white/10',
		group ? 'rounded-b-xl border-t-0' : 'rounded-xl my-3',
		open ? 'pb-0' : '',
		className
	)}
>
	{#if title !== null}
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div
			class="{buttonClassName} flex flex-1 justify-between items-center gap-2 cursor-pointer {open &&
			!hide
				? 'mb-2'
				: ''}"
			on:click={() => {
				if (!disabled) {
					open = !open;
					userOpen = open;
				}
			}}
		>
			<div class=" text-xs flex flex-1 gap-2 overflow-hidden">
				{#if !messageDone && attributes?.done && attributes?.done !== 'true'}
					<div>
						<Spinner className="size-4" />
					</div>
					<div class="shimmer">
						{$i18n.t('Thinking...')}
					</div>
				{:else}
					<div class="size-4 shrink-0 flex justify-center items-center">
						<div class=" size-1 rounded-full bg-black/50 dark:bg-white/50" />
					</div>
					{#if open}
						<div>{$i18n.t('Thought Process')}</div>
					{/if}
					{#if !open}
						<div class="text-xs text-black/30 dark:text-white/30 truncate max-w-full">
							{markdownToText(text)}
						</div>
					{/if}
				{/if}
			</div>
			<div class="flex self-end items-center gap-2 px-2">
				<div
					class=" flex item-center gap-1 text-black/50 dark:text-white/50 text-xs font-medium whitespace-nowrap"
				>
					{#if attributes?.type === 'reasoning'}
						<!-- {#if attributes?.done === 'true' && attributes?.duration}
							<TimerIcon className="size-4" strokeWidth="1.5" />
							<span>
								{Number(attributes.duration) === 0 ? 1 : Number(attributes.duration)}s
							</span>
						{/if} -->
					{:else if attributes?.type === 'code_interpreter'}
						{#if attributes?.done === 'true'}
							{$i18n.t('Analyzed')}
						{:else}
							{$i18n.t('Analyzing...')}
						{/if}
					{:else}
						{title}
					{/if}
				</div>
				{#if open}
					<ChevronUp strokeWidth="3" className="size-3" />
				{:else}
					<ChevronDown strokeWidth="3" className="size-3" />
				{/if}
			</div>
		</div>
	{:else}
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div
			class="{buttonClassName} cursor-pointer"
			on:click={() => {
				if (!disabled) {
					open = !open;
					userOpen = true;
				}
			}}
		>
			<div>
				<div class="flex items-start justify-between">
					<slot />

					{#if chevron}
						<div class="flex self-start translate-y-1">
							{#if open}
								<ChevronUp strokeWidth="3.5" className="size-3.5" />
							{:else}
								<ChevronDown strokeWidth="3.5" className="size-3.5" />
							{/if}
						</div>
					{/if}
				</div>

				{#if grow}
					{#if open && !hide}
						<div
							on:pointerup={(e) => {
								e.stopPropagation();
							}}
						>
							<slot name="content" />
						</div>
					{/if}
				{/if}
			</div>
		</div>
	{/if}

	{#if attributes?.type === 'tool_calls'}
		{@const args = decode(attributes?.arguments)}
		{@const result = decode(attributes?.result ?? '')}
		{@const files = parseJSONString(decode(attributes?.files ?? ''))}

		{#if !grow}
			{#if attributes?.done === 'true'}
				{#if typeof files === 'object'}
					{#each files ?? [] as file, idx}
						{#if file.startsWith('data:image/')}
							<Image
								id={`${collapsibleId}-tool-calls-${attributes?.id}-result-${idx}`}
								src={file}
								alt="Image"
							/>
						{/if}
					{/each}
				{/if}
			{/if}
		{/if}
	{:else if !grow}
		<div class="relative overflow-hidden">
			<div
				class="headShadow absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-[#FBFCFDAC] dark:from-[#111415BC] to-transparent pointer-events-none z-1 transition-opacity duration-200 {showHeadShadow &&
				!userOpen
					? 'opacity-100'
					: 'opacity-0'}"
			></div>
			<div
				transition:slide
				bind:this={detailsContainer}
				on:scroll={updateShadows}
				class={twMerge(
					'detailsSubContainer relative transition-all',
					open && !hide ? `pb-1 overflow-y-auto scrollbar-none` : 'h-0 overflow-hidden',
					userOpen ? '' : 'max-h-40'
				)}
			>
				<slot name="content" />
			</div>
			<div
				class="tailShadow absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-[#FBFCFDAC] dark:from-[#111415BC] to-transparent pointer-events-none z-1 transition-opacity duration-200 {showTailShadow &&
				!userOpen
					? 'opacity-100'
					: 'opacity-0'}"
			></div>
		</div>
	{/if}
</div>
