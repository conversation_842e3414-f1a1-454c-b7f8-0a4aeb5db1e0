<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { formatFileSize } from '$lib/utils';
	import { getFileExtension } from '$lib/utils/fileValidator';

	import FileItemModal from './FileItemModal.svelte';
	import Spinner from './Spinner.svelte';
	import Tooltip from './Tooltip.svelte';
	import FileTypeIcon from './FileTypeIcon.svelte';

	const dispatch = createEventDispatcher();

	export let className = 'w-60';
	export let colorClassName = 'bg-white dark:bg-gray-850 border border-gray-50 dark:border-white/5';
	export let url: string | null = null;

	export let dismissible = false;
	export let loading = false;

	export let item = null;
	export let edit = false;
	export let small = false;

	export let name: string;
	export let type: string;
	export let size: number;

	let showModal = false;

	// Get file type from extension
	$: extension = getFileExtension(name) || '';
	$: displayFileType = extension ? extension.toUpperCase() : 'File';
</script>

{#if item}
	<FileItemModal bind:show={showModal} bind:item {edit} />
{/if}

<button
	class="relative group p-1.5 {className} flex items-center gap-1 {colorClassName} {small
		? 'rounded-xl'
		: 'rounded-2xl'} text-left"
	type="button"
	on:click={async () => {
		if (item?.file?.data?.content) {
			showModal = !showModal;
		} else {
			if (url) {
				if (type === 'file') {
					window.open(`${url}/content`, '_blank').focus();
				} else {
					window.open(`${url}`, '_blank').focus();
				}
			}
		}

		dispatch('click');
	}}
>
	{#if !small}
		<div class="flex items-center justify-center">
			{#if !loading}
				<FileTypeIcon
					filename={name}
					fileUrl={url}
					size="md"
					showLabel={false}
					clickable={false}
				/>
			{:else}
				<div class="p-3 bg-black/20 dark:bg-white/10 text-white rounded-xl">
					<Spinner />
				</div>
			{/if}
		</div>
	{/if}

	{#if !small}
		<div class="flex flex-col justify-center -space-y-0.5 px-2.5 w-full">
			<div class=" dark:text-gray-100 text-sm font-medium line-clamp-1 mb-1">
				{decodeURIComponent(name)}
			</div>

			<div class=" flex justify-between text-gray-500 text-xs line-clamp-1">
				<span class="uppercase">{displayFileType}</span>
				{#if size}
					<span>{formatFileSize(size)}</span>
				{/if}
			</div>
		</div>
	{:else}
		<Tooltip
			content={decodeURIComponent(name)}
			className="flex flex-col w-full"
			placement="top-start"
		>
			<div class="flex items-center gap-2 px-2.5 w-full">
				{#if loading}
					<div class="shrink-0">
						<Spinner className="size-4" />
					</div>
				{:else}
					<div class="shrink-0">
						<FileTypeIcon
							filename={name}
							fileUrl={url}
							size="sm"
							showLabel={false}
							clickable={false}
						/>
					</div>
				{/if}
				<div class="flex-1 min-w-0">
					<div class="dark:text-gray-100 text-sm font-medium line-clamp-1">{decodeURIComponent(name)}</div>
				</div>
				<div class="text-gray-500 text-xs shrink-0">{formatFileSize(size)}</div>
			</div>
		</Tooltip>
	{/if}

	{#if dismissible}
		<div class=" absolute -top-1 -right-1">
			<button
				class=" bg-white text-black border border-white rounded-full group-hover:visible invisible transition"
				type="button"
				on:click|stopPropagation={() => {
					dispatch('dismiss');
				}}
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 20 20"
					fill="currentColor"
					class="w-4 h-4"
				>
					<path
						d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"
					/>
				</svg>
			</button>

			<!-- <button
				class=" p-1 dark:text-gray-300 dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5 rounded-full group-hover:visible invisible transition"
				type="button"
				on:click={() => {
				}}
			>
				<GarbageBin />
			</button> -->
		</div>
	{/if}
</button>
