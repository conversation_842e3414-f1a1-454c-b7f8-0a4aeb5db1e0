<script lang="ts">
	import { getFileIconConfig, shouldShowThumbnail } from '$lib/utils/fileIcons';
	import { getFileExtension } from '$lib/utils/fileValidator';
	import Image from './Image.svelte';

	export let filename: string;
	export let fileUrl: string | null = null;
	export let size: 'sm' | 'md' | 'lg' = 'md';
	export let clickable: boolean = false;
	export let showLabel: boolean = true;

	$: extension = getFileExtension(filename) || '';
	$: iconConfig = getFileIconConfig(extension);
	$: isImage = shouldShowThumbnail(extension);

	// Size configurations
	const sizeConfig = {
		sm: {
			container: 'w-8 h-8',
			icon: 'text-sm',
			label: 'text-xs'
		},
		md: {
			container: 'w-12 h-12',
			icon: 'text-lg',
			label: 'text-sm'
		},
		lg: {
			container: 'w-16 h-16',
			icon: 'text-xl',
			label: 'text-base'
		}
	};

	$: config = sizeConfig[size];

	function handleClick() {
		if (clickable && fileUrl && isImage) {
			// Open image in new tab for preview
			window.open(fileUrl, '_blank');
		}
	}
</script>

<div class="flex flex-col items-center gap-1">
	{#if isImage && fileUrl}
		<!-- Image thumbnail -->
		<button
			class="relative {config.container} rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 {clickable
				? 'cursor-pointer hover:opacity-80 transition-opacity'
				: ''}"
			on:click={handleClick}
			disabled={!clickable}
		>
			<Image
				src={fileUrl}
				alt={filename}
				imageClassName="w-full h-full object-cover object-center"
				className="w-full h-full"
			/>
			{#if clickable}
				<div
					class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center"
				>
					<svg
						class="w-4 h-4 text-white opacity-0 hover:opacity-100 transition-opacity"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
						/>
					</svg>
				</div>
			{/if}
		</button>
	{:else}
		<!-- File type icon -->
		<div class="flex items-center justify-center {config.container} rounded-lg">
			{#if iconConfig.localIcon}
				<!-- Use local SVG icon if available -->
				<img
					src={iconConfig.localIcon}
					alt={iconConfig.label}
					class="w-full h-full object-contain"
					on:error={() => {
						// Fallback to emoji icon if local icon fails to load
						iconConfig = { ...iconConfig, localIcon: undefined };
					}}
				/>
			{:else}
				<!-- Fallback to emoji icon -->
				<div
					class="flex items-center justify-center w-full h-full rounded-lg border border-gray-200 dark:border-gray-700"
					style="background-color: {iconConfig.bgColor}; color: {iconConfig.color};"
				>
					<span class="{config.icon} font-medium">
						{iconConfig.icon}
					</span>
				</div>
			{/if}
		</div>
	{/if}

	{#if showLabel}
		<span class="{config.label} text-gray-600 dark:text-gray-400 text-center max-w-full truncate">
			{isImage ? iconConfig.label : iconConfig.label}
		</span>
	{/if}
</div>
