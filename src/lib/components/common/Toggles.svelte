<script lang="ts">
	import { ToggleGroup } from 'bits-ui';
	import { mobile } from '$lib/stores';

	type ToggleGroupItem = {
		value: string;
		label: string;
		icon?: string;
	};

	export let value: string | string[] | undefined;
	export let items: ToggleGroupItem[] = [];
	export let iconsMap: Record<string, ConstructorOfATypedSvelteComponent> = {};
	export let disabled = false;
	export let onChange: (value: string | string[] | undefined) => void = () => {};
</script>

<ToggleGroup.Root class="flex-1 flex items-center gap-2 border-xl" {value} {disabled}>
	{#each items as item (item.value)}
		<ToggleGroup.Item
			value={item.value}
			class="flex flex-1 flex-col justify-around items-center h-full gap-2 p-5 rounded-xl active:bg-[#F2F4F6] data-[state=on]:bg-[#F2F4F6] dark:data-[state=on]:bg-[#3C3E40] transition-colors border-1 border-black/10 dark:border-white/10 active:text-black dark:active:text-white data-[state=on]:text-black dark:data-[state=on]:text-white {disabled
				? 'bg-gray-100/10 text-gray-400 dark:text-gray-700 cursor-not-allowed'
				: !$mobile
					? 'hover:bg-[#F2F4F6] dark:hover:bg-[#3C3E40] text-black/80 dark:text-white/80'
					: 'text-black/80 dark:text-white/80'}"
			aria-label={item.label}
			on:click={(e) => {
				value = item.value;
				onChange(item.value);
				e.preventDefault(); // Prevent the default click behavior
			}}
		>
			{#if item.icon && iconsMap[item.icon]}
				<svelte:component this={iconsMap[item.icon]} className="size-5" strokeWidth="1.5" />
			{/if}
			<div class="">
				{item.label}
			</div>
		</ToggleGroup.Item>
	{/each}
</ToggleGroup.Root>
