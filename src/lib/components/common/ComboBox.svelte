<script lang="ts">
	import { Combobox } from 'bits-ui';
	import { fly } from 'svelte/transition';
	import Check from '../icons/Check.svelte';
	import ChevronDown from '../icons/ChevronDown.svelte';

	export let value = '';
	export let placeholder = 'Select an option';
	export let searchPlaceholder = placeholder;
	export let items: Array<{ value: string; label: string }> = [];
	export let name = '';
	export let searchEnabled = true;
	export let className = '';

	let inputValue = '';
	let touchedInput = false;

	$: filteredItems =
		inputValue && touchedInput && searchEnabled
			? items.filter((item) => item.value.toLowerCase().includes(inputValue.toLowerCase()))
			: items;

	export let onChange: (value: string) => void = () => {};
</script>

<Combobox.Root
	{items}
	bind:inputValue
	selected={items.find((item) => item.value === value)}
	onSelectedChange={(selectedItem) => {
		touchedInput = false;
		if (selectedItem) {
			value = selectedItem.value;
			onChange(selectedItem.value);
		}
	}}
>
	<div
		class="relative flex items-center text-sm rounded-lg border border-black/10 dark:border-white/10 dark:text-white"
	>
		<Combobox.Input
			class="px-3 py-[9px] min-w-45 outline-none flex-1 bg-transparent {className}"
			{placeholder}
			on:input={() => {
				touchedInput = true;
			}}
			aria-label={searchPlaceholder}
			disabled={!searchEnabled}
		/>
		<Combobox.Arrow asChild>
			<ChevronDown className="absolute right-3 size-5 text-black/40 dark:text-white/40" />
		</Combobox.Arrow>
	</div>

	<Combobox.Content
		class="w-full p-1 rounded-lg shadow-lg outline-none bg-white dark:bg-gray-850 dark:text-white z-9999"
		transition={fly}
		sideOffset={4}
	>
		{#each filteredItems as item (item.value)}
			<Combobox.Item
				class="flex w-full items-center rounded-lg py-2 px-3 mb-1 text-sm outline-none transition-all duration-75 data-[highlighted]:bg-[#F4F6F8] data-[selected]:bg-[#F2F4F8] dark:data-[selected]:text-black data-hovered:bg-[#F4F6F8] hover:bg-[#F4F6F8] dark:data-[state=highlighted]:bg-gray-800 dark:data-hovered:bg-gray-800 dark:hover:bg-gray-800 truncate"
				value={item.value}
				label={item.label}
			>
				{item.label}
				<!-- <Combobox.ItemIndicator class="ml-auto" asChild={false}>
					<Check />
				</Combobox.ItemIndicator> -->
			</Combobox.Item>
		{:else}
			<span class="block px-5 py-2 text-sm text-gray-500 dark:text-gray-400">No results found</span>
		{/each}
	</Combobox.Content>
	<Combobox.HiddenInput {name} />
</Combobox.Root>
