<script lang="ts">
	export let size = 'md';
	export let className = '';
</script>

<div
	class="loading-container {className}"
	class:loading-sm={size === 'sm'}
	class:loading-md={size === 'md'}
	class:loading-lg={size === 'lg'}
>
	<div class="loading-dot"></div>
	<div class="loading-dot"></div>
	<div class="loading-dot"></div>
</div>

<style>
	.loading-container {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
	}

	.loading-sm {
		gap: 0.25rem;
	}

	.loading-lg {
		gap: 0.75rem;
	}

	.loading-dot {
		background-color: currentColor;
		border-radius: 50%;
		opacity: 0.6;
		animation: loading-animation 1s infinite ease-in-out both;
	}

	.loading-sm .loading-dot {
		width: 6px;
		height: 6px;
	}

	.loading-md .loading-dot {
		width: 10px;
		height: 10px;
	}

	.loading-lg .loading-dot {
		width: 14px;
		height: 14px;
	}

	.loading-dot:nth-child(1) {
		animation-delay: -0.32s;
	}

	.loading-dot:nth-child(2) {
		animation-delay: -0.16s;
	}

	@keyframes loading-animation {
		0%,
		80%,
		100% {
			opacity: 0.2;
			transform: scale(0.6);
		}
		40% {
			opacity: 1;
			transform: scale(1.4);
		}
	}
</style>
