<script lang="ts">
	import { WEBUI_BASE_URL } from '$lib/constants';
	import AudioPreview from './AudioPreview.svelte';

	export let src = '';
	export let alt = '';

	export let className = 'w-full outline-hidden focus:outline-hidden';
	export let audioClassName = 'rounded-lg w-full';

	let _src = '';
	$: _src = src.startsWith('/') ? `${WEBUI_BASE_URL}${src}` : src;

	let showAudioPreview = false;
</script>

<button
	class={className}
	on:click={() => {
		showAudioPreview = true;
	}}
	type="button"
>
	<div class="flex items-center gap-2 p-2 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-audio">
			<path d="M17.5 22h.5c.5 0 1-.2 1.4-.6.4-.4.6-.9.6-1.4V7.5L14.5 2H6c-.5 0-1 .2-1.4.6C4.2 3 4 3.5 4 4v16c0 .5.2 1 .6 *******.9.6 1.4.6h4.5"/>
			<polyline points="14 2 14 8 20 8"/>
			<path d="M10 20v-1a2 2 0 1 1 4 0v1a2 2 0 1 1-4 0z"/>
			<path d="M12 12v4"/>
		</svg>
		<div class="flex flex-col">
			<span class="text-sm font-medium">{alt || '音频文件'}</span>
			<span class="text-xs text-muted-foreground">点击播放</span>
		</div>
	</div>
</button>

<AudioPreview bind:show={showAudioPreview} src={_src} {alt} />