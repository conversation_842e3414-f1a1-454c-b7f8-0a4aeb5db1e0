<script lang="ts">
	import { page } from '$app/stores';
	import { getPPTSnapshot } from '$lib/apis/mcp';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import emblaCarouselSvelte from 'embla-carousel-svelte';
	import { onMount, getContext } from 'svelte';
	import ChevronLeft from '$lib/components/icons/ChevronLeft.svelte';
	import ChevronRight from '$lib/components/icons/ChevronRight.svelte';
	import type { PPTPages } from '$lib/types';
	import {
		proxyIframeResources,
		IsIosWechat,
		IsChinese
	} from '$lib/utils/artifacts/artifacts_helper';
	import FullScreenIcon from './icons/FullScreenIcon.svelte';
	import SlidesIcon from './icons/SlidesIcon.svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import { config, mobile, theme } from '$lib/stores';
	import { goto } from '$app/navigation';
	import DeployProjectButton from './chat/Artifacts/DeployProjectButton.svelte';
	import ShareOne from './icons/ShareOne.svelte';
	import Tooltip from './common/Tooltip.svelte';
	import KiraKira from './icons/KiraKira.svelte';
	import { isMobile } from 'mobile-device-detect';
	import { getMobileOrientation } from '$lib/utils';

	// 组件内部自己处理所有逻辑，不需要外部参数

	const i18n: Writable<i18n> = getContext('i18n');

	let pptData: PPTPages | null = null;
	let error: string | null = null;

	let emblaApi: any;
	let emblaThumbApi: any;
	const options = { loop: false };
	const thumbOptions = { dragFree: true, containScroll: 'keepSnaps' };

	let overlay = true;
	let selectedIndex = 0;
	let pptContainer: HTMLElement | null = null;
	let orientation = getMobileOrientation() ?? '';
	$: console.log('orientation', orientation);

	function onInit(event: any) {
		console.log('[PPTViewer] Embla carousel initialized');
		try {
			emblaApi = event.detail;
			console.log('[PPTViewer] Embla API:', emblaApi);
			console.log('[PPTViewer] Slide nodes:', emblaApi.slideNodes());
			initKeyBoardEvent();
			window.addEventListener('orientationchange', () => {
				orientation = getMobileOrientation() ?? '';
			});
		} catch (error) {
			console.error('[PPTViewer] Error in onInit:', error);
		}
	}

	function initKeyBoardEvent() {
		// 注册键盘事件
		document.addEventListener('keydown', (event) => {
			switch (event.code) {
				case 'ArrowLeft':
					emblaApi?.scrollPrev();
					break;
				case 'ArrowRight':
				case 'Space':
				case 'Enter':
					emblaApi?.scrollNext();
					break;
				default:
					return;
			}
		});
	}

	function onInitThumb(event: any) {
		console.log('[PPTViewer] Embla thumbnail initialized');
		try {
			emblaThumbApi = event.detail;
			console.log('[PPTViewer] Embla thumb API:', emblaThumbApi);
		} catch (error) {
			console.error('[PPTViewer] Error in onInitThumb:', error);
		}
	}

	function calculateScale(containerWidth: number, containerHeight: number) {
		const targetWidth = 1280;
		const targetHeight = 720;
		const scaleX = containerWidth / targetWidth;
		const scaleY = containerHeight / targetHeight;
		const scale = Math.min(scaleX, scaleY);
		console.log('[PPTViewer] Scale calculation:', {
			containerWidth,
			containerHeight,
			targetWidth,
			targetHeight,
			scaleX,
			scaleY,
			scale
		});
		return scale;
	}

	function handleResize(node: HTMLElement) {
		console.log('[PPTViewer] Setting up resize handler for node');
		const container = node.parentElement;
		if (!container) {
			console.error('[PPTViewer] No parent container found for resize handler');
			return;
		}

		const updateScale = () => {
			try {
				const scale = calculateScale(container.clientWidth, container.clientHeight);
				node.style.width = '1280px';
				node.style.height = '720px';
				node.style.transform = `scale(${scale})`;
				node.style.transformOrigin = 'center';
				console.log('[PPTViewer] Scale updated:', scale, 'Container size:', {
					width: container.clientWidth,
					height: container.clientHeight
				});
			} catch (error) {
				console.error('[PPTViewer] Error in updateScale:', error);
			}
		};

		const resizeObserver = new ResizeObserver(() => {
			console.log('[PPTViewer] Resize observed');
			updateScale();
		});

		try {
			resizeObserver.observe(container);
			updateScale();
			console.log('[PPTViewer] ResizeObserver successfully set up');
		} catch (error) {
			console.error('[PPTViewer] Error setting up ResizeObserver:', error);
		}

		return {
			destroy() {
				console.log('[PPTViewer] Destroying resize handler');
				resizeObserver.disconnect();
			}
		};
	}

	const onSelect = () => {
		try {
			const newIndex = emblaApi.selectedScrollSnap();
			console.log('[PPTViewer] Slide selected:', newIndex, 'Previous index:', selectedIndex);
			selectedIndex = newIndex;
			emblaThumbApi?.scrollTo(emblaApi.selectedScrollSnap());
		} catch (error) {
			console.error('[PPTViewer] Error in onSelect:', error);
		}
	};

	$: if (emblaApi) {
		console.log('[PPTViewer] Setting up embla event listeners');
		try {
			emblaApi.on('select', onSelect).on('init', onSelect);
		} catch (error) {
			console.error('[PPTViewer] Error setting up embla listeners:', error);
		}
	}

	onMount(async () => {
		console.log('[PPTViewer] Component mounted, starting initialization');
		console.log('[PPTViewer] Current page:', $page);
		console.log('[PPTViewer] User agent:', navigator.userAgent);
		console.log('[PPTViewer] Viewport size:', {
			width: window.innerWidth,
			height: window.innerHeight
		});

		try {
			const { id } = $page.params;
			const startTime = Date.now();
			// 添加超时处理
			const timeoutPromise = new Promise((_, reject) => {
				setTimeout(() => reject(new Error('Request timeout after 30 seconds')), 30000);
			});

			const dataPromise = getPPTSnapshot(id);

			pptData = (await Promise.race([dataPromise, timeoutPromise])) as PPTPages;

			const fetchTime = Date.now() - startTime;
			console.log('[PPTViewer] PPT data fetched successfully in', fetchTime, 'ms');
			console.log('[PPTViewer] PPT data structure:', pptData);

			if (pptData) {
				const originalPagesCount = pptData.pages.length;
				pptData.pages = pptData.pages.filter((page) => typeof page === 'string');
				const filteredPagesCount = pptData.pages.length;
				console.log('[PPTViewer] Pages filtered:', { originalPagesCount, filteredPagesCount });
				console.log(
					'[PPTViewer] Sample page content length:',
					pptData.pages[0]?.length || 'No pages'
				);
			} else {
				console.warn('[PPTViewer] PPT data is null or undefined');
			}
		} catch (e) {
			const errorMessage = (e as Error).message || 'Failed to load PPT';
			console.error('[PPTViewer] Error during initialization:', e);
			console.error('[PPTViewer] Error stack:', (e as Error).stack);
			console.error('[PPTViewer] Error name:', (e as Error).name);

			// 添加网络错误的详细信息
			if (e instanceof TypeError && e.message.includes('fetch')) {
				console.error('[PPTViewer] Network fetch error detected');
			}
			if (e instanceof Error && e.message.includes('timeout')) {
				console.error('[PPTViewer] Timeout error detected');
			}

			error = errorMessage;
		}
	});

	function handleClick(event: MouseEvent) {
		console.log('[PPTViewer] Handle click:', {
			button: event.button,
			type: event.type,
			clientX: event.clientX
		});
		if (!emblaApi) {
			console.warn('[PPTViewer] EmblaApi not available for click handling');
			return;
		}

		try {
			// 获取点击元素的宽度
			const targetElement = event.currentTarget as HTMLElement;
			const elementWidth = targetElement.offsetWidth;
			// 获取点击位置相对于元素的X坐标
			const clickX = event.clientX;
			// 获取元素的左边界坐标
			const elementLeft = targetElement.getBoundingClientRect().left;
			// 计算点击位置相对于元素的偏移量
			const relativeX = clickX - elementLeft;
			// 判断点击位置是在元素的左半部分还是右半部分
			const isLeftHalf = relativeX < elementWidth / 2;

			if (isLeftHalf) {
				// 点击左半部分，切换到上一张
				console.log('[PPTViewer] Click on left half - scrolling prev');
				emblaApi.scrollPrev();
			} else {
				// 点击右半部分，切换到下一张
				console.log('[PPTViewer] Click on right half - scrolling next');
				emblaApi.scrollNext();
			}
			event.preventDefault();
		} catch (error) {
			console.error('[PPTViewer] Error in handleClick:', error);
		}
	}

	const requestFullscreen = () => {
		console.log('[PPTViewer] Requesting fullscreen');
		if (pptContainer) {
			try {
				if (pptContainer.requestFullscreen) {
					console.log('[PPTViewer] Using standard requestFullscreen');
					pptContainer.requestFullscreen();
				} else if ((pptContainer as any).webkitRequestFullscreen) {
					console.log('[PPTViewer] Using webkit requestFullscreen');
					(pptContainer as any).webkitRequestFullscreen();
				} else if ((pptContainer as any).msRequestFullscreen) {
					console.log('[PPTViewer] Using ms requestFullscreen');
					(pptContainer as any).msRequestFullscreen();
				} else {
					console.warn('[PPTViewer] No fullscreen API available');
				}
			} catch (error) {
				console.error('[PPTViewer] Error requesting fullscreen:', error);
			}
		} else {
			console.error('[PPTViewer] PPT container not available for fullscreen');
		}
	};

	function handleIframeLoad(e) {
		console.log('[PPTViewer] Iframe load event triggered');
		const iframeRef = e.target;

		if (!iframeRef?.contentWindow) {
			console.error('[PPTViewer] Iframe contentWindow not available');
			return;
		}

		try {
			console.log('[PPTViewer] Processing iframe content');
			// 二次缩放，匹配超出宽度多的一侧，ppt文档的缩放比例是固定的，不随容器大小变化，设置一次就好
			const iframeDoc = iframeRef.contentDocument;

			if (!iframeDoc) {
				console.error('[PPTViewer] Cannot access iframe document');
				return;
			}

			let height;
			let width;
			if (iframeDoc.body?.childElementCount === 1) {
				const firstChild = iframeDoc?.body?.firstElementChild as HTMLElement;
				height = firstChild?.offsetHeight || 720;
				width = firstChild?.offsetWidth || 1280;
			} else {
				height = iframeDoc?.body?.scrollHeight || 720;
				width = iframeDoc?.body?.scrollWidth || 1280;
			}

			console.log('[PPTViewer] Iframe dimensions:', { width, height });

			const scaleY = 720 / height >= 1 ? 1 : 720 / height;
			const scaleX = 1280 / width >= 1 ? 1 : 1280 / width;
			const scale = Math.min(scaleX, scaleY);
			console.log('[PPTViewer] Iframe scaling:', { scaleX, scaleY, scale });

			const styleContent = `
				::-webkit-scrollbar {
					display: none;
				}
				body {
					-ms-overflow-style: none;
					scrollbar-width: none;
					overflow: hidden;
					transform: scale(${scale});
					transform-origin: center 0;
				}
			`;
			const style = document.createElement('style');
			style.textContent = styleContent;
			iframeRef.contentDocument?.head.appendChild(style);
			console.log('[PPTViewer] Iframe styling applied successfully');
		} catch (error) {
			console.error('[PPTViewer] Error in handleIframeLoad:', error);
		}
	}

	function handleIframeError(e) {
		console.error('[PPTViewer] Iframe error event:', e);
		console.error('[PPTViewer] Iframe src:', e.target?.src);
		console.error('[PPTViewer] Iframe srcdoc length:', e.target?.srcdoc?.length);
	}
</script>

<div
	class="w-screen h-screen max-w-screen max-h-screen flex flex-col items-center relative justify-center bg-[#F4F6F8] dark:bg-[#141718] text-black dark:text-white"
>
	{#if IsIosWechat}
		<div
			class="flex flex-col items-center justify-start p-8 max-w-md mx-auto text-center pb-20 h-full"
		>
			<div class="absolute top-4 right-4">
				<svg
					width="115"
					height="100"
					viewBox="0 0 115 100"
					fill="none"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M1.99731 98.361C11.3013 97.1373 28.494 83.9653 27.3284 68.9107C26.6669 60.3709 21.2589 65.2402 22.4971 71.147C23.7358 77.0539 28.6302 79.8118 34.7085 75.3483C46.2033 66.9065 42.8766 40.5558 35.1288 45.5415C27.381 50.5272 40.9567 77.3942 56.393 69.0794C71.8294 60.7646 75.0269 44.5242 83.1309 32.9445C91.2351 21.3641 102.948 10.1937 111.912 4.97608M111.912 4.97608C111.912 4.97608 107.952 3.91492 103.616 2.39184M111.912 4.97608C111.912 4.97608 109.685 9.20894 106.564 16.5493"
						stroke={$theme === 'dark' ? '#fff' : '#333'}
						stroke-width="3"
						stroke-miterlimit="16"
						stroke-linecap="round"
					/>
				</svg>
			</div>
			<h3
				class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2 mt-[100px] flex items-center"
			>
				{IsChinese ? '请点击屏幕右上角的 [' : 'Tap top-right corner ['}
				<div style="transform: translateY(-3px)">...</div>
				{']'}
			</h3>
			<p class="text-base text-gray-600 dark:text-gray-400 flex items-center">
				{IsChinese ? '在' : 'Open in'}
				<span class="inline-flex items-center">
					<svg
						class="icon size-8 mx-2"
						viewBox="0 0 1024 1024"
						version="1.1"
						xmlns="http://www.w3.org/2000/svg"
						width="200"
						height="200"
						><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#FFFFFF"
						></path><path
							d="M512 0C229.688889 0 0 229.688889 0 512s229.688889 512 512 512 512-229.688889 512-512S794.311111 0 512 0zM68.266667 512c0-107.008 38.087111-205.283556 101.404444-281.998222 6.627556 7.395556 12.060444 17.208889 14.592 30.236444 5.319111 27.932444-14.222222 119.608889 10.666667 139.747556 37.944889 30.72 96.711111 4.892444 126.919111 23.466666 30.606222 18.773333 106.865778 106.353778 136.647111 88.547556 50.062222-29.866667-35.754667-147.968-39.452444-179.911111-5.461333-47.303111 24.405333-102.769778 78.904888-82.631111 34.190222 12.657778 41.301333 58.225778 73.244445 60.103111 31.943111 1.877333 17.038222-98.417778 16.896-114.574222-0.341333-45.255111 44.259556-77.454222 91.733333-93.667556 161.678222 66.304 275.911111 225.365333 275.911111 410.680889a440.888889 440.888889 0 0 1-50.915555 206.023111c-3.697778-39.736889-13.368889-83.968-37.546667-97.507555-46.961778-26.282667-99.214222-1.137778-137.102222-43.207112-15.701333-17.351111 4.010667-95.004444-43.178667-113.664-91.733333-36.266667-166.058667 38.542222-188.586666 98.645334-9.415111 25.144889-12.714667 55.893333-8.248889 85.873778 0.938667 96.341333-48.924444 96.426667-30.008889 135.054222 20.736 42.325333 94.805333 27.306667 80.384 115.911111-2.019556 13.084444-8.362667 25.088-18.005334 34.133333-8.590222 5.688889-17.351111 11.150222-26.282666 16.355556a124.131556 124.131556 0 0 1-15.246222 4.892444C250.766222 938.581333 68.266667 746.268444 68.266667 512z"
							fill="#3576EA"
						></path></svg
					>
					{IsChinese ? '浏览器打开 / 转发给朋友' : 'Browser / Share with friends'}
				</span>
			</p>
		</div>
	{:else if error}
		<div class="text-center p-4">
			<h1 class="text-xl font-bold text-red-600 mb-2">加载错误</h1>
			<p class="text-gray-600">{error}</p>
			<p class="text-sm text-gray-500 mt-2">请检查浏览器控制台获取详细错误信息</p>
		</div>
	{:else if !pptData}
		<div class="text-center">
			<Spinner className="size-6" />
			<div class="animate-pulse mt-2">Loading...</div>
			<p class="text-sm text-gray-500 mt-2">正在加载PPT数据...</p>
		</div>
	{:else}
		<nav
			class="@container flex items-center justify-between gap-2 w-full px-5 {isMobile
				? 'py-2'
				: 'py-3'} border-b-1 border-black/10"
		>
			<div class="flex shrink overflow-hidden gap-2 items-center font-semibold">
				{#if !isMobile || !orientation.includes('portrait')}
					<SlidesIcon className="size-5" />
				{/if}
				<span class=" min-w-0 truncate flex-1 shrink">{pptData.title}</span>
			</div>

			{#if !isMobile || !orientation.includes('portrait')}
				<div class="flex justify-center items-center gap-2">
					<button
						on:click={() => {
							const { default_ppt_model } = $config;
							goto(`/?model=${default_ppt_model ?? ''}&suggest=AI Slides`);
						}}
						class="gradientButton text-white flex gap-2 items-center px-4 py-2 rounded-lg whitespace-nowrap @max-lg:px-2 @max-lg:py-1 @max-lg:text-sm"
					>
						<KiraKira className="size-5 @max-lg:size-4" />
						<span>{$i18n.t('Create for Free')}</span>
					</button>
					{#if !isMobile}
						<DeployProjectButton shareTitle={pptData.title} run={async () => location.href}>
							<Tooltip
								content={$i18n.t('Share')}
								className="inline-flex items-center gap-2 p-2 font-semibold rounded-lg hover:bg-black/5 dark:hover:bg-white/10 whitespace-nowrap"
								slot="trigger"
							>
								<ShareOne className="size-5" />
							</Tooltip>
						</DeployProjectButton>
					{/if}
					{#if !isMobile}
						<Tooltip
							content={$i18n.t('Full Screen')}
							className="inline-flex items-center gap-2 p-2 font-semibold rounded-lg hover:bg-black/5 dark:hover:bg-white/10 whitespace-nowrap"
						>
							<button on:click={requestFullscreen}
								><FullScreenIcon className="size-5" strokeWidth="2" /></button
							>
						</Tooltip>
					{/if}
				</div>
			{/if}
		</nav>
		<div class=" flex flex-1 w-full max-h-full flex-col justify-center overflow-hidden">
			<div
				class="flex flex-col justify-center overflow-hidden {isMobile &&
				orientation.includes('portrait')
					? 'pt-2 pb-8'
					: 'pt-2 pb-4'}"
			>
				<div class="flex-1 flex items-center justify-center overflow-hidden gap-5 px-3 pb-0">
					{#if !$mobile}
						<button
							class="embla__button embla__button--prev inline-flex justify-center items-center rounded-full size-14 text-xl p-2 bg-white dark:bg-white/10 hover:bg-black/5 shadow-xl border-1 border-black/10 dark:border-white/10"
							on:click={() => {
								console.log('[PPTViewer] Previous button clicked');
								emblaApi?.scrollPrev();
							}}><ChevronLeft className="size-6" strokeWidth="2" /></button
						>
					{/if}
					<div class="flex-1 flex max-h-full">
						<div
							use:emblaCarouselSvelte={{ options, plugins: [] }}
							on:emblaInit={onInit}
							class="flex-1 embla relative overflow-hidden"
							bind:this={pptContainer}
						>
							<div class=" embla__container w-full h-full flex aspect-[16/9] rounded-xl">
								{#each pptData.pages as page, idx}
									<div
										class="embla__slide relative flex items-center justify-center flex-0 flex-shrink-0 basis-[100%] rounded-xl overflow-hidden"
									>
										<iframe
											title="PPT"
											srcdoc={proxyIframeResources(page)}
											on:load={handleIframeLoad}
											on:error={handleIframeError}
											use:handleResize
											class=" absolute w-[1280px] h-[720px] overflow-hidden rounded-xl"
											frameborder="0"
											allowfullscreen
										></iframe>
										{#if overlay}
											<div
												role="button"
												tabindex="0"
												on:keydown={() => {}}
												class="absolute h-full w-full z-1"
												on:click={handleClick}
												on:contextmenu|preventDefault={handleClick}
											></div>
										{/if}
									</div>
								{/each}
							</div>
						</div>
					</div>
					{#if !$mobile}
						<button
							class="embla__button embla__button--next inline-flex justify-center items-center rounded-full size-14 text-xl p-2 bg-white dark:bg-white/10 hover:bg-black/5 shadow-xl border-1 border-black/10 dark:border-white/10"
							on:click={() => {
								console.log('[PPTViewer] Next button clicked');
								emblaApi?.scrollNext();
							}}><ChevronRight className="size-6" strokeWidth="2" /></button
						>
					{/if}
				</div>
				<div class={isMobile && orientation.includes('portrait') ? '' : ''}>
					{#if !isMobile || orientation.includes('portrait')}
						<div class="embla__controls w-full flex justify-center items-center py-6">
							<div class="flex justify-center items-center gap-5">
								{#if $mobile}
									<button
										class="embla__button embla__button--prev"
										on:click={() => {
											console.log('[PPTViewer] Previous button clicked');
											emblaApi?.scrollPrev();
										}}
									>
										<ChevronLeft className="size-6" strokeWidth="2" />
									</button>
								{/if}
								<div class="font-semibold">
									<span>{selectedIndex + 1}</span>
									<span>/</span>
									<span>{pptData.pages.length}</span>
								</div>
								{#if $mobile}
									<button
										class="embla__button embla__button--next"
										on:click={() => {
											console.log('[PPTViewer] Next button clicked');
											emblaApi?.scrollNext();
										}}
									>
										<ChevronRight className="size-6" strokeWidth="2" />
									</button>
								{/if}
							</div>
						</div>
					{/if}

					{#if isMobile && orientation.includes('portrait')}
						<div class="flex justify-center items-center pt-6">
							<button
								on:click={() => {
									const { default_ppt_model } = $config;
									goto(`/?model=${default_ppt_model ?? ''}&suggest=AI Slides`);
								}}
								class="gradientButton text-white flex gap-2 items-center px-4 py-2 rounded-lg whitespace-nowrap @max-lg:px-2 @max-lg:py-1 @max-lg:text-sm"
							>
								<KiraKira className="size-5 @max-lg:size-4" />
								<span>{$i18n.t('Create for Free')}</span>
							</button>
						</div>
					{/if}
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	@keyframes gradientBreathing {
		0% {
			background-position: 100% 50%;
		}
		100% {
			background-position: 0% 50%;
		}
	}

	.gradientButton {
		position: relative;
		background: linear-gradient(
			145deg,
			#191a1d 0%,
			#222327 20%,
			#44454d 35%,
			#747689 44%,
			#a8aab8 50%,
			#747689 56%,
			#44454d 65%,
			#222327 80%,
			#191a1d 100%
		);
		animation: gradientBreathing 5s ease-in-out normal infinite;
		background-size: 600% 600%;
		border: none;
		font-weight: 500;
		letter-spacing: 0.3px;
	}
</style>
