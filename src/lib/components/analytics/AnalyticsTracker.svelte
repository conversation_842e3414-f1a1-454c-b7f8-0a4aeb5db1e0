<script>
  import { onMount } from 'svelte';
  import { trackPageView } from '$lib/utils/analytics';

  export let md = '';
  export let ct = '';
  export let ctvl = '';

  // 是否在组件挂载时自动触发
  export let autoTrack = true;

  // 手动触发事件上报
  export const track = () => {
    trackPageView(md, ct, ctvl);
  };

  onMount(() => {
    if (autoTrack) {
      track();
    }
  });
</script>

<!-- 这是一个无UI组件，不渲染任何内容 -->
