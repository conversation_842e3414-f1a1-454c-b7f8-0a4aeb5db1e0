<script>
	import { getContext, createEventDispatcher } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import dayjs from 'dayjs';
	
	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	// 筛选参数
	export let filters = {
		role: '',
		created_after: null,
		created_before: null,
		updated_after: null,
		updated_before: null
	};

	// 角色选项
	const roleOptions = [
		{ value: '', label: $i18n.t('All Roles') },
		{ value: 'admin', label: $i18n.t('Admin') },
		{ value: 'user', label: $i18n.t('User') },
		{ value: 'guest', label: $i18n.t('Guest') },
		{ value: 'pending', label: $i18n.t('Pending') }
	];

	// 时间范围预设选项
	const timeRangeOptions = [
		{ value: '', label: $i18n.t('All Time') },
		{ value: 'today', label: $i18n.t('Today') },
		{ value: 'week', label: $i18n.t('This Week') },
		{ value: 'month', label: $i18n.t('This Month') },
		{ value: 'quarter', label: $i18n.t('This Quarter') },
		{ value: 'year', label: $i18n.t('This Year') }
	];

	let showAdvancedFilters = false;
	let createdTimeRange = '';
	let updatedTimeRange = '';

	// 从URL参数初始化筛选器
	$: if ($page.url.searchParams) {
		const urlParams = $page.url.searchParams;
		filters.role = urlParams.get('role') || '';
		filters.created_after = urlParams.get('created_after') ? parseInt(urlParams.get('created_after')) : null;
		filters.created_before = urlParams.get('created_before') ? parseInt(urlParams.get('created_before')) : null;
		filters.updated_after = urlParams.get('updated_after') ? parseInt(urlParams.get('updated_after')) : null;
		filters.updated_before = urlParams.get('updated_before') ? parseInt(urlParams.get('updated_before')) : null;
	}

	// 应用时间范围预设
	const applyTimeRange = (range, type) => {
		const now = dayjs();
		let start, end;

		switch (range) {
			case 'today':
				start = now.startOf('day');
				end = now.endOf('day');
				break;
			case 'week':
				start = now.startOf('week');
				end = now.endOf('week');
				break;
			case 'month':
				start = now.startOf('month');
				end = now.endOf('month');
				break;
			case 'quarter':
				start = now.startOf('quarter');
				end = now.endOf('quarter');
				break;
			case 'year':
				start = now.startOf('year');
				end = now.endOf('year');
				break;
			default:
				start = null;
				end = null;
		}

		if (type === 'created') {
			filters.created_after = start ? start.unix() : null;
			filters.created_before = end ? end.unix() : null;
		} else if (type === 'updated') {
			filters.updated_after = start ? start.unix() : null;
			filters.updated_before = end ? end.unix() : null;
		}

		applyFilters();
	};

	// 应用筛选器
	const applyFilters = () => {
		const url = new URL($page.url);
		
		// 清除现有的筛选参数
		url.searchParams.delete('role');
		url.searchParams.delete('created_after');
		url.searchParams.delete('created_before');
		url.searchParams.delete('updated_after');
		url.searchParams.delete('updated_before');
		url.searchParams.delete('page'); // 重置页码

		// 添加新的筛选参数
		if (filters.role) {
			url.searchParams.set('role', filters.role);
		}
		if (filters.created_after) {
			url.searchParams.set('created_after', filters.created_after.toString());
		}
		if (filters.created_before) {
			url.searchParams.set('created_before', filters.created_before.toString());
		}
		if (filters.updated_after) {
			url.searchParams.set('updated_after', filters.updated_after.toString());
		}
		if (filters.updated_before) {
			url.searchParams.set('updated_before', filters.updated_before.toString());
		}

		goto(url.toString(), { replaceState: true });
		dispatch('filter', filters);
	};

	// 重置筛选器
	const resetFilters = () => {
		filters = {
			role: '',
			created_after: null,
			created_before: null,
			updated_after: null,
			updated_before: null
		};
		createdTimeRange = '';
		updatedTimeRange = '';
		
		const url = new URL($page.url);
		url.searchParams.delete('role');
		url.searchParams.delete('created_after');
		url.searchParams.delete('created_before');
		url.searchParams.delete('updated_after');
		url.searchParams.delete('updated_before');
		url.searchParams.delete('page');

		goto(url.toString(), { replaceState: true });
		dispatch('filter', filters);
	};

	// 格式化时间戳为日期字符串
	const formatTimestamp = (timestamp) => {
		return timestamp ? dayjs.unix(timestamp).format('YYYY-MM-DD') : '';
	};

	// 解析日期字符串为时间戳
	const parseDate = (dateStr) => {
		return dateStr ? dayjs(dateStr).unix() : null;
	};
</script>

<div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-4">
	<div class="flex flex-wrap items-center gap-4">
		<!-- 角色筛选 -->
		<div class="flex flex-col">
			<label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
				{$i18n.t('Role')}
			</label>
			<select
				bind:value={filters.role}
				on:change={applyFilters}
				class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
			>
				{#each roleOptions as option}
					<option value={option.value}>{option.label}</option>
				{/each}
			</select>
		</div>

		<!-- 高级筛选切换 -->
		<div class="flex items-end">
			<button
				type="button"
				on:click={() => showAdvancedFilters = !showAdvancedFilters}
				class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
			>
				{showAdvancedFilters ? $i18n.t('Hide Advanced') : $i18n.t('Advanced Filters')}
			</button>
		</div>

		<!-- 重置按钮 -->
		<div class="flex items-end">
			<button
				type="button"
				on:click={resetFilters}
				class="px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md transition-colors"
			>
				{$i18n.t('Reset Filters')}
			</button>
		</div>
	</div>

	<!-- 高级筛选面板 -->
	{#if showAdvancedFilters}
		<div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<!-- 创建时间筛选 -->
				<div class="space-y-3">
					<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
						{$i18n.t('Created Time')}
					</h4>
					
					<div class="flex flex-col space-y-2">
						<select
							bind:value={createdTimeRange}
							on:change={() => applyTimeRange(createdTimeRange, 'created')}
							class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
						>
							{#each timeRangeOptions as option}
								<option value={option.value}>{option.label}</option>
							{/each}
						</select>
						
						<div class="flex space-x-2">
							<input
								type="date"
								value={formatTimestamp(filters.created_after)}
								on:change={(e) => {
									filters.created_after = parseDate(e.target.value);
									applyFilters();
								}}
								placeholder={$i18n.t('From')}
								class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
							<input
								type="date"
								value={formatTimestamp(filters.created_before)}
								on:change={(e) => {
									filters.created_before = parseDate(e.target.value);
									applyFilters();
								}}
								placeholder={$i18n.t('To')}
								class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
					</div>
				</div>

				<!-- 更新时间筛选 -->
				<div class="space-y-3">
					<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
						{$i18n.t('Updated Time')}
					</h4>
					
					<div class="flex flex-col space-y-2">
						<select
							bind:value={updatedTimeRange}
							on:change={() => applyTimeRange(updatedTimeRange, 'updated')}
							class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
						>
							{#each timeRangeOptions as option}
								<option value={option.value}>{option.label}</option>
							{/each}
						</select>
						
						<div class="flex space-x-2">
							<input
								type="date"
								value={formatTimestamp(filters.updated_after)}
								on:change={(e) => {
									filters.updated_after = parseDate(e.target.value);
									applyFilters();
								}}
								placeholder={$i18n.t('From')}
								class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
							<input
								type="date"
								value={formatTimestamp(filters.updated_before)}
								on:change={(e) => {
									filters.updated_before = parseDate(e.target.value);
									applyFilters();
								}}
								placeholder={$i18n.t('To')}
								class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
