<script>
	import { getContext, createEventDispatcher } from 'svelte';
	import { searchUsers } from '$lib/apis/users';
	import { toast } from 'svelte-sonner';
	
	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let placeholder = $i18n.t('Search users...');
	export let excludeAdmins = true;
	export let limit = 10;
	export let showRoles = true;
	export let selectedUsers = []; // 已选择的用户ID列表

	let searchQuery = '';
	let searchResults = [];
	let loading = false;
	let showDropdown = false;
	let searchTimeout;

	// 防抖搜索
	const debouncedSearch = async (query) => {
		if (!query.trim()) {
			searchResults = [];
			showDropdown = false;
			return;
		}

		loading = true;
		try {
			const results = await searchUsers(localStorage.token, {
				q: query,
				limit,
				exclude_admins: excludeAdmins
			});
			
			// 过滤掉已选择的用户
			searchResults = results.filter(user => !selectedUsers.includes(user.id));
			showDropdown = true;
		} catch (error) {
			toast.error(`搜索用户失败: ${error}`);
			searchResults = [];
		} finally {
			loading = false;
		}
	};

	// 处理搜索输入
	const handleSearchInput = () => {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			debouncedSearch(searchQuery);
		}, 300);
	};

	// 选择用户
	const selectUser = (user) => {
		dispatch('select', user);
		searchQuery = '';
		searchResults = [];
		showDropdown = false;
	};

	// 获取角色显示文本
	const getRoleText = (role) => {
		const roleMap = {
			admin: $i18n.t('Admin'),
			user: $i18n.t('User'),
			guest: $i18n.t('Guest'),
			pending: $i18n.t('Pending')
		};
		return roleMap[role] || role;
	};

	// 获取角色样式
	const getRoleClass = (role) => {
		const roleClasses = {
			admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
			user: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
			guest: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
			pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
		};
		return roleClasses[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
	};

	// 点击外部关闭下拉框
	const handleClickOutside = (event) => {
		if (!event.target.closest('.user-search-container')) {
			showDropdown = false;
		}
	};

	// 键盘导航
	let selectedIndex = -1;
	const handleKeydown = (event) => {
		if (!showDropdown || searchResults.length === 0) return;

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				selectedIndex = Math.min(selectedIndex + 1, searchResults.length - 1);
				break;
			case 'ArrowUp':
				event.preventDefault();
				selectedIndex = Math.max(selectedIndex - 1, -1);
				break;
			case 'Enter':
				event.preventDefault();
				if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
					selectUser(searchResults[selectedIndex]);
				}
				break;
			case 'Escape':
				showDropdown = false;
				selectedIndex = -1;
				break;
		}
	};

	// 重置选中索引当搜索结果变化时
	$: if (searchResults) {
		selectedIndex = -1;
	}
</script>

<svelte:window on:click={handleClickOutside} />

<div class="user-search-container relative">
	<div class="relative">
		<input
			type="text"
			bind:value={searchQuery}
			on:input={handleSearchInput}
			on:keydown={handleKeydown}
			{placeholder}
			class="w-full px-4 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
		
		<!-- 搜索图标或加载指示器 -->
		<div class="absolute inset-y-0 right-0 flex items-center pr-3">
			{#if loading}
				<div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
			{:else}
				<svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
				</svg>
			{/if}
		</div>
	</div>

	<!-- 搜索结果下拉框 -->
	{#if showDropdown && searchResults.length > 0}
		<div class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
			{#each searchResults as user, index}
				<button
					type="button"
					on:click={() => selectUser(user)}
					class="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700 focus:outline-none transition-colors {selectedIndex === index ? 'bg-gray-50 dark:bg-gray-700' : ''}"
				>
					<div class="flex items-center justify-between">
						<div class="flex-1 min-w-0">
							<div class="flex items-center space-x-3">
								<!-- 用户头像占位符 -->
								<div class="flex-shrink-0 w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
									<span class="text-sm font-medium text-gray-600 dark:text-gray-300">
										{user.name.charAt(0).toUpperCase()}
									</span>
								</div>
								
								<div class="flex-1 min-w-0">
									<p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
										{user.name}
									</p>
									<p class="text-sm text-gray-500 dark:text-gray-400 truncate">
										{user.email}
									</p>
								</div>
							</div>
						</div>
						
						{#if showRoles}
							<div class="flex-shrink-0 ml-2">
								<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getRoleClass(user.role)}">
									{getRoleText(user.role)}
								</span>
							</div>
						{/if}
					</div>
				</button>
			{/each}
		</div>
	{/if}

	<!-- 无搜索结果提示 -->
	{#if showDropdown && searchQuery.trim() && searchResults.length === 0 && !loading}
		<div class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
			<div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
				{$i18n.t('No users found')}
			</div>
		</div>
	{/if}
</div>

<style>
	/* 确保下拉框在其他元素之上 */
	.user-search-container {
		z-index: 10;
	}
</style>
