<script>
	import { getContext } from 'svelte';
	import Users from './Users.svelte';
	
	const i18n = getContext('i18n');

	// 模拟用户数据
	const mockUsers = [
		{
			id: '1',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'user',
			profile_image_url: '/user.png'
		},
		{
			id: '2',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'user',
			profile_image_url: '/user.png'
		},
		{
			id: '3',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'guest',
			profile_image_url: '/user.png'
		},
		{
			id: '4',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'user',
			profile_image_url: '/user.png'
		},
		{
			id: '5',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'guest',
			profile_image_url: '/user.png'
		},
		{
			id: '6',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'user',
			profile_image_url: '/user.png'
		}
	];

	// 初始选中的用户ID
	let selectedUserIds = ['1', '3'];

	// 监听用户选择变化
	$: console.log('Selected user IDs:', selectedUserIds);
</script>

<div class="max-w-4xl mx-auto p-6">
	<div class="mb-8">
		<h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
			权限组用户管理界面演示
		</h1>
		<p class="text-gray-600 dark:text-gray-400">
			展示改进后的权限组用户管理功能，包括用户搜索、筛选和选择功能。
		</p>
	</div>

	<div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
		<div class="mb-4">
			<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
				编辑权限组: 开发团队
			</h2>
			<p class="text-sm text-gray-600 dark:text-gray-400">
				管理权限组成员，添加或移除用户。
			</p>
		</div>

		<!-- 权限组用户管理组件 -->
		<Users 
			users={mockUsers} 
			bind:userIds={selectedUserIds}
		/>

		<!-- 显示当前选择状态 -->
		<div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
			<h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
				当前选中的用户 ({selectedUserIds.length})
			</h3>
			{#if selectedUserIds.length > 0}
				<div class="flex flex-wrap gap-2">
					{#each selectedUserIds as userId}
						{@const user = mockUsers.find(u => u.id === userId)}
						{#if user}
							<span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
								{user.name}
							</span>
						{/if}
					{/each}
				</div>
			{:else}
				<p class="text-sm text-gray-500 dark:text-gray-400">
					没有选中任何用户
				</p>
			{/if}
		</div>

		<!-- 功能说明 -->
		<div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
			<h3 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
				功能特点
			</h3>
			<ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
				<li>• <strong>用户搜索</strong>：点击"显示"按钮展开搜索区域，支持按姓名和邮箱搜索</li>
				<li>• <strong>实时筛选</strong>：在"筛选组成员"输入框中输入关键词快速筛选现有用户</li>
				<li>• <strong>角色标识</strong>：每个用户显示角色标签（user、admin、guest等）</li>
				<li>• <strong>成员状态</strong>：已选中的用户显示"member"标签</li>
				<li>• <strong>防抖搜索</strong>：搜索输入有300ms防抖，避免频繁请求</li>
				<li>• <strong>键盘导航</strong>：搜索结果支持上下箭头键和回车键选择</li>
				<li>• <strong>响应式设计</strong>：适配不同屏幕尺寸</li>
			</ul>
		</div>

		<!-- 操作按钮 -->
		<div class="mt-6 flex justify-end space-x-3">
			<button 
				type="button"
				class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
				on:click={() => selectedUserIds = []}
			>
				清空选择
			</button>
			<button 
				type="button"
				class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
				on:click={() => {
					console.log('保存权限组，选中用户:', selectedUserIds);
					alert(`已保存权限组，包含 ${selectedUserIds.length} 个用户`);
				}}
			>
				保存权限组
			</button>
		</div>
	</div>

	<!-- 技术说明 -->
	<div class="mt-8 bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
		<h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
			技术实现说明
		</h3>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
					前端改进
				</h4>
				<ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
					<li>• TypeScript类型定义</li>
					<li>• 组件化用户搜索</li>
					<li>• 响应式UI设计</li>
					<li>• 无障碍访问支持</li>
					<li>• 状态管理优化</li>
				</ul>
			</div>
			<div>
				<h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
					后端支持
				</h4>
				<ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
					<li>• 用户搜索API</li>
					<li>• 分页和筛选</li>
					<li>• 权限验证</li>
					<li>• 性能优化</li>
					<li>• 错误处理</li>
				</ul>
			</div>
		</div>
	</div>
</div>
