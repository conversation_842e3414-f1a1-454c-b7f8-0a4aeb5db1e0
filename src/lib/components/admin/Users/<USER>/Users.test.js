import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import { vi } from 'vitest';
import Users from './Users.svelte';

// Mock the i18n context
const mockI18n = {
	t: (key) => key
};

// Mock the APIs
vi.mock('$lib/apis/users', () => ({
	searchUsers: vi.fn()
}));

// Mock toast
vi.mock('svelte-sonner', () => ({
	toast: {
		error: vi.fn()
	}
}));

describe('Users Component (Groups)', () => {
	const mockUsers = [
		{
			id: '1',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'user',
			profile_image_url: '/user.png'
		},
		{
			id: '2',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'admin',
			profile_image_url: '/user.png'
		},
		{
			id: '3',
			name: '<PERSON>',
			email: '<EMAIL>',
			role: 'guest',
			profile_image_url: '/user.png'
		}
	];

	const defaultProps = {
		users: mockUsers,
		userIds: ['1']
	};

	beforeEach(() => {
		vi.clearAllMocks();
		// Mock getContext to return our mock i18n
		vi.mock('svelte', async () => {
			const actual = await vi.importActual('svelte');
			return {
				...actual,
				getContext: vi.fn(() => mockI18n)
			};
		});
	});

	test('renders user list with correct information', () => {
		render(Users, { props: defaultProps });

		// Check if users are displayed
		expect(screen.getByText('John Doe')).toBeInTheDocument();
		expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
		expect(screen.getByText('Jane Smith')).toBeInTheDocument();
		expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
	});

	test('shows member badge for selected users', () => {
		render(Users, { props: defaultProps });

		// John Doe should have member badge since he's in userIds
		expect(screen.getByText('member')).toBeInTheDocument();
	});

	test('filters users based on search query', async () => {
		render(Users, { props: defaultProps });

		const searchInput = screen.getByPlaceholderText('Filter group members...');
		
		// Search for "john"
		await fireEvent.input(searchInput, { target: { value: 'john' } });

		// Should show John Doe but not Jane Smith
		expect(screen.getByText('John Doe')).toBeInTheDocument();
		expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
	});

	test('shows role badges for users', () => {
		render(Users, { props: defaultProps });

		// Check if role badges are displayed
		expect(screen.getByText('user')).toBeInTheDocument();
		expect(screen.getByText('admin')).toBeInTheDocument();
		expect(screen.getByText('guest')).toBeInTheDocument();
	});

	test('toggles add user section', async () => {
		render(Users, { props: defaultProps });

		const toggleButton = screen.getByText('Show');
		
		// Initially, the add user section should be hidden
		expect(screen.queryByText('Search and add users...')).not.toBeInTheDocument();

		// Click to show the add user section
		await fireEvent.click(toggleButton);

		// Now the add user section should be visible
		expect(screen.getByText('Search and add users...')).toBeInTheDocument();
		expect(screen.getByText('Hide')).toBeInTheDocument();
	});

	test('handles user selection and deselection', async () => {
		const { component } = render(Users, { props: defaultProps });

		// Get the checkbox for Jane Smith (not initially selected)
		const checkboxes = screen.getAllByRole('checkbox');
		const janeCheckbox = checkboxes[1]; // Assuming Jane is the second user

		// Click to select Jane
		await fireEvent.click(janeCheckbox);

		// Check if userIds prop was updated
		const userIds = component.userIds;
		expect(userIds).toContain('2'); // Jane's ID
	});

	test('shows appropriate empty states', () => {
		// Test with no users
		render(Users, { props: { users: [], userIds: [] } });
		expect(screen.getByText('No users available for this group.')).toBeInTheDocument();

		// Test with search query but no results
		const { rerender } = render(Users, { props: { users: mockUsers, userIds: [] } });
		
		const searchInput = screen.getByPlaceholderText('Filter group members...');
		fireEvent.input(searchInput, { target: { value: 'nonexistent' } });

		expect(screen.getByText('No users found matching "nonexistent"')).toBeInTheDocument();
	});

	test('displays user count in header', () => {
		render(Users, { props: defaultProps });

		// Should show the count of filtered users
		expect(screen.getByText(/Group Members.*\(3\)/)).toBeInTheDocument();
	});

	test('excludes admin users from regular user list when appropriate', () => {
		const usersWithoutAdmins = mockUsers.filter(user => user.role !== 'admin');
		render(Users, { props: { users: usersWithoutAdmins, userIds: [] } });

		// Should not show Jane Smith (admin)
		expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
		// Should show other users
		expect(screen.getByText('John Doe')).toBeInTheDocument();
		expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
	});
});
