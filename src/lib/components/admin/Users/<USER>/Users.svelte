<script lang="ts">
	import { getContext } from 'svelte';
	const i18n = getContext('i18n');

	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import { WEBUI_BASE_URL } from '$lib/constants';
	import Checkbox from '$lib/components/common/Checkbox.svelte';
	import Badge from '$lib/components/common/Badge.svelte';
	import UserSearch from '../UserSearch.svelte';

	import { getUsersByIds } from '$lib/apis/users';

	interface User {
		id: string;
		name: string;
		email: string;
		role: string;
		profile_image_url: string;
	}

	export let userIds: string[] = [];

	let groupUsers: User[] = [];
	let showAddUserSection = false;
	let loading = false;

	// 获取权限组中的用户详细信息
	const fetchGroupUsers = async () => {
		if (userIds.length === 0) {
			groupUsers = [];
			return;
		}

		loading = true;
		try {
			groupUsers = await getUsersByIds(localStorage.token, userIds);
		} catch (error) {
			console.error('获取用户详细信息失败:', error);
			groupUsers = [];
		} finally {
			loading = false;
		}
	};

	// 当userIds变化时重新获取用户信息
	$: if (userIds) {
		fetchGroupUsers();
	}

	let query = '';
	let filteredUsers: User[] = [];

	$: filteredUsers = groupUsers
		.filter((user) => {
			if (query === '') {
				return true;
			}

			return (
				user.name.toLowerCase().includes(query.toLowerCase()) ||
				user.email.toLowerCase().includes(query.toLowerCase())
			);
		})
		.sort((a, b) => {
			const aUserIndex = userIds.indexOf(a.id);
			const bUserIndex = userIds.indexOf(b.id);

			// Compare based on userIds or fall back to alphabetical order
			if (aUserIndex !== -1 && bUserIndex === -1) return -1; // 'a' has valid userId -> prioritize
			if (bUserIndex !== -1 && aUserIndex === -1) return 1; // 'b' has valid userId -> prioritize

			// Both a and b are either in the userIds array or not, so we'll sort them by their indices
			if (aUserIndex !== -1 && bUserIndex !== -1) return aUserIndex - bUserIndex;

			// If both are not in the userIds, fallback to alphabetical sorting by name
			return a.name.localeCompare(b.name);
		});

	// 处理从搜索中选择用户
	const handleUserSelect = (event: CustomEvent<User>) => {
		const selectedUser = event.detail;
		if (!userIds.includes(selectedUser.id)) {
			userIds = [...userIds, selectedUser.id];
			// 显示成功提示
			console.log(`已添加用户: ${selectedUser.name}`);
		}
	};

	// 切换添加用户区域显示
	const toggleAddUserSection = () => {
		showAddUserSection = !showAddUserSection;
	};
</script>

<div>
	<!-- 添加用户区域 -->
	<div class="mb-4">
		<div class="flex items-center justify-between mb-3">
			<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
				{$i18n.t('Add Users to Group')}
			</h4>
			<button
				type="button"
				on:click={toggleAddUserSection}
				class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
			>
				{showAddUserSection ? $i18n.t('Hide') : $i18n.t('Show')}
			</button>
		</div>

		{#if showAddUserSection}
			<div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
				<UserSearch
					placeholder={$i18n.t('Search and add users...')}
					excludeAdmins={true}
					selectedUsers={userIds}
					on:select={handleUserSelect}
				/>
				<p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
					{$i18n.t('Search for users by name or email to add them to this group')}
				</p>
			</div>
		{/if}
	</div>

	<!-- 现有用户过滤搜索 -->
	<div class="mb-3">
		<div class="flex items-center justify-between mb-2">
			<h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
				{$i18n.t('Group Members')} ({filteredUsers.length})
			</h4>
		</div>

		<div class="flex w-full">
			<div class="flex flex-1">
				<div class="self-center mr-3">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 20 20"
						fill="currentColor"
						class="w-4 h-4 text-gray-400"
					>
						<path
							fill-rule="evenodd"
							d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
							clip-rule="evenodd"
						/>
					</svg>
				</div>
				<input
					class="w-full text-sm pr-4 rounded-r-xl outline-hidden bg-transparent border-0 focus:ring-0 placeholder-gray-400"
					bind:value={query}
					placeholder={$i18n.t('Filter group members...')}
				/>
			</div>
		</div>
	</div>

	<div class="mt-3 max-h-[22rem] overflow-y-auto scrollbar-hidden">
		<div class="flex flex-col gap-2.5">
			{#if filteredUsers.length > 0}
				{#each filteredUsers as user (user.id)}
					<div class="flex flex-row items-center gap-3 w-full text-sm p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
						<div class="flex items-center">
							<Checkbox
								state={userIds.includes(user.id) ? 'checked' : 'unchecked'}
								on:change={(e) => {
									if (e.detail === 'checked') {
										userIds = [...userIds, user.id];
									} else {
										userIds = userIds.filter((id) => id !== user.id);
									}
								}}
							/>
						</div>

						<div class="flex w-full items-center justify-between">
							<Tooltip content={user.email} placement="top-start">
								<div class="flex items-center">
									<img
										class="rounded-full size-6 object-cover mr-3"
										src={user.profile_image_url.startsWith(WEBUI_BASE_URL) ||
										user.profile_image_url.startsWith('https://www.gravatar.com/avatar/') ||
										user.profile_image_url.startsWith('data:')
											? user.profile_image_url
											: `/user.png`}
										alt="user"
									/>

									<div class="flex flex-col">
										<div class="font-medium text-gray-900 dark:text-gray-100">{user.name}</div>
										<div class="text-xs text-gray-500 dark:text-gray-400">{user.email}</div>
									</div>
								</div>
							</Tooltip>

							<div class="flex items-center gap-2">
								<!-- 用户角色标签 -->
								<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
									{user.role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
									 user.role === 'user' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
									 user.role === 'guest' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
									 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'}">
									{user.role}
								</span>

								{#if userIds.includes(user.id)}
									<Badge type="success" content="member" />
								{/if}
							</div>
						</div>
					</div>
				{/each}
			{:else if query}
				<div class="text-gray-500 text-xs text-center py-4 px-10">
					<div class="mb-2">
						<svg class="w-8 h-8 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					{$i18n.t('No users found matching')} "{query}"
				</div>
			{:else}
				<div class="text-gray-500 text-xs text-center py-4 px-10">
					<div class="mb-2">
						<svg class="w-8 h-8 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
						</svg>
					</div>
					{$i18n.t('No users available for this group.')}
				</div>
			{/if}
		</div>
	</div>
</div>
