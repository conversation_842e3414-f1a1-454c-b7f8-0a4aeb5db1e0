<script lang="ts">
	import { getPPTExportConfig, setPPTExportConfig } from '$lib/apis/configs';
	import Switch from '$lib/components/common/Switch.svelte';
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';

	const i18n = getContext('i18n');

	export let saveHandler: Function;

	let config = null;

	const submitHandler = async () => {
		const res = await setPPTExportConfig(localStorage.token, {
			ENABLE_PPT_EXPORT: config.ENABLE_PPT_EXPORT,
			ENABLE_PDF_EXPORT: config.ENABLE_PDF_EXPORT
		});

		if (res) {
			config = res;
		}
	};

	onMount(async () => {
		const res = await getPPTExportConfig(localStorage.token);

		if (res) {
			config = res;
		}
	});
</script>

<form
	class="flex flex-col h-full justify-between space-y-3 text-sm"
	on:submit|preventDefault={async () => {
		await submitHandler();
		saveHandler();
	}}
>
	<div class=" space-y-3 overflow-y-scroll scrollbar-hidden h-full">
		{#if config}
			<div>
				<div class="mb-3.5">
					<div class=" mb-2.5 text-base font-medium">{$i18n.t('PPT Export Settings')}</div>

					<hr class=" border-gray-100 dark:border-gray-850 my-2" />

					<div class="mb-2.5">
						<div class=" flex w-full justify-between">
							<div class=" self-center text-xs font-medium">
								{$i18n.t('Enable PPT Export')}
							</div>

							<Switch bind:state={config.ENABLE_PPT_EXPORT} />
						</div>
						<div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
							{$i18n.t('Allow users to export conversations as PowerPoint presentations')}
						</div>
					</div>

					<div class="mb-2.5">
						<div class=" flex w-full justify-between">
							<div class=" self-center text-xs font-medium">
								{$i18n.t('Enable PDF Export')}
							</div>

							<Switch bind:state={config.ENABLE_PDF_EXPORT} />
						</div>
						<div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
							{$i18n.t('Allow users to export conversations as PDF documents')}
						</div>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<div class="flex justify-end pt-3 text-sm font-medium">
		<button
			class=" px-4 py-2 bg-emerald-700 hover:bg-emerald-800 text-gray-100 transition rounded-lg"
			type="submit"
		>
			{$i18n.t('Save')}
		</button>
	</div>
</form>
