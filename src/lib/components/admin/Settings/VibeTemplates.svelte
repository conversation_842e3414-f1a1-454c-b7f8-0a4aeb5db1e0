<script lang="ts">
	import { onMount, getContext, tick } from 'svelte';
	import { user } from '$lib/stores';
	import { toast } from 'svelte-sonner';
	import {
		getVibeTemplates,
		getVibeTemplate,
		createVibeTemplate,
		updateVibeTemplate,
		deleteVibeTemplate,
		initializeVibeTemplates
	} from '$lib/apis';
	import SpinnerIcon from '$lib/components/common/Spinner.svelte';
	import Modal from '$lib/components/common/Modal.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';

	const i18n = getContext('i18n');

	export let saveHandler = () => {};

	let templates: any[] = [];
	let isLoading = true;
	let isSaving = false;
	let error: any = null;

	// 模态框相关
	let showTemplateModal = false;
	let editingTemplate: any = null;
	let modalMode: 'create' | 'edit' = 'create';

	// 表单数据
	let formData = {
		id: '',
		name: '',
		description: '',
		template_type: 'ppt_edit',
		content: '',
		is_active: true
	};

	const templateTypes = [
		{
			value: 'ppt_edit',
			label: 'PPT模式模板',
			color: 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
			icon: '📊'
		},
		{
			value: 'artifacts_edit',
			label: 'Artifacts模式模板',
			color: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
			icon: '⚡'
		}
	];

	onMount(async () => {
		await loadTemplates();
	});

	// 响应式调试语句
	// $: {
	// 	console.log('showTemplateModal changed to:', showTemplateModal);
	// 	console.log('modalMode:', modalMode);
	// 	console.log('editingTemplate:', editingTemplate);
	// }

	async function loadTemplates() {
		isLoading = true;
		error = null;

		try {
			templates = await getVibeTemplates(localStorage.token);
		} catch (err) {
			error = err;
			toast.error($i18n.t('Failed to load vibe templates'));
			console.error('Error loading vibe templates:', err);
		} finally {
			isLoading = false;
		}
	}

	function openCreateModal() {
		// console.log('openCreateModal called');
		// console.log('current showTemplateModal state:', showTemplateModal);

		modalMode = 'create';
		editingTemplate = null;
		formData = {
			id: '',
			name: '',
			description: '',
			template_type: 'ppt_edit',
			content: '',
			is_active: true
		};
		showTemplateModal = true;

		// console.log('after setting showTemplateModal to true (create):', showTemplateModal);
		// console.log('modalMode:', modalMode);
	}

	async function openEditModal(template) {
		// console.log('openEditModal called with template:', template);
		// console.log('current showTemplateModal state:', showTemplateModal);

		// 先确保模态框是关闭的
		if (showTemplateModal) {
			// console.log('Modal was still open, closing first...');
			showTemplateModal = false;
			await tick(); // 等待DOM更新
		}

		modalMode = 'edit';
		editingTemplate = template;
		formData = {
			id: template.id,
			name: template.name,
			description: template.description || '',
			template_type: template.template_type,
			content: template.content,
			is_active: template.is_active
		};

		await tick(); // 等待状态更新
		showTemplateModal = true;

		// console.log('after setting showTemplateModal to true:', showTemplateModal);
		// console.log('modalMode:', modalMode);
		// console.log('formData:', formData);
	}

	async function closeModal() {
		// console.log('closeModal called');
		// console.log('current showTemplateModal state before close:', showTemplateModal);

		showTemplateModal = false;
		editingTemplate = null;
		error = null;
		isSaving = false;
		// 重置表单数据
		formData = {
			id: '',
			name: '',
			description: '',
			template_type: 'ppt_edit',
			content: '',
			is_active: true
		};

		await tick(); // 等待状态更新

		// console.log('after closing modal:', {
		// 	showTemplateModal,
		// 	editingTemplate,
		// 	error,
		// 	isSaving,
		// 	formData
		// });

		// 强制刷新组件状态
		// setTimeout(() => {
		// 	console.log('Timeout check - showTemplateModal:', showTemplateModal);
		// }, 100);
	}

	async function saveTemplate() {
		if (!formData.id || !formData.name || !formData.content) {
			toast.error($i18n.t('Please fill in all required fields'));
			return;
		}

		isSaving = true;
		error = null;

		try {
			if (modalMode === 'create') {
				await createVibeTemplate(localStorage.token, formData);
				toast.success($i18n.t('Template created successfully'));
			} else {
				await updateVibeTemplate(localStorage.token, formData.id, formData);
				toast.success($i18n.t('Template updated successfully'));
			}

			await closeModal();
			await loadTemplates();
			saveHandler();
		} catch (err) {
			error = err;
			toast.error($i18n.t('Failed to save template'));
			console.error('Error saving template:', err);
		} finally {
			isSaving = false;
		}
	}

	async function deleteTemplate(template) {
		if (!confirm($i18n.t('Are you sure you want to delete this template?'))) {
			return;
		}

		try {
			await deleteVibeTemplate(localStorage.token, template.id);
			toast.success($i18n.t('Template deleted successfully'));
			await loadTemplates();
		} catch (err) {
			toast.error($i18n.t('Failed to delete template'));
			console.error('Error deleting template:', err);
		}
	}

	async function initializeDefaults() {
		try {
			await initializeVibeTemplates(localStorage.token);
			toast.success($i18n.t('Default templates initialized successfully'));
			await loadTemplates();
		} catch (err) {
			toast.error($i18n.t('Failed to initialize default templates'));
			console.error('Error initializing defaults:', err);
		}
	}

	function getTemplateTypeInfo(type) {
		const typeObj = templateTypes.find((t) => t.value === type);
		return (
			typeObj || {
				value: type,
				label: type,
				color: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
				icon: '📄'
			}
		);
	}
</script>

<div class="flex flex-col h-full overflow-hidden gap-6">
	<!-- 头部区域 -->
	<div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
		<div class="space-y-1">
			<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
				{$i18n.t('Vibe Templates Configuration')}
			</h1>
			<p class="text-sm text-gray-600 dark:text-gray-400">
				{$i18n.t('Manage vibe coding prompt templates for different modes')}
			</p>
			<div
				class="text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/30 px-3 py-2 rounded-lg border border-amber-200 dark:border-amber-800"
			>
				⚠️
				注意：每种模板类型（PPT、Artifacts）只能有一个激活的模板。激活新模板时，同类型的其他模板将自动停用。
			</div>
		</div>

		<div class="flex flex-wrap gap-3">
			<button
				class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors shadow-sm"
				type="button"
				on:click={initializeDefaults}
				disabled={isLoading}
			>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
					/>
				</svg>
				{$i18n.t('Initialize Defaults')}
			</button>

			<button
				class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 transition-all shadow-sm"
				type="button"
				on:click={openCreateModal}
				disabled={isLoading}
			>
				<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 6v6m0 0v6m0-6h6m-6 0H6"
					/>
				</svg>
				{$i18n.t('Create Template')}
			</button>
		</div>
	</div>

	<!-- 内容区域 -->
	{#if isLoading}
		<div class="flex flex-col items-center justify-center py-16 space-y-4">
			<SpinnerIcon />
			<p class="text-gray-500 dark:text-gray-400">Loading templates...</p>
		</div>
	{:else if error}
		<div class="flex flex-col items-center justify-center py-16 space-y-4">
			<div
				class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center"
			>
				<svg
					class="w-8 h-8 text-red-600 dark:text-red-400"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
			</div>
			<div class="text-center">
				<h3 class="text-lg font-medium text-gray-900 dark:text-white">Error Loading Templates</h3>
				<p class="text-red-600 dark:text-red-400 mt-1">
					{error.message || $i18n.t('An error occurred')}
				</p>
			</div>
		</div>
	{:else}
		<div class="flex-1 overflow-y-auto">
			{#if templates.length === 0}
				<div class="flex flex-col items-center justify-center py-16 space-y-6">
					<div
						class="w-20 h-20 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
					>
						<svg
							class="w-10 h-10 text-gray-400"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
							/>
						</svg>
					</div>
					<div class="text-center space-y-2">
						<h3 class="text-lg font-medium text-gray-900 dark:text-white">No Templates Found</h3>
						<p class="text-gray-500 dark:text-gray-400 max-w-md">
							{$i18n.t('No templates found')} Get started by initializing default templates or creating
							a new one.
						</p>
					</div>
					<button
						class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700 dark:hover:bg-blue-800 transition-colors"
						on:click={initializeDefaults}
					>
						<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
							/>
						</svg>
						{$i18n.t('Initialize default templates')}
					</button>
				</div>
			{:else}
				<div class="grid gap-4 sm:gap-6">
					{#each templates as template}
						{@const typeInfo = getTemplateTypeInfo(template.template_type)}
						<div
							class="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600"
						>
							<div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
								<!-- 模板信息 -->
								<div class="flex-1 min-w-0">
									<div class="flex flex-wrap items-center gap-3 mb-3">
										<div class="flex items-center gap-2">
											<span class="text-lg">{typeInfo.icon}</span>
											<h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
												{template.name}
											</h3>
										</div>
										<span
											class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {typeInfo.color}"
										>
											{typeInfo.label}
										</span>
										{#if !template.is_active}
											<span
												class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
											>
												<span class="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></span>
												{$i18n.t('Inactive')}
											</span>
										{:else}
											<span
												class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300"
											>
												<span class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></span>
												Active
											</span>
										{/if}
									</div>

									{#if template.description}
										<p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
											{template.description}
										</p>
									{/if}

									<div
										class="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400"
									>
										<div class="flex items-center gap-1">
											<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"
												/>
											</svg>
											ID: {template.id}
										</div>
										<div class="flex items-center gap-1">
											<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
												/>
											</svg>
											Updated: {new Date(template.updated_at * 1000).toLocaleDateString()}
										</div>
									</div>
								</div>

								<!-- 操作按钮 -->
								<div
									class="flex items-center gap-2 sm:opacity-0 group-hover:opacity-100 transition-opacity"
								>
									<Tooltip content={$i18n.t('Edit')}>
										<button
											class="inline-flex items-center justify-center w-8 h-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900 rounded-lg transition-colors"
											on:click={async () => {
												// console.log('Edit button clicked for template:', template.id);
												await openEditModal(template);
											}}
										>
											<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
												/>
											</svg>
										</button>
									</Tooltip>

									<Tooltip content={$i18n.t('Delete')}>
										<button
											class="inline-flex items-center justify-center w-8 h-8 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900 rounded-lg transition-colors"
											on:click={() => deleteTemplate(template)}
										>
											<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
												/>
											</svg>
										</button>
									</Tooltip>
								</div>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>

<!-- 优化的模板编辑模态框 -->
<Modal show={showTemplateModal} on:close={closeModal}>
	<div class="w-full max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-xl">
		<!-- 模态框头部 -->
		<div
			class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"
		>
			<div class="flex items-center gap-3">
				<div
					class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"
				>
					<svg
						class="w-5 h-5 text-blue-600 dark:text-blue-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
						/>
					</svg>
				</div>
				<div>
					<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
						{modalMode === 'create' ? $i18n.t('Create Template') : $i18n.t('Edit Template')}
					</h2>
					<p class="text-sm text-gray-500 dark:text-gray-400">
						{modalMode === 'create'
							? 'Create a new vibe template'
							: 'Edit existing template configuration'}
					</p>
				</div>
			</div>
			<button
				class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				on:click={closeModal}
			>
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M6 18L18 6M6 6l12 12"
					/>
				</svg>
			</button>
		</div>

		<!-- 模态框内容 -->
		<div class="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
			<!-- 基本信息 -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div class="space-y-2">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						{$i18n.t('Template ID')} *
					</label>
					<input
						type="text"
						bind:value={formData.id}
						disabled={modalMode === 'edit'}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-50 dark:disabled:bg-gray-600 disabled:text-gray-500 transition-colors"
						placeholder="e.g., html_edit"
					/>
				</div>

				<div class="space-y-2">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						{$i18n.t('Template Type')} *
					</label>
					<select
						bind:value={formData.template_type}
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
					>
						{#each templateTypes as type}
							<option value={type.value}>{type.icon} {type.label}</option>
						{/each}
					</select>
				</div>
			</div>

			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
					{$i18n.t('Template Name')} *
				</label>
				<input
					type="text"
					bind:value={formData.name}
					class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
					placeholder="e.g., HTML代码修改模板"
				/>
			</div>

			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
					{$i18n.t('Description')}
				</label>
				<input
					type="text"
					bind:value={formData.description}
					class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
					placeholder="Brief description of the template..."
				/>
			</div>

			<div class="flex items-center">
				<label class="flex items-center gap-3 cursor-pointer">
					<input
						type="checkbox"
						bind:checked={formData.is_active}
						class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
					/>
					<span class="text-sm font-medium text-gray-700 dark:text-gray-300"
						>{$i18n.t('Active')}</span
					>
					<span class="text-xs text-gray-500 dark:text-gray-400">Enable this template for use</span>
				</label>
			</div>

			<!-- 模板内容 -->
			<div class="space-y-3">
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
					{$i18n.t('Template Content')} *
				</label>
				<div class="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
					<div class="flex items-start gap-2">
						<svg
							class="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						<div class="text-xs text-blue-700 dark:text-blue-300">
							<p class="font-medium mb-1">Available variables:</p>
							<p class="font-mono">
								&#x7B;content&#x7D;, &#x7B;ppt_desc&#x7D;, &#x7B;line&#x7D;, &#x7B;code&#x7D;,
								&#x7B;vibe_mode&#x7D;, &#x7B;ppt_index&#x7D;, &#x7B;filename&#x7D;,
								&#x7B;column&#x7D;
							</p>
						</div>
					</div>
				</div>
				<textarea
					bind:value={formData.content}
					rows="12"
					class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm leading-relaxed transition-colors resize-none"
					placeholder="Enter template content with variables like &#123;content&#125;, &#123;code&#125;, etc."
				></textarea>
			</div>
		</div>

		<!-- 错误信息 -->
		{#if error}
			<div class="px-6 py-3 border-t border-gray-200 dark:border-gray-700">
				<div class="flex items-center gap-2 text-red-600 dark:text-red-400">
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
					<span class="text-sm">{error.message || $i18n.t('An error occurred')}</span>
				</div>
			</div>
		{/if}

		<!-- 模态框底部 -->
		<div
			class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 rounded-b-xl"
		>
			<button
				class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
				type="button"
				on:click={closeModal}
				disabled={isSaving}
			>
				{$i18n.t('Cancel')}
			</button>

			<button
				class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
				type="button"
				on:click={saveTemplate}
				disabled={isSaving}
			>
				{#if isSaving}
					<SpinnerIcon />
				{:else}
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M5 13l4 4L19 7"
						/>
					</svg>
					{modalMode === 'create' ? $i18n.t('Create') : $i18n.t('Save')}
				{/if}
			</button>
		</div>
	</div>
</Modal>
