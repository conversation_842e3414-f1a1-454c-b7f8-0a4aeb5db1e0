<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { getSnippet } from '$lib/apis/deploy';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import { handleHTMLContent, handleJSXContent } from '$lib/utils/artifacts/artifacts_helper';

	let code = '';
	let lang = '';
	let iframeElement: HTMLIFrameElement;

	const init = async () => {
		try {
			let id: string;

			// fallback 到 URL 参数的 chatId
			id = $page.params.id;

			if (id) {
				let res = await getSnippet(id);
				if (res) {
					lang = res.lang;
					if (lang === 'html') {
						code = handleHTMLContent(res.code);
					} else if (lang === 'jsx') {
						code = handleJSXContent(res.code);
					}
				}
			}
		} catch (e) {}
	};

	onMount(() => {
		init();
	});
</script>

{#if code}
	<iframe
		bind:this={iframeElement}
		title={lang}
		class="h-screen w-screen"
		srcdoc={code}
		frameborder="0"
	/>
{:else}
	<div class=" h-screen w-screen flex justify-center-safe items-center-safe gap-2">
		<Spinner className="size-6" />
		<div>Loading...</div>
	</div>
{/if}
