<script lang="ts">
	import { getContext } from 'svelte';
	import Checkbox from '$lib/components/common/Checkbox.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import { marked } from 'marked';

	const i18n = getContext('i18n');

	const helpText = {
		vision: $i18n.t('Model accepts image inputs'),
		usage: $i18n.t(
			'Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.'
		),
		citations: $i18n.t('Displays citations in the response'),
		preview_mode: $i18n.t('PreviewMode'),
		web_search: $i18n.t('Web search'),
		language_detection: $i18n.t('Language detection'),
		restore_n_source: $i18n.t('Restore n+source'),
		mcp: $i18n.t('是否启用 MCP Server'),
		file_qa: $i18n.t('Enables file question answering capabilities for the model'),
		returnFc: $i18n.t('是否启用 FC 参数回传 adapter'),
		returnThink: $i18n.t('是否启用 Think 参数回传 adapter')
	};

	export let capabilities: {
		vision?: boolean;
		usage?: boolean;
		citations?: boolean;
		preview_mode?: boolean;
		web_search?: boolean;
		language_detection?: boolean;
		restore_n_source?: boolean;
		mcp?: boolean;
		file_qa?: boolean;
		returnFc?: boolean;
		returnThink?: boolean;
	} = {};
</script>

<div>
	<div class="flex w-full justify-between mb-1">
		<div class=" self-center text-sm font-semibold">{$i18n.t('Capabilities')}</div>
	</div>
	<div class="flex flex-col">
		{#each Object.keys(capabilities) as capability}
			<div class=" flex items-center gap-2 mr-3">
				<Checkbox
					state={capabilities[capability] ? 'checked' : 'unchecked'}
					on:change={(e) => {
						capabilities[capability] = e.detail === 'checked';
					}}
				/>

				<div class=" py-0.5 text-sm capitalize">
					<Tooltip content={marked.parse(helpText[capability])}>
						{$i18n.t(capability)}
					</Tooltip>
				</div>
			</div>
		{/each}
	</div>
</div>
