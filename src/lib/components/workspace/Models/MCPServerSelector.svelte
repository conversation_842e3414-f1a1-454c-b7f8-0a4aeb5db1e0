<script lang="ts">
	import { getContext } from 'svelte';
	import { config } from '$lib/stores';

	const i18n = getContext('i18n');

	export let selectedMCPServerIds: string[] = [];

	// 直接从 config 获取 MCP servers
	$: mcpServers = ($config as any)?.mcp_servers || [];
	$: loading = false; // 不需要异步加载

	const toggleServer = (serverId: string) => {
		if (selectedMCPServerIds.includes(serverId)) {
			selectedMCPServerIds = selectedMCPServerIds.filter(id => id !== serverId);
		} else {
			selectedMCPServerIds = [...selectedMCPServerIds, serverId];
		}
	};

	const selectAll = () => {
		selectedMCPServerIds = mcpServers.map((server: any) => server.name);
	};

	const clearAll = () => {
		selectedMCPServerIds = [];
	};

	// 从 config 获取的 MCP servers 都是启用的，所以直接使用
	$: enabledServers = mcpServers;
</script>

<div class="my-2">
	<div class="flex w-full justify-between items-center mb-2">
		<div class="text-sm font-semibold">{$i18n.t('MCP Server Visibility')}</div>
		<div class="flex gap-2">
			<button
				type="button"
				class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded transition"
				on:click={selectAll}
				disabled={loading || enabledServers.length === 0}
			>
				{$i18n.t('Select All')}
			</button>
			<button
				type="button"
				class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded transition"
				on:click={clearAll}
				disabled={loading || selectedMCPServerIds.length === 0}
			>
				{$i18n.t('Clear All')}
			</button>
		</div>
	</div>

	<div class="text-xs text-gray-500 dark:text-gray-400 mb-3">
		{$i18n.t('Select which MCP servers are visible for this model. If none selected, will not display MCP tools.')}
	</div>

	{#if loading}
		<div class="flex justify-center items-center py-4">
			<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-white"></div>
		</div>
	{:else if enabledServers.length === 0}
		<div class="text-center py-4 text-gray-500 dark:text-gray-400">
			{$i18n.t('No enabled MCP servers found')}
		</div>
	{:else}
		<div class="space-y-2 max-h-48 overflow-y-auto">
			{#each enabledServers as server (server.name)}
				<label class="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
					<input
						type="checkbox"
						class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
						checked={selectedMCPServerIds.includes(server.name)}
						on:change={() => toggleServer(server.name)}
					/>
					<div class="flex-1 min-w-0">
						<div class="text-sm font-medium text-gray-900 dark:text-white">
							{server.title}
						</div>
						{#if server.description}
							<div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
								{server.description}
							</div>
						{/if}
						<div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
							ID: {server.name}
						</div>
					</div>
				</label>
			{/each}
		</div>
	{/if}

	{#if selectedMCPServerIds.length > 0}
		<div class="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
			<div class="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">
				{$i18n.t('Selected MCP Servers')} ({selectedMCPServerIds.length})
			</div>
			<div class="flex flex-wrap gap-1">
				{#each selectedMCPServerIds as serverId}
					{@const server = mcpServers.find(s => s.name === serverId)}
					{#if server}
						<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200">
							{server.title}
							<button
								type="button"
								class="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
								on:click={() => toggleServer(serverId)}
							>
								×
							</button>
						</span>
					{/if}
				{/each}
			</div>
		</div>
	{/if}
</div>
