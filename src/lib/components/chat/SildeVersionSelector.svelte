<script lang="ts">
	import { DropdownMenu } from 'bits-ui';
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import CheckBoxIndicator from '../icons/CheckBoxIndicator.svelte';
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import timezone from 'dayjs/plugin/timezone';
	import relativeTime from 'dayjs/plugin/relativeTime';
	import { getUserTimezone } from '$lib/utils';
	import { toast } from 'svelte-sonner';
	import Spinner from '../common/Spinner.svelte';
	import ChevronDown from '../icons/ChevronDown.svelte';
	import ChevronUp from '../icons/ChevronUp.svelte';
	import Tooltip from '../common/Tooltip.svelte';

	dayjs.extend(relativeTime);
	dayjs.extend(utc);
	dayjs.extend(timezone);
	const i18n: Writable<i18n> = getContext('i18n');

	// 组件属性
	export let title: string = '';
	export let versions: { version: number; created_at: number }[] = [];
	export let selectedVersion: number = -1;
	export let disabled: boolean = false;
	export let hidden: boolean = false;
	export let showVersionMenu: boolean = false;
	export let onVersionSelect: (version: number) => Promise<void> = async () => {};

	let loading = false;

	// 格式化时间函数
	function formatTime(timestamp: number) {
		const timeZone = getUserTimezone();
		const now = dayjs.utc().tz(timeZone);
		const inputTime = dayjs.utc(timestamp).tz(timeZone);
		const diffMinute = now.diff(inputTime, 'minute');
		const diffHour = now.diff(inputTime, 'hour');
		const diffDay = now.diff(inputTime, 'day');

		if (diffHour < 1) {
			// 1小时内
			if (diffMinute < 1) {
				return $i18n.t('just now');
			}
			return $i18n.t('{{diffMinute}} minutes ago', { diffMinute });
		} else if (diffHour < 24) {
			// 24小时内
			return $i18n.t('{{diffHour}} hours ago', { diffHour });
		} else if (diffDay < 30) {
			// 30天内
			return $i18n.t('{{diffDay}} days ago', { diffDay });
		} else {
			// 超过30天
			return inputTime.format('YYYY-MM-DD HH:mm');
		}
	}

	// 处理版本选择
	async function handleVersionSelect(version: number) {
		const oldSelectedVersion = selectedVersion;
		selectedVersion = version;
		loading = true;
		try {
			await onVersionSelect(version);
		} catch (error) {
			toast.error($i18n.t('Version switch failed'));
			selectedVersion = oldSelectedVersion;
		} finally {
			loading = false;
			showVersionMenu = false;
		}
	}
</script>

<DropdownMenu.Root bind:open={showVersionMenu}>
	<DropdownMenu.Trigger class="w-full overflow-hidden">
		<button
			{disabled}
			hidden={hidden || selectedVersion === -1}
			class="flex flex-1 max-w-full overflow-hidden items-center gap-2 pointer-events-auto p-1 font-medium"
		>
			<Tooltip
				content={title}
				className="shrink-0 max-w-[230px] min-w-0 flex-1 truncate {disabled
					? ''
					: 'hover:underline'}"
			>
				{title}
			</Tooltip>
			<span class="shrink-0 text-xs px-2 py-0.5 rounded-md bg-gray-100 dark:bg-gray-700">
				V{selectedVersion}
			</span>
			{#if showVersionMenu}
				<ChevronUp className="size-4 opacity-60" strokeWidth="2" />
			{:else}
				<ChevronDown className="size-4 opacity-60" strokeWidth="2" />
			{/if}
		</button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content
		class="w-full max-w-[260px] rounded-xl pl-2 pr-1 py-1.5 z-50 bg-white dark:bg-gray-850 dark:text-white shadow-lg max-h-[300px] overflow-y-scroll overflow-x-hidden border-1 border-black/10 dark:border-white/10"
		align="start"
		sideOffset={4}
	>
		{#each versions as v, idx}
			<DropdownMenu.Item
				class="flex justify-between items-center mt-1 px-3 py-1.5 text-sm cursor-pointer hover:bg-[#F4F6F8] dark:hover:bg-[#37383A] rounded-md"
				on:click={(e) => {
					e.preventDefault();
					handleVersionSelect(v.version);
				}}
			>
				<div class="flex flex-col w-full">
					<div class="flex-1 w-full flex justify-between items-center">
						<div class="flex-1 flex">
							<div class="text-sm text-black font-medium dark:text-white">
								{$i18n.t(idx == 0 ? 'Latest version' : 'Historical version')}
							</div>
							<div class="text-sm px-1 font-medium">
								V{v.version}
							</div>
						</div>
						{#if selectedVersion == v.version}
							<div>
								{#if loading}
									<Spinner className="size-3" />
								{:else}
									<CheckBoxIndicator className="size-4" strokeWidth="1" />
								{/if}
							</div>
						{/if}
					</div>
					<span class="text-[10px] text-black/30 dark:text-white/50"
						>{formatTime(v.created_at * 1000)}</span
					>
				</div>
			</DropdownMenu.Item>
		{/each}
	</DropdownMenu.Content>
</DropdownMenu.Root>
