<script lang="ts">
	import { chatId, showControls, eventBus, currentHistory } from '$lib/stores';
	import Spinner from '../common/Spinner.svelte';
	import XMark from '../icons/XMark.svelte';
	import PptListRender from './Messages/Markdown/GLMBlockRender/PPTListRender.svelte';
	import DeployProjectButton from './Artifacts/DeployProjectButton.svelte';
	import { deployPPT, getPPT } from '$lib/apis/mcp';
	import { toast } from 'svelte-sonner';
	import { getContext, onDestroy, onMount, tick } from 'svelte';
	import type { PPTPages } from '$lib/types';
	import FaceSmile from '../icons/FaceSmile.svelte';
	import { EventBus } from '$lib/constants';

	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import Divider from '../common/Divider.svelte';
	import { mcpData } from '$lib/stores/mcp';
	import SildeVersionSelector from './SildeVersionSelector.svelte';
	import PlaySlideIcon from '../icons/PlaySlideIcon.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import { isMobile } from 'mobile-device-detect';

	const i18n: Writable<i18n> = getContext('i18n');

	let pptPages: PPTPages | null = null;
	let versions: { version: number; created_at: number }[] = [];

	let loading = false;
	let abortController: AbortController | null = null;
	let showVersionMenu = false;
	let selectedVersion = -1;
	let editing = false;
	let editingIndex = -1;
	let selectedIndex = 0;

	$: messageDone = !loading && $currentHistory?.messages[$currentHistory.currentId as string]?.done;

	const fetchPPT = async (version?: number) => {
		if (loading) return;
		loading = true;
		editing = false;
		editingIndex = -1;
		abortController?.abort();
		abortController = new AbortController();
		let lastOperation = '';
		await getPPT({
			chatId: $chatId,
			signal: abortController?.signal,
			callback: async (data) => {
				if (data.slides) {
					pptPages = data.slides;
				} else if (data.delta) {
					if (!pptPages) return;
					const { op = '', position, html } = data.delta;
					switch (op) {
						case 'add_slide':
							// 如果上次操作为空或者上次的操作不是add_slide，则更新lastOperationPosition
							if (!lastOperation || lastOperation !== op) {
								// 在索引位置插入ppt
								lastOperation = op;
								let old = [...pptPages.pages];
								old.splice(position, 0, '');
								pptPages.pages = old;
								selectedIndex = position;
								await tick();
								$eventBus.emit(EventBus.PPT_WORK_PROCESSING, position);
								$eventBus.emit(EventBus.PPT_SELECTED, position);
							}
							break;
						case 'update_slide':
							if (!lastOperation || lastOperation !== op) {
								lastOperation = op;
								pptPages.pages.splice(position, 1, '');
								selectedIndex = position;
								await tick();
								$eventBus.emit(EventBus.PPT_WORK_PROCESSING, position);
								$eventBus.emit(EventBus.PPT_SELECTED, position);
							}
							break;
						default:
							break;
					}
					pptPages.pages[position] += html;
				}
				if (data.versions) {
					versions = data.versions;
					selectedVersion = version ?? versions[0].version;
				}
			},
			onClose: () => {
				loading = false;
				abortController = null;
			},
			onError: (err) => {
				throw err;
			},
			version: version
		});
	};

	const init = async (version?: number) => {
		if (!$chatId) {
			console.error('chatId is not set');
			return;
		}
		try {
			await fetchPPT($mcpData?.ppt?.version);
		} catch (error) {
			console.error('error: get ppt -->', error);
			toast.error($i18n.t('Get PPT failed'));
		}
	};

	// 创建一个类型兼容的事件处理函数
	const handlePPTUpdate = () => {
		init($mcpData?.ppt?.version);
	};

	onMount(() => {
		init($mcpData?.ppt?.version);
		isMobile && (selectedIndex = $mcpData?.ppt?.position - 1 || 0);
		$eventBus.on(EventBus.PPT_UPDATE, handlePPTUpdate); // 监听ppt更新事件
	});

	onDestroy(() => {
		if (abortController) {
			abortController.abort();
			abortController = null;
			pptPages = null;
		}
		$eventBus.off(EventBus.PPT_UPDATE, handlePPTUpdate);
	});
</script>

<div class=" pptPreviewer w-full h-full relative flex flex-col bg-white dark:bg-[#26282A]">
	<div
		class=" pptPreviewerHeadBar max-w-full flex justify-between overflow-hidden items-center-safe py-3.5 px-5 border-b-1 border-black/10"
	>
		<div
			class="flex-1 max-w-full overflow-hidden pointer-events-none flex gap-2 items-center justify-start whitespace-nowrap"
		>
			<SildeVersionSelector
				{versions}
				bind:selectedVersion
				bind:showVersionMenu
				disabled={editing}
				hidden={selectedVersion === -1}
				onVersionSelect={async (version) => await fetchPPT(version)}
				title={pptPages?.title ?? ''}
			/>
		</div>

		<!-- {#if editingIndex !== -1}
			<button
				class=" inline-flex items-center gap-2 text-sm font-medium text-black/50 dark:text-white/50 px-2 py-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5"
				on:click={() => {
					$eventBus.emit(EventBus.PPT_SELECTED, editingIndex);
				}}
			>
				{$i18n.t('Editing slide {{index}}', { index: editingIndex + 1 })}
			</button>
		{/if} -->

		<div class=" shrink-0 flex items-center gap-4 justify-end">
			{#if pptPages?.pages.length && !editing}
				{@const disabled = !messageDone}
				<DeployProjectButton
					{disabled}
					text={$i18n.t('Share')}
					shareTitle={pptPages?.title}
					showExportPDF
					{pptPages}
					chatId={$chatId}
					pptVersion={selectedVersion}
					run={async () => {
						try {
							const res = await deployPPT($chatId, selectedVersion);
							if (res) {
								const pptShareId = res.url.split('/').at(-1);
								if (pptShareId) {
									return location.origin + `/space/${pptShareId}`;
								} else {
									toast.error($i18n.t('Failed to deploy PPT'));
								}
							}
						} catch (error) {
							toast.error(error instanceof Error ? error.message : String(error));
						}
						return '';
					}}
				/>
				<Tooltip content={$i18n.t('Play')}>
					{@const disabled = !messageDone}
					<button
						{disabled}
						class="p-1.5 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 {disabled
							? 'cursor-not-allowed text-gray-300'
							: ''} "
						on:click={async () => {
							try {
								const res = await deployPPT($chatId, selectedVersion);
								if (res) {
									const pptShareId = res.url.split('/').at(-1);
									if (pptShareId) {
										window.open(location.origin + `/space/${pptShareId}`);
									} else {
										toast.error($i18n.t('Failed to Play PPT'));
									}
								}
							} catch (error) {
								toast.error(error instanceof Error ? error.message : String(error));
							}
						}}
					>
						<PlaySlideIcon className="size-5" strokeWidth="2" />
					</button>
				</Tooltip>
			{/if}
			<Divider className="shrink-0 h-6" orientation="vertical" />
			<button
				class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
				on:click={() => {
					showControls.set('');
				}}
			>
				<XMark className="size-5 text-gray-900 dark:text-white" />
			</button>
		</div>
	</div>
	{#if loading && !pptPages?.pages.length}
		<div class=" flex flex-1 justify-center items-center gap-2">
			<Spinner className="size-6" />
			<span>{$i18n.t('loading...')}</span>
		</div>
	{:else if !pptPages?.pages.length}
		<div class=" flex flex-1 flex-col justify-center items-center gap-2">
			<FaceSmile className="size-6" />
			<div>{$i18n.t('No PPT found')}</div>
		</div>
	{:else}
		<div class=" mcpSidePanelContent flex-1 relative overflow-x-hidden overflow-y-scroll p-4">
			<PptListRender
				bind:selected={selectedIndex}
				filter={selectedVersion == versions[0]?.version && editing}
				showEdit={!isMobile}
				pptRawList={pptPages.pages}
				editable={!isMobile && messageDone && selectedVersion == versions[0]?.version}
				bind:editingIndex
				onEdit={(idx, type) => {
					if (type === 'open') {
						editing = true;
						editingIndex = idx;
					} else {
						editing = false;
						editingIndex = -1;
					}
				}}
			/>
		</div>
	{/if}
</div>
