<script lang="ts">
	import Spinner from '$lib/components/common/Spinner.svelte';
	import { settings, showArtifacts, showControls } from '$lib/stores';
	import { artifactsMessages, selectedArtifactsMessageID } from '$lib/stores/artifacts';
	import { isSupportPreview } from '$lib/utils/artifacts/artifacts_helper';
	import { getContext, onMount } from 'svelte';
	import mermaid from 'mermaid';
	import SvgPanZoom from '../../../../components/common/SVGPanZoom.svelte';
	import { v4 as uuidv4 } from 'uuid';
	import ArtifactsCodeIcon from '$lib/components/icons/ArtifactsCodeIcon.svelte';
	import Divider from '$lib/components/common/Divider.svelte';
	import RecordingIcon from '$lib/components/icons/RecordingIcon.svelte';
	import type { I18nType } from '$lib/types';
	import CodeBlock from '../CodeBlock.svelte';
	import type { Token } from 'marked';

	const i18n: I18nType = getContext('i18n');

	export let icon = '';
	export let title = '';
	export let description = '';
	export let version = '';
	export let className = '';
	export let loading = false;

	export let mid = '';
	export let id = '';
	export let lang = '';
	export let code = '';
	export let onCode = (data: unknown) => {};
	export let token: Token;

	let mermaidHtml = null;

	const drawMermaidDiagram = async () => {
		try {
			if (await mermaid.parse(code)) {
				const { svg } = await mermaid.render(`mermaid-${uuidv4()}`, code);
				mermaidHtml = svg;
			}
		} catch (error) {
			console.log('Error:', error);
		}
	};

	$: if (!loading && lang === 'mermaid') {
		drawMermaidDiagram();
	}

	$: onCode({ lang, code, id, mid });

	onMount(() => {
		if (lang && code) {
			onCode({ lang, code, id, mid });
		}

		// 初始化 mermaid
		if (document.documentElement.classList.contains('dark')) {
			mermaid.initialize({
				startOnLoad: true,
				theme: 'dark',
				securityLevel: 'loose'
			});
		} else {
			mermaid.initialize({
				startOnLoad: true,
				theme: 'default',
				securityLevel: 'loose'
			});
		}

		// 如果是 mermaid 代码，则渲染图表
		if (lang === 'mermaid') {
			drawMermaidDiagram();
		}
	});
</script>

{#if lang === 'mermaid'}
	{#if mermaidHtml}
		<SvgPanZoom
			className="border border-gray-100 dark:border-gray-850 rounded-lg max-h-[720px] overflow-hidden"
			svg={mermaidHtml}
			content={code}
		/>
	{:else}
		<pre class="mermaid">{code}</pre>
	{/if}
{:else if !['html', 'svg'].includes(lang)}
	<CodeBlock
		{id}
		{mid}
		{lang}
		{code}
		{token}
		run={false}
		collapsed={$settings?.collapseCodeBlocks ?? false}
	/>
{:else}
	<div
		role="button"
		tabindex="0"
		class="{isSupportPreview(lang, code)
			? 'autoShowArtifacts'
			: ''} inline-flex flex-col min-w-[240px] rounded-xl gap-2 border border-black/10 hover:border-black/40 dark:hover:border-white/40 dark:border-white/20 dark:bg-[#16181A] px-3 py-2 max-w-full cursor-pointer transition select-none {className}"
		on:keydown={() => {}}
		on:click={() => {
			showControls.set('artifacts');
			if (!$artifactsMessages[id]) {
				artifactsMessages.update((current) => {
					return {
						...current,
						[id]: { id, lang, code, mid }
					};
				});
			}
			selectedArtifactsMessageID.set(id);
		}}
	>
		<div class="flex flex-1 max-w-full items-center gap-25">
			<div class="flex flex-1 items-center gap-2">
				<div class=" flex shrink-0 items-center justify-center">
					{#if loading}
						<Spinner className="size-4" />
					{:else if icon}
						<slot name="icon" />
					{:else}
						<ArtifactsCodeIcon className="size-4" strokeWidth="1.333" />
					{/if}
				</div>
				<div class="flex flex-col shrink truncate">
					<span class="font-semibold text-sm">{title}</span>
					{#if description}
						<div class=" text-gray-400 text-xs truncate">{description}</div>
					{/if}
				</div>
			</div>
			<div class="shrink-0">
				{#if version}
					<span class=" px-1 py-0.5 rounded-sm bg-black/5 dark:bg-white/5 text-xs text-gray-500"
						>{version}</span
					>
				{/if}
			</div>
		</div>
		<Divider orientation="horizontal" className="w-full" />
		<div class="flex items-center gap-2 px-1 text-black/50 text-xs dark:text-white/30">
			<RecordingIcon className="size-3 text-black/30 dark:text-white/30" />
			<span
				>{$i18n.t('Generate')}
				{lang === 'html' ? 'index.html' : lang === 'svg' ? `SVG ${$i18n.t('File')}` : 'File'}</span
			>
		</div>
	</div>
{/if}
