<script lang="ts">
	import SearchResultCard from './ToolResultRender/SearchResultCard.svelte';
	import MSearchResultCard from './ToolResultRender/MSearchResultCard.svelte';
	import type { GLMBlockGroup } from '$lib/types';
	export let name: string;
	export let result: any[];
	export let group: GLMBlockGroup;
</script>

{#if name === 'browser_search'}
	<SearchResultCard data={result} {group} />
{/if}

{#if name === 'browser_multi_search'}
	<SearchResultCard data={result} {group} />
{/if}
