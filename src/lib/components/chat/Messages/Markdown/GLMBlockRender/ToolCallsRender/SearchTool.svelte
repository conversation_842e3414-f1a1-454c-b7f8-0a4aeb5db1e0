<script lang="ts">
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import Search from '$lib/components/icons/Search.svelte';
	import type { GLMBlockGroup } from '$lib/types';

	const groupStyle = {
		start: 'rounded-t-xl mt-3',
		middle: 'rounded-none border-t-0',
		end: 'rounded-b-xl border-t-0',
		undefined: 'rounded-xl my-3'
	};

	export let query: string = '';
	export let group: GLMBlockGroup;

	const i18n: Writable<i18nType> = getContext('i18n');
</script>

<div
	class="flex items-center gap-2 px-4 py-3 text-xs bg-[#F7F9FA] dark:bg-[#26282A] border-1 border-black/10 dark:border-white/10 {groupStyle[
		group
	]}"
>
	<div>
		<Search className=" size-4" strokeWidth="1.5" />
	</div>
	<div class="">
		{$i18n.t('Searching "{{searchQuery}}"', {
			searchQuery: query
		})}
	</div>
</div>
