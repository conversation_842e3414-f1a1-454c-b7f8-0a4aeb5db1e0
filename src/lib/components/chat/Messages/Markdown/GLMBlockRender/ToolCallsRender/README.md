# MCP 工具图标配置系统

这个系统为不同的 MCP 工具提供自定义图标显示功能。

## 文件结构

- `McpToolCalls.svelte` - 主要的工具调用显示组件
- `toolIconConfig.ts` - 图标配置文件，定义工具与图标的映射关系

## 如何添加新的工具图标

### 1. 在 `toolIconConfig.ts` 中添加配置

```typescript
export const TOOL_ICON_MAP: Record<string, ToolIconConfig> = {
  // 添加新工具配置
  your_tool_name: {
    component: YourIconComponent,
    className: 'size-3',
    strokeWidth: '2',
    showText: true
  }
};
```

### 2. 导入所需的图标组件

在 `toolIconConfig.ts` 文件顶部导入你需要的图标：

```typescript
import YourIconComponent from '$lib/components/icons/YourIconComponent.svelte';
```

## 配置选项

### ToolIconConfig 接口

```typescript
interface ToolIconConfig {
  component: any;        // Svelte 图标组件
  className?: string;    // CSS 类名，控制图标大小
  strokeWidth?: string;  // SVG 描边宽度
  showText?: boolean;    // 是否在图标旁显示 "View" 文本
}
```

### 配置示例

```typescript
// 只显示图标，不显示文本
search_tool: {
  component: Search,
  className: 'size-4',
  strokeWidth: '1.5',
  showText: false
},

// 显示图标和文本
visit_page: {
  component: ExternalLink,
  className: 'size-3',
  strokeWidth: '2',
  showText: true
}
```

## 当前支持的工具

### 浏览器相关工具
- `visit_page` - 外跳链接图标
- `search` - 搜索图标
- `find_on_page_ctrl_f` - 页面查找图标
- `find_next` - 查找下一个图标
- `click` - 点击图标
- `page_up` - 向上翻页图标
- `page_down` - 向下翻页图标
- `go_back` - 返回图标

### 代码执行工具
- `python` - 命令行图标

### PPT 相关工具
- `create_slide` - 创建文档图标
- `add_slide` - 添加图标
- `update_slide` - 编辑图标
- `remove_slides` - 删除图标

## 回退行为

如果工具名称不在 `TOOL_ICON_MAP` 中，组件将显示默认的 "View" 文本，不带图标。

## 扩展指南

1. **添加新图标组件**：在 `src/lib/components/icons/` 目录下创建新的 SVG 图标组件
2. **更新配置**：在 `toolIconConfig.ts` 中添加新的工具映射
3. **测试**：确保图标在不同主题下正常显示

## 最佳实践

- 保持图标大小一致（推荐 `size-3`）
- 使用适当的描边宽度（推荐 `strokeWidth="2"`）
- 为相似功能的工具使用相似的图标
- 确保图标在深色和浅色主题下都清晰可见 