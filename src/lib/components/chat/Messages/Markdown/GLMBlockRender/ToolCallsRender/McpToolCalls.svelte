<script lang="ts">
	import Divider from '$lib/components/common/Divider.svelte';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import { getToolIconConfig, MCPServerStyleMap } from './toolIconConfig';
	import { onMount, getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import type { GLMBlockGroup } from '$lib/types';
	import { isValidURL } from '$lib/utils';
	import Tooltip from '$lib/components/common/Tooltip.svelte';

	const groupStyle = {
		start: 'rounded-t-xl mt-3',
		middle: 'rounded-none border-t-0',
		end: 'rounded-b-xl border-t-0 mb-2',
		undefined: 'rounded-xl my-3'
	};

	export let name: string = 'unknown tool';
	export let mcpServerName: string = 'unknown';
	export let toolArguments: string = ''; // 支持展示的参数，如果为空则不展示参数
	export let result: string = ''; // 格式化后的结果，如果为空则不展示结果
	export let status: 'executing' | 'completed' | 'error' | undefined;
	export let supportView: boolean = false;
	export let group: GLMBlockGroup;
	export let onClick: () => void = () => {};
	export let onShow: () => void = () => {};
	export let originalUrl: string = ''; // 原始URL，用于支持title替换时的链接跳转

	const i18n: Writable<i18nType> = getContext('i18n');

	// 获取当前工具的图标配置
	$: toolIconConfig = getToolIconConfig(name);

	onMount(() => {
		onShow();
	});
</script>

<div
	class=" mcpCard flex justify-between items-center max-w-full overflow-hidden px-4 py-2 {toolIconConfig?.bg ??
		'bg-[#F7F9FA] dark:bg-[#26282A]'} text-xs whitespace-nowrap border-1 border-black/10 dark:border-white/10 {supportView
		? 'supportPreview'
		: ''} {status === 'error' ? 'border-l-4 border-red-500' : ''} {groupStyle[group] ??
		'rounded-lg my-2'}"
>
	<div class="flex items-center flex-1 gap-2 min-w-0">
		<div class="font-medium flex-shrink-0 text-black/80 dark:text-white/80 flex items-center gap-2">
			{#if toolIconConfig?.icon}
				<svelte:component this={toolIconConfig.icon} className="size-4" strokeWidth="1.5" />
			{:else}
				<div class="size-4 flex justify-center items-center">
					<div class=" size-1 rounded-full border-1 border-black/50 dark:border-white/50" />
				</div>
			{/if}
		</div>
		<div class="flex-shrink-0">
			{$i18n.t(name, { ns: 'mcp_tools' })}
		</div>

		<!-- 显示参数（如果支持） -->
		{#if toolArguments}
			{@const isURL = isValidURL(toolArguments)}
			{@const hasOriginalUrl = originalUrl && isValidURL(originalUrl)}
			<Divider orientation="vertical" className="flex-shrink-0" />
			{#if isURL}
				<a
					class="text-black/50 dark:text-white/50 truncate not-prose no-underline hover:underline"
					href={toolArguments}
					target="_blank"
					rel="noopener noreferrer"
				>
					{toolArguments}
				</a>
			{:else if hasOriginalUrl}
				<!-- 当显示的是title但有原始URL时，渲染为可点击链接 -->
				<a
					class="text-black/50 dark:text-white/50 no-underline hover:underline truncate not-prose"
					href={originalUrl}
					target="_blank"
					rel="noopener noreferrer"
				>
					{toolArguments}
				</a>
			{:else}
				<Tooltip content={toolArguments} className="text-black/50 dark:text-white/50 truncate">
					{toolArguments}
				</Tooltip>
			{/if}
		{/if}

		<!-- 显示状态或结果 -->
		{#if status === 'executing'}
			<div class="flex items-center gap-2 text-gray-600 dark:text-gray-400">
				<Spinner className="size-3" />
			</div>
		{:else if status === 'error' && result}
			<!-- 显示错误结果 -->
			<div class="text-red-600 dark:text-red-400 truncate">{result}</div>
		{:else if !toolArguments && result}
			<!-- 只有不显示参数且有结果的工具才显示完成结果 -->
			<Divider orientation="vertical" className="flex-shrink-0" />
			<Tooltip content={result} className="text-black/30 dark:text-white/30 truncate"
				>{result}</Tooltip
			>
		{/if}
	</div>

	{#if supportView}
		<div class="flex-shrink-0">
			<button
				class="px-2 py-1 flex items-center gap-1 rounded-lg hover:bg-[#ECEEF0] dark:hover:bg-white/5 font-medium leading-4"
				on:click={onClick}
			>
				{$i18n.t('View')}
			</button>
		</div>
	{:else}
		<div class=" size-6"></div>
	{/if}
</div>
