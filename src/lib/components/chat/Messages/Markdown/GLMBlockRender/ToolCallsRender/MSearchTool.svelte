<script lang="ts">
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import Search from '$lib/components/icons/Search.svelte';
	import type { GLMBlockGroup } from '$lib/types';
	import Divider from '$lib/components/common/Divider.svelte';

	const groupStyle = {
		start: 'rounded-t-xl mt-3',
		middle: 'rounded-none border-t-0',
		end: 'rounded-b-xl border-t-0',
		undefined: 'rounded-xl my-3'
	};

	export let group: GLMBlockGroup;
	export let data: { name: string; arguments: any; loading?: string };

	const i18n: Writable<i18nType> = getContext('i18n');
</script>

<div
	class="w-full flex items-center gap-2 px-4 py-3 text-xs bg-[#F7F9FA] dark:bg-[#26282A] border-1 border-black/10 dark:border-white/10 {groupStyle[
		group
	]}"
>
	<div class="flex items-center gap-2 whitespace-nowrap">
		<Search className="size-4" strokeWidth="1.5" />
		<div>{$i18n.t('Search Keywords')}</div>
	</div>
	{#if data.arguments.queries && data.arguments.queries.length > 0}
		<Divider orientation="vertical" className="flex-shrink-0" />
		<div class="truncate text-black/50 dark:text-white/50">
			{data.arguments.queries.join(', ')}
		</div>
	{/if}
</div>
