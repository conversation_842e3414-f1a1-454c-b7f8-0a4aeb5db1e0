import DeleteIcon from '$lib/components/icons/DeleteIcon.svelte';
import FindOnPageIcon from '$lib/components/icons/FindOnPageIcon.svelte';
import GlobeAlt from '$lib/components/icons/GlobeAlt.svelte';
import MousePointer from '$lib/components/icons/MousePointer.svelte';
import PageDownIcon from '$lib/components/icons/PageDownIcon.svelte';
import Search from '$lib/components/icons/Search.svelte';
import SlidesIcon from '$lib/components/icons/SlidesIcon.svelte';

export const MCPServerStyleMap = {
	'ppt-maker': {
		color: '#F07010',
		icon: SlidesIcon,
		bg: 'white'
	}
};

export interface ToolIconConfig {
	icon: any;
	color?: string;
	bg?: string;
	controls?: string | false;
}

/**
 * MCP 工具图标映射配置
 *
 * 用于定义不同工具对应的图标组件和显示属性
 * 如果工具名称不在此映射中，将显示默认的 "View" 文本
 */
export const TOOL_STYLE_MAP: Record<string, ToolIconConfig> = {
	// ppt-maker 工具
	create_slide: {
		icon: SlidesIcon,
		controls: false
	},
	// 插入幻灯片
	add_slide: {
		icon: SlidesIcon,
		color: '#F07010',
		bg: 'bg-white dark:bg-[#26282A]',
		controls: 'ppt'
	},

	// 更新幻灯片
	update_slide: {
		icon: SlidesIcon,
		color: '#F07010',
		bg: 'bg-white dark:bg-[#26282A]',
		controls: 'ppt'
	},

	// 浏览器相关工具
	visit_page: {
		icon: GlobeAlt,
		controls: false
	},

	// 点击工具
	click: {
		icon: MousePointer,
		controls: false
	},

	// 搜索工具
	search: {
		icon: Search,
		controls: 'search'
	},

	// 删除ppt
	remove_slides: {
		icon: DeleteIcon,
		controls: 'ppt'
	},

	page_down: {
		icon: PageDownIcon,
		controls: false
	},

	find_on_page_ctrl_f: {
		icon: FindOnPageIcon,
		controls: false
	}
};

/**
 * 获取工具图标配置
 * @param toolName 工具名称
 * @returns 图标配置对象或 undefined
 */
export function getToolIconConfig(toolName: string): ToolIconConfig | undefined {
	return TOOL_STYLE_MAP[toolName];
}

/**
 * 检查工具是否有自定义图标
 * @param toolName 工具名称
 * @returns 是否有自定义图标
 */
export function hasCustomIcon(toolName: string): boolean {
	return toolName in TOOL_STYLE_MAP;
}
