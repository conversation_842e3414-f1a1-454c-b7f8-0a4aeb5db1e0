<script lang="ts">
	import type { GLMBlockGroup } from '$lib/types';
	import McpContainer from './McpContainer.svelte';
	import ToolCall from './ToolCalls.svelte';
	import ToolResult from './ToolResult.svelte';

	export let type: string;
	export let data: any;
	export let messageDone: boolean = false;
	export let messageId: string = '';
	export let group: GLMBlockGroup;
</script>

{#if type === 'tool_call'}
	<ToolCall {data} {messageDone} {group} />
{/if}

{#if type === 'tool_result'}
	<ToolResult name={data.name} result={data.result} {group} />
{/if}

{#if type === 'mcp'}
	<McpContainer {data} {messageId} {messageDone} {group} />
{/if}
