<script lang="ts">
	import { onMount, getContext, onDestroy } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	const i18n: Writable<i18nType> = getContext('i18n');
	export let data: { title: string; url: string; source?: string }[] = [];

	let scrollContainer: HTMLElement;
	let showLeftButton = false;
	let showRightButton = true;
	let prevDataLength = 0;
	let searchCompleteTimer: ReturnType<typeof setTimeout> | null = null;
	// 搜索完成后多久返回初始位置 (毫秒)
	const RETURN_DELAY = 6000;

	function updateButtonVisibility() {
		if (!scrollContainer) return;
		showLeftButton = scrollContainer.scrollLeft > 0;
		showRightButton =
			scrollContainer.scrollWidth > scrollContainer.clientWidth &&
			scrollContainer.scrollLeft < scrollContainer.scrollWidth - scrollContainer.clientWidth - 1;
	}

	function scroll(direction: 'left' | 'right') {
		const scrollAmount = 300;
		if (scrollContainer) {
			const newScrollLeft =
				direction === 'left'
					? scrollContainer.scrollLeft - scrollAmount
					: scrollContainer.scrollLeft + scrollAmount;
			scrollContainer.scrollTo({
				left: newScrollLeft,
				behavior: 'smooth'
			});
		}
	}

	function scrollToEnd() {
		if (scrollContainer) {
			scrollContainer.scrollTo({
				left: scrollContainer.scrollWidth,
				behavior: 'smooth'
			});
		}
	}

	function scrollToStart() {
		if (scrollContainer) {
			scrollContainer.scrollTo({
				left: 0,
				behavior: 'smooth'
			});
		}
	}

	// 重置搜索完成计时器
	function resetSearchCompleteTimer() {
		if (searchCompleteTimer) {
			clearTimeout(searchCompleteTimer);
		}

		searchCompleteTimer = setTimeout(() => {
			// 搜索完成后返回初始位置
			scrollToStart();
			updateButtonVisibility();
		}, RETURN_DELAY);
	}

	// 监听数据变化
	$: if (data && data.length > prevDataLength && scrollContainer) {
		// 如果数据增加了，自动滚动到最右侧
		setTimeout(() => {
			scrollToEnd();
			updateButtonVisibility();
		}, 100); // 短暂延迟确保DOM已更新
		prevDataLength = data.length;

		// 重置搜索完成计时器
		resetSearchCompleteTimer();
	}

	onMount(() => {
		updateButtonVisibility();
		prevDataLength = data?.length || 0;

		// 初始化搜索完成计时器
		if (data.length > 0) {
			resetSearchCompleteTimer();
		}
	});

	onDestroy(() => {
		// 清理计时器
		if (searchCompleteTimer) {
			clearTimeout(searchCompleteTimer);
		}
	});
</script>

<div
	class=" relative flex gap-3 items-center mb-2 mt-1 text-left text-sm text-gray-500 dark:text-gray-400"
>
	<div
		class=" inline-block rounded-full w-1 aspect-square bg-gray-500 dark:bg-gray-400 relative -translate-x-1/2"
	></div>
	<div>{$i18n.t('Search Result Count')}: {data.length}</div>
</div>

<div class="relative w-full">
	<!-- 左滚动按钮 -->
	{#if showLeftButton}
		<button
			class="absolute -left-4 top-1/2 -translate-y-1/2 z-10 bg-white/70 dark:bg-gray-600/70 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700/70"
			on:click={() => scroll('left')}
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="h-4 w-4"
				viewBox="0 0 20 20"
				fill="currentColor"
			>
				<path
					fill-rule="evenodd"
					d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
					clip-rule="evenodd"
				/>
			</svg>
		</button>
	{/if}

	<!-- 卡片容器 -->
	<div
		bind:this={scrollContainer}
		class="flex overflow-x-auto py-2 px-4 gap-4 no-scrollbar scroll-smooth border-l-2 border-[#E8E9EB] dark:border-[#27282A]"
		on:scroll={updateButtonVisibility}
	>
		{#each data || [] as result, index}
			<a
				href={result.url}
				target="_blank"
				rel="noopener noreferrer"
				class="flex-shrink-0 w-64 bg-gray-100/40 dark:bg-[#26282A] rounded-lg hover:shadow-md transition-shadow p-4 cursor-pointer not-prose"
			>
				<div
					class="text-sm font-medium truncate text-gray-900 dark:text-white line-clamp-2 mb-2 no-underline"
				>
					{index + 1}. {result.title}
				</div>
				<p class="text-xs text-gray-500 dark:text-gray-400 truncate no-underline">
					{result.source ?? result.url}
				</p>
			</a>
		{/each}
	</div>

	<!-- 右滚动按钮 -->
	{#if showRightButton}
		<button
			class="absolute -right-4 top-1/2 -translate-y-1/2 z-10 bg-white/70 dark:bg-gray-600/70 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700/70"
			on:click={() => scroll('right')}
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="h-4 w-4"
				viewBox="0 0 20 20"
				fill="currentColor"
			>
				<path
					fill-rule="evenodd"
					d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
					clip-rule="evenodd"
				/>
			</svg>
		</button>
	{/if}
</div>

<style>
	/* 隐藏滚动条但保持可滚动 */
	.no-scrollbar {
		-ms-overflow-style: none;
		scroll-width: none;
	}
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}
</style>
