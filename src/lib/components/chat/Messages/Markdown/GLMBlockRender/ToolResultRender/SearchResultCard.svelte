<script lang="ts">
	import GlobeAlt from '$lib/components/icons/GlobeAlt.svelte';
	import { showControls } from '$lib/stores';
	import { mcpData } from '$lib/stores/mcp';
	import type { GLMBlockGroup, I18nType } from '$lib/types';
	import { getContext } from 'svelte';

	const i18n: I18nType = getContext('i18n');

	const groupStyle = {
		start: 'rounded-t-xl mt-3',
		middle: 'rounded-none border-t-0',
		end: 'rounded-b-xl border-t-0',
		undefined: 'rounded-xl my-3'
	};
	export let group: GLMBlockGroup;
	export let data: { title: string; url: string; source?: string; text?: string }[] = [];

	const onClick = () => {
		mcpData.set({
			metadata: {
				id: '',
				name: 'search',
				arguments: '',
				result: '',
				duration: ''
			},
			browser: {
				search_result: data.map((item, idx) => {
					return {
						title: item.title,
						url: item.url,
						index: idx,
						text: item.text ?? ''
					};
				})
			}
		});
		showControls.set('search');
	};
</script>

<div class="w-full">
	<div
		class="flex items-center justify-between px-4 py-2 bg-[#F7F9FA] dark:bg-[#26282A] border-1 border-black/10 dark:border-white/10 text-xs {groupStyle[
			group
		]}"
	>
		<div class="flex items-center gap-2">
			<GlobeAlt className="size-4" strokeWidth="1.5" />
			<div>{$i18n.t('Search Result Count')}: {data.length}</div>
		</div>

		<div class="flex-shrink-0">
			<button
				class="px-2 py-1 flex items-center gap-1 rounded-lg hover:bg-[#ECEEF0] dark:hover:bg-white/5 font-medium"
				on:click={onClick}
			>
				{$i18n.t('View')}
			</button>
		</div>
	</div>
</div>
