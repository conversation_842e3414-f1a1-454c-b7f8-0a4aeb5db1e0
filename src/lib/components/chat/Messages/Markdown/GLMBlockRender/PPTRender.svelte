<script lang="ts">
	import CodeEditor from '$lib/components/common/CodeEditor.svelte';
	import Tab from '$lib/components/common/Tab.svelte';
	import { chatId, currentHistory, eventBus } from '$lib/stores';
	import Skeleton from '../../Skeleton.svelte';
	import { proxyIframeResources } from '$lib/utils/artifacts/artifacts_helper';
	import { EventBus } from '$lib/constants';
	import { afterUpdate, getContext, onDestroy, onMount, tick } from 'svelte';
	import CodeHighlight from '$lib/components/common/CodeHighlight.svelte';
	import { browser } from '$app/environment';
	import { copyToClipboard, sleep, throttle } from '$lib/utils';
	import Copy from '$lib/components/icons/Copy.svelte';
	import type { I18nType } from '$lib/types';
	import ArtifactsEditor from '$lib/utils/artifacts/artifactsEditor';
	import { updatePPT } from '$lib/apis/mcp';
	import { toast } from 'svelte-sonner';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import Pencil from '$lib/components/icons/Pencil.svelte';

	const i18n: I18nType = getContext('i18n');

	export let pptRaw: string = '';
	export let idx: number = 0;
	export let total: number = 0;
	export let editable = false;
	export let onEdit: (idx: number, type: 'open' | 'submit' | 'close') => void;
	export let filter = false;
	export let showEdit = false;

	// $: pptRawChangeEffect(pptRaw);
	// function pptRawChangeEffect(newPPT: string) {
	// 	loaded = false;
	// }

	let containerRef: HTMLElement;
	let iframeRef: HTMLIFrameElement;
	let originalWidth: number | null = null;
	let originalHeight: number | null = null;
	let resizeObserver: ResizeObserver;
	let scale: number = 1;

	let showType = 'preview';
	let userChangedShowType = false;
	let processing = false;
	let loaded = false;
	let copied = false;

	let editing = false;
	let artifactsEditor: ArtifactsEditor;
	let loadingUpdate = false;

	let prevDoneState: boolean | undefined = undefined;

	$: {
		const currentDoneState = $currentHistory?.messages[$currentHistory?.currentId as string]?.done;
		if (!prevDoneState && currentDoneState === true) {
			userChangedShowType = false; // Reset when message finishes loading
			processing = false;
		}
		autoSwitch(currentDoneState);
		prevDoneState = currentDoneState;
	}

	const autoSwitch = (done?: boolean) => {
		if (userChangedShowType) return; // Don't auto switch if user has manually changed it

		if (!done) {
			// showType = 'html';
		} else if (done) {
			showType = 'preview';
		}
	};

	const resizeIframe = throttle(() => {
		if (!iframeRef) return;

		const iframeDoc = iframeRef.contentDocument;
		const iframeBody = iframeDoc?.body;
		if (iframeBody && iframeRef.contentWindow) {
			// 可选：取iframeBody和firstChild中宽度最接近1280的作为采样元素

			// 获取内容的原始尺寸
			if (iframeBody.childElementCount === 1) {
				const firstChild = iframeBody.firstElementChild as HTMLElement;
				originalWidth = firstChild.offsetWidth;
				originalHeight = firstChild.offsetHeight;
			} else {
				originalWidth = iframeBody.scrollWidth;
				originalHeight = iframeBody.scrollHeight;
			}
			// 对文档尺寸的有限兜底，不太过分的基本可以支持
			if (originalWidth < 1280) {
				originalWidth = 1280;
				originalHeight = originalHeight > 1280 ? 720 : originalHeight;
			}

			if (originalWidth && originalHeight && containerRef) {
				const containerWidth = containerRef.offsetWidth;
				scale = containerWidth / originalWidth;
				artifactsEditor?.scaleInput(1 / scale);

				iframeRef.style.width = `${originalWidth}px`;
				iframeRef.style.height = `${originalHeight}px`;
				iframeRef.style.transform = `scale(${scale})`;
				iframeRef.style.transformOrigin = 'top left';

				const scaledHeight = originalHeight * scale;
				const parentDiv = iframeRef.parentElement;
				if (parentDiv) {
					parentDiv.style.height = `${scaledHeight}px`;
				}
			}
		}
	}, 80);

	async function handleIframeLoad() {
		if (!browser || !iframeRef?.contentWindow || !iframeRef?.contentDocument) return;
		// console.log('iframe loaded');
		const iframeDoc = iframeRef.contentDocument;
		const iframeBody = iframeDoc.body;
		loaded = true;

		// 注入样式
		const styleEl = iframeDoc.createElement('style');
		styleEl.textContent = `
			html, body {
				margin: 0;
				padding: 0;
				overflow: hidden; /* 防止出现滚动条影响尺寸计算 */
			}
			body > * {
				transform-origin: top left;
			}
		`;
		iframeDoc.head?.appendChild(styleEl);

		// 确保样式生效后再获取尺寸
		await tick();

		if (resizeObserver) {
			resizeObserver.disconnect();
		}

		resizeObserver = new ResizeObserver(() => {
			resizeIframe();
		});

		if (containerRef) {
			resizeObserver.observe(containerRef);
		}
		await sleep(100); // 由于输出的文档可能没有一个有效的尺寸呢，被流式布局后的高度由于宽高的兜底导致下方有空白区，此时再resize一下即可
		resizeIframe();

		if (editing) {
			artifactsEditor?.invokeEditorCode(iframeRef);
			artifactsEditor?.initMessageHandler((type, data) => {
				if (type === 'editElement') {
					editing = false;
					onEdit(idx, 'submit');
					const { line, code, text } = data;
					if (line && code && text) {
						window.postMessage(
							{
								type: 'input:prompt:submit',
								text,
								vibeInfo: {
									vibeMode: 'ppt',
									vibeReference: {
										line,
										code,
										filename: 'index.html',
										pptIndex: idx + 1
									}
								}
							},
							window.origin
						);
					}
				}
			});
			artifactsEditor?.scaleInput(1 / scale);
		}
	}

	onMount(() => {
		$eventBus.on(EventBus.PPT_SELECTED, (position: number) => {
			if (position === idx) {
				containerRef?.scrollIntoView({
					behavior: 'smooth',
					block: 'center',
					inline: 'center'
				});
			}
		});
		// 表示PPT的编辑状态，来自某些副作用
		$eventBus.on(EventBus.PPT_WORK_COMPLETE, async (position: number) => {
			if (position === idx) {
				showType = 'preview';
				userChangedShowType = true;
				processing = false;
			}
		});
		$eventBus.on(EventBus.PPT_WORK_PROCESSING, async (position: number) => {
			if (position === idx) {
				showType = 'html';
				userChangedShowType = true;
				processing = true;
			}
		});
		// 处理独立且纯粹的切换事件，即不来自某种副作用
		$eventBus.on(EventBus.PPT_SHOW_TYPE, (value: 'html' | 'preview') => {
			if (value === 'html' || value === 'preview') {
				showType = value;
				userChangedShowType = true; // 存疑，考虑要不要有
			}
		});
	});

	afterUpdate(() => {
		if (editable === false) {
			editing = false;
		}
	});

	onDestroy(() => {
		resizeObserver?.disconnect();
		editing = false;
	});
</script>

<div
	class="container @container max-w-full relative border-1 border-black/10 rounded-xl overflow-hidden"
	bind:this={containerRef}
>
	<div
		class=" tabContainer absolute z-10 h-0 top-3 left-3 right-3 mt-4 @max-md:top-2 @max-md:left-2 @max-md:right-2 @max-md:mt-3 flex justify-between items-center text-sm"
	>
		<div class=" bg-white rounded-full">
			{#if !editing}
				<Tab
					classNames={{
						trigger: ' @max-md:px-2 @max-md:py-0.5 @max-md:text-xs',
						list: 'border border-black/10 dark:border-white/10'
					}}
					bind:value={showType}
					items={[
						{
							value: 'preview',
							label: 'Preview'
						},
						{
							value: 'html',
							label: 'HTML'
						}
					]}
					onValueChange={(value) => {
						if (value) {
							showType = value;
							userChangedShowType = true;
						}
					}}
				/>
			{/if}
		</div>
		{#if showType === 'preview'}
			<div class="flex items-center gap-2">
				{#if !editing}
					{#if showEdit}
						<button
							disabled={!editable}
							class="flex items-center gap-1 border-none text-sm bg-white hover:bg-black/30 dark:bg-gray-850 dark:hover:bg-gray-800 transition rounded-full py-1.5 px-5 @max-md:px-2.5 @max-md:py-1 {editable
								? ''
								: ' cursor-not-allowed text-gray-400'}"
							on:click={() => {
								editing = true;
								onEdit?.(idx, 'open');
								artifactsEditor = new ArtifactsEditor(pptRaw, iframeRef);
								artifactsEditor.walkAST();
								artifactsEditor.serializeAST(proxyIframeResources);
							}}
						>
							<Pencil className="size-4" />
							<span>{$i18n.t('Edit')}</span>
						</button>
					{/if}
					<div
						class=" bg-white dark:bg-black rounded-full py-1.5 px-4.5 @max-md:px-2.5 @max-md:py-1"
					>
						{idx + 1}/{total}
					</div>
				{:else if showEdit}
					<button
						class="flex items-center gap-2 border-1 border-white/20 text-xs bg-black/10 hover:bg-gray-100 dark:bg-gray-850 dark:hover:bg-gray-800 transition rounded-full py-2 px-5 @max-md:px-2.5 @max-md:py-1"
						on:click={() => {
							editing = false;
							onEdit?.(idx, 'close');
						}}
					>
						{$i18n.t('Cancel')}
					</button>
					<button
						disabled={loadingUpdate}
						class="flex items-center gap-2 text-xs bg-white hover:bg-gray-100 dark:bg-gray-850 dark:hover:bg-gray-800 transition rounded-full py-2 px-5 @max-md:px-2.5 @max-md:py-1"
						on:click={async () => {
							if (!artifactsEditor.operated) {
								onEdit?.(idx, 'close');
								editing = false;
								return;
							}
							const afterEditHtml = artifactsEditor.getSerializeCode();
							try {
								loadingUpdate = true;
								await updatePPT({
									chatId: $chatId,
									html: afterEditHtml,
									index: idx + 1
								});
								$eventBus.emit(EventBus.PPT_UPDATE);
								editing = false;
								onEdit?.(idx, 'submit');
							} catch (error) {
								toast.error(error.message);
							} finally {
								loadingUpdate = false;
							}
						}}
					>
						{$i18n.t('Save')}
					</button>
				{/if}
			</div>
		{:else if showType === 'html'}
			<button
				class="flex items-center gap-2 border-none text-xs bg-white hover:bg-gray-100 dark:bg-gray-850 dark:hover:bg-gray-800 transition rounded-full py-2 px-5 @max-md:px-2.5 @max-md:py-1"
				on:click={() => {
					copyToClipboard(pptRaw);
					copied = true;

					setTimeout(() => {
						copied = false;
					}, 2000);
				}}
			>
				<Copy className="size-4 @max-md:size-3 inline" strokeWidth="1.5" />
				<span class="">
					{copied ? $i18n.t('Copied') : $i18n.t('Copy')}
				</span>
			</button>
		{/if}
	</div>
	{#if pptRaw?.length === 0}
		<div class="w-full flex p-2 aspect-[16/9]">
			<Skeleton />
		</div>
	{:else}
		<div class="w-full {showType === 'html' ? 'flex' : 'hidden'} aspect-[16/9] overflow-auto">
			<CodeHighlight code={pptRaw} language="xml" autoScroll />
		</div>
		{#if processing}
			<div
				class="w-full flex justify-center aspect-[16/9] p-2 {showType === 'preview'
					? 'block'
					: 'hidden'}"
			>
				<Skeleton />
			</div>
		{:else}
			<div
				class="w-full {showType === 'preview' ? 'block' : 'hidden'} overflow-hidden relative {loaded
					? ''
					: 'aspect-16/9'} {filter ? '' : ''}"
			>
				<div
					class="{loaded
						? 'hidden'
						: ''} absolute top-0 left-0 w-full h-full bg-white flex items-center justify-center gap-2 z-10"
				>
					<Spinner className="size-8" />
					<div class="text-sm text-gray-500">{$i18n.t('Loading')}</div>
				</div>

				<div
					class="{filter
						? ''
						: 'hidden'} absolute top-0 left-0 w-full h-full bg-black/50 flex items-center justify-center gap-2 z-10"
				></div>

				<iframe
					bind:this={iframeRef}
					title="PPT"
					srcdoc={editing ? artifactsEditor.codeSerialized : proxyIframeResources(pptRaw)}
					frameborder="0"
					allowfullscreen
					src="about:blank"
					class="absolute top-0 left-0 w-auto h-auto rounded-b-xl {loaded ? '' : 'w-full h-full'}"
					style="border: none;"
					on:error={(e) => {
						console.error(e);
					}}
					on:load={handleIframeLoad}
				></iframe>
			</div>
		{/if}
	{/if}
</div>

<style>
	.tabContainer {
		transition:
			opacity 0.1s ease-in-out,
			visibility 0.1s ease-in-out;
	}
	.container:not(:hover) .tabContainer {
		opacity: 0;
		visibility: hidden;
	}
	.container:hover .tabContainer {
		opacity: 1;
		visibility: visible;
	}
</style>
