<script lang="ts">
	import ChevronLeft from '$lib/components/icons/ChevronLeft.svelte';
	import ChevronRight from '$lib/components/icons/ChevronRight.svelte';
	import { mobile } from '$lib/stores';
	import { isMobile } from 'mobile-device-detect';
	import PptRender from './PPTRender.svelte';

	export let pptRawList: string[] = [];
	export let editable = false;
	export let onEdit: (idx: number, type: 'open' | 'close') => void = () => {};
	export let editingIndex = -1;
	export let filter = false;
	export let showEdit = false;
	export let selected = 0;
	$: length = pptRawList.length;
</script>

{#if $mobile}
	<div class="h-full w-full flex flex-col gap-8 justify-center items-center">
		<PptRender
			pptRaw={pptRawList[selected]}
			total={length}
			showEdit={!isMobile}
			idx={selected}
			editable={editable && (editingIndex === -1 || selected === editingIndex)}
			onEdit={(idx, type) => {
				onEdit?.(idx, type);
			}}
		/>
		<div class="flex justify-center items-center gap-2">
			<button
				disabled={editingIndex === selected}
				class="inline-flex items-center gap-2 text-xl p-2 rounded-lg hover:bg-black/5"
				on:click={() => {
					selected = selected === 0 ? 0 : selected - 1;
				}}><ChevronLeft className="size-6" strokeWidth="2" /></button
			>
			<div class="font-semibold">
				<span>{selected + 1}</span>
				<span>/</span>
				<span>{length}</span>
			</div>
			<button
				disabled={editingIndex === selected}
				class="inline-flex items-center gap-2 text-xl p-2 rounded-lg hover:bg-black/5"
				on:click={() => {
					selected = selected === length - 1 ? selected : selected + 1;
				}}><ChevronRight className="size-6" strokeWidth="2" /></button
			>
		</div>
	</div>
{:else}
	<div class="flex flex-col gap-4 max-w-full">
		{#each pptRawList as pptRaw, idx (idx)}
			<PptRender
				{pptRaw}
				total={length}
				{idx}
				{showEdit}
				filter={filter && idx !== editingIndex}
				editable={editable && (editingIndex === -1 || idx === editingIndex)}
				onEdit={(idx, type) => {
					onEdit?.(idx, type);
				}}
			/>
		{/each}
	</div>
{/if}
