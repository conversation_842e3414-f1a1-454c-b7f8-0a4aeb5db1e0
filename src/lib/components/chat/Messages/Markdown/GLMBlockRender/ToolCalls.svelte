<script lang="ts">
	import { getContext, onMount } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import SearchTool from './ToolCallsRender/SearchTool.svelte';
	import BookOpen from '$lib/components/icons/BookOpen.svelte';
	import Search from '$lib/components/icons/Search.svelte';
	import Spinner from '$lib/components/common/Spinner.svelte';
	import type { GLMBlockGroup } from '$lib/types';
	import MSearchTool from './ToolCallsRender/MSearchTool.svelte';

	export let data: { name: string; arguments: any; loading?: string };
	export let messageDone: boolean = false;
	export let group: GLMBlockGroup;
	const i18n: Writable<i18nType> = getContext('i18n');

	const groupStyle = {
		start: 'rounded-t-xl mt-3',
		middle: 'rounded-none border-t-0',
		end: 'rounded-b-xl border-t-0',
		undefined: 'rounded-xl my-3'
	};
</script>

{#if data.name === 'search'}
	<SearchTool query={data.arguments.query} {group} />
{/if}

{#if data.name === 'msearch'}
	<MSearchTool {data} {group} />
{/if}

{#if data.name === 'open' || data.name === 'click'}
	<div class="relative flex w-full items-center gap-2 text-sm">
		<div
			class="flex flex-1 max-w-full items-center gap-2 px-4 py-3 {groupStyle[
				group
			]} bg-[#F7F9FA] dark:bg-[#26282A] border-1 border-black/10 dark:border-white/10 text-xs"
		>
			<BookOpen className=" flex-shrink-0 size-4" />
			<div class="inline-flex max-w-full overflow-hidden items-center gap-2 flex-nowrap">
				<span class=" whitespace-nowrap">{$i18n.t('Open link')}</span>
				<a class=" max-w-full truncate" href={data.arguments.url}>{data.arguments.url}</a>
			</div>
			{#if data.loading && !messageDone}
				<div class="ml-2">
					<Spinner className="size-4 text-gray-500" />
				</div>
			{/if}
		</div>
	</div>
{/if}
