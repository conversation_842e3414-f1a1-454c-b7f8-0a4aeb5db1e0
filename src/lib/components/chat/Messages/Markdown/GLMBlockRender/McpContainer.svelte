<script lang="ts">
	import { getContext, tick, onMount } from 'svelte';
	import { showControls, eventBus, currentHistory } from '$lib/stores';
	import { mcpData, type MCPData } from '$lib/stores/mcp';
	import McpToolCalls from './ToolCallsRender/McpToolCalls.svelte';
	import { EventBus } from '$lib/constants';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import type { GLMBlockGroup } from '$lib/types';
	import type { SearchCacheManager } from '$lib/utils/SearchCacheManager';
	import { isMobile } from 'mobile-device-detect';

	type McpCardStatus = 'executing' | 'completed' | 'error' | undefined;

	type ToolsConfig = {
		showArguments: boolean;
		showPreviewButton: boolean;
		showResults: boolean; // 控制是否显示结果
	};

	// 配置需要显示参数的工具列表，哪天太多了就搬到外面
	const TOOLS_CONFIG_MAP: Record<string, ToolsConfig> = {
		search: {
			showArguments: true,
			showPreviewButton: true,
			showResults: false
		},
		visit_page: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		find_on_page_ctrl_f: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		page_up: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		page_down: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		python: {
			showArguments: false,
			showPreviewButton: true,
			showResults: false
		},
		find_next: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		click: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		},
		go_back: {
			showArguments: true,
			showPreviewButton: false,
			showResults: false
		}
	};

	// PPT 相关工具列表，如果有必要的时候改为enum
	const PPT_TOOLS: Record<string, boolean> = {
		create_slide: true,
		add_slide: true,
		update_slide: true
	};

	const i18n: Writable<i18nType> = getContext('i18n');
	const searchCacheManager: SearchCacheManager = getContext('searchCacheManager');

	export let data: MCPData;
	export let messageId: string = '';
	export let messageDone: boolean = true;
	export let group: GLMBlockGroup;

	let status: McpCardStatus = undefined;
	$: status = $currentHistory?.messages[messageId]?.done ? 'completed' : data.metadata.status;
	$: statusChangeEffect(status);

	// 监听搜索结果数据变化，自动添加到缓存
	$: if (data.browser?.search_result && data.browser.search_result.length > 0) {
		searchCacheManager.addCitations(data.browser.search_result);
	}
	function statusChangeEffect(status: McpCardStatus) {
		if (!status) return;
		if (messageDone) return; // 已完成的消息不再响应状态变化
		if (data.ppt?.show) {
			// PPT每完成一张就给右侧发送一个事件，预览对应页的ppt
			if (status === 'completed') {
				$eventBus.emit(
					EventBus.PPT_WORK_COMPLETE,
					data.ppt.position === 0 ? 0 : data.ppt.position - 1
				);
			}
		}
	}

	$: showPreviewButton = enableShowPreviewButton(data);
	// 以具有特殊字段的优先，其次按工具命名配置
	function enableShowPreviewButton(data: MCPData) {
		if (data.ppt) {
			return data.ppt.show;
		} else {
			return TOOLS_CONFIG_MAP[data.metadata.name]?.showPreviewButton ?? false;
		}
	}

	// 响应式计算：是否显示参数和格式化后的参数
	$: showArguments = shouldShowArguments(data.metadata.name);
	$: formattedArguments = showArguments ? formatArguments(data.metadata.arguments || '') : '';

	// 响应式计算：获取原始URL（用于支持title替换时的链接跳转）
	$: originalUrl = getOriginalUrl(data.metadata.name, data.metadata.arguments || '');

	// 响应式计算：是否显示结果和格式化后的结果
	$: showResults = shouldShowResults(data.metadata.name);
	$: formattedResults = showResults
		? formatResults(data.metadata?.display_result || data.metadata.result || '')
		: '';

	function shouldShowArguments(toolName: string): boolean {
		return TOOLS_CONFIG_MAP[toolName]?.showArguments ?? false;
	}

	function shouldShowResults(toolName: string): boolean {
		const config = TOOLS_CONFIG_MAP[toolName];
		// 使用showResults参数，如果没有配置则默认为true保持兼容性
		return config?.showResults ?? true;
	}

	// 获取原始URL的函数
	function getOriginalUrl(toolName: string, args: string): string {
		if (!args) return '';

		try {
			const parsed = JSON.parse(args);
			if (typeof parsed === 'object' && parsed !== null) {
				// 对于 visit_page 工具，返回原始 URL
				if (toolName === 'visit_page' && parsed.url) {
					return parsed.url;
				}

				// 对于 click 工具，优先使用 browser 数据中的 current_url
				if (toolName === 'click') {
					if (data.browser?.current_url) {
						return data.browser.current_url;
					}
				}
			}
		} catch (e) {
			// 如果解析失败，返回空字符串
		}

		return '';
	}

	// 格式化参数显示
	function formatArguments(args: string): string {
		if (!args) return '';
		try {
			const parsed = JSON.parse(args);
			if (typeof parsed === 'object' && parsed !== null) {
				// 对于 click 工具，优先显示 current_url
				if (data.metadata.name === 'click') {
					// 首先尝试从 browser 数据中获取 page_title
					const pageTitle = data.browser?.page_title;
					if (pageTitle) {
						return pageTitle;
					}
					if (data.browser?.current_url) {
						return data.browser.current_url;
					}
					return ``;
				}

				// 将对象转换为只显示值的格式
				const entries = Object.entries(parsed);
				if (entries.length === 0) return '';

				// 对于 visit_page 工具，如果只有一个 URL 参数，直接尝试替换为标题
				if (
					data.metadata.name === 'visit_page' &&
					entries.length === 1 &&
					entries[0][0] === 'url'
				) {
					const title = searchCacheManager.findTitleByUrl(entries[0][1] as string);
					if (title) {
						return title;
					}
				}

				if (data.metadata.name === 'search' && parsed.queries) {
					const queryString = parsed.queries.slice(0, 3).join('\u3000');
					if (parsed.queries.length > 3) {
						return `${queryString} ...`;
					}
					return queryString;
				}

				// 限制显示的参数数量和长度，只显示值
				const displayValues = entries.slice(0, 3).map(([key, value]) => {
					let displayValue = String(value);

					if (displayValue.length > 80) {
						displayValue = displayValue.substring(0, 77) + '...';
					}
					return displayValue;
				});

				let result = displayValues.join(', ');
				if (entries.length > 3) {
					result += ', ...';
				}
				return result;
			}
		} catch (e) {
			// 如果解析失败，直接显示字符串（截断长度）
			if (args.length > 100) {
				return args.substring(0, 97) + '...';
			}
			return args;
		}

		return '';
	}

	// 格式化结果显示
	function formatResults(result: string): string {
		if (!result) return '';

		// 结果通常是简单文本，限制长度即可
		if (result.length > 150) {
			return result.substring(0, 147) + '...';
		}
		return result;
	}

	$: isPPT = isPPTTool(data.metadata.name);
	function isPPTTool(toolName: string): boolean {
		return PPT_TOOLS[toolName] ?? false;
	}

	onMount(async () => {
		console.log('mcp data:', data);
		// 自动展开的逻辑
		if (data.ppt?.show) {
			mcpData.set(data);
			await tick();
			if (isMobile) {
				status !== 'completed' && showControls.set('ppt');
			} else {
				showControls.set('ppt');
			}
			$eventBus.emit(EventBus.PPT_UPDATE);
			await tick();
		}
	});

	async function onClickMcpCard() {
		// 对于 visit_page 工具，直接跳转到 URL
		if (data.metadata.name === 'visit_page') {
			try {
				const args = JSON.parse(data.metadata.arguments || '{}');
				if (args.url) {
					window.open(args.url, '_blank');
					return;
				}
			} catch (e) {
				console.error('Failed to parse visit_page arguments:', e);
			}
		}

		// 对于 click 工具，跳转到当前页面 URL
		if (data.metadata.name === 'click') {
			try {
				// 优先使用 browser 数据中的 current_url
				const currentUrl = data.browser?.current_url;
				if (currentUrl) {
					window.open(currentUrl, '_blank');
					return;
				}
			} catch (e) {
				console.error('Failed to parse click arguments:', e);
			}
		}

		if (data.metadata.name?.toLowerCase().includes('search')) {
			showControls.set('search');
			mcpData.set(data);
			return;
		}

		if (data.ppt?.show) {
			mcpData.set(data);
			await tick(); // 并不意味着右侧会立即显示
			showControls.set('ppt');
			$eventBus.emit(EventBus.PPT_UPDATE);
			$eventBus.emit(EventBus.PPT_SELECTED, data.ppt.position === 0 ? 0 : data.ppt.position - 1);
		} else {
			showControls.set('mcp');
			mcpData.set(data);
		}
	}
</script>

<McpToolCalls
	name={data.metadata.name}
	mcpServerName={data.metadata.mcp_server?.name || 'unknown'}
	toolArguments={formattedArguments}
	result={formattedResults}
	{status}
	supportView={showPreviewButton}
	onClick={onClickMcpCard}
	{group}
	{originalUrl}
/>
