<script>
	import { marked } from 'marked';
	import { replaceTokens, processResponseContent, postLexer } from '$lib/utils';
	import { user } from '$lib/stores';

	import markedExtension from '$lib/utils/marked/extension';
	import markedKatexExtension from '$lib/utils/marked/katex-extension';
	import markedGLMExtension from '$lib/utils/marked/glm-extension';

	import MarkdownTokens from './Markdown/MarkdownTokens.svelte';
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	export let id = '';
	export let content;
	export let model = null;
	export let save = false;
	export let messageDone = false;
	export let sourceIds = [];

	export let onSourceClick = () => {};
	export let onTaskClick = () => {};

	let tokens = [];

	const options = {
		throwOnError: false
	};

	marked.use(markedGLMExtension(options));
	marked.use(markedKatexExtension(options));
	marked.use(markedExtension(options));

	$: (async (content) => {
		if (content) {
			tokens = postLexer(
				marked.lexer(
					replaceTokens(processResponseContent(content), sourceIds, model?.name, $user?.name)
				)
			);
		}
	})(content);
</script>

{#key id}
	<MarkdownTokens
		{tokens}
		{id}
		{save}
		{onTaskClick}
		{onSourceClick}
		{messageDone}
		on:update={(e) => {
			dispatch('update', e.detail);
		}}
		on:code={(e) => {
			dispatch('code', e.detail);
		}}
	/>
{/key}
