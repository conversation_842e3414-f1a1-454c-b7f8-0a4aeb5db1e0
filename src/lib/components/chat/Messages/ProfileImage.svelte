<script lang="ts">
	import { WEBUI_BASE_URL } from '$lib/constants';
	import { userTheme } from '$lib/stores';

	export let className = 'size-8';
	export let src = `${WEBUI_BASE_URL}/static/logoLight.svg`;
</script>

<img
	crossorigin="anonymous"
	src={src === ''
		? $userTheme === 'light'
			? '/static/logoLight.svg'
			: '/static/logoDark.svg'
		: src}
	class=" {className} object-contain -translate-y-[1px]"
	alt="profile"
	draggable="false"
/>
