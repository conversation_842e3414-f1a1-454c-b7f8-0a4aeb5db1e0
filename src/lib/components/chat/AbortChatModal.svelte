<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { fade } from 'svelte/transition';
	import { flyAndScale } from '$lib/utils/transitions';

	export let show = false;
	export let onConfirm = () => {};
	export let onCancel = () => {};

	const i18n = getContext('i18n');

	let modalElement = null;
	let mounted = false;

	const handleKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Escape') {
			show = false;
			onCancel();
		}

		if (event.key === 'Enter') {
			confirmHandler();
		}
	};

	const confirmHandler = async () => {
		show = false;
		await onConfirm();
	};

	onMount(() => {
		mounted = true;
	});

	$: if (mounted) {
		if (show && modalElement) {
			document.body.appendChild(modalElement);
			window.addEventListener('keydown', handleKeyDown);
			document.body.style.overflow = 'hidden';
		} else if (modalElement) {
			window.removeEventListener('keydown', handleKeyDown);
			document.body.removeChild(modalElement);
			document.body.style.overflow = 'unset';
		}
	}
</script>

{#if show}
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div
		bind:this={modalElement}
		class="fixed top-0 right-0 left-0 bottom-0 bg-black/60 w-full h-screen max-h-[100dvh] flex justify-center z-99999999 overflow-hidden overscroll-contain"
		in:fade={{ duration: 10 }}
		on:mousedown={() => {
			show = false;
			onCancel();
		}}
	>
		<div
			class="m-auto rounded-2xl text-center max-w-full w-[480px] mx-2 bg-gray-50 dark:bg-gray-950 max-h-[100dvh] shadow-3xl"
			in:flyAndScale
			on:mousedown={(e) => {
				e.stopPropagation();
			}}
		>
			<div class="p-[40px] flex flex-col items-center">
				<div class="text-[24px] font-medium dark:text-gray-200 mb-[12px]">
					{$i18n.t('You can only generate three tasks simultaneously.')}
				</div>

				<div class="text-sm text-gray-500 flex-1">
					{$i18n.t('Please wait for the session to complete.')}
				</div>
				<div class="flex justify-between gap-1.5 mt-8 w-full h-[48px]">
					<button
						class="bg-gray-100 hover:bg-gray-200 text-white dark:bg-gray-200 dark:hover:bg-gray-300 dark:text-white font-medium w-full py-2.5 rounded-lg transition"
						on:click={() => {
							show = false;
							onConfirm();
						}}
						type="button"
						style={`background: linear-gradient(124.94deg, #191A1D 11.04%, #747689 96.98%, #191A1D 164.2%);`}
					>
						{$i18n.t('OK')}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	.modal-content {
		animation: scaleUp 0.1s ease-out forwards;
	}

	@keyframes scaleUp {
		from {
			transform: scale(0.985);
			opacity: 0;
		}
		to {
			transform: scale(1);
			opacity: 1;
		}
	}
</style>
