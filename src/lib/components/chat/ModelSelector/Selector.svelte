<script lang="ts">
	import { DropdownMenu } from 'bits-ui';
	import { marked } from 'marked';
	import Fuse from 'fuse.js';

	import { flyAndScale } from '$lib/utils/transitions';
	import { createEventDispatcher, onMount, getContext, tick } from 'svelte';

	import ChevronDown from '$lib/components/icons/ChevronDown.svelte';
	import Check from '$lib/components/icons/Check.svelte';
	import Search from '$lib/components/icons/Search.svelte';

	import { deleteModel, getOllamaVersion, pullModel } from '$lib/apis/ollama';

	import {
		user,
		MODEL_DOWNLOAD_POOL,
		models,
		mobile,
		temporaryChatEnabled,
		settings,
		config
	} from '$lib/stores';
	import { toast } from 'svelte-sonner';
	import { capitalizeFirstLetter, sanitizeResponseContent, splitStream } from '$lib/utils';
	import { getModels } from '$lib/apis';

	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import Switch from '$lib/components/common/Switch.svelte';
	import ChatBubbleOval from '$lib/components/icons/ChatBubbleOval.svelte';
	import { goto } from '$app/navigation';
	import Drawer from '$lib/components/common/Drawer.svelte';
	import Block from '$lib/components/common/Block.svelte';
	import { trackButtonClick } from '$lib/utils/analytics';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let id = '';
	export let value = '';
	export let placeholder = 'Select a model';
	export let searchEnabled = true;
	export let searchPlaceholder = $i18n.t('Search model');

	export let showTemporaryChatControl = false;

	export let items: {
		label: string;
		value: string;
		model: Model;
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		[key: string]: any;
	}[] = [];

	export let className = 'w-[32rem]';
	export let triggerClassName = ' text-black dark:text-white';

	export let disabled = false;

	let featureSupported = false;
	let tagsContainerElement;

	let show = false;
	let tags = [];

	let selectedModel = '';
	$: selectedModel = items.find((item) => item.value === value) ?? '';

	let searchValue = '';

	let selectedTag = '';
	let selectedConnectionType = '';

	let ollamaVersion = null;
	let selectedModelIdx = 0;

	const fuse = new Fuse(
		items.map((item) => {
			const _item = {
				...item,
				modelName: item.model?.name,
				tags: (item.model?.tags ?? []).map((tag) => tag.name).join(' '),
				desc: item.model?.info?.meta?.description
			};
			return _item;
		}),
		{
			keys: ['value', 'tags', 'modelName'],
			threshold: 0.4
		}
	);

	$: filteredItems = searchValue
		? fuse
				.search(searchValue)
				.map((e) => {
					return e.item;
				})
				.filter((item) => {
					if (selectedTag === '') {
						return true;
					}
					return (item.model?.tags ?? []).map((tag) => tag.name).includes(selectedTag);
				})
				.filter((item) => {
					if (selectedConnectionType === '') {
						return true;
					} else if (selectedConnectionType === 'ollama') {
						return item.model?.owned_by === 'ollama';
					} else if (selectedConnectionType === 'openai') {
						return item.model?.owned_by === 'openai';
					} else if (selectedConnectionType === 'direct') {
						return item.model?.direct;
					}
				})
		: items
				.filter((item) => {
					if (selectedTag === '') {
						return true;
					}
					return (item.model?.tags ?? []).map((tag) => tag.name).includes(selectedTag);
				})
				.filter((item) => {
					if (selectedConnectionType === '') {
						return true;
					} else if (selectedConnectionType === 'ollama') {
						return item.model?.owned_by === 'ollama';
					} else if (selectedConnectionType === 'openai') {
						return item.model?.owned_by === 'openai';
					} else if (selectedConnectionType === 'direct') {
						return item.model?.direct;
					}
				});

	$: if (selectedTag || selectedConnectionType) {
		resetView();
	} else {
		resetView();
	}

	const resetView = async () => {
		await tick();

		const selectedInFiltered = filteredItems.findIndex((item) => item.value === value);

		if (selectedInFiltered >= 0) {
			// The selected model is visible in the current filter
			selectedModelIdx = selectedInFiltered;
		} else {
			// The selected model is not visible, default to first item in filtered list
			selectedModelIdx = 0;
		}

		await tick();
		const item = document.querySelector(`[data-arrow-selected="true"]`);
		item?.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'instant' });
	};

	const pullModelHandler = async () => {
		const sanitizedModelTag = searchValue.trim().replace(/^ollama\s+(run|pull)\s+/, '');

		console.log($MODEL_DOWNLOAD_POOL);
		if ($MODEL_DOWNLOAD_POOL[sanitizedModelTag]) {
			toast.error(
				$i18n.t(`Model '{{modelTag}}' is already in queue for downloading.`, {
					modelTag: sanitizedModelTag
				})
			);
			return;
		}
		if (Object.keys($MODEL_DOWNLOAD_POOL).length === 3) {
			toast.error(
				$i18n.t('Maximum of 3 models can be downloaded simultaneously. Please try again later.')
			);
			return;
		}

		const [res, controller] = await pullModel(localStorage.token, sanitizedModelTag, '0').catch(
			(error) => {
				toast.error(`${error}`);
				return null;
			}
		);

		if (res) {
			const reader = res.body
				.pipeThrough(new TextDecoderStream())
				.pipeThrough(splitStream('\n'))
				.getReader();

			MODEL_DOWNLOAD_POOL.set({
				...$MODEL_DOWNLOAD_POOL,
				[sanitizedModelTag]: {
					...$MODEL_DOWNLOAD_POOL[sanitizedModelTag],
					abortController: controller,
					reader,
					done: false
				}
			});

			while (true) {
				try {
					const { value, done } = await reader.read();
					if (done) break;

					let lines = value.split('\n');

					for (const line of lines) {
						if (line !== '') {
							let data = JSON.parse(line);
							console.log(data);
							if (data.error) {
								throw data.error;
							}
							if (data.detail) {
								throw data.detail;
							}

							if (data.status) {
								if (data.digest) {
									let downloadProgress = 0;
									if (data.completed) {
										downloadProgress = Math.round((data.completed / data.total) * 1000) / 10;
									} else {
										downloadProgress = 100;
									}

									MODEL_DOWNLOAD_POOL.set({
										...$MODEL_DOWNLOAD_POOL,
										[sanitizedModelTag]: {
											...$MODEL_DOWNLOAD_POOL[sanitizedModelTag],
											pullProgress: downloadProgress,
											digest: data.digest
										}
									});
								} else {
									toast.success(data.status);

									MODEL_DOWNLOAD_POOL.set({
										...$MODEL_DOWNLOAD_POOL,
										[sanitizedModelTag]: {
											...$MODEL_DOWNLOAD_POOL[sanitizedModelTag],
											done: data.status === 'success'
										}
									});
								}
							}
						}
					}
				} catch (error) {
					console.log(error);
					if (typeof error !== 'string') {
						error = error.message;
					}

					toast.error(`${error}`);
					// opts.callback({ success: false, error, modelName: opts.modelName });
					break;
				}
			}

			if ($MODEL_DOWNLOAD_POOL[sanitizedModelTag].done) {
				toast.success(
					$i18n.t(`Model '{{modelName}}' has been successfully downloaded.`, {
						modelName: sanitizedModelTag
					})
				);

				models.set(
					await getModels(
						localStorage.token,
						$config?.features?.enable_direct_connections && ($settings?.directConnections ?? null)
					)
				);
			} else {
				toast.error($i18n.t('Download canceled'));
			}

			delete $MODEL_DOWNLOAD_POOL[sanitizedModelTag];

			MODEL_DOWNLOAD_POOL.set({
				...$MODEL_DOWNLOAD_POOL
			});
		}
	};

	onMount(async () => {
		ollamaVersion = await getOllamaVersion(localStorage.token).catch((error) => false);

		if (items) {
			tags = items.flatMap((item) => item.model?.tags ?? []).map((tag) => tag.name);

			// Remove duplicates and sort
			tags = Array.from(new Set(tags)).sort((a, b) => a.localeCompare(b));
		}
	});

	const cancelModelPullHandler = async (model: string) => {
		const { reader, abortController } = $MODEL_DOWNLOAD_POOL[model];
		if (abortController) {
			abortController.abort();
		}
		if (reader) {
			await reader.cancel();
			delete $MODEL_DOWNLOAD_POOL[model];
			MODEL_DOWNLOAD_POOL.set({
				...$MODEL_DOWNLOAD_POOL
			});
			await deleteModel(localStorage.token, model);
			toast.success(`${model} download has been canceled`);
		}
	};
</script>

<DropdownMenu.Root
	bind:open={show}
	onOpenChange={async (opened) => {
		searchValue = '';
		window.setTimeout(() => document.getElementById('model-search-input')?.focus(), 0);
		trackButtonClick('modelcard', opened ? 'modelcard_unfold' : 'modelcard_fold');
		resetView();
	}}
	closeFocus={false}
	closeOnOutsideClick={!$mobile}
>
	<DropdownMenu.Trigger
		class="relative w-full font-primary {disabled ? 'cursor-default' : ''}"
		aria-label={placeholder}
		id="model-selector-{id}-button"
		{disabled}
	>
		<div
			class="flex w-full text-left px-0.5 outline-hidden bg-transparent truncate {triggerClassName} {$mobile
				? 'text-sm'
				: 'text-lg'} justify-between font-medium placeholder-gray-400 focus:outline-hidden"
		>
			{#if selectedModel}
				{selectedModel.label}
			{:else}
				{placeholder}
			{/if}
			{#if !disabled}
				<ChevronDown className=" self-center ml-2 size-3" strokeWidth="2.5" />
			{/if}
		</div>
	</DropdownMenu.Trigger>

	<DropdownMenu.Content
		class=" z-40 {$mobile
			? `w-full`
			: `${className}`} max-w-[calc(100vw-1rem)] justify-start rounded-xl  bg-white dark:bg-gray-850 dark:text-white shadow-lg  outline-hidden"
		side={$mobile ? 'bottom' : 'bottom-start'}
		sideOffset={3}
	>
		<svelte:component this={$mobile ? Drawer : Block} bind:show>
			<div class=" font-semibold pl-7 pt-4 mb-2">Model</div>

			{#if searchEnabled}
				<div
					class="flex items-center gap-2.5 mt-2 mx-4 mb-2 border-1 border-black/10 rounded-lg p-3"
				>
					<Search className="size-4" strokeWidth="2.5" />

					<input
						id="model-search-input"
						bind:value={searchValue}
						class="w-full text-sm bg-transparent outline-hidden"
						placeholder={searchPlaceholder}
						autocomplete="off"
						on:keydown={(e) => {
							if (e.code === 'Enter' && filteredItems.length > 0) {
								value = filteredItems[selectedModelIdx].value;
								show = false;
								return; // dont need to scroll on selection
							} else if (e.code === 'ArrowDown') {
								selectedModelIdx = Math.min(selectedModelIdx + 1, filteredItems.length - 1);
							} else if (e.code === 'ArrowUp') {
								selectedModelIdx = Math.max(selectedModelIdx - 1, 0);
							} else {
								// if the user types something, reset to the top selection.
								selectedModelIdx = 0;
							}

							const item = document.querySelector(`[data-arrow-selected="true"]`);
							item?.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'instant' });
						}}
					/>
				</div>
			{/if}

			<div class="px-4 mb-4 max-h-72 overflow-y-auto scrollbar-hidden group relative">
				{#if featureSupported && tags && items.filter((item) => !(item.model?.info?.meta?.hidden ?? false)).length > 0}
					<div
						class=" flex w-full sticky top-0 z-10 bg-white dark:bg-gray-850 overflow-x-auto scrollbar-none"
						on:wheel={(e) => {
							if (e.deltaY !== 0) {
								e.preventDefault();
								e.currentTarget.scrollLeft += e.deltaY;
							}
						}}
					>
						<div
							class="flex gap-1 w-fit text-center text-sm font-medium rounded-full bg-transparent px-1.5 pb-0.5"
							bind:this={tagsContainerElement}
						>
							<button
								class="min-w-fit outline-none p-1.5 {selectedTag === '' &&
								selectedConnectionType === ''
									? ''
									: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition capitalize"
								on:click={() => {
									selectedConnectionType = '';
									selectedTag = '';
								}}
							>
								{$i18n.t('All')}
							</button>

							{#if items.find((item) => item.model?.owned_by === 'ollama') && items.find((item) => item.model?.owned_by === 'openai')}
								<button
									class="min-w-fit outline-none p-1.5 {selectedConnectionType === 'ollama'
										? ''
										: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition capitalize"
									on:click={() => {
										selectedTag = '';
										selectedConnectionType = 'ollama';
									}}
								>
									{$i18n.t('Local')}
								</button>
								<button
									class="min-w-fit outline-none p-1.5 {selectedConnectionType === 'openai'
										? ''
										: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition capitalize"
									on:click={() => {
										selectedTag = '';
										selectedConnectionType = 'openai';
									}}
								>
									{$i18n.t('External')}
								</button>
							{/if}

							{#if items.find((item) => item.model?.direct)}
								<button
									class="min-w-fit outline-none p-1.5 {selectedConnectionType === 'direct'
										? ''
										: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition capitalize"
									on:click={() => {
										selectedTag = '';
										selectedConnectionType = 'direct';
									}}
								>
									{$i18n.t('Direct')}
								</button>
							{/if}

							{#each tags as tag}
								<button
									class="min-w-fit outline-none p-1.5 {selectedTag === tag
										? ''
										: 'text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white'} transition capitalize"
									on:click={() => {
										selectedConnectionType = '';
										selectedTag = tag;
									}}
								>
									{tag}
								</button>
							{/each}
						</div>
					</div>
				{/if}

				{#each filteredItems.filter((item) => !(item.model?.info?.meta?.hidden ?? false)) as item, index}
					<button
						aria-label="model-item"
						class="flex items-center w-full text-left font-medium line-clamp-1 select-none p-3 mb-1 text-sm text-black dark:text-white/80 outline-hidden transition-all duration-75 hover:bg-[#F4F6F8] dark:hover:bg-[#3C3E40] rounded-lg cursor-pointer data-highlighted:bg-muted {index ===
						selectedModelIdx
							? ' bg-[#F4F6F8] dark:bg-[#3C3E40] group-hover:bg-transparent'
							: ''}"
						data-arrow-selected={index === selectedModelIdx}
						data-value={item.value}
						on:click={() => {
							let model_type = '';
							switch (item.value) {
								case 'deep-research':
									model_type = 'Rumination';
									break;
								case 'main_chat':
									model_type = '32B';
									break;
								case 'zero':
									model_type = 'Z1';
									break;
								default:
									model_type = item.value;
							}
							trackButtonClick('modelcard', 'modelcard_click', model_type);
							value = item.value;
							selectedModelIdx = index;

							show = false;
						}}
					>
						<div class="flex flex-col justify-center">
							<div class="flex w-full lext-left font-medium line-clamp-1 items-center">
								<div class="flex flex-col">
									{#if $mobile && (item?.model?.tags ?? []).length > 0}
										<div class="flex gap-0.5 self-start h-full mb-1.5 -translate-x-1">
											{#each item.model?.tags.sort((a, b) => a.name.localeCompare(b.name)) as tag}
												<div
													class=" text-xs font-bold px-1 rounded-sm uppercase line-clamp-1 bg-gray-500/20 text-gray-700 dark:text-gray-200"
												>
													{tag.name}
												</div>
											{/each}
										</div>
									{/if}
									<div class="flex items-center gap-2">
										<div class="flex items-center min-w-fit">
											<div class="line-clamp-1">
												<div class="flex items-center min-w-fit">
													<div class="flex items-center line-clamp-1">
														<div class="line-clamp-1 font-medium">
															{item.label}
														</div>

														{#if item.model.owned_by === 'ollama' && (item.model.ollama?.details?.parameter_size ?? '') !== ''}
															<div class="flex ml-1 items-center translate-y-[0.5px]">
																<Tooltip
																	content={`${
																		item.model.ollama?.details?.quantization_level
																			? item.model.ollama?.details?.quantization_level + ' '
																			: ''
																	}${
																		item.model.ollama?.size
																			? `(${(item.model.ollama?.size / 1024 ** 3).toFixed(1)}GB)`
																			: ''
																	}`}
																	className="self-end"
																>
																	<span
																		class=" text-xs font-medium text-gray-600 dark:text-gray-400 line-clamp-1"
																		>{item.model.ollama?.details?.parameter_size ?? ''}</span
																	>
																</Tooltip>
															</div>
														{/if}
													</div>
												</div>
											</div>
										</div>

										<!-- {item.model?.info?.meta?.description} -->

										{#if !$mobile && (item?.model?.tags ?? []).length > 0}
											<div
												class="flex gap-0.5 self-center items-center h-full translate-y-[0.5px] overflow-x-auto scrollbar-none"
											>
												{#each item.model?.tags.sort((a, b) => a.name.localeCompare(b.name)) as tag}
													<Tooltip content={tag.name} className="flex-shrink-0">
														<div
															class=" text-xs font-bold px-1 rounded-sm uppercase bg-gray-500/20 text-gray-700 dark:text-gray-200"
														>
															{tag.name}
														</div>
													</Tooltip>
												{/each}
											</div>
										{/if}
									</div>
								</div>
							</div>
							{#if item.model?.info?.meta?.description}
								<!-- 不展示 tooltip 直接另起一行 -->
								<div class="text-xs text-gray-500 dark:text-gray-400 line-clamp-1 mt-[6px]">
									{$i18n.t(item.model?.info?.meta?.description ?? '')}
								</div>
							{/if}
						</div>

						{#if value === item.value}
							<div class="ml-auto pl-2 pr-2 md:pr-0">
								<Check />
							</div>
						{/if}
					</button>
				{:else}
					<div>
						<div class="block px-3 py-2 text-sm text-gray-700 dark:text-gray-100">
							{$i18n.t('No results found')}
						</div>
					</div>
				{/each}

				{#if !(searchValue.trim() in $MODEL_DOWNLOAD_POOL) && searchValue && ollamaVersion && $user?.role === 'admin'}
					<Tooltip
						content={$i18n.t(`Pull "{{searchValue}}" from Ollama.com`, {
							searchValue: searchValue
						})}
						placement="top-start"
					>
						<button
							class="flex w-full font-medium line-clamp-1 select-none items-center py-2 pl-3 pr-1.5 text-sm text-gray-700 dark:text-gray-100 outline-hidden transition-all duration-75 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg cursor-pointer data-highlighted:bg-muted"
							on:click={() => {
								pullModelHandler();
							}}
						>
							<div class=" truncate">
								{$i18n.t(`Pull "{{searchValue}}" from Ollama.com`, { searchValue: searchValue })}
							</div>
						</button>
					</Tooltip>
				{/if}

				{#each Object.keys($MODEL_DOWNLOAD_POOL) as model}
					<div
						class="flex w-full justify-between font-medium select-none py-2 pl-3 pr-1.5 text-sm text-gray-700 dark:text-gray-100 outline-hidden transition-all duration-75 rounded-lg cursor-pointer data-highlighted:bg-muted"
					>
						<div class="flex">
							<div class="-ml-2 mr-2.5 translate-y-0.5">
								<svg
									class="size-4"
									viewBox="0 0 24 24"
									fill="currentColor"
									xmlns="http://www.w3.org/2000/svg"
								>
									<style>
										.spinner_ajPY {
											transform-origin: center;
											animation: spinner_AtaB 0.75s infinite linear;
										}

										@keyframes spinner_AtaB {
											100% {
												transform: rotate(360deg);
											}
										}
									</style>
									<path
										d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"
										opacity=".25"
									/>
									<path
										d="M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z"
										class="spinner_ajPY"
									/>
								</svg>
							</div>

							<div class="flex flex-col self-start">
								<div class="flex gap-1">
									<div class="line-clamp-1">
										Downloading "{model}"
									</div>

									<div class="shrink-0">
										{'pullProgress' in $MODEL_DOWNLOAD_POOL[model]
											? `(${$MODEL_DOWNLOAD_POOL[model].pullProgress}%)`
											: ''}
									</div>
								</div>

								{#if 'digest' in $MODEL_DOWNLOAD_POOL[model] && $MODEL_DOWNLOAD_POOL[model].digest}
									<div class="-mt-1 h-fit text-[0.7rem] dark:text-gray-500 line-clamp-1">
										{$MODEL_DOWNLOAD_POOL[model].digest}
									</div>
								{/if}
							</div>
						</div>

						<div class="mr-2 ml-1 translate-y-0.5">
							<Tooltip content={$i18n.t('Cancel')}>
								<button
									class="text-gray-800 dark:text-gray-100"
									on:click={() => {
										cancelModelPullHandler(model);
									}}
								>
									<svg
										class="w-4 h-4 text-gray-800 dark:text-white"
										aria-hidden="true"
										xmlns="http://www.w3.org/2000/svg"
										width="24"
										height="24"
										fill="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke="currentColor"
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M6 18 17.94 6M18 18 6.06 6"
										/>
									</svg>
								</button>
							</Tooltip>
						</div>
					</div>
				{/each}
			</div>

			{#if false}
				<hr class="border-gray-100 dark:border-gray-800" />

				<div class="flex items-center">
					<button
						class="flex justify-between w-full font-medium line-clamp-1 select-none items-center py-4.5 px-7 text-sm text-gray-700 dark:text-gray-100 outline-hidden transition-all duration-75 hover:bg-[#F4F6F8] dark:hover:bg-gray-800 rounded-lg cursor-pointer data-highlighted:bg-muted"
						on:click={async () => {
							temporaryChatEnabled.set(!$temporaryChatEnabled);
							trackButtonClick(
								'modelcard',
								'modelcard_temporarychat',
								temporaryChatEnabled ? 'on' : 'off'
							);
							await goto('/');
							const newChatButton = document.getElementById('new-chat-button');
							setTimeout(() => {
								!$mobile && newChatButton?.click();
							}, 0);

							// add 'temporary-chat=true' to the URL
							if ($temporaryChatEnabled) {
								history.replaceState(null, '', '?temporary-chat=true');
							} else {
								history.replaceState(null, '', location.pathname);
							}

							// show = false;
						}}
					>
						<div class="flex gap-2.5 items-center">
							<ChatBubbleOval className="size-5" strokeWidth="2" />

							{$i18n.t(`Temporary Chat`)}
						</div>

						<div>
							<Switch state={$temporaryChatEnabled} />
						</div>
					</button>
				</div>
			{/if}

			<div class="hidden w-[42rem]" />
			<div class="hidden w-[32rem]" />
		</svelte:component>
	</DropdownMenu.Content>
</DropdownMenu.Root>
