<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { chatId } from '$lib/stores';
	import { inputFlagsMap } from '$lib/constants';
	import SlidesIcon from '../icons/SlidesIcon.svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import XMark from '../icons/XMark.svelte';
	import { twMerge } from 'tailwind-merge';
	import Tooltip from '../common/Tooltip.svelte';

	const i18n: Writable<i18n> = getContext('i18n');
	const iconsMap = {
		slides: SlidesIcon
	};

	export let flags: string[] = [];
	export let onResize: (indent: number) => void = () => {};
	export let scrollHeight: number = 0;
	export let onClose: (flag: string) => void = () => {};
	export let closable = true;

	let container: HTMLDivElement | null = null;

	onMount(() => {
		if (!container) return;

		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				const width = entry.contentRect.width;
				onResize(width);
			}
		});

		resizeObserver.observe(container);

		return () => {
			resizeObserver.disconnect();
		};
	});
</script>

<div
	class=" flagsContainer absolute py-2 select-none z-1"
	bind:this={container}
	style="transform:translateY(-{scrollHeight}px);"
>
	{#each flags as flag}
		{#if inputFlagsMap[flag]}
			<Tooltip offset={[0, 8]} content={closable ? $i18n.t('Click to exit the scene') : ''}>
				<button
					on:click={() => closable && onClose(flag)}
					class={twMerge(
						'relative flex items-center gap-1 pl-2 pr-2 py-1 text-sm rounded-lg',
						inputFlagsMap[flag].color ?? 'text-[#F07010]',
						closable ? `inputFlag ${inputFlagsMap[flag].bgColor}` : ' cursor-default'
					)}
				>
					{#if iconsMap[inputFlagsMap[flag].icon]}
						<span>
							<svelte:component this={iconsMap[inputFlagsMap[flag].icon]} className="size-4" />
						</span>
					{/if}
					<span>{$i18n.t(inputFlagsMap[flag].text)}</span>
					<button
						class="cancelFlag absolute p-0.5 flex items-center justify-center -top-2 -right-2 rounded-full text-white/80 bg-black dark:bg-[#17181A] shadow-lg"
					>
						<XMark className="size-3" />
					</button>
				</button>
			</Tooltip>
		{/if}
	{/each}
</div>

<style>
	.cancelFlag {
		display: none;
	}

	.inputFlag {
	}

	.inputFlag:hover .cancelFlag {
		display: block;
	}
</style>
