<script lang="ts">
	import { getContext } from 'svelte';
	import type { CitationItem } from '$lib/stores/mcp';
	import type { I18nType } from '$lib/types';

	const i18n: I18nType = getContext('i18n');

	export let citations: CitationItem[] = [];
	export let originalResult: string = '';

	const openUrl = (url: string) => {
		window.open(url, '_blank', 'noopener,noreferrer');
	};
</script>

<div class="search-result-container">
	<!-- 原始结果文本 -->
	{#if originalResult && (!citations || citations.length === 0)}
		<div class="original-result">
			{originalResult}
		</div>
	{/if}

	<!-- 搜索结果列表 -->
	{#if citations && citations.length > 0}
		<div class="results-list">
			{#each citations as citation (citation.index)}
				<div class="result-item">
					<!-- 标题作为超链接，包含 favicon -->
					<a
						href={citation.url}
						target="_blank"
						rel="noopener noreferrer"
						class="result-title underline underline-offset-2"
						title={citation.title}
					>
						<div class="title-container">
							{#if citation.favicon}
								<img
									src={citation.favicon}
									alt="favicon"
									class="favicon"
									on:error={(e) => {
										// 如果 favicon 加载失败，隐藏图片
										e.target.style.display = 'none';
									}}
								/>
							{/if}
							<span class="title-text truncate">{citation.title}</span>
						</div>
					</a>

					<!-- 文本内容 -->
					{#if citation.text && citation.text.trim()}
						<div class="result-text line-clamp-3">
							{citation.text}
						</div>
					{/if}
				</div>
			{/each}
		</div>
	{:else if !originalResult}
		<div class="no-results">
			<div class="text-gray-500 dark:text-gray-400">{$i18n.t('No Results')}</div>
		</div>
	{/if}
</div>

<style>
	.search-result-container {
		width: 100%;
		padding: 0;
	}

	.original-result {
		color: #374151;
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	:global(.dark) .original-result {
		color: #d1d5db;
	}

	.results-list {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.result-item {
		padding: 0;
	}

	.result-title {
		display: block;
		font-weight: 500;
		color: #0068e0;
		margin-bottom: 0.5rem;
		text-decoration-color: #0068e0;
	}

	.result-title:hover {
		text-decoration: underline;
	}

	:global(.dark) .result-title {
		color: #60a5fa;
	}

	.title-container {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.favicon {
		width: 16px;
		height: 16px;
		flex-shrink: 0;
		border-radius: 2px;
	}

	.title-text {
		flex: 1;
		min-width: 0; /* 允许文本截断 */
	}

	.result-text {
		color: rgba(0, 0, 0, 0.6);
		line-height: 1.6;
		font-size: 14px;
	}

	:global(.dark) .result-text {
		color: rgba(255, 255, 255, 0.6);
	}

	.no-results {
		text-align: center;
		padding: 2rem 0;
		color: #6b7280;
	}

	:global(.dark) .no-results {
		color: #9ca3af;
	}
</style>
