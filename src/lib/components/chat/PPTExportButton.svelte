<script lang="ts">
	import Export from '../icons/Export.svelte';
	import PDF from '../icons/PDF.svelte';
	import PPT from '../icons/PPT.svelte';
	import { getContext, onMount, onDestroy } from 'svelte';
	import { toast } from 'svelte-sonner';
	import type { PPTPages } from '$lib/types';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import { eventBus, config } from '$lib/stores';
	import { EventBus } from '$lib/constants';
	import { sseConversionService, type ConversionProgress } from '$lib/apis/conversion';

	const i18n: Writable<i18n> = getContext('i18n');

	export let pptPages: PPTPages | null = null;
	export let disabled = false;
	export let chatId: string = ''; // 新增chatId参数
	export let pptVersion: number = 0; // 新增pptVersion参数
	export let inDropdown = false; // 新增inDropdown参数，用于在其他下拉菜单中使用
	let open = false;
	let exporting = false;
	let exportProgress = { current: 0, total: 0, status: '' };
	let dropdownElement: HTMLDivElement;

	// 下载确认弹窗状态
	let showDownloadModal = false;
	let downloadInfo = { filename: '', url: '', type: '', fileSize: '' };
	let downloading = false; // 新增下载loading状态

	// 屏幕上方导出提示状态
	let showTopNotification = false;
	let topNotificationMessage = '';

	// 跟踪正在处理中的PPT页面
	let processingPages = new Set<number>();

	// 翻译进度消息
	const translateProgressMessage = (message: string): string => {
		// 处理包含数字的消息
		const slideCountMatch = message.match(/Prepared (\d+) slides for PDF generation/);
		if (slideCountMatch) {
			return $i18n.t('Prepared {{count}} slides for PDF generation', { count: parseInt(slideCountMatch[1]) });
		}

		const parallelPdfMatch = message.match(/Starting parallel PDF generation for (\d+) slides/);
		if (parallelPdfMatch) {
			return $i18n.t('Starting parallel PDF generation for {{count}} slides', { count: parseInt(parallelPdfMatch[1]) });
		}

		// 处理固定消息
		const messageMap: Record<string, string> = {
			'Connection established': $i18n.t('Connection established'),
			'Initializing project': $i18n.t('Initializing project'),
			'Processing slides type': $i18n.t('Processing slides type'),
			'Creating conversion job...': $i18n.t('Creating conversion job...'),
			'Uploading files...': $i18n.t('Uploading files...'),
			'Uploading HTML file...': $i18n.t('Uploading HTML file...'),
			'File uploaded successfully': $i18n.t('File uploaded successfully'),
			'Successfully generated PDF': $i18n.t('Successfully generated PDF'),
			'Uploading PDF to blob storage': $i18n.t('Uploading PDF to blob storage'),
			'PDF generated and uploaded successfully': $i18n.t('PDF generated and uploaded successfully')
		};

		return messageMap[message] || message;
	};

	// 导出进度管理器
	const createProgressManager = (total: number) => {
		return {
			updateProgress: (current: number, status: string) => {
				const translatedStatus = translateProgressMessage(status);
				exportProgress = { current, total, status: translatedStatus };
				// 同时更新屏幕上方的提示信息
				if (showTopNotification) {
					topNotificationMessage = translatedStatus;
				}
			},
			reset: () => {
				exportProgress = { current: 0, total: 0, status: '' };
			}
		};
	};

	// 计算是否有页面正在生成中
	$: isGenerating = processingPages.size > 0;

	// 最终的禁用状态：原有的disabled或者正在生成中或者正在导出中
	$: finalDisabled = disabled || isGenerating || exporting;

	// 点击外部关闭下拉菜单
	const handleClickOutside = (event: MouseEvent) => {
		// 如果正在显示进度条或下载弹窗，不关闭下拉菜单
		if (showTopNotification || showDownloadModal) {
			return;
		}
		if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
			open = false;
		}
	};

	// 处理PPT工作状态事件 - 处理中
	const handlePPTWorkProcessing = (position: unknown) => {
		if (typeof position === 'number') {
			processingPages.add(position);
			processingPages = processingPages; // 触发响应式更新
		}
	};

	// 处理PPT工作状态事件 - 完成
	const handlePPTWorkComplete = (position: unknown) => {
		if (typeof position === 'number') {
			processingPages.delete(position);
			processingPages = processingPages; // 触发响应式更新
		}
	};

	onMount(() => {
		document.addEventListener('click', handleClickOutside);

		// 监听PPT工作状态事件
		$eventBus.on(EventBus.PPT_WORK_PROCESSING, handlePPTWorkProcessing);
		$eventBus.on(EventBus.PPT_WORK_COMPLETE, handlePPTWorkComplete);

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	onDestroy(() => {
		// 清理事件监听器
		document.removeEventListener('click', handleClickOutside);
		$eventBus.off(EventBus.PPT_WORK_PROCESSING, handlePPTWorkProcessing);
		$eventBus.off(EventBus.PPT_WORK_COMPLETE, handlePPTWorkComplete);
	});

	// 计算页面尺寸的辅助函数
	const calculatePageDimensions = async (
		htmlContent: string
	): Promise<{ width: number; height: number }> => {
		return new Promise((resolve) => {
			// 创建临时iframe来测量页面尺寸
			const tempIframe = document.createElement('iframe');
			tempIframe.style.position = 'absolute';
			tempIframe.style.left = '-9999px';
			tempIframe.style.top = '-9999px';
			tempIframe.style.width = '1280px';
			tempIframe.style.height = '720px';
			tempIframe.style.border = 'none';

			document.body.appendChild(tempIframe);

			tempIframe.onload = () => {
				try {
					const iframeDoc = tempIframe.contentDocument;
					const iframeBody = iframeDoc?.body;

					if (iframeBody) {
						// 注入样式确保准确测量
						const styleEl = iframeDoc.createElement('style');
						styleEl.textContent = `
							html, body {
								margin: 0;
								padding: 0;
								overflow: hidden;
							}
							body > * {
								transform-origin: top left;
							}
						`;
						iframeDoc.head?.appendChild(styleEl);

						// 等待样式生效后测量
						setTimeout(() => {
							let width: number, height: number;
							if (iframeBody.childElementCount === 1) {
								// 单个子元素：使用子元素的实际尺寸（更精确）
								const firstChild = iframeBody.firstElementChild as HTMLElement;
								width = firstChild?.offsetWidth || 1280;
								height = firstChild?.offsetHeight || 720;
								console.log('Export: Using single child dimensions:', { width, height });
							} else {
								// 多个子元素：使用body的滚动尺寸
								width = iframeBody.scrollWidth || 1280;
								height = iframeBody.scrollHeight || 720;
								console.log('Export: Using body scroll dimensions:', { width, height });
							}

							// 转换像素到厘米 (96 DPI: 1英寸 = 96像素 = 2.54厘米)
							// 使用向下取整减少白边，保留两位小数
							const widthCm = Math.floor(((width / 96) * 2.54) * 100) / 100;
							const heightCm = Math.floor(((height / 96) * 2.54) * 100) / 100;

							console.log(`Page dimensions calculated: ${width}px x ${height}px -> ${widthCm}cm x ${heightCm}cm`);

							document.body.removeChild(tempIframe);
							// 横版纸张，交换宽高，-0.01这个是究极魔法操作，没别的招了，等我想到办法再极限优化
							resolve({ width: heightCm - 0.01, height: widthCm });
						}, 1000);
					} else {
						document.body.removeChild(tempIframe);
						// 使用精确的默认尺寸（1280x720px 的精确转换）
						resolve({ width: 33.87, height: 19.05 });
					}
				} catch (error) {
					console.warn('Failed to measure page dimensions:', error);
					document.body.removeChild(tempIframe);
					// 使用精确的默认尺寸（1280x720px 的精确转换）
					resolve({ width: 33.87, height: 19.05 });
				}
			};

			tempIframe.srcdoc = htmlContent;
		});
	};

	// PDF导出功能 - 使用 SSE 流式转换
	const exportToPDF = async () => {
		console.log('PDF export clicked', {
			pptPages,
			disabled,
			exporting,
			isGenerating,
			processingPages: Array.from(processingPages),
			chatId
		});

		if (!pptPages?.pages.length) {
			toast.error($i18n.t('No PPT found'));
			return;
		}

		if (!chatId) {
			toast.error($i18n.t('Chat ID is required'));
			return;
		}

		if (isGenerating) {
			toast.warning($i18n.t('PPT is still generating, please wait'));
			return;
		}

		exporting = true;
		open = false; // 关闭下拉菜单

		// 显示导出开始的toast提示和屏幕上方提示
		// toast.info($i18n.t('Exporting PDF...'));
		showTopNotification = true;
		topNotificationMessage = $i18n.t('Exporting PDF...');

		const progressManager = createProgressManager(pptPages.pages.length);
		progressManager.updateProgress(0, $i18n.t('Initializing export...'));

		try {
			// 计算每页的尺寸并准备页面元数据
			progressManager.updateProgress(0, $i18n.t('Calculating page dimensions...'));

			const pageMetadata = await Promise.all(
				pptPages.pages.map(async (page, index) => {
					try {
						const dimensions = await calculatePageDimensions(page as string);
						return {
							pageWidth: dimensions.width,
							pageHeight: dimensions.height
						};
					} catch (error) {
						console.warn(`Failed to calculate dimensions for page ${index + 1}:`, error);
						// 使用精确的默认尺寸（1280x720px 的精确转换）
						return {
							pageWidth: 33.87,
							pageHeight: 19.05
						};
					}
				})
			);

			// 转换选项（全局默认值）
			const options = {
				orientation: 'landscape',
				marginTop: 0,
				marginBottom: 0,
				marginLeft: 0,
				marginRight: 0,
				printBackground: true,
				waitUntil: 'networkidle0',
				waitTime: 1500,
				zoom: 1.0
			};

			// 使用新的 SSE 转换服务，传递chatId和页面尺寸数据
			await sseConversionService.convertToPdf(
				chatId,
				options,
				{
					onProgress: (progress: ConversionProgress) => {
						console.log('Conversion progress:', progress);

						// 更新进度显示
						const progressNum = progress.progress ? parseInt(progress.progress) : 0;
						progressManager.updateProgress(
							Math.floor((progressNum / 100) * pptPages.pages.length),
							progress.message
						);
					},
					onComplete: async (result: ConversionProgress) => {
						console.log('Conversion completed:', result);

						progressManager.updateProgress(pptPages.pages.length, $i18n.t('Download ready'));

						// 检查是否有下载链接
						if (result.pdf_url) {
							// 设置下载信息并显示下载确认弹窗
							const filename = result.aidrive_upload_result?.filename || `${pptPages.title || 'PPT'}.pdf`;
							const fileSize = String(result.aidrive_upload_result?.file_size || '100.45 KB'); // 转换为字符串
							downloadInfo = {
								filename,
								url: result.pdf_url,
								type: 'PDF',
								fileSize
							};
							showTopNotification = false;
							showDownloadModal = true;
						} else {
							toast.error($i18n.t('No download URL available'));
							showTopNotification = false;
						}
					},
					onError: (error: string) => {
						console.error('Conversion failed:', error);
						const friendlyError = parseErrorMessage(error);
						toast.error($i18n.t('Failed to export PDF: {{error}}', { error: friendlyError }));
						showTopNotification = false;
					}
				},
				pageMetadata,
				pptVersion
			);
		} catch (error) {
			console.error('PDF export error:', error);
			toast.error($i18n.t('Failed to export PDF'));
			showTopNotification = false;
		} finally {
			exporting = false;
			progressManager.reset();
		}
	};

	// PPTX导出功能 - 使用 SSE 流式转换
	const exportToPPTX = async () => {
		console.log('PPTX export clicked', {
			pptPages,
			disabled,
			exporting,
			isGenerating,
			processingPages: Array.from(processingPages),
			chatId
		});

		if (!pptPages?.pages.length) {
			toast.error($i18n.t('No PPT found'));
			return;
		}

		if (!chatId) {
			toast.error($i18n.t('Chat ID is required'));
			return;
		}

		if (isGenerating) {
			toast.warning($i18n.t('PPT is still generating, please wait'));
			return;
		}

		exporting = true;
		open = false; // 关闭下拉菜单

		// 显示导出开始的toast提示和屏幕上方提示
		// toast.info($i18n.t('Exporting PPTX...'));
		showTopNotification = true;
		topNotificationMessage = $i18n.t('Exporting PPTX...');

		const progressManager = createProgressManager(pptPages.pages.length);
		progressManager.updateProgress(0, $i18n.t('Initializing export...'));

		try {
			// 计算每页的尺寸并准备页面元数据
			progressManager.updateProgress(0, $i18n.t('Calculating page dimensions...'));

			const pageMetadata = await Promise.all(
				pptPages.pages.map(async (page, index) => {
					try {
						const dimensions = await calculatePageDimensions(page as string);
						return {
							pageWidth: dimensions.width,
							pageHeight: dimensions.height
						};
					} catch (error) {
						console.warn(`Failed to calculate dimensions for page ${index + 1}:`, error);
						// 使用精确的默认尺寸（1280x720px 的精确转换）
						return {
							pageWidth: 33.87,
							pageHeight: 19.05
						};
					}
				})
			);

			// 转换选项（全局默认值）
			const options = {
				orientation: 'landscape',
				marginTop: 0,
				marginBottom: 0,
				marginLeft: 0,
				marginRight: 0,
				printBackground: true,
				waitUntil: 'networkidle0',
				waitTime: 1500,
				zoom: 1.0
			};

			// 使用新的 SSE 转换服务，传递chatId和页面尺寸数据
			await sseConversionService.convertToPpt(
				chatId,
				options,
				{
					onProgress: (progress: ConversionProgress) => {
						console.log('PPTX Conversion progress:', progress);

						// 更新进度显示
						const progressNum = progress.progress ? parseInt(progress.progress) : 0;
						progressManager.updateProgress(
							Math.floor((progressNum / 100) * pptPages.pages.length),
							progress.message
						);
					},
					onComplete: async (result: ConversionProgress) => {
						console.log('PPTX Conversion completed:', result);

						progressManager.updateProgress(pptPages.pages.length, $i18n.t('Download ready'));

						// 检查是否有下载链接
						if (result.ppt_url) {
							// 设置下载信息并显示下载确认弹窗
							const filename = result.aidrive_upload_result?.filename || `${pptPages.title || 'PPT'}.pptx`;
							const fileSize = String(result.aidrive_upload_result?.file_size || '100.45 KB'); // 转换为字符串
							downloadInfo = {
								filename,
								url: result.ppt_url,
								type: 'PPTX',
								fileSize
							};
							showTopNotification = false;
							showDownloadModal = true;
						} else {
							toast.error($i18n.t('No download URL available'));
							showTopNotification = false;
						}
					},
					onError: (error: string) => {
						console.error('PPTX Conversion failed:', error);
						const friendlyError = parseErrorMessage(error);
						toast.error($i18n.t('Failed to export PPTX: {{error}}', { error: friendlyError }));
						showTopNotification = false;
					}
				},
				pageMetadata,
				pptVersion
			);
		} catch (error) {
			console.error('PPTX export error:', error);
			toast.error($i18n.t('Failed to export PPTX'));
			showTopNotification = false;
		} finally {
			exporting = false;
			progressManager.reset();
		}
	};

	// PPTX导出功能（自研） - 使用 SSE 流式转换
	const exportToPPTXInhouse = async () => {
		console.log('PPTX inhouse export clicked', {
			pptPages,
			disabled,
			exporting,
			isGenerating,
			processingPages: Array.from(processingPages),
			chatId
		});

		if (!pptPages?.pages.length) {
			toast.error($i18n.t('No PPT found'));
			return;
		}

		if (!chatId) {
			toast.error($i18n.t('Chat ID is required'));
			return;
		}

		if (isGenerating) {
			toast.warning($i18n.t('PPT is still generating, please wait'));
			return;
		}

		exporting = true;
		open = false; // 关闭下拉菜单

		// 显示导出开始的toast提示和屏幕上方提示
		showTopNotification = true;
		topNotificationMessage = $i18n.t('Exporting PPTX (Inhouse)...');

		const progressManager = createProgressManager(pptPages.pages.length);
		progressManager.updateProgress(0, $i18n.t('Initializing export...'));

		try {
			// 计算每页的尺寸并准备页面元数据
			progressManager.updateProgress(0, $i18n.t('Calculating page dimensions...'));

			const pageMetadata = await Promise.all(
				pptPages.pages.map(async (page, index) => {
					try {
						const dimensions = await calculatePageDimensions(page as string);
						return {
							pageWidth: dimensions.width,
							pageHeight: dimensions.height
						};
					} catch (error) {
						console.warn(`Failed to calculate dimensions for page ${index + 1}:`, error);
						// 使用精确的默认尺寸（1280x720px 的精确转换）
						return {
							pageWidth: 33.87,
							pageHeight: 19.05
						};
					}
				})
			);

			// 转换选项（全局默认值）
			const options = {
				orientation: 'landscape',
				marginTop: 0,
				marginBottom: 0,
				marginLeft: 0,
				marginRight: 0,
				printBackground: true,
				waitUntil: 'networkidle0',
				waitTime: 1500,
				zoom: 1.0,
				exportMethod: 'inhouse' // 指定使用自研导出方案
			};

			// 使用新的 SSE 转换服务，传递chatId和页面尺寸数据
			await sseConversionService.convertToPpt(
				chatId,
				options,
				{
					onProgress: (progress: ConversionProgress) => {
						console.log('PPTX Inhouse Conversion progress:', progress);

						// 更新进度显示
						const progressNum = progress.progress ? parseInt(progress.progress) : 0;
						progressManager.updateProgress(
							Math.floor((progressNum / 100) * pptPages.pages.length),
							progress.message
						);
					},
					onComplete: async (result: ConversionProgress) => {
						console.log('PPTX Inhouse Conversion completed:', result);

						progressManager.updateProgress(pptPages.pages.length, $i18n.t('Download ready'));

						// 检查是否有下载链接
						if (result.ppt_url) {
							// 设置下载信息并显示下载确认弹窗
							const filename = result.aidrive_upload_result?.filename || `${pptPages.title || 'PPT'}.pptx`;
							const fileSize = String(result.aidrive_upload_result?.file_size || '100.45 KB'); // 转换为字符串
							downloadInfo = {
								filename,
								url: result.ppt_url,
								type: 'PPTX (Inhouse)',
								fileSize
							};
							showTopNotification = false;
							showDownloadModal = true;
						} else {
							toast.error($i18n.t('No download URL available'));
							showTopNotification = false;
						}
					},
					onError: (error: string) => {
						console.error('PPTX Inhouse Conversion failed:', error);
						const friendlyError = parseErrorMessage(error);
						toast.error($i18n.t('Failed to export PPTX (Inhouse): {{error}}', { error: friendlyError }));
						showTopNotification = false;
					}
				},
				pageMetadata,
				pptVersion
			);
		} catch (error) {
			console.error('PPTX inhouse export error:', error);
			toast.error($i18n.t('Failed to export PPTX (Inhouse)'));
			showTopNotification = false;
		} finally {
			exporting = false;
			progressManager.reset();
		}
	};

	// 处理下载确认
	const handleDownload = async () => {
		if (downloading) return; // 防止重复点击

		downloading = true;
		try {
			await sseConversionService.downloadFile(downloadInfo.url, downloadInfo.filename);
			toast.success($i18n.t(`${downloadInfo.type} downloaded successfully`));
			showDownloadModal = false;
		} catch (error) {
			console.error('Download failed:', error);
			toast.error($i18n.t(`Failed to download ${downloadInfo.type}`));
		} finally {
			downloading = false;
		}
	};

	// 关闭下载弹窗
	const closeDownloadModal = () => {
		showDownloadModal = false;
	};

	// 解析结构化错误消息
	const parseErrorMessage = (error: string): string => {
		// 检查是否为结构化错误
		if (error.startsWith('STRUCTURED_ERROR:')) {
			try {
				const parts = error.split(':');
				const errorType = parts[1];
				const errorData = parts.length > 2 ? JSON.parse(parts.slice(2).join(':')) : {};

				// 处理限流错误
				if (errorType === 'CONVERTER_DAILY_LIMIT_EXCEEDED') {
					return $i18n.t('Daily conversion limit reached ({{limit}} times). Please try again tomorrow.', {
						limit: errorData.limit || 50
					});
				}

				// 可以在这里添加其他错误类型的处理
				return error;
			} catch (parseError) {
				console.warn('Failed to parse structured error:', error, parseError);
				return error;
			}
		}

		// 如果不是结构化错误，直接返回原始错误消息
		return error;
	};
</script>

{#if inDropdown}
	<!-- 在其他下拉菜单中使用时，只渲染导出按钮 -->
	{#if $config?.features?.enable_pdf_export}
		<button
			class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
				? 'opacity-50 cursor-not-allowed'
				: ''}"
			disabled={finalDisabled}
			on:click={exportToPDF}
		>
			<PDF className="size-5" />
			<div class="flex-1">
				<div>{$i18n.t('Export as PDF')}</div>
			</div>
		</button>
	{/if}

	<!-- PPTX导出按钮 -->
	{#if $config?.features?.enable_ppt_export}
		<button
			class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
				? 'opacity-50 cursor-not-allowed'
				: ''}"
			disabled={finalDisabled}
			on:click={exportToPPTX}
		>
			<PPT className="size-5" />
			<div class="flex-1">
				<div>{$i18n.t('Export as PPTX')}</div>
			</div>
		</button>
	{/if}

	<!-- PPTX导出按钮（自研） -->
	{#if $config?.features?.enable_ppt_export}
		<button
			class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
				? 'opacity-50 cursor-not-allowed'
				: ''}"
			disabled={finalDisabled}
			on:click={exportToPPTXInhouse}
		>
			<PPT className="size-5" />
			<div class="flex-1">
				<div>{$i18n.t('Export as PPTX (Inhouse)')}</div>
			</div>
		</button>
	{/if}
{:else}
	<!-- 独立使用时的完整下拉菜单 -->
	<div class="relative" bind:this={dropdownElement}>
		<button
			disabled={finalDisabled}
			class="flex items-center gap-1 flex-nowrap whitespace-nowrap bg-none border-none px-1.5 py-0.5 font-medium text-sm {finalDisabled
				? 'opacity-50 cursor-not-allowed'
				: 'hover:underline'}"
			on:click={() => {
				if (!finalDisabled) {
					open = !open;
				}
			}}
		>
			<Export className="size-4 inline" strokeWidth="2" />
			<span class="hover:underline">
				{#if isGenerating}
					{$i18n.t('Export')} ({processingPages.size} {$i18n.t('generating')})
				{:else}
					{$i18n.t('Export')}
				{/if}
			</span>
		</button>

		{#if open}
			<div
				class="absolute top-full right-0 mt-2 min-w-[180px] rounded-2xl border border-black/10 dark:border-white/10 p-2 z-50 bg-white dark:bg-[#25282A] dark:text-white shadow-xl text-sm"
			>
				{#if $config?.features?.enable_pdf_export}
					<button
						class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
							? 'opacity-50 cursor-not-allowed'
							: ''}"
						disabled={finalDisabled}
						on:click={exportToPDF}
					>
						<PDF className="size-5" />
						<div class="flex-1">
							<div>{$i18n.t('Export as PDF')}</div>
						</div>
					</button>
				{/if}

				<!-- PPTX导出按钮 -->
				{#if $config?.features?.enable_ppt_export}
					<button
						class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
							? 'opacity-50 cursor-not-allowed'
							: ''}"
						disabled={finalDisabled}
						on:click={exportToPPTX}
					>
						<PPT className="size-5" />
						<div class="flex-1">
							<div>{$i18n.t('Export as PPTX')}</div>
						</div>
					</button>
				{/if}

				<!-- PPTX导出按钮（自研） -->
				{#if $config?.features?.enable_ppt_export}
					<button
						class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer text-left flex items-center gap-2 whitespace-nowrap {finalDisabled
							? 'opacity-50 cursor-not-allowed'
							: ''}"
						disabled={finalDisabled}
						on:click={exportToPPTXInhouse}
					>
						<PPT className="size-5" />
						<div class="flex-1">
							<div>{$i18n.t('Export as PPTX (Inhouse)')}</div>
						</div>
					</button>
				{/if}
			</div>
		{/if}
	</div>
{/if}

<!-- 屏幕上方导出提示 -->
{#if showTopNotification}
	<!-- 背景遮罩，阻止点击外部关闭 -->
	<div class="fixed inset-0 z-40 bg-black/20 dark:bg-black/40"></div>
	<div class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
		<div class="bg-[#F4F6F8] dark:bg-[#141618] text-gray-900 dark:text-white px-4 py-3 rounded-lg shadow-lg w-[480px] border border-gray-300 dark:border-gray-600">
			<div class="flex items-center gap-3">
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 dark:border-white"></div>
				<div class="flex-1 min-w-0">
					<div class="text-sm font-medium truncate">{topNotificationMessage}</div>
					{#if exportProgress.total > 0}
						<div class="flex items-center gap-2 mt-1">
							<div class="flex-1 bg-gray-300 dark:bg-gray-600 rounded-full h-1">
								<div
									class="bg-gray-900 dark:bg-white h-1 rounded-full transition-all duration-300"
									style="width: {(exportProgress.current / exportProgress.total) * 100}%"
								></div>
							</div>
							<span class="text-xs text-gray-600 dark:text-gray-300">{exportProgress.current}/{exportProgress.total}</span>
						</div>
					{/if}
					<div class="text-xs text-gray-600 dark:text-gray-300 mt-1">{$i18n.t('Estimated time: 1-10 minutes.')}</div>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- 下载确认弹窗 - 屏幕中间，带背景遮罩 -->
{#if showDownloadModal}
	<!-- 背景遮罩，阻止点击外部关闭 -->
	<div class="fixed inset-0 z-40 bg-black/50 dark:bg-black/70"></div>
	<div class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
		<div class="bg-[#F4F6F8] dark:bg-[#141618] rounded-lg p-6 shadow-2xl border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white w-96 min-w-96">
			<!-- 标题栏 -->
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-medium">
					{$i18n.t('Export Successful')}
				</h3>
				<button
					class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white transition-colors"
					on:click={closeDownloadModal}
				>
					<svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>

			<!-- 描述文案 -->
			<p class="text-gray-600 dark:text-gray-300 mb-6 text-center">
				{$i18n.t('File has been successfully exported')}
			</p>

			<!-- 文件信息 -->
			<div class="bg-gray-200 dark:bg-gray-700 rounded-lg p-4 mb-6">
				<div class="flex items-center gap-3">
					<!-- 左侧文件图标 -->
					<div class="flex-shrink-0">
						{#if downloadInfo.type === 'PDF'}
							<PDF className="size-8" />
						{:else}
							<PPT className="size-8" />
						{/if}
					</div>

					<!-- 右侧文件名（带后缀） -->
					<div class="flex-1 min-w-0">
						<div class="text-gray-900 dark:text-white font-medium truncate">
							{downloadInfo.filename}
						</div>
					</div>
				</div>
			</div>

			<!-- 按钮组 -->
			<div class="flex gap-3">
				<button
					class="flex-1 bg-[#43444F] dark:bg-[#484A58] hover:bg-[#3a3b45] dark:hover:bg-[#525461] text-white px-4 py-2 rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
					on:click={handleDownload}
					disabled={downloading}
				>
					{#if downloading}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
					{/if}
					{$i18n.t('Download')}
				</button>
				<button
					class="flex-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-white px-4 py-2 rounded-lg transition-colors font-medium"
					on:click={closeDownloadModal}
				>
					{$i18n.t('Cancel')}
				</button>
			</div>
		</div>
	</div>
{/if}
