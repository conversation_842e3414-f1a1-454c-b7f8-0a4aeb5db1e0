<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { onMount, getContext, createEventDispatcher, onDestroy } from 'svelte';
	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	import { chatId, eventBus, showArtifacts, showControls } from '$lib/stores';
	import XMark from '../icons/XMark.svelte';
	import { copyToClipboard, createMessagesList, isEmptyObject } from '$lib/utils';
	import ArrowsPointingOut from '../icons/ArrowsPointingOut.svelte';
	import Download from '../icons/Download.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import SvgPanZoom from '../common/SVGPanZoom.svelte';
	import {
		handleHTMLContent,
		handleJSXContent,
		isSupportPreview
	} from '$lib/utils/artifacts/artifacts_helper';
	import Dropdown from '../common/Dropdown.svelte';
	import { DropdownMenu } from 'bits-ui';
	import { Canvg } from 'canvg';
	import Tracker from '@aliyun-sls/web-track-browser';
	import { trackButtonClick } from '$lib/utils/analytics';
	import { selectedArtifactsMessageID, artifactsMessages } from '$lib/stores/artifacts';
	import Tab from '../common/Tab.svelte';
	import CodeEditor from '../common/CodeEditor.svelte';
	import DeployProjectButton from './Artifacts/DeployProjectButton.svelte';
	import { deployProject } from '$lib/apis/deploy';
	import Copy from '../icons/Copy.svelte';
	import Divider from '../common/Divider.svelte';
	import CodeFile from '../icons/CodeFile.svelte';
	import FullScreenIcon from '../icons/FullScreenIcon.svelte';
	import ArtifactsEditor from '$lib/utils/artifacts/artifactsEditor';
	import KiraKira from '../icons/KiraKira.svelte';
	import ChevronLeft from '../icons/ChevronLeft.svelte';
	import Spinner from '../common/Spinner.svelte';
	import Pencil from '../icons/Pencil.svelte';
	import BrowserIcon from '../icons/BrowserIcon.svelte';
	import { EventBus } from '$lib/constants';
	import { isMobile } from 'mobile-device-detect';

	export let showEdit = false;
	export let showDeploy = false;
	export let overlay = false;
	export let history;

	export let saveChatHandler: Function = () => {};

	let messages: any[] = [];

	let copied = false;
	let iframeElement: HTMLIFrameElement;
	let slsTracker: Tracker | null = null;
	let showDownloadMenu = false;
	let supportPreview = false;
	$: showDeployButton =
		showDeploy &&
		history.messages[history.currentId]?.done &&
		['html', 'jsx'].includes($artifactsMessages[$selectedArtifactsMessageID]?.lang ?? '');

	let editing = false;
	let artifactsEditor: ArtifactsEditor;
	let loading = false;

	let showType = 'preview';
	$: if ($artifactsMessages[$selectedArtifactsMessageID]) {
		const { lang, code, mid } = $artifactsMessages[$selectedArtifactsMessageID];
		if (history.currentId === mid) {
			supportPreview = history.messages[history.currentId]?.done && isSupportPreview(lang, code);
		} else {
			supportPreview = isSupportPreview(lang, code);
		}
		if (!supportPreview) {
			showType = 'code';
		}
	}

	// 用于存储已发送的错误消息，防止重复上报
	let sentErrorMessages = new Set<string>();

	const getCodeContent = () => {
		if (!$artifactsMessages[$selectedArtifactsMessageID]) return null;
		const { lang, code, id } = $artifactsMessages[$selectedArtifactsMessageID];
		return { content: code, lang, id };
	};

	const getPreviewContent = () => {
		if (!$artifactsMessages[$selectedArtifactsMessageID]) return null;
		const { lang, code, id } = $artifactsMessages[$selectedArtifactsMessageID];
		if (lang === 'html') {
			return { type: 'iframe', content: handleHTMLContent(code), id };
		} else if (lang === 'jsx') {
			return { type: 'iframe', content: handleJSXContent(code), id };
		} else if (lang === 'svg') {
			return { type: 'svg', content: code, id };
		}
		return null;
	};

	let previewContent: { type: string; content: string; id: string } | null = null;
	$: if ($artifactsMessages[$selectedArtifactsMessageID] && showType === 'preview') {
		previewContent = getPreviewContent();
	}

	let codeContent: { content: string; lang: string; id: string } | null = null;
	$: if ($artifactsMessages[$selectedArtifactsMessageID] && showType === 'code') {
		codeContent = getCodeContent();
	}

	const downloadSvg = () => {
		const content = $artifactsMessages[$selectedArtifactsMessageID].code;
		const blob = new Blob([content], { type: 'image/svg+xml' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `content_${$selectedArtifactsMessageID}.svg`;
		a.click();
		trackButtonClick('artifacts', 'download_click', 'download_svg');
		URL.revokeObjectURL(url);
	};

	const downloadPng = async () => {
		const content = $artifactsMessages[$selectedArtifactsMessageID].code;
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d');
		const instance = await Canvg.fromString(ctx, content);
		await instance.render();
		const pngData = canvas.toDataURL('image/png');
		const a = document.createElement('a');
		a.href = pngData;
		a.download = `content_${$selectedArtifactsMessageID}.png`;
		trackButtonClick('artifacts', 'download_click', 'download_png');
		a.click();
	};

	// 统一的错误上报函数
	const reportError = (errorInfo: {
		level: string;
		message: string;
		log_type: string;
		filename?: string;
		lineno?: number;
		call_stack?: string;
		event?: string;
		url?: string;
		[key: string]: any;
	}) => {
		if (!slsTracker) return;

		// 使用消息内容创建唯一标识，最多取前100个字符
		const messageKey = errorInfo.message?.substring(0, 100);
		if (!messageKey) return;

		// 检查消息是否已发送过，避免重复上报
		if (sentErrorMessages.has(messageKey)) return;

		// 记录已发送消息
		sentErrorMessages.add(messageKey);

		if (history) {
			messages = createMessagesList(history, history.currentId);
		}

		// 构建基础上报数据
		const reportData: Record<string, string> = {
			artifacts_code_block: JSON.stringify(
				$artifactsMessages[$selectedArtifactsMessageID].code || 'iframe-content'
			),
			history: JSON.stringify(messages || []),
			timestamp: new Date().toISOString()
		};

		// 合并传入的错误信息，避免字段重复
		Object.keys(errorInfo).forEach((key) => {
			reportData[key] = errorInfo[key];
		});

		// 发送日志
		try {
			slsTracker.sendImmediate(reportData);
		} catch (e) {
			console.error('SLS发送错误:', e);
		}
	};

	const iframeLoadHandler = (e) => {
		// console.log('iframe loaded, add event listeners');
		if (iframeElement.contentWindow) {
			iframeElement.contentWindow.addEventListener(
				'error',
				function (event: ErrorEvent) {
					console.error('inner iframe error:', event);
					reportError({
						level: 'error',
						message: event.message || 'unknown error',
						filename: event.filename,
						lineno: event.lineno,
						log_type: 'uncaught_exception',
						call_stack: event.error?.stack || 'unknown',
						url: iframeElement.contentWindow?.location.href
					});
				},
				true
			);

			iframeElement.contentWindow.addEventListener(
				'unhandledrejection',
				function (event: PromiseRejectionEvent) {
					console.error('unhandledrejection error:', event);
					const message = event.reason
						? event.reason.message || String(event.reason)
						: 'Unknown Promise Rejection';
					reportError({
						level: 'error',
						message: message,
						log_type: 'unhandled_rejection',
						url: iframeElement.contentWindow?.location.href
					});
				},
				true
			);

			iframeElement.contentWindow.addEventListener(
				'click',
				function (e) {
					const target =
						e.target && (e.target as HTMLElement).closest
							? (e.target as HTMLElement).closest('a')
							: null;
					if (target && (target as HTMLAnchorElement).href) {
						e.preventDefault();
						const url = new URL((target as HTMLAnchorElement).href, iframeElement.baseURI);
						if (url.origin === window.location.origin) {
							if (iframeElement.contentWindow) {
								iframeElement.contentWindow.history.pushState(
									null,
									'',
									url.pathname + url.search + url.hash
								);
							}
						} else {
							window.open(url.href, '_blank');
						}
					}
				},
				true
			);

			// Cancel drag when hovering over iframe
			iframeElement.contentWindow.addEventListener('mouseenter', function (e) {
				e.preventDefault();
				if (iframeElement.contentWindow) {
					iframeElement.contentWindow.addEventListener('dragstart', (event) => {
						event.preventDefault();
					});
				}
			});

			if (editing) {
				artifactsEditor?.invokeEditorCode(iframeElement);
				artifactsEditor?.initMessageHandler((type, data) => {
					if (type === 'editElement') {
						editing = false;
						const { line, code, text } = data;
						window.postMessage(
							{
								type: 'input:prompt:submit',
								text,
								vibeInfo: {
									vibeMode: 'artifacts',
									vibeReference: {
										line,
										code,
										filename: 'index.html'
									}
								}
							},
							window.origin
						);
					}
				});
			}
		}
	};

	const showFullScreen = () => {
		if (iframeElement) {
			if (iframeElement.requestFullscreen) {
				iframeElement.requestFullscreen();
			} else if ((iframeElement as any).webkitRequestFullscreen) {
				(iframeElement as any).webkitRequestFullscreen();
			} else if ((iframeElement as any).msRequestFullscreen) {
				(iframeElement as any).msRequestFullscreen();
			}
		}
	};

	onMount(() => {
		console.log('Artifacts组件已挂载，已引入CDN代理功能');
		$eventBus.on(EventBus.SUBMIT_PROMPT, (prompt) => {
			editing = false;
		});
		// 初始化阿里云SLS日志服务
		function initComponentSLS() {
			try {
				// 创建Tracker实例
				slsTracker = new Tracker({
					host: 'cn-hongkong.log.aliyuncs.com', // 阿里云SLS的Endpoint
					project: 'k8s-log-c1f874a604cb64b29bb69219c8faaf8b7', // Project名称
					logstore: 'prod-open-webui-frontend', // Logstore名称
					time: 10, // 发送日志间隔(秒)
					count: 10, // 发送日志数量
					topic: 'artifacts-component-events', // 日志主题
					source: 'artifacts-component', // 日志来源
					tags: { component: 'artifacts', version: '1.0.0', log_type: 'component_logs' }
				});
			} catch (error) {
				console.error('SLS initialization failed:', error);
			}
		}

		// 执行初始化
		initComponentSLS();
	});

	onDestroy(() => {
		slsTracker = null;
		// selectedArtifactsMessageID.set('');
	});

	let lastDone = undefined;
	$: {
		const msg = history?.messages?.[history?.currentId];
		const done = msg?.done;

		if (!lastDone && done === true && supportPreview) {
			showType = 'preview';
		}
		lastDone = done;
	}
</script>

<div class=" @container w-full h-full relative flex flex-col bg-white dark:bg-[#26282A]">
	<div
		class="artifactsHeader w-full flex gap-2 justify-between items-center border-b-1 px-4 py-3 border-black/10 overflow-hidden"
	>
		{#if editing}
			<div class="flex items-center gap-2">
				<button
					class="p-2 hover:bg-black/10 dark:hover:bg-white/10 rounded-md"
					disabled={loading}
					on:click={() => {
						editing = false;
					}}
				>
					<ChevronLeft className="size-5" />
				</button>
				{#if loading}
					<Spinner />
				{/if}
			</div>
			<div class="flex gap-2 items-center">
				<button
					disabled={loading}
					on:click={() => {
						editing = false;
					}}
					class="px-3 py-1.5 hover:bg-black/10 dark:hover:bg-white/10 rounded-lg text-sm border-1 border-black/10 dark:border-white/10 {loading
						? 'text-gray-300 dark:text-gray-600  '
						: ''}">{$i18n.t('Cancel')}</button
				>
				<button
					disabled={loading}
					on:click={async () => {
						if (!artifactsEditor.operated) {
							// 没操作过直接退出
							editing = false;
							return;
						}
						const afterEditHtml = artifactsEditor.getSerializeCode();
						const mid = $artifactsMessages[$selectedArtifactsMessageID].mid;
						const sourceCodeEnableReplace = history.messages[mid].content.includes(
							artifactsEditor.code
						);

						if (sourceCodeEnableReplace) {
							try {
								loading = true;
								const newHistory = {
									...history,
									messages: {
										...history.messages,
										[mid]: {
											...history.messages[mid],
											content: history.messages[mid].content.replace(
												artifactsEditor.code,
												afterEditHtml
											)
										}
									}
								};
								await saveChatHandler($chatId, newHistory);

								// 调用返回成功再本地更新
								artifactsMessages.update((messages) => {
									messages[$selectedArtifactsMessageID].code = afterEditHtml;
									return messages;
								});
							} catch (error) {
								toast.error(error.message);
							} finally {
								loading = false;
							}
						} else {
							toast.error('source code not found');
						}
						editing = false;
					}}
					class="px-3 py-1.5 dark:bg-[#484A58] dark:hover:bg-[#5A5C68] text-sm rounded-lg {loading
						? 'text-white bg-gray-200 dark:text-white/40 dark:bg-gray-700'
						: 'text-white dark:text-white button-gradient'}">{$i18n.t('Save')}</button
				>
			</div>
		{:else}
			<div class="flex-1 shrink-1 flex items-center gap-2">
				<div
					class="shrink-1 pointer-events-none z-50 flex items-center gap-2 whitespace-nowrap font-medium"
				>
					<CodeFile className="size-5" />
					<span>{$i18n.t('File')}</span>
				</div>
			</div>

			<div class=" flex-1 shrink-1 z-50 flex items-center justify-end gap-2">
				{#if showDeployButton}
					<DeployProjectButton
						text={$i18n.t('Share')}
						run={async () => {
							const mid = $artifactsMessages[$selectedArtifactsMessageID].mid;
							const idx = [
								...Object.keys($artifactsMessages).filter((id) => id.startsWith(mid))
							].findIndex((id) => id === $selectedArtifactsMessageID);
							try {
								const res = await deployProject($chatId, mid, idx);
								if (res) {
									const shareId = res.site_url.split('/').at(-1);
									if (shareId) {
										return location.origin + `/space/${shareId}`;
									}
								}
							} catch (e) {
								toast.error($i18n.t('Failed to deploy html'));
							}
							return '';
						}}
					/>
				{/if}
				{#if !isMobile && showEdit && $artifactsMessages[$selectedArtifactsMessageID]?.lang === 'html'}
					{@const disabled = showType !== 'preview'}
					<Tooltip content={$i18n.t('Edit')}>
						<button
							{disabled}
							class="p-1.5 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 {disabled
								? 'cursor-not-allowed text-gray-400 dark:text-gray-600'
								: ''}"
							on:click={() => {
								editing = true;
								artifactsEditor = new ArtifactsEditor(
									$artifactsMessages[$selectedArtifactsMessageID].code,
									iframeElement
								);
								artifactsEditor.walkAST();
								artifactsEditor.serializeAST(handleHTMLContent);
							}}
						>
							<Pencil className="size-5" strokeWidth="2" />
						</button>
					</Tooltip>
				{/if}
				{#if showDeployButton}
					<Tooltip content={$i18n.t('Open Website')}>
						<button
							class="p-1.5 rounded-lg hover:bg-black/5 dark:hover:bg-white/5"
							on:click={async () => {
								const mid = $artifactsMessages[$selectedArtifactsMessageID].mid;
								const idx = [
									...Object.keys($artifactsMessages).filter((id) => id.startsWith(mid))
								].findIndex((id) => id === $selectedArtifactsMessageID);
								try {
									const res = await deployProject($chatId, mid, idx);
									if (res) {
										const shareId = res.site_url.split('/').at(-1);
										if (shareId) {
											window.open(location.origin + `/space/${shareId}`, '_blank');
										}
									}
								} catch (e) {
									toast.error($i18n.t('Failed to deploy html'));
								}
								return '';
							}}
						>
							<BrowserIcon className="size-5" strokeWidth="2" />
						</button>
					</Tooltip>
				{/if}
				{#if $artifactsMessages[$selectedArtifactsMessageID]?.lang === 'svg'}
					<div class="">
						<Dropdown bind:show={showDownloadMenu}>
							<button
								class=" flex items-center gap-2 flex-nowrap whitespace-nowrap bg-none border-none px-1.5 py-0.5 font-medium text-sm"
								on:mouseenter={() => (showDownloadMenu = true)}
								on:click={downloadSvg}
							>
								<div class="flex items-center gap-2">
									<Download className="size-4" strokeWidth="2" />
									<span class="hover:underline @max-md:hidden">{$i18n.t('Download')}</span>
								</div>
							</button>
							<div slot="content">
								<DropdownMenu.Content
									class="max-w-[200px] rounded-lg z-50 bg-white dark:bg-gray-850 dark:text-white shadow-lg"
									sideOffset={10}
									side="bottom"
									align="start"
								>
									<!-- svelte-ignore a11y-no-static-element-interactions -->
									<div
										class="flex flex-col gap-1 p-1 text-sm"
										on:mouseleave={() => {
											showDownloadMenu = false;
										}}
									>
										<DropdownMenu.Item
											class="px-4 py-1 rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800"
										>
											<button on:click={downloadSvg}>
												{$i18n.t('Download SVG')}
											</button>
										</DropdownMenu.Item>
										<DropdownMenu.Item
											class="px-4 py-1 rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800"
										>
											<button on:click={downloadPng}>
												{$i18n.t('Download PNG')}
											</button>
										</DropdownMenu.Item>
									</div>
								</DropdownMenu.Content>
							</div>
						</Dropdown>
					</div>
				{/if}
				<Divider className="shrink-0 h-6" orientation="vertical" />
				<button
					class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
					on:click={() => {
						dispatch('close');
						selectedArtifactsMessageID.set('');
						showControls.set('');
					}}
				>
					<XMark className="size-5 text-gray-900 dark:text-white" />
				</button>
			</div>
		{/if}
	</div>

	<div class=" flex-1 relative overflow-hidden">
		{#if overlay}
			<div class=" absolute top-0 left-0 right-0 bottom-0 z-10"></div>
		{/if}

		<div class="artifactsContainer flex-1 w-full h-full">
			<div class=" h-full flex relative">
				{#if supportPreview && !editing}
					<div class="artifactsActions absolute top-0 left-0 flex z-11 py-3 px-4">
						<Tab
							classNames={{
								root: 'artifactTabs text-sm border-1 border-black/10 dark:border-white/10'
							}}
							value={showType}
							onValueChange={(value) => {
								if (value) showType = value;
							}}
							items={[
								...(supportPreview ? [{ label: $i18n.t('Preview'), value: 'preview' }] : []),
								{ label: $i18n.t('Code'), value: 'code' }
							]}
						/>
					</div>
				{/if}

				<div class="artifactsActions absolute top-0 right-0 flex z-11 py-3 px-4">
					{#if showType === 'code'}
						<button
							class=" flex items-center gap-2 border-none text-sm bg-white hover:bg-gray-100 dark:bg-gray-850 dark:hover:bg-gray-800 transition rounded-full py-2 px-5 shadow-lg"
							on:click={() => {
								copyToClipboard($artifactsMessages[$selectedArtifactsMessageID].code);
								copied = true;

								setTimeout(() => {
									copied = false;
								}, 2000);
							}}
						>
							<Copy className="size-4 inline" strokeWidth="1.5" />
							<span class="">
								{copied ? $i18n.t('Copied') : $i18n.t('Copy')}
							</span>
						</button>
					{/if}
				</div>
				<div class="max-w-full w-full h-full">
					{#if showType === 'preview'}
						{#if previewContent?.type === 'iframe'}
							<iframe
								bind:this={iframeElement}
								title="Content"
								srcdoc={editing ? artifactsEditor.codeSerialized : previewContent.content}
								class="w-full border-0 h-full rounded-none"
								sandbox="allow-scripts allow-forms allow-same-origin"
								on:load={iframeLoadHandler}
								on:error={(e) => {
									console.error('iframe error:', e);
								}}
							></iframe>
						{:else if previewContent?.type === 'svg'}
							<SvgPanZoom
								className=" max-h-full max-w-full h-full w-full"
								svg={previewContent.content}
							/>
						{/if}
					{:else if showType === 'code' && codeContent}
						<CodeEditor
							autoScroll={true}
							readOnly
							lang={codeContent.lang}
							value={codeContent.content}
						/>
					{:else}
						<div class="m-auto font-medium text-xs text-gray-900 dark:text-white">
							{$i18n.t('No HTML, CSS, or JavaScript content found.')}
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	.artifactsActions {
		transition:
			opacity 0.1s ease-in-out,
			visibility 0.1s ease-in-out;
	}
	.artifactsContainer:not(:hover) .artifactsActions {
		opacity: 0;
		visibility: hidden;
	}
	.artifactsContainer:hover .artifactsActions {
		opacity: 1;
		visibility: visible;
	}
</style>
