<script lang="ts">
	import { getContext } from 'svelte';

	import {
		config,
		user,
		showNotifySignInModal,
		NotifySignInModelType,
		SignInModalType,
		SignInModalSource
	} from '$lib/stores';

	import Tooltip from '$lib/components/common/Tooltip.svelte';

	const i18n = getContext('i18n');

	export let uploadFilesHandler: Function;

	// 直接处理点击事件，触发文件上传
	const handleClick = () => {
		// 检查登录状态
		if (!$user || $user?.role === 'guest') {
			NotifySignInModelType.set([SignInModalType.FileUpload, SignInModalSource.Home]);
			showNotifySignInModal.set(true);
			return;
		}

		// 检查用户文件上传权限
		if ($user?.role !== 'admin' && !($user?.permissions?.chat?.file_upload ?? true)) {
			return;
		}

		// 直接触发文件上传
		uploadFilesHandler();
	};
</script>

<Tooltip
	content={$config?.file?.max_count || $config?.file?.max_size
		? (() => {
				const parts = [];
				if ($config?.file?.max_count) {
					parts.push($i18n.t('Up to {{maxCount}} files', { maxCount: $config.file.max_count }));
				}
				if ($config?.file?.max_size) {
					parts.push($i18n.t('Max {{maxSize}} MB per file', { maxSize: $config.file.max_size }));
				}
				return parts.length > 0 ? parts.join(', ') : $i18n.t('Upload Files');
			})()
		: $i18n.t('Upload Files')}
>
	<div
		on:click={handleClick}
		role="button"
		tabindex="0"
		on:keydown={(e) => e.key === 'Enter' && handleClick()}
	>
		<slot />
	</div>
</Tooltip>
