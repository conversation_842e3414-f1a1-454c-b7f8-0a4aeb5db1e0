<script>
	import Copy from '$lib/components/icons/Copy.svelte';
	import Download2Icon from '$lib/components/icons/Download2Icon.svelte';
	import ShareOne from '$lib/components/icons/ShareOne.svelte';
	import WechatIcon from '$lib/components/icons/WechatIcon.svelte';
	import XIcon from '$lib/components/icons/XIcon.svelte';
	import PPTExportButton from '../PPTExportButton.svelte';
	import { copyToClipboard } from '$lib/utils';
	import { DropdownMenu } from 'bits-ui';
	import { getContext } from 'svelte';
	import QRCode from 'qrcode';
	import { mobile } from '$lib/stores';
	import { twMerge } from 'tailwind-merge';
	const i18n = getContext('i18n');

	const emptyClassNames = {
		trigger: ''
	};

	let open = false;
	let loading = false;
	let site_url = '';
	let copied = false;
	let showWechatDialog = false;
	let wechatPinned = false; // 记录微信对话框是否被点击固定
	let qrcodeDataUrl = '';
	export let text = '';
	export let run = async () => '';
	export let shareTitle = '';
	export let disabled = false;
	export let showExportPDF = false;
	export let classNames = {
		trigger: ''
	};

	// PPT导出相关属性
	export let pptPages = null;
	export let chatId = '';
	export let pptVersion = 0;

	$: _classNames = {
		...emptyClassNames,
		...classNames
	};

	const runDeploy = async () => {
		loading = true;
		const res = await run();
		try {
			const qrcode = await QRCode.toDataURL(res);
			qrcodeDataUrl = qrcode;
		} catch (error) {
			console.error(error);
		}
		loading = false;
		return res;
	};






</script>

<DropdownMenu.Root
	bind:open
	closeFocus={false}
	typeahead={false}
	on:change={(e) => {
		if (!e.detail) {
			copied = false;
			showWechatDialog = false;
			wechatPinned = false;
		}
	}}
>
	<DropdownMenu.Trigger {disabled} class="flex items-center justify-center">
		<button
			{disabled}
			on:click|stopPropagation={async () => {
				if (open) {
					// 如果当前是打开状态，直接关闭
					open = false;
					wechatPinned = false;
					showWechatDialog = false;
				} else {
					// 如果当前是关闭状态，执行部署并打开
					const url = await runDeploy();
					if (url) {
						site_url = url;
						open = true;
					}
				}
			}}
		>
			<slot name="trigger">
				<button
					class={twMerge(
						'flex items-center gap-1 text-sm text-white whitespace-nowrap py-2 px-3 rounded-lg font-medium',
						disabled
							? 'cursor-not-allowed bg-gray-200 dark:bg-gray-300 dark:text-black/30'
							: 'deploy-html-button'
					)}
				>
					<ShareOne className="size-4 inline" />
					<!-- {#if loading}
			<Spinner className="size-4 inline" />
		{:else}
		{/if} -->
					{#if text}
						<span>{text}</span>
					{/if}
				</button>
			</slot>
		</button>
	</DropdownMenu.Trigger>

	<DropdownMenu.Content
		class="relative min-w-[110px] md:min-w-[280px]  rounded-2xl border-1 border-black/10 dark:border-white/10 px-3 py-5 z-50 bg-white dark:bg-[#25282A] dark:text-white shadow-xl text-sm"
		side="bottom"
		align="start"
		sideOffset={10}
	>
		{#if showWechatDialog}
			<div
				class="absolute bg-white dark:bg-[#25282A] dark:text-white/80 shadow-xl px-4 pt-4 pb-2 dark:pb-4 text-sm wechat-dialog flex flex-col items-center"
				style={$mobile ? 'left:0;top:100%;' : ''}
			>
				<div class="text-black dark:text-white/80 opacity-60 mb-1 text-center">
					{$i18n.t('Scan with WeChat to share with friends')}
				</div>
				<div>
					<img
						src={qrcodeDataUrl}
						alt={$i18n.t('Scan with WeChat to share with friends')}
						width="140"
						height="140"
						class="qr-code-image"
					/>
				</div>
			</div>
		{/if}

		<div class="flex flex-col gap-1">
			<div class="text-sm font-normal px-3 mb-1 dark:text-white/80 text-black/60">
				<p class="mb-1">
					{$i18n.t('Anyone with this link can view')}
				</p>
				<p>{$i18n.t('Updates in this thread')}</p>
			</div>
			<div
				role="button"
				tabindex="0"
				on:keydown={() => {}}
				class="px-3 py-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer flex items-center gap-2 dark:text-white/80"
				on:click={() => {
					copyToClipboard(site_url);
					copied = true;
					setTimeout(() => {
						copied = false;
					}, 2000);
				}}
			>
				<Copy className="inline size-5" />
				{$i18n.t(copied ? 'Copied' + '!' : 'Copy Link')}
			</div>
			<DropdownMenu.Item
				class="w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer "
			>
				<button
					on:click={() => {
						const prefixText = $i18n.t('Powered by z.ai');
						let text = `${prefixText}\n${site_url}`;
						if (shareTitle) {
							text = `✨ ${shareTitle} | ${text}`;
						} else {
							text = `✨ ${text}`;
						}
						const twitterUrl = `https://x.com/intent/tweet?text=${encodeURIComponent(text)}`;
						const a = document.createElement('a');
						a.href = twitterUrl;
						a.target = '_blank';
						a.rel = 'noopener noreferrer';
						a.click();
					}}
					class="px-3 py-2 flex items-center gap-2 w-full dark:text-white/80"
					><XIcon className="size-5 inline" /> {$i18n.t('Share to X')}</button
				>
			</DropdownMenu.Item>
			<button
				on:mouseenter={() => {
					if (!wechatPinned) {
						showWechatDialog = true;
					}
				}}
				on:mouseleave={() => {
					if (!wechatPinned) {
						showWechatDialog = false;
					}
				}}
				on:click={() => {
					if (wechatPinned) {
						// 如果已经固定，点击取消固定
						wechatPinned = false;
						showWechatDialog = false;
					} else {
						// 如果未固定，点击固定显示
						wechatPinned = true;
						showWechatDialog = true;
					}
				}}
				class="px-3 py-2 flex items-center gap-2 w-full rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 cursor-pointer dark:text-white/80"
				><WechatIcon className="size-5  inline" />
				{$i18n.t('Share to WeChat')}</button
			>
			{#if pptPages?.pages.length}
				<!-- PPT导出组件 -->
				<PPTExportButton
					{pptPages}
					{disabled}
					chatId={chatId}
					pptVersion={pptVersion}
					inDropdown={true}
				/>
			{:else if showExportPDF}
				<button
					class="px-3 py-1 flex items-center gap-2 text-black/40 dark:text-white/40 select-none cursor-not-allowed"
				>
					<Download2Icon className="size-5  inline" />
					{$i18n.t('Export as PDF')}
					<span
						class="text-xs px-2 py-[6px] rounded-[6px] bg-[#F0F0F0] dark:bg-gray-800 text-black/40 font-semibold dark:text-white/40"
					>
						{$i18n.t('Coming soon')}
					</span>
				</button>
			{/if}
		</div>
	</DropdownMenu.Content>
</DropdownMenu.Root>



<style>
	.deploy-html-button {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}

	.wechat-dialog {
		border: 1px solid #0000001a;
		box-shadow: 0px 1px 4px 0px #0000001a;
		border-radius: 16px;
		top: 60%;
		left: -194px;
		width: 190px;
		/* height: 190px; */
		overflow: hidden;
		/* transform: translate(-102%, 70%); */
	}
	/* :global(.dark) .wechat-dialog {
		height: 198px;
	} */
</style>
