<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { marked } from 'marked';

	import { onMount, getContext, tick, createEventDispatcher } from 'svelte';
	import { blur, fade } from 'svelte/transition';

	const dispatch = createEventDispatcher();

	import {
		config,
		user,
		models as _models,
		temporaryChatEnabled,
		mobile,
		userTheme,
		showModalPPTNotifySignin
	} from '$lib/stores';
	import { sanitizeResponseContent, extractCurlyBraceWords } from '$lib/utils';
	import { WEBUI_BASE_URL } from '$lib/constants';

	import Suggestions from './Suggestions.svelte';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import EyeSlash from '$lib/components/icons/EyeSlash.svelte';
	import MessageInput from './MessageInput.svelte';
	import AnalyticsTracker from '$lib/components/analytics/AnalyticsTracker.svelte';

	const i18n = getContext('i18n');

	export let transparentBackground = false;

	export let createMessagePair: Function;
	export let stopResponse: Function;

	export let autoScroll = false;

	export let atSelectedModel: Model | undefined;
	export let selectedModels: [''];

	export let history;

	export let prompt = '';
	export let files = [];

	export let selectedToolIds = [];
	export let imageGenerationEnabled = false;
	export let codeInterpreterEnabled = false;
	export let webSearchEnabled = false;
	export let selectedMCPServers = [];
	export let disabledMCPServerMap: Record<string, boolean> = {};

	export let toolServers = [];

	let models = [];

	export let inputFlags: string[] = [];
	export let inputFeatures: any[] | undefined;

	let selectedGroupName = '';
	let selectedGroup = '';
	let suggestionsComponent;

	// 导出重置函数供外部调用
	export const resetSuggestions = () => {
		suggestionsComponent?.resetToFirstLevel?.();
		// selectedGroupName = ''; // 确保 selectedGroupName 被清空
		inputFlags = []; // 清除已有的 flag
		inputFeatures = undefined; // 清除已有的 features
		selectedMCPServers = [];
		disabledMCPServerMap = {};
	};

	const selectSuggestionPrompt = async (p) => {
		let { text, flags, features } = p;

		let _text = text;

		if (_text.includes('{{CLIPBOARD}}')) {
			const clipboardText = await navigator.clipboard.readText().catch((err) => {
				toast.error($i18n.t('Failed to read clipboard contents'));
				return '{{CLIPBOARD}}';
			});

			_text = _text.replaceAll('{{CLIPBOARD}}', clipboardText);

			console.log('Clipboard text:', clipboardText, _text);
		}

		prompt = _text;
		flags && (inputFlags = flags);
		features && (inputFeatures = features);

		await tick();

		const chatInputContainerElement = document.getElementById('chat-input-container');
		const chatInputElement = document.getElementById('chat-input');

		if (chatInputContainerElement) {
			chatInputContainerElement.scrollTop = chatInputContainerElement.scrollHeight;
		}

		await tick();
		if (chatInputElement) {
			// chatInputElement.focus();
			chatInputElement.dispatchEvent(new Event('input'));
		}

		await tick();
	};

	const selectSuggestionGroup = (data: any) => {
		const { text, features = [], flags = [] } = data;
		if (text !== undefined && text !== '') {
			prompt = text;
		}
		inputFlags = flags;
		inputFeatures = features;
	};

	let selectedModelIdx = 0;

	$: if (selectedModels.length > 0) {
		selectedModelIdx = models.length - 1;
	}

	$: models = selectedModels.map((id) => $_models.find((m) => m.id === id));

	// 监听 selectedModels 的变化。
	// 则调用 resetSuggestions 来重置建议提示回到一级菜单。
	let oldSelectedModel = selectedModels[0] ?? '';
	$: if (selectedModels[0] !== oldSelectedModel) {
		resetSuggestions();
		oldSelectedModel = selectedModels[0];
	}
</script>

<div
	class="m-auto w-full {$mobile
		? ''
		: 'translate-y-[-68px]'} max-w-6xl px-2 @2xl:px-20 translate-y-6 text-center"
>
	<AnalyticsTracker md="landingpage" ct="landingpage_pv" autoTrack={true} />

	<div
		class="w-full text-3xl text-gray-800 dark:text-gray-100 text-center flex items-center gap-4 font-primary"
	>
		<div class="w-full flex flex-col {$mobile ? 'gap-6' : 'gap-1'} justify-center items-center">
			{#if $mobile}
				<div class="mt-[15%]">
					<img
						class="w-16 h-12"
						src="/static/{$userTheme === 'light' ? 'logoLight.png' : 'logoDark.png'}"
						alt="logo"
					/>
				</div>
			{/if}
			<div class="flex flex-row justify-center gap-3 @sm:gap-3.5 w-fit px-5">
				<div
					class=" text-[40px] @max-md:text-[24px] font-semibold line-clamp-1"
					in:fade={{ duration: 100 }}
				>
					<span>{$i18n.t('Hi,')}</span>
					{#if $user.role === 'guest'}
						<span>{$i18n.t(`I'm`)}</span>
					{/if}
					<span class={$userTheme === 'light' ? 'textGradient' : 'textGradientDark'}
						>{$user?.role === 'guest' ? 'Z.ai' : $user?.name}</span
					>
				</div>
			</div>

			<div class="h-4"></div>

			<!-- 这里的模型介绍不要 -->
			<!-- <div class="flex mt-1 mb-2">
				<div in:fade={{ duration: 100, delay: 50 }}> -->
			<!-- {#if models[selectedModelIdx]?.info?.meta?.description ?? null}
						<Tooltip
							className=" w-fit"
							content={marked.parse(
								sanitizeResponseContent(models[selectedModelIdx]?.info?.meta?.description ?? '')
							)}
							placement="top"
						>
							<div
								class="mt-0.5 px-2 text-sm font-normal text-gray-500 dark:text-gray-400 line-clamp-2 max-w-xl markdown"
							>
								{@html marked.parse(
									sanitizeResponseContent(models[selectedModelIdx]?.info?.meta?.description)
								)}
							</div>
						</Tooltip>

						{#if models[selectedModelIdx]?.info?.meta?.user}
							<div class="mt-0.5 text-sm font-normal text-gray-400 dark:text-gray-500">
								By
								{#if models[selectedModelIdx]?.info?.meta?.user.community}
									<a
										href="https://openwebui.com/m/{models[selectedModelIdx]?.info?.meta?.user
											.username}"
										>{models[selectedModelIdx]?.info?.meta?.user.name
											? models[selectedModelIdx]?.info?.meta?.user.name
											: `@${models[selectedModelIdx]?.info?.meta?.user.username}`}</a
									>
								{:else}
									{models[selectedModelIdx]?.info?.meta?.user.name}
								{/if}
							</div>
						{/if}
					{/if} -->
			<!-- </div>
			</div> -->

			{#if !$mobile}
				{#if $temporaryChatEnabled && $user?.role != 'guest'}
					<Tooltip
						content={$i18n.t(
							'This chat won’t appear in history and your messages will not be saved.'
						)}
						className="w-full flex justify-center mb-0.5"
						placement="top"
					>
						<div class="flex items-center gap-2 text-gray-500 font-medium text-sm w-fit">
							<EyeSlash strokeWidth="2" className="size-4" />{$i18n.t('Temporary Chat')}
						</div>
					</Tooltip>
				{/if}
				<div class="text-base font-normal @md:max-w-3xl w-full {atSelectedModel ? 'mt-2' : ''}">
					<MessageInput
						{selectedGroup}
						{history}
						{selectedModels}
						bind:files
						bind:prompt
						bind:autoScroll
						bind:selectedToolIds
						bind:selectedMCPServers
						bind:disabledMCPServerMap
						bind:imageGenerationEnabled
						bind:codeInterpreterEnabled
						bind:webSearchEnabled
						bind:atSelectedModel
						bind:flags={inputFlags}
						bind:features={inputFeatures}
						{toolServers}
						{transparentBackground}
						{stopResponse}
						{createMessagePair}
						{resetSuggestions}
						placeholder={$i18n.t('How can I help you today?')}
						on:upload={(e) => {
							dispatch('upload', e.detail);
						}}
						on:submit={(e) => {
							if ($user?.role === 'guest' && inputFlags.includes('ppt_composer')) {
								showModalPPTNotifySignin.set(true);
								return;
							}
							dispatch('submit', e.detail);
						}}
					/>
				</div>
			{/if}
		</div>
	</div>
	{#if !$mobile}
		<div class="mt-1 mx-auto max-w-3xl font-primary" in:fade={{ duration: 200, delay: 200 }}>
			<div class="">
				<Suggestions
					bind:selectedGroup
					flags={inputFlags}
					bind:this={suggestionsComponent}
					suggestionPrompts={atSelectedModel?.info?.meta?.suggestion_prompts ??
						models[selectedModelIdx]?.info?.meta?.suggestion_prompts ??
						[]}
					className="flex flex-row"
					inputValue={prompt}
					{selectedModels}
					bind:selectedGroupName
					on:select_group={(e) => {
						selectSuggestionGroup(e.detail);
					}}
					on:select_prompt={(e) => {
						selectSuggestionPrompt(e.detail);
					}}
				/>
			</div>
		</div>
	{/if}
</div>

{#if $mobile}
	{#if $temporaryChatEnabled && $user?.role != 'guest'}
		<Tooltip
			content={$i18n.t('This chat won’t appear in history and your messages will not be saved.')}
			className="w-full flex justify-center mb-0.5"
			placement="top"
		>
			<div class="flex items-center gap-2 text-gray-500 font-medium text-sm w-fit pl-6">
				<EyeSlash strokeWidth="2" className="size-4" />{$i18n.t('Temporary Chat')}
			</div>
		</Tooltip>
	{/if}
	<div class="font-primary" in:fade={{ duration: 200, delay: 200 }}>
		<div class="mx-5">
			<Suggestions
				bind:selectedGroup
				{selectedModels}
				flags={inputFlags}
				bind:this={suggestionsComponent}
				suggestionPrompts={atSelectedModel?.info?.meta?.suggestion_prompts ??
					models[selectedModelIdx]?.info?.meta?.suggestion_prompts ??
					[]}
				className="flex flex-row justify-start"
				inputValue={prompt}
				bind:selectedGroupName
				on:select_group={(e) => {
					selectSuggestionGroup(e.detail);
				}}
				on:select_prompt={(e) => {
					selectSuggestionPrompt(e.detail);
				}}
			/>
		</div>
	</div>
	<div class="text-base font-normal @md:max-w-3xl w-full px-3 py-3 {atSelectedModel ? 'mt-2' : ''}">
		<MessageInput
			{selectedGroup}
			{history}
			{selectedModels}
			{resetSuggestions}
			bind:flags={inputFlags}
			bind:features={inputFeatures}
			bind:files
			bind:prompt
			bind:autoScroll
			bind:selectedToolIds
			bind:selectedMCPServers
			bind:disabledMCPServerMap
			bind:imageGenerationEnabled
			bind:codeInterpreterEnabled
			bind:webSearchEnabled
			bind:atSelectedModel
			{toolServers}
			{transparentBackground}
			{stopResponse}
			{createMessagePair}
			placeholder={$i18n.t('How can I help you today?')}
			on:upload={(e) => {
				dispatch('upload', e.detail);
			}}
			on:submit={(e) => {
				if ($user?.role === 'guest' && inputFlags.includes('ppt_composer')) {
					showModalPPTNotifySignin.set(true);
					return;
				}
				dispatch('submit', e.detail);
			}}
		/>
	</div>
{/if}

<style>
	.textGradient {
		background: linear-gradient(135deg, #191a1d 0%, #747689 46%, #191a1d 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}

	.textGradientDark {
		background: linear-gradient(135deg, #ffffff 0%, #a4a6b3 46%, #ffffff 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}
</style>
