<script lang="ts">
	import { getContext } from 'svelte';
	import { toast } from 'svelte-sonner';

	import {
		WEBUI_NAME,
		chatId,
		mobile,
		settings,
		showArchivedChats,
		showControls,
		showSidebar,
		temporaryChatEnabled,
		user,
		showNotifySignInModal,
		NotifySignInModelType,
		SignInModalType,
		SignInModalSource
	} from '$lib/stores';

	import { slide } from 'svelte/transition';
	import { page } from '$app/stores';

	import ShareChatModal from '../chat/ShareChatModal.svelte';
	import ModelSelector from '../chat/ModelSelector.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import Menu from '$lib/components/layout/Navbar/Menu.svelte';
	import UserMenu from '$lib/components/layout/Sidebar/UserMenu.svelte';
	import MenuLines from '../icons/MenuLines.svelte';
	import AdjustmentsHorizontal from '../icons/AdjustmentsHorizontal.svelte';

	import PencilSquare from '../icons/PencilSquare.svelte';
	import { goto } from '$app/navigation';
	import { trackButtonClick } from '$lib/utils/analytics';
	import Share from '../icons/Share.svelte';
	import HistoryIcon from '../icons/HistoryIcon.svelte';

	const i18n = getContext('i18n');

	export let initNewChat: Function;
	export let title: string = $WEBUI_NAME;
	export let shareEnabled: boolean = false;

	export let chat;
	export let selectedModels;
	export let showModelSelector = true;
	export let disableModelSelector = false;

	let showShareChatModal = false;
	let showDownloadChatModal = false;
</script>

<ShareChatModal bind:show={showShareChatModal} chatId={$chatId} />

<nav
	class="sticky top-0 z-30 w-full {$mobile
		? 'p-2'
		: 'px-5 py-3.75'} -mb-8 flex items-center drag-region"
>
	<div
		class=" bg-linear-to-b via-50% from-[#F4F6F8] via-[#F4F6F8] to-transparent dark:from-[#141618] dark:via-[#141618] dark:to-transparent pointer-events-none absolute inset-0 -bottom-7 z-[-1]"
	></div>

	<div class=" flex max-w-full w-full mx-auto px-1 pt-0.5 bg-transparent">
		<div class="flex justify-between gap-2 items-center w-full max-w-full">
			<div class="flex gap-1">
				{#if ['user', 'admin'].includes($user?.role) && !$showSidebar}
					<div
						class="{$showSidebar
							? 'md-hidden'
							: ''} self-start flex flex-none p-1 items-center rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-850 transition"
					>
						<Tooltip content={$i18n.t('Chat History')}>
							<button
								id="sidebar-toggle-button"
								class="cursor-pointer flex"
								on:click={() => {
									trackButtonClick('sidebar', 'sidebar_unfold');
									showSidebar.set(!$showSidebar);
								}}
								aria-label="Toggle Sidebar"
							>
								<div class="self-center">
									<MenuLines
										className=" {$mobile ? 'size-5' : 'size-6'} text-black dark:text-white "
									/>
								</div>
							</button>
						</Tooltip>
					</div>
				{/if}
				{#if ['guest'].includes($user?.role) && !$showSidebar}
					<Tooltip content={$i18n.t('Chat History')}>
						<div
							class="{$showSidebar
								? 'md-hidden'
								: ''} self-start flex flex-none p-1 items-center rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-850 transition"
						>
							<button
								id="sidebar-toggle-button"
								class="cursor-pointer flex"
								on:click={() => {
									//登录提醒
									NotifySignInModelType.set([SignInModalType.History, SignInModalSource.Home]);
									showNotifySignInModal.set(true);
								}}
								aria-label="Toggle Sidebar"
							>
								<div class="self-center">
									{#if $user?.role === 'guest'}
										<HistoryIcon
											className=" {$mobile ? 'size-5' : 'size-6'} text-black dark:text-white"
										/>
									{:else}
										<MenuLines
											className=" {$mobile ? 'size-5' : 'size-6'} text-black dark:text-white "
										/>
									{/if}
								</div>
							</button>
						</div>
					</Tooltip>
				{/if}
				<Tooltip content={$i18n.t('New Chat')}>
					<button
						id="new-chat-button"
						class="navNewChat flex {$showSidebar
							? 'md:hidden'
							: ''} cursor-pointer rounded-xl p-1 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-850 transition"
						on:click={() => {
							// 根据角色有不同的行为
							if (['user', 'admin'].includes($user?.role)) {
								initNewChat();
							} else {
								if (chat.id) {
									NotifySignInModelType.set([SignInModalType.NewChat, SignInModalSource.Chat]);
									showNotifySignInModal.set(true);
								}
							}
						}}
						aria-label="New Chat"
					>
						<div class=" m-auto self-center">
							<PencilSquare
								className=" text-black dark:text-white {$mobile ? 'size-5' : 'size-6'}"
								strokeWidth="2"
							/>
						</div>
					</button>
				</Tooltip>
			</div>
			<div
				class=" flex-1 overflow-hidden max-w-full py-0.5
			{$showSidebar ? 'ml-1' : ''}
			"
			>
				{#if showModelSelector}
					<ModelSelector
						bind:selectedModels
						showSetDefault={!shareEnabled}
						disabled={disableModelSelector}
					/>
				{/if}
			</div>

			<div class="self-start flex flex-none gap-2 items-center text-black dark:text-white">
				<!-- <div class="md:hidden flex self-center w-[1px] h-5 mx-2 bg-gray-300 dark:bg-stone-700" /> -->
				{#if shareEnabled && chat && (chat.id || $temporaryChatEnabled)}
					<button
						class="flex gap-2 items-center rounded-lg px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-850 transition"
						on:click={() => {
							trackButtonClick('sidebar', 'sidebar_chat', 'share');
							if ($user?.role === 'guest') {
								NotifySignInModelType.set([SignInModalType.Share, SignInModalSource.Home]);
								showNotifySignInModal.set(true);
							} else {
								showShareChatModal = !showShareChatModal;
							}
						}}
					>
						<Share className="size-4" />
						<span class="font-medium">{$i18n.t('Share Session')}</span>
					</button>
				{/if}
				{#if false && $user?.role === 'admin'}
					<Tooltip content={$i18n.t('Controls')}>
						<button
							class=" flex cursor-pointer rounded-xl hover:bg-gray-50 dark:hover:bg-gray-850 transition"
							on:click={async () => {
								await showControls.set('');
							}}
							aria-label="Controls"
						>
							<div class=" m-auto self-center">
								<AdjustmentsHorizontal
									className={$mobile ? 'size-5' : 'size-6'}
									strokeWidth="0.5"
								/>
							</div>
						</button>
					</Tooltip>
				{/if}

				{#if $user?.role === 'guest'}
					<div class="flex gap-2">
						<button
							class="flex h-8 flex-col items-center justify-center rounded-xl dark:bg-[#484A58] dark:hover:bg-[#5A5C68] button-gradient px-4 py-3 text-sm font-medium text-white hover:opacity-90 md:h-10"
							on:click={() => {
								trackButtonClick('Signin', 'signin_click');
								goto('/auth');
							}}>{$i18n.t('Sign in')}</button
						>
						<!-- {#if !$mobile}
							<button
								class="flex h-8 items-center justify-center rounded-xl border-1 border-black/10 bg-white px-4 py-3 text-sm font-medium text-black hover:bg-gray-50 dark:border-white/10 dark:bg-transparent dark:text-gray-100 dark:hover:bg-white/5 md:h-10"
								on:click={() => {
									goto('/auth?action=signup');
								}}>{$i18n.t('Sign up')}</button
							>
						{/if} -->
					</div>
				{:else}
					<UserMenu
						className="max-w-[200px]"
						role={$user?.role}
						on:show={(e) => {
							if (e.detail === 'archived-chat') {
								showArchivedChats.set(true);
							}
						}}
					>
						<button
							class="select-none flex rounded-xl w-full hover:bg-gray-50 dark:hover:bg-gray-850 transition"
							aria-label="User Menu"
						>
							<div class=" self-center">
								<img
									src={$user?.profile_image_url}
									class="size-7.5 object-cover rounded-full"
									alt="User profile"
									draggable="false"
								/>
							</div>
						</button>
					</UserMenu>
				{/if}
			</div>
		</div>
	</div>
</nav>

<style>
	.buttonGradient {
		background: linear-gradient(124.94deg, #191a1d 11.04%, #747689 96.98%, #191a1d 164.2%);
	}
</style>
