<script lang="ts">
	import { showControls } from '$lib/stores';
	import { mcpData, showMCPSidePanel } from '$lib/stores/mcp';
	import CodeEditor from '../common/CodeEditor.svelte';
	import Tab from '../common/Tab.svelte';
	import XMark from '../icons/XMark.svelte';
	import SearchResult from './SearchResult.svelte';

	export let history: any;

	let activeTab: string = 'arguments';

	let extraTab: Record<string, { label: string; value: string }> = {};
	const initExtraTab = () => {};

	$: $mcpData && initExtraTab();

	// 判断是否是搜索工具
	$: isSearchTool =
		$mcpData?.metadata?.name && ['search'].includes($mcpData.metadata.name.toLowerCase());
</script>

<div class="mcpSidePanel w-full h-full relative flex flex-col bg-white dark:bg-[#26282A]">
	<!-- 搜索工具的简化头部 -->
	{#if isSearchTool && $mcpData?.browser?.search_result}
		<div
			class="mcpSidePanelHeadBar flex justify-between items-center py-2 border-b-1 border-black/10"
		>
			<div class="flex-1 shrink-1 pointer-events-none flex items-center justify-end px-4">
				<button
					class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
					on:click={() => {
						showControls.set('');
					}}
				>
					<XMark className="size-5 text-gray-900 dark:text-white" />
				</button>
			</div>
		</div>

		<!-- 直接展示搜索结果 -->
		<div class="mcpSidePanelContent flex-1 relative overflow-x-hidden overflow-y-auto p-4">
			<SearchResult
				citations={$mcpData.browser.search_result}
				originalResult={$mcpData.metadata.result}
			/>
		</div>
	{:else}
		<!-- 其他工具的原有tab展示方式 -->
		<div
			class="mcpSidePanelHeadBar flex justify-between items-center-safe py-2 border-b-1 border-black/10"
		>
			<div
				class="flex-1 shrink-1 pointer-events-none flex items-center justify-start px-4 whitespace-nowrap"
			>
				{$mcpData?.metadata.name}
			</div>
			<div class="flex justify-center flex-auto shrink-0">
				<Tab
					classNames={{
						root: 'artifactTabs text-sm'
					}}
					value={activeTab}
					onValueChange={(value) => {
						if (value) activeTab = value;
					}}
					items={[
						{ label: 'arguments', value: 'arguments' },
						{ label: 'result', value: 'result' },
						...Object.values(extraTab)
					]}
				/>
			</div>
			<div class="flex-1 shrink-1 pointer-events-none flex items-center justify-end px-4">
				<button
					class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
					on:click={() => {
						showControls.set('');
					}}
				>
					<XMark className="size-5 text-gray-900 dark:text-white" />
				</button>
			</div>
		</div>

		<div class="mcpSidePanelContent flex-1 relative overflow-x-hidden overflow-y-auto p-4">
			{#if activeTab === 'arguments'}
				<CodeEditor autoScroll={true} readOnly lang="json" value={$mcpData?.metadata.arguments} />
			{/if}
			{#if activeTab === 'result'}
				<CodeEditor autoScroll={true} readOnly lang="txt" value={$mcpData?.metadata.result} />
			{/if}
		</div>
	{/if}
</div>
