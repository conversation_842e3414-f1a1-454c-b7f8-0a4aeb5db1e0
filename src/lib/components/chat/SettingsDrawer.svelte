<script lang="ts">
	import { showSettingsDrawer, settings } from '$lib/stores';
	import { getContext } from 'svelte';
	import ChevronLeft from '../icons/ChevronLeft.svelte';
	import XMark from '../icons/XMark.svelte';
	import General from './Settings/General.svelte';
	import GeneralIcon from '../icons/GeneralIcon.svelte';
	import Interface from './Settings/Interface.svelte';
	import InterfaceIcon from '../icons/InterfaceIcon.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';
	import Chats from './Settings/Chats.svelte';
	import ChatBubbleOval from '../icons/ChatBubbleOval.svelte';
	import Account from './Settings/Account.svelte';
	import AccountIcon from '../icons/AccountIcon.svelte';
	import About from './Settings/About.svelte';
	import Info from '../icons/Info.svelte';
	import { updateUserSettings } from '$lib/apis/users';
	import { toast } from 'svelte-sonner';

	const i18n = getContext('i18n');
	const mainTitle = 'System settings';
	const menuList = [
		{
			id: 'general',
			title: 'General',
			component: General,
			icon: GeneralIcon
		},
		{
			id: 'interface',
			title: 'Interface',
			component: Interface,
			icon: InterfaceIcon
		},
		{
			id: 'dialogue',
			title: 'Dialogue',
			component: Chats,
			icon: ChatBubbleOval
		},
		{
			id: 'account',
			title: 'Account',
			component: Account,
			icon: AccountIcon
		},
		{
			id: 'about',
			title: 'About',
			component: About,
			icon: Info
		}
	];
	let steps = [mainTitle];
	let currentComponent = null;

	const saveSettings = async (updated) => {
		settings.set({ ...$settings, ...updated });
		await updateUserSettings(localStorage.token, { ui: $settings });
	};
</script>

<div class="flex flex-col gap-4 px-4 pt-5 pb-8 h-full">
	<div class="flex justify-between items-center">
		<div class="flex justify-between items-center gap-2">
			{#if steps.length > 1}
				<button
					class="p-1"
					on:click={() => {
						steps = [...steps.slice(0, -1)];
						currentComponent = menuList.find((item) => item.id === steps.at(-1))?.component ?? null;
					}}
				>
					<ChevronLeft className=" size-4 text-gray-600" strokeWidth="2" />
				</button>
			{/if}
			<div class=" font-semibold dark:text-white/80">
				{$i18n.t(steps.at(-1))}
			</div>
		</div>
		<button
			on:click={() => {
				showSettingsDrawer.set(false);
			}}
		>
			<XMark className=" size-6 text-black/40 dark:text-white/40" />
		</button>
	</div>
	<div class="flex-1 dark:text-white/80 overflow-hidden">
		{#if currentComponent}
			<svelte:component
				this={currentComponent}
				{saveSettings}
				saveHandler={() => {
					toast.success($i18n.t('Settings saved successfully!'));
				}}
				on:save={() => {
					toast.success($i18n.t('Settings saved successfully!'));
				}}
			/>
		{:else}
			<div class="flex flex-col gap-2.5">
				{#each menuList as item}
					<button
						class="flex justify-between items-center px-2 py-2.5 dark:text-white/80 hover:bg-[#F4F6F8] dark:hover:bg-[#3C3E40] transition-colors duration-100 rounded-lg"
						on:click={() => {
							currentComponent = item.component;
							steps = [...steps, item.title];
						}}
					>
						<div class="flex gap-2 items-center">
							<svelte:component
								this={item.icon}
								className="size-5 dark:text-white/80"
								strokeWidth="1.5"
							/>
							<div>{$i18n.t(item.title)}</div>
						</div>
						<div>
							<ChevronRight className=" size-5 text-black/60 dark:text-white/60" />
						</div>
					</button>
				{/each}
			</div>
		{/if}
	</div>
</div>
