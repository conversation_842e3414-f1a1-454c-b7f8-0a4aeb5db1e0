<script lang="ts">
	import { Checkbox, Popover } from 'bits-ui';
	import { config, showModalPPTNotifySignin, user, models, language } from '$lib/stores';
	import CheckBoxIndicator from '../icons/CheckBoxIndicator.svelte';
	import Search from '../icons/Search.svelte';
	import SlidesIcon from '../icons/SlidesIcon.svelte';
	import Cube from '../icons/Cube.svelte';

	const iconMap = {
		search: Search,
		presentation: SlidesIcon,
		slider: SlidesIcon,
		ppt: SlidesIcon
	};

	export let selectedServers: string[] = [];
	export let availableMCPServers: any[] = []; // 接收预计算的 MCP Server 列表

	export let disabledServersMap = {};
	export let onChange: (
		selectedServers: string[],
		server: string,
		open: boolean
	) => void = () => {};

	// 如果传入了预计算的列表就使用，否则自己计算
	$: mcpToolServers = availableMCPServers;

	function toggleServer(serverName: string) {
		if (serverName === 'ppt-maker' && $user?.role === 'guest') {
			showModalPPTNotifySignin.set(true);
			return;
		}

		if (selectedServers.includes(serverName)) {
			selectedServers = selectedServers.filter((name) => name !== serverName);
			onChange?.(selectedServers, serverName, false);
		} else {
			selectedServers = [...selectedServers, serverName];
			onChange?.(selectedServers, serverName, true);
		}
	}

	// 当可用的 MCP Server 发生变化时，清理不再可用的选择
	$: {
		const availableServerNames = mcpToolServers.map((server) => server.name);
		const filteredSelectedServers = selectedServers.filter((serverName) =>
			availableServerNames.includes(serverName)
		);

		if (filteredSelectedServers.length !== selectedServers.length) {
			selectedServers = filteredSelectedServers;
		}
	}
</script>

<Popover.Root>
	<Popover.Trigger>
		<slot />
	</Popover.Trigger>
	<Popover.Content
		sideOffset={4}
		align="start"
		class="z-10 bg-white dark:bg-gray-850 rounded-2xl py-3 px-2 shadow-lg min-w-[320px] text-black dark:text-white"
	>
		<div class="max-h-[200px] overflow-y-auto flex flex-col">
			{#each mcpToolServers as toolServer}
				{@const disabled = disabledServersMap[toolServer.name]}
				<button
					{disabled}
					class=" px-3 py-2 rounded-lg hover:bg-[#F2F4F6] dark:hover:bg-gray-800 text-sm {disabled
						? 'cursor-not-allowed text-gray-400 dark:text-gray-600'
						: ''}"
					on:click={() => {
						toggleServer(toolServer.name);
					}}
				>
					<div class="flex justify-between items-center">
						<div class="flex items-center gap-2">
							<svelte:component this={iconMap[toolServer.icon_name] ?? Cube} className="size-4" />
							<div class=" font-medium">
								{$language === 'zh-CN' ? toolServer.title_zh : toolServer.title_en}
							</div>
						</div>
						<Checkbox.Root
							class="size-5"
							checked={selectedServers.includes(toolServer.name)}
							{disabled}
						>
							<Checkbox.Indicator let:isChecked>
								{#if isChecked}
									<div
										class=" rounded-md aspect-square flex justify-center items-center {disabled
											? 'cursor-not-allowed bg-white dark:bg-black/10 border-1 border-black/10 dark:border-white/10'
											: 'button-gradient'}"
									>
										<CheckBoxIndicator
											className="size-3 {disabled
												? 'text-black/10 dark:text-white/10'
												: 'text-white dark:text-white'}"
											strokeWidth="1.5"
										/>
									</div>
								{:else}
									<div
										class="rounded-md border size-5 border-black/10 dark:border-white/10 aspect-square flex justify-center items-center {disabled
											? 'cursor-not-allowed'
											: ''}"
									></div>
								{/if}
							</Checkbox.Indicator>
						</Checkbox.Root>
					</div>
				</button>
			{/each}
		</div>
	</Popover.Content>
</Popover.Root>
