<script lang="ts">
	import { v4 as uuidv4 } from 'uuid';
	import { toast } from 'svelte-sonner';
	import mermaid from 'mermaid';
	import { PaneGroup, Pane, PaneResizer } from 'paneforge';

	import { getContext, onDestroy, onMount, tick } from 'svelte';
	import { decode } from 'html-entities';

	const i18n: Writable<i18nType> = getContext('i18n');

	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import logger from '$lib/utils/logger';

	import { get, type Unsubscriber, type Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import type { VibeInfo } from '$lib/types';
	import { EventBus, WEBUI_BASE_URL } from '$lib/constants';

	import {
		chatId,
		chats,
		config,
		type Model,
		models,
		tags as allTags,
		settings,
		showSidebar,
		WEBUI_NAME,
		banners,
		user,
		socket,
		showControls,
		showCallOverlay,
		currentChatPage,
		temporaryChatEnabled,
		mobile,
		showOverview,
		chatTitle,
		showArtifacts,
		tools,
		toolServers,
		pendingChatId,
		showAbortChatModal,
		chatReplying,
		userTheme,
		autoArtifacts,
		currentHistory,
		eventBus
	} from '$lib/stores';
	import {
		convertMessagesToHistory,
		copyToClipboard,
		getMessageContentParts,
		createMessagesList,
		findLatestMessageId,
		promptTemplate,
		getPromptVariables,
		isEmptyObject
	} from '$lib/utils';
	import { parseContent } from '$lib/utils/parser/contentParser';

	import {
		createNewChat,
		getAllTags,
		getChatById,
		getChatList,
		getTagsById,
		updateChatById
	} from '$lib/apis/chats';
	import { continueUnFinishedWithSSE, generateOpenAIChatCompletion } from '$lib/apis/openai';
	import { processWeb, processWebSearch, processYoutubeVideo } from '$lib/apis/retrieval';
	import { createOpenAITextStream } from '$lib/apis/streaming';
	import { queryMemory } from '$lib/apis/memories';
	import { getAndUpdateUserLocation, getUserSettings } from '$lib/apis/users';
	import { chatCompleted, chatAction, generateMoACompletion, stopTask } from '$lib/apis';
	import { getTools } from '$lib/apis/tools';

	import Banner from '../common/Banner.svelte';
	import MessageInput from '$lib/components/chat/MessageInput.svelte';
	import Messages from '$lib/components/chat/Messages.svelte';
	import Navbar from '$lib/components/chat/Navbar.svelte';
	import ChatControls from './ChatControls.svelte';
	import EventConfirmDialog from '../common/ConfirmDialog.svelte';
	import Placeholder from './Placeholder.svelte';
	import NotificationToast from '../NotificationToast.svelte';
	import Spinner from '../common/Spinner.svelte';
	import { trackButtonClick, trackNetworkError } from '$lib/utils/analytics';
	import AbortChatModal from './AbortChatModal.svelte';
	import Divider from '../common/Divider.svelte';
	import MailIcon from '../icons/MailIcon.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import Dropdown from '../common/Dropdown.svelte';
	import XIcon from '../icons/XIcon.svelte';
	import GithubIcon from '../icons/GithubIcon.svelte';
	import Discord from '../icons/Discord.svelte';
	import HuggingFace from '../icons/HuggingFace.svelte';
	import { artifactsMessages, selectedArtifactsMessageID } from '$lib/stores/artifacts';
	import ModalPptNotifySignin from './ModalPPTNotifySignin.svelte';

	export let chatIdProp = '';
	let previousChatId = null;

	let loading = false;

	const eventTarget = new EventTarget();
	let controlPane;
	let controlPaneComponent;

	let autoScroll = true;
	let processing = false; // 模型正在回复
	let messagesContainerElement: HTMLDivElement;

	let navbarElement;

	let showEventConfirmation = false;
	let eventConfirmationTitle = '';
	let eventConfirmationMessage = '';
	let eventConfirmationInput = false;
	let eventConfirmationInputPlaceholder = '';
	let eventConfirmationInputValue = '';
	let eventCallback = null;

	let chatPhase = 'idle';

	let chatIdUnsubscriber: Unsubscriber | undefined;

	let selectedModels = [''];
	let atSelectedModel: Model | undefined;
	let selectedModelIds = [];
	$: selectedModelIds = atSelectedModel !== undefined ? [atSelectedModel.id] : selectedModels;

	let selectedToolIds = [];
	let imageGenerationEnabled = false;
	let webSearchEnabled = false;
	let codeInterpreterEnabled = false;
	let chatAbortController: AbortController | null = null;

	let inputFlags: string[] = [];

	let inputFeatures: any[] | undefined;

	let chat = null;
	let tags = [];

	let history = {
		messages: {},
		currentId: null
	};
	$: currentHistory.set(history);

	let showMessagePage = false;

	$: showMessagePage =
		$settings?.landingPageMode === 'chat' ||
		createMessagesList(history, history.currentId).length > 0;

	$: if (
		showMessagePage &&
		get(autoArtifacts) &&
		history.currentId &&
		!isEmptyObject(get(artifactsMessages))
	) {
		const lastCodeBlock = Object.values(get(artifactsMessages))
			.filter(({ mid }) => mid === history.currentId)
			.at(-1);
		if (!get(mobile) && lastCodeBlock) {
			selectedArtifactsMessageID.set(lastCodeBlock.id);
			showControls.set('artifacts');
		}
	}

	let taskId = null;

	// Chat Input
	let prompt = '';
	let chatFiles = [];
	let files = [];
	let params = {};

	let selectedMCPServers = [];
	let disabledMCPServerMap: Record<string, boolean> = {};

	$: if (chatIdProp && chatIdProp !== previousChatId) {
		(async () => {
			previousChatId = chatIdProp;

			chatAbortController?.abort();
			chatAbortController = null;

			loading = true;
			console.log('chatId:', chatIdProp);

			prompt = '';
			files = [];
			selectedToolIds = [];
			webSearchEnabled = false;
			imageGenerationEnabled = false;

			if (chatIdProp && (await loadChat())) {
				await tick();
				loading = false;

				if (localStorage.getItem(`chat-input-${chatIdProp}`)) {
					try {
						const input = JSON.parse(localStorage.getItem(`chat-input-${chatIdProp}`) ?? '{}');

						prompt = input.prompt;
						files = input.files;
						selectedToolIds = input.selectedToolIds;
						webSearchEnabled = input.webSearchEnabled;
						imageGenerationEnabled = input.imageGenerationEnabled;
					} catch (e) {}
				}

				window.setTimeout(() => scrollToBottom(), 0);
				const chatInput = document.getElementById('chat-input');
				!$mobile && chatInput?.focus();

				await tick();
				const autoShowArtifacts = document.querySelectorAll('.autoShowArtifacts');
				if (!$mobile && $autoArtifacts && autoShowArtifacts.length > 0) {
					autoShowArtifacts[autoShowArtifacts.length - 1].click();
				}
			} else {
				await goto('/');
			}
		})();
	}

	// $: if (selectedModels && chatIdProp !== '') {
	// 	saveSessionSelectedModels();
	// }

	async function continueUnFinishedChat(message) {
		// 自己找最后一个 messsage 吧
		console.log('continueUnFinishedChat', message);
		if (message.content == '') {
			// 空的对话，还是需要继续的！
			const continueMessageId = message.id;
			// 历史的 done 得是 false
			history.messages[continueMessageId].done = false;

			eventTarget.dispatchEvent(
				new CustomEvent('chat:start', {
					detail: {
						id: continueMessageId
					}
				})
			);
			chatReplying.set(true);
			console.log('setChatReplying', true);
			await tick();
			chatAbortController = new AbortController();
			const res = await continueUnFinishedWithSSE(
				localStorage.token,
				continueMessageId,
				`${WEBUI_BASE_URL}/api`,
				chatAbortController
			).catch(async (error) => {
				chatReplying.set(false);
				console.error('continueUnFinishedChatAPIJustError', error);
			});
			if (!res || !res.body) {
				console.error('continueUnFinishedChatError empty response', res);
				return;
			}
			const reader = res.body.getReader();
			const decoder = new TextDecoder('utf-8');
			let buffer = '';

			try {
				while (true) {
					const { done, value } = await reader.read();
					if (done) break;
					buffer += decoder.decode(value, { stream: true });

					let lines = buffer.split('\n\n');
					buffer = lines.pop()!; // 最后一段可能是不完整的，留到下次

					for (const line of lines) {
						if (line.startsWith('data:')) {
							const data = line.slice(5).trim();
							const event = JSON.parse(data);
							await chatEventHandler(event, (e) => {}, continueMessageId);
						}
					}
				}
			} catch (error) {
				// const errorEventMock = {
				// 	"type": "chat:completion",
				// 	"data": {
				// 		"error": {
				// 			"detail": JSON.stringify(error)
				// 		}
				// 	},
				// }
				// chatEventHandler(errorEventMock, (e) => {}, responseMessageId);
				console.error(error);
			}
		}

		// 在这里添加你的继续未完成聊天的逻辑
	}

	const saveSessionSelectedModels = () => {
		if (selectedModels.length === 0 || (selectedModels.length === 1 && selectedModels[0] === '')) {
			return;
		}
		localStorage.selectedModels = JSON.stringify(selectedModels);
		// console.log('saveSessionSelectedModels', selectedModels, sessionStorage.selectedModels);
	};

	// Track previous model selection to detect changes
	let previousSelectedModels = [...selectedModels];

	$: if (selectedModels) {
		setToolIds();
		// Reset webSearchEnabled only when model actually changes
		if (JSON.stringify(previousSelectedModels) !== JSON.stringify(selectedModels)) {
			webSearchEnabled = false;
			previousSelectedModels = [...selectedModels];
		}
	}

	$: if (atSelectedModel || selectedModels) {
		setToolIds();
	}

	const setToolIds = async () => {
		if (!$tools) {
			tools.set($user?.role != 'guest' ? await getTools(localStorage.token) : []);
		}

		if (selectedModels.length !== 1 && !atSelectedModel) {
			return;
		}

		const model = atSelectedModel ?? $models.find((m) => m.id === selectedModels[0]);
		if (model) {
			selectedToolIds = (model?.info?.meta?.toolIds ?? []).filter((id) =>
				$tools.find((t) => t.id === id)
			);
		}
	};

	const showMessage = async (message) => {
		const _chatId = JSON.parse(JSON.stringify($chatId));
		let _messageId = JSON.parse(JSON.stringify(message.id));

		let messageChildrenIds = [];
		if (_messageId === null) {
			messageChildrenIds = Object.keys(history.messages).filter(
				(id) => history.messages[id].parentId === null
			);
		} else {
			messageChildrenIds = history.messages[_messageId].childrenIds;
		}

		while (messageChildrenIds.length !== 0) {
			_messageId = messageChildrenIds.at(-1);
			messageChildrenIds = history.messages[_messageId].childrenIds;
		}

		history.currentId = _messageId;

		await tick();
		await tick();
		await tick();

		const messageElement = document.getElementById(`message-${message.id}`);
		if (messageElement) {
			messageElement.scrollIntoView({ behavior: 'smooth' });
		}

		await tick();
		saveChatHandler(_chatId, history);
	};

	// 默认必须 handler 自己的 event 吧，ws 既然不用了，逻辑简化
	const chatEventHandler = async (event, cb, message_id) => {
		// console.log('event', event);

		const type = event?.type ?? null;
		const data = event?.data ?? null;
		const message = history.messages[message_id];

		if (type === 'status') {
			if (message?.statusHistory) {
				message.statusHistory.push(data);
			} else {
				message.statusHistory = [data];
			}
		} else if (type === 'source' || type === 'citation') {
			if (data?.type === 'code_execution') {
				// Code execution; update existing code execution by ID, or add new one.
				if (!message?.code_executions) {
					message.code_executions = [];
				}

				const existingCodeExecutionIndex = message.code_executions.findIndex(
					(execution) => execution.id === data.id
				);

				if (existingCodeExecutionIndex !== -1) {
					message.code_executions[existingCodeExecutionIndex] = data;
				} else {
					message.code_executions.push(data);
				}

				message.code_executions = message.code_executions;
			} else {
				// Regular source.
				if (message?.sources) {
					message.sources.push(data);
				} else {
					message.sources = [data];
				}
			}
		} else if (type === 'chat:completion') {
			await chatCompletionEventHandler(data, message);
		} else if (type === 'chat:title') {
			chatTitle.set(data);
			currentChatPage.set(1);
			await chats.set(await getChatList(localStorage.token, $currentChatPage));
		} else if (type === 'chat:tags') {
			chat = await getChatById(localStorage.token, $chatId);
			allTags.set(await getAllTags(localStorage.token));
		} else if (type === 'chat:message:delta' || type === 'message') {
			message.content += data.content;
		} else if (type === 'chat:message' || type === 'replace') {
			message.content = data.content;
		} else if (type === 'confirmation') {
			eventCallback = cb;

			eventConfirmationInput = false;
			showEventConfirmation = true;

			eventConfirmationTitle = data.title;
			eventConfirmationMessage = data.message;
		} else if (type === 'execute') {
			eventCallback = cb;

			try {
				// Use Function constructor to evaluate code in a safer way
				const asyncFunction = new Function(`return (async () => { ${data.code} })()`);
				const result = await asyncFunction(); // Await the result of the async function

				if (cb) {
					cb(result);
				}
			} catch (error) {
				console.error('Error executing code:', error);
			}
		} else if (type === 'input') {
			eventCallback = cb;

			eventConfirmationInput = true;
			showEventConfirmation = true;

			eventConfirmationTitle = data.title;
			eventConfirmationMessage = data.message;
			eventConfirmationInputPlaceholder = data.placeholder;
			eventConfirmationInputValue = data?.value ?? '';
		} else if (type === 'notification') {
			const toastType = data?.type ?? 'info';
			const toastContent = data?.content ?? '';

			if (toastType === 'success') {
				toast.success(toastContent);
			} else if (toastType === 'error') {
				toast.error(toastContent);
			} else if (toastType === 'warning') {
				toast.warning(toastContent);
			} else {
				toast.info(toastContent);
			}
		} else if (type === 'conn:heartbeat') {
			// nothing
		} else {
			// console.log('Unknown message type', data);
		}

		history.messages[message_id] = message;
	};

	const abortChat = async () => {
		if ($chatReplying) {
			chatAbortController?.abort();
			chatAbortController = null;
		}
		chatReplying.set(false);
		await goto($pendingChatId ? `/c/${$pendingChatId}` : '/');
		pendingChatId.set('');
		showAbortChatModal.set(false);
		const newChatButton = document.getElementById('new-chat-button');
		setTimeout(() => {
			!$mobile && newChatButton?.click();
		}, 0);
	};

	const onMessageHandler = async (event: {
		origin: string;
		data: { type: string; text: string };
	}) => {
		if (event.origin !== window.origin) {
			return;
		}

		// Replace with your iframe's origin
		if (event.data.type === 'input:prompt') {
			console.debug(event.data.text);

			const inputElement = document.getElementById('chat-input');

			if (inputElement) {
				prompt = event.data.text;
				inputElement.focus();
			}
		}

		if (event.data.type === 'action:submit') {
			console.debug(event.data.text);

			if (prompt !== '') {
				await tick();
				submitPrompt(prompt);
			}
		}

		if (event.data.type === 'input:prompt:submit') {
			console.debug(event.data.text);

			if (event.data.text !== '') {
				await tick();
				submitPrompt(event.data.text, undefined, event.data.vibeInfo);
			}
		}
	};

	onMount(async () => {
		// console.log('mounted');
		window.addEventListener('message', onMessageHandler);
		window.addEventListener('switchNewChat', abortChat);
		$socket?.on('chat-events', chatEventHandler);

		if (!$chatId) {
			chatIdUnsubscriber = chatId.subscribe(async (value) => {
				if (!value) {
					await initNewChat();
				}
			});
		} else {
			if ($temporaryChatEnabled) {
				await goto('/');
			}
		}

		if (localStorage.getItem(`chat-input-${chatIdProp}`)) {
			try {
				const input = JSON.parse(localStorage.getItem(`chat-input-${chatIdProp}`));
				prompt = input.prompt;
				files = input.files;
				selectedToolIds = input.selectedToolIds;
				webSearchEnabled = input.webSearchEnabled;
				imageGenerationEnabled = input.imageGenerationEnabled;
			} catch (e) {
				prompt = '';
				files = [];
				selectedToolIds = [];
				webSearchEnabled = false;
				imageGenerationEnabled = false;
			}
		}

		showControls.subscribe(async (value) => {
			if (controlPane && !$mobile) {
				try {
					if (value) {
						controlPaneComponent.openPane();
					} else {
						controlPane.collapse();
					}
				} catch (e) {
					// ignore
				}
			}

			if (!value) {
				showCallOverlay.set(false);
				showOverview.set(false);
			}
		});

		const chatInput = document.getElementById('chat-input');
		!$mobile && chatInput?.focus();

		chats.subscribe(() => {});
	});

	onDestroy(() => {
		chatIdUnsubscriber?.();
		// abortController.abort();
		window.removeEventListener('message', onMessageHandler);
		window.removeEventListener('switchNewChat', abortChat);
		$socket?.off('chat-events', chatEventHandler);
	});

	// File upload functions

	const uploadGoogleDriveFile = async (fileData) => {
		console.log('Starting uploadGoogleDriveFile with:', {
			id: fileData.id,
			name: fileData.name,
			url: fileData.url,
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		// Validate input
		if (!fileData?.id || !fileData?.name || !fileData?.url || !fileData?.headers?.Authorization) {
			throw new Error('Invalid file data provided');
		}

		const tempItemId = uuidv4();
		const fileItem = {
			type: 'file',
			file: '',
			id: null,
			url: fileData.url,
			name: fileData.name,
			collection_name: '',
			status: 'uploading',
			error: '',
			itemId: tempItemId,
			size: 0
		};

		try {
			files = [...files, fileItem];
			console.log('Processing web file with URL:', fileData.url);

			// Configure fetch options with proper headers
			const fetchOptions = {
				headers: {
					Authorization: fileData.headers.Authorization,
					Accept: '*/*'
				},
				method: 'GET'
			};

			// Attempt to fetch the file
			console.log('Fetching file content from Google Drive...');
			const fileResponse = await fetch(fileData.url, fetchOptions);

			if (!fileResponse.ok) {
				const errorText = await fileResponse.text();
				throw new Error(`Failed to fetch file (${fileResponse.status}): ${errorText}`);
			}

			// Get content type from response
			const contentType = fileResponse.headers.get('content-type') || 'application/octet-stream';
			console.log('Response received with content-type:', contentType);

			// Convert response to blob
			console.log('Converting response to blob...');
			const fileBlob = await fileResponse.blob();

			if (fileBlob.size === 0) {
				throw new Error('Retrieved file is empty');
			}

			console.log('Blob created:', {
				size: fileBlob.size,
				type: fileBlob.type || contentType
			});

			// Create File object with proper MIME type
			const file = new File([fileBlob], fileData.name, {
				type: fileBlob.type || contentType
			});

			console.log('File object created:', {
				name: file.name,
				size: file.size,
				type: file.type
			});

			if (file.size === 0) {
				throw new Error('Created file is empty');
			}

			// Upload file to server
			console.log('Uploading file to server...');
			const uploadedFile = await uploadFile(localStorage.token, file);

			if (!uploadedFile) {
				throw new Error('Server returned null response for file upload');
			}

			console.log('File uploaded successfully:', uploadedFile);

			// Update file item with upload results
			fileItem.status = 'uploaded';
			fileItem.file = uploadedFile;
			fileItem.id = uploadedFile.id;
			fileItem.size = file.size;
			fileItem.collection_name = uploadedFile?.meta?.collection_name;
			fileItem.url = `${WEBUI_API_BASE_URL}/files/${uploadedFile.id}`;

			files = files;
			toast.success($i18n.t('File uploaded successfully'));
		} catch (e) {
			console.error('Error uploading file:', e);
			files = files.filter((f) => f.itemId !== tempItemId);
			toast.error(
				$i18n.t('Error uploading file: {{error}}', {
					error: e.message || 'Unknown error'
				})
			);
		}
	};

	const uploadWeb = async (url) => {
		console.log(url);

		const fileItem = {
			type: 'doc',
			name: url,
			collection_name: '',
			status: 'uploading',
			url: url,
			error: ''
		};

		try {
			files = [...files, fileItem];
			const res = await processWeb(localStorage.token, '', url);

			if (res) {
				fileItem.status = 'uploaded';
				fileItem.collection_name = res.collection_name;
				fileItem.file = {
					...res.file,
					...fileItem.file
				};

				files = files;
			}
		} catch (e) {
			// Remove the failed doc from the files array
			files = files.filter((f) => f.name !== url);
			toast.error(JSON.stringify(e));
		}
	};

	const uploadYoutubeTranscription = async (url) => {
		console.log(url);

		const fileItem = {
			type: 'doc',
			name: url,
			collection_name: '',
			status: 'uploading',
			context: 'full',
			url: url,
			error: ''
		};

		try {
			files = [...files, fileItem];
			const res = await processYoutubeVideo(localStorage.token, url);

			if (res) {
				fileItem.status = 'uploaded';
				fileItem.collection_name = res.collection_name;
				fileItem.file = {
					...res.file,
					...fileItem.file
				};
				files = files;
			}
		} catch (e) {
			// Remove the failed doc from the files array
			files = files.filter((f) => f.name !== url);
			toast.error(`${e}`);
		}
	};

	//////////////////////////
	// Web functions
	//////////////////////////

	const initNewChat = async () => {
		// Reset all parameters first
		webSearchEnabled = false;
		if ($page.url.searchParams.get('models')) {
			selectedModels = $page.url.searchParams.get('models')?.split(',');
		} else if ($page.url.searchParams.get('model')) {
			const urlModels = $page.url.searchParams.get('model')?.split(',');

			if (urlModels.length === 1) {
				const m = $models.find((m) => m.id === urlModels[0]);
				if (!m) {
					const modelSelectorButton = document.getElementById('model-selector-0-button');
					if (modelSelectorButton) {
						modelSelectorButton.click();
						await tick();

						const modelSelectorInput = document.getElementById('model-search-input');
						if (modelSelectorInput) {
							modelSelectorInput.focus();
							modelSelectorInput.value = urlModels[0];
							modelSelectorInput.dispatchEvent(new Event('input'));
						}
					}
				} else {
					selectedModels = urlModels;
				}
			} else {
				selectedModels = urlModels;
			}
		} else {
			const oldmodels = selectedModels[0];
			if (localStorage.selectedModels) {
				selectedModels = JSON.parse(localStorage.selectedModels);
				// localStorage.removeItem('selectedModels');
			} else {
				if ($settings?.models) {
					selectedModels = $settings?.models;
				} else if ($config?.default_models) {
					console.log($config?.default_models.split(',') ?? '');
					selectedModels = $config?.default_models.split(',');
				}
			}
			if (oldmodels !== selectedModels[0]) {
				inputFlags = [];
				inputFeatures = [];
			}
		}

		selectedModels = selectedModels.filter((modelId) => $models.map((m) => m.id).includes(modelId));
		if (selectedModels.length === 0 || (selectedModels.length === 1 && selectedModels[0] === '')) {
			if ($models.length > 0) {
				selectedModels = [$models[0].id];
			} else {
				selectedModels = [''];
			}
		}

		await showCallOverlay.set(false);
		await showOverview.set(false);

		goto('/', { replaceState: true });

		autoScroll = true;

		await chatId.set('');
		await chatTitle.set('');

		history = {
			messages: {},
			currentId: null
		};

		chatFiles = [];
		params = {};

		if ($page.url.searchParams.get('youtube')) {
			uploadYoutubeTranscription(
				`https://www.youtube.com/watch?v=${$page.url.searchParams.get('youtube')}`
			);
		}
		if ($page.url.searchParams.get('web-search') === 'true') {
			webSearchEnabled = true;
		}

		if ($page.url.searchParams.get('image-generation') === 'true') {
			imageGenerationEnabled = true;
		}

		if ($page.url.searchParams.get('tools')) {
			selectedToolIds = $page.url.searchParams
				.get('tools')
				?.split(',')
				.map((id) => id.trim())
				.filter((id) => id);
		} else if ($page.url.searchParams.get('tool-ids')) {
			selectedToolIds = $page.url.searchParams
				.get('tool-ids')
				?.split(',')
				.map((id) => id.trim())
				.filter((id) => id);
		}

		if ($page.url.searchParams.get('call') === 'true') {
			// showCallOverlay.set(true);
			// showControls.set(true);
		}

		if ($page.url.searchParams.get('q')) {
			prompt = $page.url.searchParams.get('q') ?? '';

			if (prompt) {
				await tick();
				submitPrompt(prompt);
			}
		}

		selectedModels = selectedModels.map((modelId) =>
			$models.map((m) => m.id).includes(modelId) ? modelId : ''
		);

		const userSettings = await getUserSettings(localStorage.token);

		if (userSettings) {
			settings.set(userSettings.ui);
		} else {
			settings.set(JSON.parse(localStorage.getItem('settings') ?? '{}'));
		}

		const chatInput = document.getElementById('chat-input');
		setTimeout(() => chatInput?.focus(), 0);
	};

	const loadChat = async () => {
		chatId.set(chatIdProp);
		chat = await getChatById(localStorage.token, $chatId).catch(async (error) => {
			await goto('/');
			return null;
		});

		if (chat) {
			tags = await getTagsById(localStorage.token, $chatId).catch(async (error) => {
				return [];
			});
			console.log('chat:', chat);
			chat.chat.id = chat.id;
			const chatContent = chat.chat;

			inputFlags = chat.meta.flags ?? [];
			inputFeatures = chat.meta?.features ?? [];
			if (chatContent) {
				console.log('historyCurrentId:', history.currentId);
				selectedModels =
					(chatContent?.models ?? undefined) !== undefined
						? chatContent.models
						: [chatContent.models ?? ''];
				history =
					(chatContent?.history ?? undefined) !== undefined
						? chatContent.history
						: convertMessagesToHistory(chatContent.messages);
				console.log('historyCurrentId:', history.currentId);
				// 确保 currentId 指向最新的消	息
				const latestMessageId = findLatestMessageId(history);
				if (latestMessageId) {
					if (history.currentId && history.currentId !== latestMessageId) {
						console.log(
							`[loadChat] currentId ${history.currentId} is not the latest message, set to ${latestMessageId}`
						);
					}
					history.currentId = latestMessageId;
					console.log(`[loadChat] Set currentId to latest message: ${latestMessageId}`);
				}

				chatTitle.set(chatContent.title);

				const userSettings = await getUserSettings(localStorage.token);

				if (userSettings) {
					await settings.set(userSettings.ui);
				} else {
					await settings.set(JSON.parse(localStorage.getItem('settings') ?? '{}'));
				}

				params = chatContent?.params ?? {};
				chatFiles = chatContent?.files ?? [];

				autoScroll = true;

				await tick();
				const currMessageId = chatContent.history.currentId;

				if (currMessageId) {
					const currMessage = chatContent.history.messages[currMessageId];
					if (currMessage.content != '') {
						history.messages[currMessageId].done = true;
					} else {
						continueUnFinishedChat(currMessage);
					}
				}

				return true;
			} else {
				return null;
			}
		}
	};

	const scrollToBottom = async () => {
		await tick();
		if (messagesContainerElement) {
			messagesContainerElement.scrollTop = messagesContainerElement.scrollHeight;
		}
	};

	const chatActionHandler = async (chatId, actionId, modelId, responseMessageId, event = null) => {
		const messages = createMessagesList(history, responseMessageId);

		if (actionId === 'tgi_log') {
			console.log(messages, responseMessageId);
			const url = `https://openai-adapter.chatglm.site/logs/${responseMessageId}`;
			window.open(url, '_blank');
			return null;
		}

		const res = await chatAction(localStorage.token, actionId, {
			model: modelId,
			messages: messages.map((m) => ({
				id: m.id,
				role: m.role,
				content: m.content,
				info: m.info ? m.info : undefined,
				timestamp: m.timestamp,
				...(m.sources ? { sources: m.sources } : {})
			})),
			...(event ? { event: event } : {}),
			model_item: $models.find((m) => m.id === modelId),
			chat_id: chatId,
			session_id: $socket?.id,
			id: responseMessageId
		}).catch((error) => {
			toast.error(`${error}`);
			messages.at(-1).error = { content: error };
			return null;
		});

		if (res !== null && res.messages) {
			// Update chat history with the new messages
			for (const message of res.messages) {
				history.messages[message.id] = {
					...history.messages[message.id],
					...(history.messages[message.id].content !== message.content
						? { originalContent: history.messages[message.id].content }
						: {}),
					...message
				};
			}
		}

		if ($chatId == chatId) {
			if (!$temporaryChatEnabled) {
				chat = await updateChatById(localStorage.token, chatId, {
					models: selectedModels,
					messages: messages,
					history: history,
					params: params,
					files: chatFiles
				});

				if (['user', 'admin'].includes($user?.role)) {
					currentChatPage.set(1);
					await chats.set(await getChatList(localStorage.token, $currentChatPage));
				}
			}
		}
	};

	const getChatEventEmitter = async (modelId: string, chatId: string = '') => {
		return setInterval(() => {
			$socket?.emit('usage', {
				action: 'chat',
				model: modelId,
				chat_id: chatId
			});
		}, 1000);
	};

	const createMessagePair = async (userPrompt) => {
		prompt = '';
		if (selectedModels.length === 0) {
			toast.error($i18n.t('Model not selected'));
		} else {
			const modelId = selectedModels[0];
			const model = $models.filter((m) => m.id === modelId).at(0);

			const messages = createMessagesList(history, history.currentId);
			const parentMessage = messages.length !== 0 ? messages.at(-1) : null;

			const userMessageId = uuidv4();
			const responseMessageId = uuidv4();

			const userMessage = {
				id: userMessageId,
				parentId: parentMessage ? parentMessage.id : null,
				childrenIds: [responseMessageId],
				role: 'user',
				content: userPrompt ? userPrompt : `[PROMPT] ${userMessageId}`,
				timestamp: Math.floor(Date.now() / 1000)
			};

			const responseMessage = {
				id: responseMessageId,
				parentId: userMessageId,
				childrenIds: [],
				role: 'assistant',
				content: `[RESPONSE] ${responseMessageId}`,
				done: true,

				model: modelId,
				modelName: model.name ?? model.id,
				modelIdx: 0,
				timestamp: Math.floor(Date.now() / 1000)
			};

			if (parentMessage) {
				parentMessage.childrenIds.push(userMessageId);
				history.messages[parentMessage.id] = parentMessage;
			}
			history.messages[userMessageId] = userMessage;
			history.messages[responseMessageId] = responseMessage;

			history.currentId = responseMessageId;

			await tick();

			if (autoScroll) {
				scrollToBottom();
			}

			if (messages.length === 0) {
				await initChatHandler(history);
			} else {
				await saveChatHandler($chatId, history);
			}
		}
	};

	const addMessages = async ({ modelId, parentId, messages }) => {
		const model = $models.filter((m) => m.id === modelId).at(0);

		let parentMessage = history.messages[parentId];
		let currentParentId = parentMessage ? parentMessage.id : null;
		for (const message of messages) {
			let messageId = uuidv4();

			if (message.role === 'user') {
				const userMessage = {
					id: messageId,
					parentId: currentParentId,
					childrenIds: [],
					timestamp: Math.floor(Date.now() / 1000),
					...message
				};

				if (parentMessage) {
					parentMessage.childrenIds.push(messageId);
					history.messages[parentMessage.id] = parentMessage;
				}

				history.messages[messageId] = userMessage;
				parentMessage = userMessage;
				currentParentId = messageId;
			} else {
				const responseMessage = {
					id: messageId,
					parentId: currentParentId,
					childrenIds: [],
					done: true,
					model: model.id,
					modelName: model.name ?? model.id,
					modelIdx: 0,
					timestamp: Math.floor(Date.now() / 1000),
					...message
				};

				if (parentMessage) {
					parentMessage.childrenIds.push(messageId);
					history.messages[parentMessage.id] = parentMessage;
				}

				history.messages[messageId] = responseMessage;
				parentMessage = responseMessage;
				currentParentId = messageId;
			}
		}

		history.currentId = currentParentId;
		await tick();

		if (autoScroll) {
			scrollToBottom();
		}

		if (messages.length === 0) {
			await initChatHandler(history);
		} else {
			await saveChatHandler($chatId, history);
		}
	};

	const chatCompletionEventHandler = async (data, message) => {
		// fix 兜底
		if (!message) {
			return;
		}

		// 逻辑： 当有 content，无脑替换，当有 delta_content，无脑追加。
		const {
			id,
			done,
			content,
			delta_content,
			edit_content,
			sources,
			selected_model_id,
			error,
			usage,
			files,
			phase = 'other',
			edit_index = 0
		} = data;
		if (error) {
			await handleOpenAIError(error, message);
		}

		if (sources) {
			message.sources = sources;
		}

		if (files) {
			message.files = files;
		}

		if (delta_content || content || edit_content) {
			chatPhase = phase;
			if (edit_content) {
				// REALTIME_CHAT_SAVE is disabled
				logger.debug('计算更新:', edit_index, edit_content);
				message.content = message.content.substring(0, edit_index) + edit_content;
			} else if (content) {
				logger.debug('全量更新:', content);
				message.content = content;
			} else {
				logger.debug('增量更新', delta_content);
				message.content = message.content + delta_content;
			}
			message.phase = phase;

			// 📳震动
			if (navigator.vibrate && ($settings?.hapticFeedback ?? false)) {
				navigator.vibrate(5);
			}

			// 这块 TTS 的逻辑，其实会有问题，但反正我们也不用🤡
			// Emit chat event for TTS
			// const messageContentParts = getMessageContentParts(
			// 	message.content,
			// 	$config?.audio?.tts?.split_on ?? 'punctuation'
			// );
			// messageContentParts.pop();

			// dispatch only last sentence and make sure it hasn't been dispatched before
			// if (
			// 	messageContentParts.length > 0 &&
			// 	messageContentParts[messageContentParts.length - 1] !== message.lastSentence
			// ) {
			// 	message.lastSentence = messageContentParts[messageContentParts.length - 1];
			// 	eventTarget.dispatchEvent(
			// 		new CustomEvent('chat', {
			// 			detail: {
			// 				id: message.id,
			// 				content: messageContentParts[messageContentParts.length - 1]
			// 			}
			// 		})
			// 	);
			// }
		}

		if (selected_model_id) {
			message.selectedModelId = selected_model_id;
			message.arena = true;
		}

		if (usage) {
			message.usage = usage;
		}

		history.messages[message.id] = message;

		if (done) {
			message.done = true;

			if ($settings.responseAutoCopy) {
				copyToClipboard(message.content);
			}

			// if ($settings.responseAutoPlayback && !$showCallOverlay) {
			// 	await tick();
			// 	document.getElementById(`speak-button-${message.id}`)?.click();
			// }

			// Emit chat event for TTS
			let lastMessageContentPart =
				getMessageContentParts(message.content, $config?.audio?.tts?.split_on ?? 'punctuation')?.at(
					-1
				) ?? '';
			if (lastMessageContentPart) {
				eventTarget.dispatchEvent(
					new CustomEvent('chat', {
						detail: { id: message.id, content: lastMessageContentPart }
					})
				);
			}
			eventTarget.dispatchEvent(
				new CustomEvent('chat:finish', {
					detail: {
						id: message.id,
						content: message.content
					}
				})
			);

			history.messages[message.id] = message;
			// await chatCompletedHandler(
			// 	chatId,
			// 	message.model,
			// 	message.id,
			// 	createMessagesList(history, message.id)
			// );

			chatReplying.set(false);
		}

		// console.log(data);
		if (autoScroll) {
			scrollToBottom();
		}
	};

	//////////////////////////
	// Chat functions
	//////////////////////////

	const submitPrompt = async (userPrompt, { _raw = false } = {}, vibeInfo = null) => {
		// console.log('submitPrompt', userPrompt, $chatId, vibeInfo);
		// vibeInfo = {
		// 	vibeMode: 'artifacts',
		// 	vibeReference: {
		// 		line: 1,
		// 		column: 1,
		// 		code: 'test',
		// 		filename: 'test.html',
		// 		pptIndex: 1
		// 	}
		// };

		const messages = createMessagesList(history, history.currentId);
		const _selectedModels = selectedModels.map((modelId) =>
			$models.map((m) => m.id).includes(modelId) ? modelId : ''
		);
		if (JSON.stringify(selectedModels) !== JSON.stringify(_selectedModels)) {
			selectedModels = _selectedModels;
		}

		if (userPrompt.trim() === '') {
			toast.error($i18n.t('Please enter a prompt'));
			return;
		}
		if (selectedModels.includes('')) {
			toast.error($i18n.t('Model not selected'));
			return;
		}

		if (messages.length != 0 && messages.at(-1).done != true) {
			// Response not done
			return;
		}
		if (messages.length != 0 && messages.at(-1).error) {
			// Error in response
			toast.error($i18n.t(`Oops! There was an error in the previous response.`));
			return;
		}
		if (
			files.length > 0 &&
			files.filter((file) => file.type !== 'image' && file.status === 'uploading').length > 0
		) {
			toast.error(
				$i18n.t(`Oops! There are files still uploading. Please wait for the upload to complete.`)
			);
			return;
		}
		if (
			($config?.file?.max_count ?? null) !== null &&
			files.length + chatFiles.length > $config?.file?.max_count
		) {
			toast.error(
				$i18n.t(`You can only chat with a maximum of {{maxCount}} file(s) at a time.`, {
					maxCount: $config?.file?.max_count
				})
			);
			return;
		}

		prompt = '';

		// Reset chat input textarea
		if (!($settings?.richTextInput ?? true)) {
			const chatInputElement = document.getElementById('chat-input');

			if (chatInputElement) {
				await tick();
				chatInputElement.style.height = '';
			}
		}

		const _files = JSON.parse(JSON.stringify(files));
		chatFiles.push(..._files.filter((item) => ['doc', 'file', 'collection'].includes(item.type)));
		chatFiles = chatFiles.filter(
			// Remove duplicates
			(item, index, array) =>
				array.findIndex((i) => JSON.stringify(i) === JSON.stringify(item)) === index
		);

		files = [];
		prompt = '';

		// Create user message
		let userMessageId = uuidv4();
		let userMessage = {
			id: userMessageId,
			parentId: messages.length !== 0 ? messages.at(-1).id : null,
			childrenIds: [],
			role: 'user',
			content: userPrompt,
			files: _files.length > 0 ? _files : undefined,
			timestamp: Math.floor(Date.now() / 1000), // Unix epoch
			models: selectedModels
		};
		if (vibeInfo) {
			userMessage.vibeInfo = vibeInfo;
		}

		// Add message to history and Set currentId to messageId
		history.messages[userMessageId] = userMessage;
		history.currentId = userMessageId;

		// Append messageId to childrenIds of parent message
		if (messages.length !== 0) {
			history.messages[messages.at(-1).id].childrenIds.push(userMessageId);
		}

		// focus on chat input
		const chatInput = document.getElementById('chat-input');
		!$mobile && chatInput?.focus();

		saveSessionSelectedModels();

		await sendPrompt(history, userPrompt, userMessageId, { newChat: true, vibeInfo });
	};

	const sendPrompt = async (
		_history,
		prompt: string,
		parentId: string,
		{ modelId = null, modelIdx = null, newChat = false, vibeInfo = null } = {}
	) => {
		if (autoScroll) {
			scrollToBottom();
		}

		$eventBus.emit(EventBus.SUBMIT_PROMPT, prompt);

		let _chatId = JSON.parse(JSON.stringify($chatId));
		_history = JSON.parse(JSON.stringify(_history));

		const responseMessageIds: Record<PropertyKey, string> = {};
		// If modelId is provided, use it, else use selected model
		let selectedModelIds = modelId
			? [modelId]
			: atSelectedModel !== undefined
				? [atSelectedModel.id]
				: selectedModels;

		// Create response messages for each selected model
		for (const [_modelIdx, modelId] of selectedModelIds.entries()) {
			const model = $models.filter((m) => m.id === modelId).at(0);

			if (model) {
				let responseMessageId = uuidv4();
				let responseMessage = {
					parentId: parentId,
					id: responseMessageId,
					childrenIds: [],
					role: 'assistant',
					content: '',
					model: model.id,
					modelName: model.name ?? model.id,
					modelIdx: modelIdx ? modelIdx : _modelIdx,
					userContext: null,
					timestamp: Math.floor(Date.now() / 1000) // Unix epoch
				};

				// Add message to history and Set currentId to messageId
				history.messages[responseMessageId] = responseMessage;
				history.currentId = responseMessageId;

				// Append messageId to childrenIds of parent message
				if (parentId !== null && history.messages[parentId]) {
					// Add null check before accessing childrenIds
					history.messages[parentId].childrenIds = [
						...history.messages[parentId].childrenIds,
						responseMessageId
					];
				}

				responseMessageIds[`${modelId}-${modelIdx ? modelIdx : _modelIdx}`] = responseMessageId;
			}
		}
		history = history;

		// Create new chat if newChat is true and first user message
		if (newChat && _history.messages[_history.currentId].parentId === null) {
			_chatId = await initChatHandler(_history);
		}

		await tick();

		_history = JSON.parse(JSON.stringify(history));
		// Save chat after all messages have been created
		saveChatHandler(_chatId, _history).catch((e) => {
			if (e.message === 'Session expired') {
				chatAbortController?.abort();
				chatAbortController = null;
			}
		});

		await Promise.all(
			selectedModelIds.map(async (modelId, _modelIdx) => {
				// console.log('modelId', modelId);
				const model = $models.filter((m) => m.id === modelId).at(0);

				if (model) {
					const messages = createMessagesList(_history, parentId);
					// If there are image files, check if model is vision capable
					const hasImages = messages.some((message) =>
						message.files?.some((file) => file.type === 'image')
					);

					if (hasImages && !(model.info?.meta?.capabilities?.vision ?? true)) {
						toast.error(
							$i18n.t('Model {{modelName}} is not vision capable', {
								modelName: model.name ?? model.id
							})
						);
					}

					let responseMessageId =
						responseMessageIds[`${modelId}-${modelIdx ? modelIdx : _modelIdx}`];
					let responseMessage = _history.messages[responseMessageId];

					let userContext = null;
					if ($settings?.memory ?? false) {
						if (userContext === null) {
							const res = await queryMemory(localStorage.token, prompt).catch((error) => {
								toast.error(`${error}`);
								return null;
							});
							if (res) {
								if (res.documents[0].length > 0) {
									userContext = res.documents[0].reduce((acc, doc, index) => {
										const createdAtTimestamp = res.metadatas[0][index].created_at;
										const createdAtDate = new Date(createdAtTimestamp * 1000)
											.toISOString()
											.split('T')[0];
										return `${acc}${index + 1}. [${createdAtDate}]. ${doc}\n`;
									}, '');
								}

								console.log(userContext);
							}
						}
					}
					responseMessage.userContext = userContext;

					const chatEventEmitter = await getChatEventEmitter(model.id, _chatId);

					scrollToBottom();
					await sendPromptSocket(_history, model, responseMessageId, _chatId);

					if (chatEventEmitter) clearInterval(chatEventEmitter);
				} else {
					toast.error($i18n.t(`Model {{modelId}} not found`, { modelId }));
				}
			})
		);

		if (['user', 'admin'].includes($user?.role)) {
			currentChatPage.set(1);
			getChatList(localStorage.token, $currentChatPage).then((res) => {
				chats.set(res);
			});
		}
	};

	const sendPromptSocket = async (_history, model, responseMessageId, _chatId) => {
		const responseMessage = _history.messages[responseMessageId];
		const userMessage = _history.messages[responseMessage.parentId];
		const isVisionModel = model?.info?.meta?.capabilities?.vision ?? false;
		let files = JSON.parse(JSON.stringify(chatFiles || []));
		files.push(
			...(userMessage?.files ?? []).filter((item) =>
				['doc', 'file', 'collection'].includes(item.type)
			),
			...(responseMessage?.files ?? []).filter((item) => ['web_search_results'].includes(item.type))
		);
		// Remove duplicates
		files = files.filter(
			(item, index, array) =>
				array.findIndex((i) => JSON.stringify(i) === JSON.stringify(item)) === index
		);

		scrollToBottom();
		eventTarget.dispatchEvent(
			new CustomEvent('chat:start', {
				detail: {
					id: responseMessageId
				}
			})
		);
		await tick();

		const stream =
			model?.info?.params?.stream_response ??
			$settings?.params?.stream_response ??
			params?.stream_response ??
			true;

		const systemItem =
			params?.system || $settings.system || (responseMessage?.userContext ?? null)
				? {
						role: 'system',
						content: `${promptTemplate(
							params?.system ?? $settings?.system ?? '',
							$user?.name,
							$settings?.userLocation
								? await getAndUpdateUserLocation(localStorage.token).catch((err) => {
										console.error(err);
										return undefined;
									})
								: undefined
						)}${
							(responseMessage?.userContext ?? null)
								? `\n\nUser Context:\n${responseMessage?.userContext ?? ''}`
								: ''
						}`
					}
				: undefined;
		const otherItems = createMessagesList(_history, responseMessageId)
			.map((message) => {
				return {
					...message,
					content: parseContent(message.content)
				};
			})
			.filter((message) => message);
		// 展开 content
		const createToolCallMessage = (call) => {
			return { role: 'assistant', content: null, tool_calls: [call] };
		};
		const withThink = model?.info?.meta?.capabilities?.returnThink ?? false;
		const withFC = model?.info?.meta?.capabilities?.returnFc ?? false;

		const flatItems = [];
		for (const item of otherItems) {
			for (const contentItem of item.content) {
				const role = item.role;
				const content = contentItem.content;
				if (contentItem.type == 'mcp') {
					if (!withFC) {
						continue;
					}
					const call = {
						id: content.id,
						type: 'function',
						function: {
							name: content.name,
							arguments: decode(content.arguments).trim()
						}
					};
					flatItems.push(createToolCallMessage(call));
					flatItems.push({
						role: 'tool',
						tool_call_id: content.id,
						name: content.name,
						content: decode(content.result).trim(),
						display_content: decode(content?.display_result).trim(),
						files: item?.files ?? []
					});
				} else {
					if (contentItem.type == 'reasoning' && !withThink) {
						continue;
					}
					const o = {
						role: role,
						content: content,
						files: item?.files ?? []
					};
					if (item.vibeInfo) {
						o.vibeInfo = item.vibeInfo;
					}
					flatItems.push(o);
				}
			}
		}
		let messages = [systemItem, ...flatItems].filter((message) => message);
		// 如果模型是 vision 模型，则需要将files中的image文件拼接成一个image_url加到message的content中
		messages = messages
			.map((message, idx, arr) => ({
				role: message.role,
				...((message.files?.filter((file) => file.isImage === true).length > 0 ?? false) &&
				message.role === 'user' &&
				isVisionModel
					? {
							content: [
								{
									type: 'text',
									text: message?.merged?.content ?? message.content
								},
								...message.files
									.filter((file) => file.isImage === true)
									.map((file) => ({
										type: 'image_url',
										image_url: {
											url: file.id + '_' + file.name
										}
									}))
							],
							vibeInfo: message?.vibeInfo
						}
					: {
							content: message?.merged?.content ?? message.content,
							tool_calls: message?.tool_calls,
							vibeInfo: message?.vibeInfo,
							// 为tool类型的消息保留必要字段
							...(message.role === 'tool'
								? {
										tool_call_id: message.tool_call_id,
										name: message.name
									}
								: {})
						})
			}))
			.filter(
				(message) => message?.role === 'user' || message?.content?.trim?.() || message?.tool_calls
			);

		chatReplying.set(true);
		chatAbortController = new AbortController();
		const res = await generateOpenAIChatCompletion(
			localStorage.token,
			{
				stream: stream,
				model: model.id,
				messages: messages,
				params: {
					...$settings?.params,
					...params,

					format: $settings.requestFormat ?? undefined,
					keep_alive: $settings.keepAlive ?? undefined,
					stop:
						(params?.stop ?? $settings?.params?.stop ?? undefined)
							? (params?.stop.split(',').map((token) => token.trim()) ?? $settings.params.stop).map(
									(str) => decodeURIComponent(JSON.parse('"' + str.replace(/\"/g, '\\"') + '"'))
								)
							: undefined
				},

				files: (files?.length ?? 0) > 0 ? files : undefined,
				tool_ids: selectedToolIds.length > 0 ? selectedToolIds : undefined,
				tool_servers: $toolServers,

				mcp_servers: selectedMCPServers.length > 0 ? selectedMCPServers : undefined,

				features: {
					image_generation:
						$config?.features?.enable_image_generation &&
						($user?.role === 'admin' || $user?.permissions?.features?.image_generation)
							? imageGenerationEnabled
							: false,
					code_interpreter:
						$config?.features?.enable_code_interpreter &&
						($user?.role === 'admin' || $user?.permissions?.features?.code_interpreter)
							? codeInterpreterEnabled
							: false,
					web_search: false,
					auto_web_search: webSearchEnabled,
					preview_mode: $autoArtifacts,
					flags: inputFlags,
					features: inputFeatures
				},
				variables: {
					...getPromptVariables(
						$user?.name,
						$settings?.userLocation
							? await getAndUpdateUserLocation(localStorage.token).catch((err) => {
									console.error(err);
									return undefined;
								})
							: undefined
					)
				},
				model_item: $models.find((m) => m.id === model.id),

				session_id: $socket?.id,
				chat_id: $chatId,
				id: responseMessageId,

				...(!$temporaryChatEnabled &&
				(messages.length == 1 ||
					(messages.length == 2 &&
						messages.at(0)?.role === 'system' &&
						messages.at(1)?.role === 'user')) &&
				(selectedModels[0] === model.id || atSelectedModel !== undefined)
					? {
							background_tasks: {
								title_generation: $settings?.title?.auto ?? true,
								tags_generation: $settings?.autoTags ?? true
							}
						}
					: {}),

				...(stream && (model.info?.meta?.capabilities?.usage ?? false)
					? {
							stream_options: {
								include_usage: true
							}
						}
					: {})
			},
			`${WEBUI_BASE_URL}/api`,
			chatAbortController
		).catch(async (error) => {
			chatReplying.set(false);
			console.error(error, 'sendPrompt Catch Error');
			const detail = `${error?.detail ?? error?.message ?? error}`;
			trackNetworkError(detail, `sendPromptCatchError`);

			if (error.startsWith('AbortError')) {
				console.log('Aborted');
				chatAbortController?.abort();
				chatAbortController = null;
				return null;
			}

			toast.error(`${error}`);

			responseMessage.error = {
				content: error
			};
			responseMessage.done = true;

			history.messages[responseMessageId] = responseMessage;
			history.currentId = responseMessageId;
			return null;
		});

		if (res) {
			if (res.error) {
				await handleOpenAIError(res.error, responseMessage);
			} else {
				// taskId = res.task_id;

				const reader = res.body.getReader();
				const decoder = new TextDecoder('utf-8');
				let buffer = '';

				try {
					while (true) {
						const { done, value } = await reader.read();
						if (done) break;
						buffer += decoder.decode(value, { stream: true });

						let lines = buffer.split('\n\n');
						buffer = lines.pop()!; // 最后一段可能是不完整的，留到下次

						for (const line of lines) {
							if (line.startsWith('data:')) {
								const data = line.slice(5).trim();
								const event = JSON.parse(data);
								await chatEventHandler(event, (e) => {}, responseMessageId);
							}
						}
					}
				} catch (error) {
					const detail = `${error?.detail ?? error?.message ?? error}`;
					trackNetworkError(detail, `sendPromptReadingError`);
					console.log('Aborted on reading stream');
					// chatAbortController?.abort();
					// chatAbortController = null;
				}
			}
		}

		await tick();
		scrollToBottom();
	};

	const handleOpenAIError = async (error, responseMessage) => {
		let errorMessage = '';
		let innerError;

		if (error) {
			innerError = error;
		}

		if (error?.code == 429) {
			showAbortChatModal.set(true);
			console.log('429, show abort chat modal');
			return;
		}

		console.error(innerError);
		if ('detail' in innerError) {
			// FastAPI error
			toast.error(innerError.detail);
			errorMessage = innerError.detail;
		} else if ('error' in innerError) {
			// OpenAI error
			if ('message' in innerError.error) {
				toast.error(innerError.error.message);
				errorMessage = innerError.error.message;
			} else {
				toast.error(innerError.error);
				errorMessage = innerError.error;
			}
		} else if ('message' in innerError) {
			// OpenAI error
			toast.error(innerError.message);
			errorMessage = innerError.message;
		}

		responseMessage.error = {
			content: $i18n.t(`Uh-oh! There was an issue with the response.`) + '\n' + errorMessage
		};
		responseMessage.done = true;

		if (responseMessage.statusHistory) {
			responseMessage.statusHistory = responseMessage.statusHistory.filter(
				(status) => status.action !== 'knowledge_search'
			);
		}

		history.messages[responseMessage.id] = responseMessage;
	};

	const stopResponse = async () => {
		if (!history.currentId) {
			console.log('没有当前消息');
			return;
		}
		chatAbortController?.abort();
		chatAbortController = null;

		const res = await stopTask(localStorage.token, history.currentId).catch((error) => {
			toast.error(`${error}`);
			return null;
		});

		console.log('stopTaskResponse', res);

		if (history.messages[history.currentId]) {
			if (chatPhase === 'thinking') {
				history.messages[history.currentId].content =
					history.messages[history.currentId].content + '</details>\n';
				history.messages[history.currentId].phase = 'done';
			}
			history.messages[history.currentId].done = true;
		}

		chatReplying.set(false);

		if (autoScroll) {
			scrollToBottom();
		}
	};

	const submitMessage = async (parentId, prompt) => {
		let userPrompt = prompt;
		let userMessageId = uuidv4();

		let userMessage = {
			id: userMessageId,
			parentId: parentId,
			childrenIds: [],
			role: 'user',
			content: userPrompt,
			models: selectedModels
		};

		if (parentId !== null) {
			history.messages[parentId].childrenIds = [
				...history.messages[parentId].childrenIds,
				userMessageId
			];
		}

		history.messages[userMessageId] = userMessage;
		history.currentId = userMessageId;

		await tick();

		if (autoScroll) {
			scrollToBottom();
		}

		await sendPrompt(history, userPrompt, userMessageId);
	};

	const regenerateResponse = async (message) => {
		console.log('regenerateResponse');

		if (history.currentId) {
			let userMessage = history.messages[message.parentId];
			let userPrompt = userMessage.content;

			if (autoScroll) {
				scrollToBottom();
			}

			if ((userMessage?.models ?? [...selectedModels]).length == 1) {
				// If user message has only one model selected, sendPrompt automatically selects it for regeneration
				await sendPrompt(history, userPrompt, userMessage.id);
			} else {
				// If there are multiple models selected, use the model of the response message for regeneration
				// e.g. many model chat
				await sendPrompt(history, userPrompt, userMessage.id, {
					modelId: message.model,
					modelIdx: message.modelIdx
				});
			}
		}
	};

	const continueResponse = async () => {
		console.log('continueResponse');
		const _chatId = JSON.parse(JSON.stringify($chatId));

		if (history.currentId && history.messages[history.currentId].done == true) {
			const responseMessage = history.messages[history.currentId];
			responseMessage.done = false;
			await tick();

			const model = $models
				.filter((m) => m.id === (responseMessage?.selectedModelId ?? responseMessage.model))
				.at(0);

			if (model) {
				await sendPromptSocket(history, model, responseMessage.id, _chatId);
			}
		}
	};

	const mergeResponses = async (messageId, responses, _chatId) => {
		console.log('mergeResponses', messageId, responses);
		const message = history.messages[messageId];
		const mergedResponse = {
			status: true,
			content: ''
		};
		message.merged = mergedResponse;
		history.messages[messageId] = message;

		try {
			const [res, controller] = await generateMoACompletion(
				localStorage.token,
				message.model,
				history.messages[message.parentId].content,
				responses
			);

			if (res && res.ok && res.body) {
				const textStream = await createOpenAITextStream(res.body, $settings.splitLargeChunks);
				for await (const update of textStream) {
					const { value, done, sources, error, usage } = update;
					if (error || done) {
						break;
					}

					if (mergedResponse.content == '' && value == '\n') {
						continue;
					} else {
						mergedResponse.content += value;
						history.messages[messageId] = message;
					}

					if (autoScroll) {
						scrollToBottom();
					}
				}

				await saveChatHandler(_chatId, history);
			} else {
				console.error(res);
			}
		} catch (e) {
			console.error(e);
		}
	};

	const initChatHandler = async (history) => {
		let _chatId = $chatId;

		if (!$temporaryChatEnabled) {
			chat = await createNewChat(localStorage.token, {
				id: _chatId,
				title: $i18n.t('New Chat'),
				models: selectedModels,
				system: $settings.system ?? undefined,
				params: params,
				history: history,
				messages: createMessagesList(history, history.currentId),
				tags: [],
				flags: inputFlags,
				features: inputFeatures,
				timestamp: Date.now()
			});
			if (chat?.err && chat?.err?.code == 429) {
				showAbortChatModal.set(true);
				console.log('429, show abort chat modal');
				return;
			}

			_chatId = chat.id;
			await chatId.set(_chatId);

			if (['user', 'admin'].includes($user?.role)) {
				getChatList(localStorage.token, $currentChatPage).then((res) => {
					chats.set(res);
					currentChatPage.set(1);
				});
			}

			window.history.replaceState(history.state, '', `/c/${_chatId}`);
		} else {
			_chatId = 'local';
			await chatId.set('local');
		}
		await tick();

		return _chatId;
	};

	const saveChatHandler = async (_chatId, newHistory) => {
		if ($chatId == _chatId) {
			if (!$temporaryChatEnabled) {
				chat = await updateChatById(localStorage.token, _chatId, {
					models: selectedModels,
					history: newHistory,
					messages: createMessagesList(newHistory, newHistory.currentId),
					params: params,
					files: chatFiles
				});

				history = { ...newHistory };

				if (['user', 'admin'].includes($user?.role)) {
					currentChatPage.set(1);
					await chats.set(await getChatList(localStorage.token, $currentChatPage));
				}
			}
		}
	};
</script>

<svelte:head>
	<title>
		{$chatTitle
			? `${$chatTitle.length > 30 ? `${$chatTitle.slice(0, 30)}...` : $chatTitle} | ${$WEBUI_NAME}`
			: `${$WEBUI_NAME}`}
	</title>
</svelte:head>

<audio id="audioElement" src="" style="display: none;" />

<EventConfirmDialog
	bind:show={showEventConfirmation}
	title={eventConfirmationTitle}
	message={eventConfirmationMessage}
	input={eventConfirmationInput}
	inputPlaceholder={eventConfirmationInputPlaceholder}
	inputValue={eventConfirmationInputValue}
	on:confirm={(e) => {
		if (e.detail) {
			eventCallback(e.detail);
		} else {
			eventCallback(true);
		}
	}}
	on:cancel={() => {
		eventCallback(false);
	}}
/>

<AbortChatModal
	bind:show={$showAbortChatModal}
	onConfirm={async () => {
		pendingChatId.set('');
		showAbortChatModal.set(false);
	}}
	onCancel={() => {
		pendingChatId.set('');
		showAbortChatModal.set(false);
	}}
/>

<div
	class="h-screen max-h-[100dvh] transition-width duration-200 ease-in-out {$showSidebar
		? '  md:max-w-[calc(100%-260px)]'
		: ' '} w-full max-w-full flex flex-col"
	id="chat-container"
>
	{#if chatIdProp === '' || (!loading && chatIdProp)}
		{#if $settings?.backgroundImageUrl ?? null}
			<div
				class="absolute {$showSidebar
					? 'md:max-w-[calc(100%-260px)] md:translate-x-[260px]'
					: ''} top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat"
				style="background-image: url({$settings.backgroundImageUrl})  "
			/>

			<div
				class="absolute top-0 left-0 w-full h-full bg-linear-to-t from-white to-white/85 dark:from-gray-900 dark:to-gray-900/90 z-0"
			/>
		{/if}

		<PaneGroup direction="horizontal" class="w-full h-full">
			<Pane defaultSize={50} class="h-full flex w-full relative">
				{#if !history.currentId && !$chatId && selectedModels.length <= 1 && ($banners.length > 0 || ($config?.license_metadata?.type ?? null) === 'trial' || (($config?.license_metadata?.seats ?? null) !== null && $config?.user_count > $config?.license_metadata?.seats))}
					<div class="absolute top-12 left-0 right-0 w-full z-30">
						<div class=" flex flex-col gap-1 w-full">
							{#if ($config?.license_metadata?.type ?? null) === 'trial'}
								<Banner
									banner={{
										type: 'info',
										title: 'Trial License',
										content: $i18n.t(
											'You are currently using a trial license. Please contact support to upgrade your license.'
										)
									}}
								/>
							{/if}

							{#if ($config?.license_metadata?.seats ?? null) !== null && $config?.user_count > $config?.license_metadata?.seats}
								<Banner
									banner={{
										type: 'error',
										title: 'License Error',
										content: $i18n.t(
											'Exceeded the number of seats in your license. Please contact support to increase the number of seats.'
										)
									}}
								/>
							{/if}

							{#each $banners.filter( (b) => (b.dismissible ? !JSON.parse(localStorage.getItem('dismissedBannerIds') ?? '[]').includes(b.id) : true) ) as banner}
								<Banner
									{banner}
									on:dismiss={(e) => {
										const bannerId = e.detail;

										localStorage.setItem(
											'dismissedBannerIds',
											JSON.stringify(
												[
													bannerId,
													...JSON.parse(localStorage.getItem('dismissedBannerIds') ?? '[]')
												].filter((id) => $banners.find((b) => b.id === id))
											)
										);
									}}
								/>
							{/each}
						</div>
					</div>
				{/if}

				<div class="flex flex-col flex-auto z-10 w-full @container">
					<Navbar
						bind:this={navbarElement}
						chat={{
							id: $chatId,
							chat: {
								title: $chatTitle,
								models: selectedModels,
								system: $settings.system ?? undefined,
								params: params,
								history: history,
								timestamp: Date.now()
							}
						}}
						disableModelSelector={$chatId !== ''}
						title={$chatTitle}
						bind:selectedModels
						shareEnabled={!!history.currentId}
						{initNewChat}
					/>
					{#if showMessagePage}
						<div
							class=" pb-2.5 flex flex-col justify-between w-full flex-auto overflow-auto h-0 max-w-full z-10 scrollbar-hidden"
							id="messages-container"
							bind:this={messagesContainerElement}
							on:scroll={(e) => {
								autoScroll =
									messagesContainerElement.scrollHeight - messagesContainerElement.scrollTop <=
									messagesContainerElement.clientHeight + 5;
							}}
						>
							<div class=" h-full w-full flex flex-col">
								<Messages
									chatId={$chatId}
									bind:history
									bind:autoScroll
									bind:prompt
									{selectedModels}
									{atSelectedModel}
									{sendPrompt}
									{showMessage}
									{submitMessage}
									{continueResponse}
									{regenerateResponse}
									{mergeResponses}
									{chatActionHandler}
									{addMessages}
									bottomPadding={files.length > 0}
									flags={inputFlags}
								/>
							</div>
						</div>

						<div class=" pb-3 px-3">
							<MessageInput
								{history}
								{selectedModels}
								bind:flags={inputFlags}
								bind:features={inputFeatures}
								bind:files
								bind:prompt
								bind:autoScroll
								bind:selectedToolIds
								bind:selectedMCPServers
								bind:disabledMCPServerMap
								bind:imageGenerationEnabled
								bind:codeInterpreterEnabled
								bind:webSearchEnabled
								bind:atSelectedModel
								showMessages={showMessagePage}
								toolServers={$toolServers}
								transparentBackground={$settings?.backgroundImageUrl ?? false}
								{stopResponse}
								{createMessagePair}
								onChange={(input) => {
									if (input.prompt) {
										localStorage.setItem(`chat-input-${$chatId}`, JSON.stringify(input));
									} else {
										localStorage.removeItem(`chat-input-${$chatId}`);
									}
								}}
								on:upload={async (e) => {
									const { type, data } = e.detail;

									if (type === 'web') {
										await uploadWeb(data);
									} else if (type === 'youtube') {
										await uploadYoutubeTranscription(data);
									} else if (type === 'google-drive') {
										await uploadGoogleDriveFile(data);
									}
								}}
								on:submit={async (e) => {
									if (e.detail || files.length > 0) {
										await tick();
										trackButtonClick('chatbox', 'chatbox_click', `submit_${selectedModels[0]}`);
										submitPrompt(
											($settings?.richTextInput ?? true)
												? e.detail.replaceAll('\n\n', '\n')
												: e.detail
										);
									}
								}}
							/>

							<div
								class="absolute bottom-1 text-xs text-gray-500 text-center line-clamp-1 right-0 left-0"
							>
								<!-- {$i18n.t('LLMs can make mistakes. Verify important information.')} -->
							</div>
						</div>
					{:else}
						<div class="overflow-auto w-full h-full flex flex-col">
							<Placeholder
								{history}
								{selectedModels}
								bind:inputFlags
								bind:inputFeatures
								bind:files
								bind:prompt
								bind:autoScroll
								bind:selectedToolIds
								bind:selectedMCPServers
								bind:disabledMCPServerMap
								bind:imageGenerationEnabled
								bind:codeInterpreterEnabled
								bind:webSearchEnabled
								bind:atSelectedModel
								transparentBackground={$settings?.backgroundImageUrl ?? false}
								toolServers={$toolServers}
								{stopResponse}
								{createMessagePair}
								on:upload={async (e) => {
									const { type, data } = e.detail;

									if (type === 'web') {
										await uploadWeb(data);
									} else if (type === 'youtube') {
										await uploadYoutubeTranscription(data);
									}
								}}
								on:submit={async (e) => {
									if (e.detail || files.length > 0) {
										await tick();
										trackButtonClick('chatbox', 'chatbox_click', `submit_${selectedModels[0]}`);
										submitPrompt(
											($settings?.richTextInput ?? true)
												? e.detail.replaceAll('\n\n', '\n')
												: e.detail
										);
									}
								}}
							/>
						</div>
					{/if}
				</div>
			</Pane>

			<ChatControls
				bind:this={controlPaneComponent}
				bind:history
				bind:chatFiles
				bind:params
				bind:files
				bind:pane={controlPane}
				chatId={$chatId}
				modelId={selectedModelIds?.at(0) ?? null}
				models={selectedModelIds.reduce((a, e, i, arr) => {
					const model = $models.find((m) => m.id === e);
					if (model) {
						return [...a, model];
					}
					return a;
				}, [])}
				{submitPrompt}
				{stopResponse}
				{showMessage}
				{eventTarget}
				{saveChatHandler}
			/>
		</PaneGroup>
		{#if !showMessagePage}
			<div
				class="flex items-center justify-center gap-1 flex-wrap font-normal text-xs text-gray-400 dark:text-white text-center"
			>
				{#if $user?.role === 'guest'}
					<div class="mb-3">
						{$i18n.t('By using Z Chat, you agree to our ')}
						<a
							href="https://chat.z.ai/legal-agreement/terms-of-service"
							class="text-gray-600 underline"
							target="_blank"
							rel="noopener noreferrer">{$i18n.t('Terms of Service')}</a
						>
						<span>{$i18n.t(' and ')}</span>
						<a
							href="https://chat.z.ai/legal-agreement/privacy-policy"
							class="text-gray-600 underline"
							target="_blank"
							rel="noopener noreferrer">{$i18n.t('Privacy Policy')}</a
						>
						{#if !$mobile && $user?.role === 'guest'}
							<div class=" inline-block h-4 align-bottom border-r-2 border-gray-400 mx-1"></div>
						{/if}
					</div>
				{/if}
				{#if !$mobile}
					<div class=" inline-flex items-center mb-3 gap-2">
						<div class=" inline-flex gap-2 items-center">
							<a href="mailto:<EMAIL>">
								<MailIcon
									className=" size-5 text-black dark:text-white"
									strokeWidth="2"
									theme={$userTheme}
								/>
							</a>
							<a href="https://github.com/THUDM" target="_blank" rel="noopener noreferrer">
								<GithubIcon className=" size-4" theme={$userTheme} />
							</a>
							<a href="https://huggingface.co/THUDM" target="_blank" rel="noopener noreferrer">
								<HuggingFace className=" size-5" />
							</a>
							<a href="https://x.com/Zai_org" target="_blank" rel="noopener noreferrer">
								<XIcon theme={$userTheme} className=" size-4 " />
							</a>
							<a
								href="https://discord.com/channels/1346756824233148527/1359832169333395496"
								target="_blank"
								rel="noopener noreferrer"
							>
								<Discord className="size-5" theme={$userTheme} fill="black" />
							</a>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	{:else if loading}
		<div class=" flex items-center justify-center h-full w-full">
			<div class="m-auto">
				<Spinner />
			</div>
		</div>
	{/if}
</div>

<ModalPptNotifySignin />
