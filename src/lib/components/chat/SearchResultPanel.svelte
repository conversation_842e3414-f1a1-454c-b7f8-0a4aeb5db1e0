<script lang="ts">
	import Search from '../icons/Search.svelte';
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import XMark from '../icons/XMark.svelte';
	import { showControls } from '$lib/stores';
	import SearchResult from './SearchResult.svelte';
	import { mcpData } from '$lib/stores/mcp';
	import Divider from '../common/Divider.svelte';

	const i18n: Writable<i18nType> = getContext('i18n');
</script>

<div
	class=" searchResultPanelContainer w-full h-full relative flex flex-col bg-white dark:bg-[#26282A]"
>
	<div
		class=" searchResultHeader flex justify-between items-center-safe px-4 py-3.5 border-b-1 border-black/10"
	>
		<div
			class="flex-1 shrink-1 pointer-events-none flex gap-2 items-center justify-start whitespace-nowrap font-semibold"
		>
			<Search className="size-5 inline" />
			<span>{$i18n.t('Search')}</span>
		</div>
		<div class="flex-1 shrink-1 pointer-events-none flex items-center justify-end gap-2">
			<Divider className="shrink-0 h-6" orientation="vertical" />
			<button
				class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
				on:click={() => {
					showControls.set('');
				}}
			>
				<XMark className="size-5 text-gray-900 dark:text-white" />
			</button>
		</div>
	</div>

	<div class=" searchResultContent flex-1 relative overflow-x-hidden overflow-y-scroll p-4">
		<SearchResult
			citations={$mcpData?.browser?.search_result}
			originalResult={$mcpData?.metadata.result}
		/>
	</div>
</div>
