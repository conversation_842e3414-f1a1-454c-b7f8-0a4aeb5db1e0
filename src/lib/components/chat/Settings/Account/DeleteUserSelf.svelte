<script lang="ts">
	import { getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { user } from '$lib/stores';
	import { deleteUserSelf } from '$lib/apis/users';

	const i18n: any = getContext('i18n');

	let show = false;
	let confirmDelete = false;

	const deleteUserHandler = async () => {
		if (confirmDelete) {
			try {
				const result = await deleteUserSelf(localStorage.token).catch((error) => {
					toast.error($i18n.t(error || 'Failed to delete account.'));
					return null;
				});

				if (result) {
					toast.success($i18n.t('Your account has been deleted.'));
					localStorage.removeItem('token');
					user.set(undefined);
					goto('/');
				}
			} catch (error) {
				toast.error($i18n.t('An error occurred. Please try again.'));
				console.error(error);
			}
			confirmDelete = false;
		}
	};
</script>

<div class="flex flex-col text-sm">
	<div class="flex justify-between items-center text-sm">
		<div class="font-medium">{$i18n.t('Delete Account')}</div>
		<button
			class="text-xs font-medium text-gray-500"
			type="button"
			on:click={() => {
				show = !show;
				confirmDelete = false;
			}}>{show ? $i18n.t('Hide') : $i18n.t('Show')}</button
		>
	</div>

	{#if show}
		<div class="py-2.5 space-y-1.5">
			<div class="text-sm text-gray-700 dark:text-gray-300 mb-2">
				{$i18n.t('This action cannot be undone. This will permanently delete your account and all associated data.')}
			</div>

			<div class="flex items-center mb-2">
				<input
					type="checkbox"
					id="confirm-delete"
					class="mr-2"
					bind:checked={confirmDelete}
				/>
				<label for="confirm-delete" class="text-sm text-gray-700 dark:text-gray-300">
					{$i18n.t('I understand the consequences of deleting my account')}
				</label>
			</div>
		</div>

		<div class="mt-3 flex justify-end">
			<button
				class="px-3.5 py-1.5 text-sm font-medium bg-red-600 hover:bg-red-700 text-white transition rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
				on:click={deleteUserHandler}
				disabled={!confirmDelete}
			>
				{$i18n.t('Delete my account')}
			</button>
		</div>
	{/if}
</div>