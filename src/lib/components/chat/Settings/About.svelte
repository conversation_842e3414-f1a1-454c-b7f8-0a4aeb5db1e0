<script lang="ts">
	import { getVersionUpdates } from '$lib/apis';
	import { getOllamaVersion } from '$lib/apis/ollama';
	import { WEBUI_BUILD_HASH, WEBUI_VERSION } from '$lib/constants';
	import { WEBUI_NAME, config, showChangelog, userTheme } from '$lib/stores';
	import { compareVersion } from '$lib/utils';
	import { onMount, getContext } from 'svelte';

	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import GithubIcon from '$lib/components/icons/GithubIcon.svelte';
	import XIcon from '$lib/components/icons/XIcon.svelte';
	import Discord from '$lib/components/icons/Discord.svelte';
	import HuggingFace from '$lib/components/icons/HuggingFace.svelte';

	const i18n = getContext('i18n');

	let ollamaVersion = '';

	let updateAvailable = null;
	let version = {
		current: '',
		latest: ''
	};

	const checkForVersionUpdates = async () => {
		updateAvailable = null;
		version = await getVersionUpdates(localStorage.token).catch((error) => {
			return {
				current: WEBUI_VERSION,
				latest: WEBUI_VERSION
			};
		});

		console.log(version);

		updateAvailable = compareVersion(version.latest, version.current);
		console.log(updateAvailable);
	};

	onMount(async () => {
		ollamaVersion = await getOllamaVersion(localStorage.token).catch((error) => {
			return '';
		});

		checkForVersionUpdates();
	});
</script>

<div class="flex flex-col h-full pr-3 justify-between text-sm">
	<div class="flex flex-col gap-4 max-h-[28rem] lg:max-h-full overflow-auto scrollbar-hidden">
		{#if $config?.license_metadata}
			<div class="text-xs">
				{#if !$WEBUI_NAME.includes('Open WebUI')}
					<span class=" text-gray-500 dark:text-gray-300 font-medium">{$WEBUI_NAME}</span> -
				{/if}

				<span class=" capitalize">{$config?.license_metadata?.type}</span> license purchased by
				<span class=" capitalize">{$config?.license_metadata?.organization_name}</span>
			</div>
		{/if}
		<div class="flex gap-2 font-semibold text-lg items-center">
			<img
				class="w-[28px] h-[22px]"
				src={$userTheme === 'dark' ? '/static/logoDark.svg' : '/static/logoLight.svg'}
				alt="logo"
			/>
			<span>Z.ai</span>
		</div>
		<div>
			<div class="text-sm font-medium mb-1">{$i18n.t('About')}</div>
			<div class="flex-1 text-xs leading-6 text-gray-700 dark:text-gray-200">
				{$i18n.t('About Z.ai')}
			</div>
		</div>
		<div>
			<div class="text-sm font-medium mb-1">{$i18n.t('Feedback Channel')}</div>
			<div class="flex-1 text-xs leading-6 text-gray-700 dark:text-gray-200">
				<EMAIL>
			</div>
		</div>
	</div>
	<div class="flex justify-between items-center">
		<div class="flex gap-4">
			<a
				class="underline font-semibold"
				href="https://chat.z.ai/legal-agreement/terms-of-service"
				target="_blank"
				rel="noopener noreferrer">{$i18n.t('Terms of Service')}</a
			>
			<a
				class="underline font-semibold"
				href="https://chat.z.ai/legal-agreement/privacy-policy"
				target="_blank"
				rel="noopener noreferrer">{$i18n.t('Privacy Policy')}</a
			>
		</div>
		<div class="flex gap-2">
			<a href="https://github.com/THUDM" target="_blank" rel="noopener noreferrer">
				<GithubIcon className=" size-5" theme={$userTheme} />
			</a>
			<a href="https://huggingface.co/THUDM" target="_blank" rel="noopener noreferrer">
				<HuggingFace className=" size-6" />
			</a>
			<a href="https://x.com/Zai_org" target="_blank" rel="noopener noreferrer">
				<XIcon theme={$userTheme} className=" size-5" />
			</a>
			<a
				href="https://discord.com/channels/1346756824233148527/1359832169333395496"
				target="_blank"
				rel="noopener noreferrer"
			>
				<Discord className="size-5" theme={$userTheme} fill="black" />
			</a>
		</div>
	</div>
</div>
