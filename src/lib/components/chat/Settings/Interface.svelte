<script lang="ts">
	import { getBackendConfig } from '$lib/apis';
	import { setDefaultPromptSuggestions } from '$lib/apis/configs';
	import { config, mobile, models, settings, user } from '$lib/stores';
	import { createEventDispatcher, onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import Tooltip from '$lib/components/common/Tooltip.svelte';
	import { updateUserInfo } from '$lib/apis/users';
	import { getUserPosition } from '$lib/utils';
	import Switch from '$lib/components/common/Switch.svelte';
	const dispatch = createEventDispatcher();

	const i18n = getContext('i18n');

	export let saveSettings: Function;

	let featureSupported = false;

	let backgroundImageUrl = null;
	let inputFiles = null;
	let filesInputElement;

	// Addons
	let titleAutoGenerate = true;
	let autoTags = true;

	let responseAutoCopy = false;
	let widescreenMode = false;
	let splitLargeChunks = false;
	let scrollOnBranchChange = true;
	let userLocation = false;

	// Interface
	let defaultModelId = '';
	let showUsername = false;
	let notificationSound = true;

	let richTextInput = true;
	let promptAutocomplete = false;

	let largeTextAsFile = false;

	let landingPageMode = '';
	let chatBubble = true;
	let chatDirection: 'LTR' | 'RTL' | 'auto' = 'auto';
	let ctrlEnterToSend = false;

	let collapseCodeBlocks = false;
	let expandDetails = false;

	let imageCompression = false;
	let imageCompressionSize = {
		width: '',
		height: ''
	};

	// Admin - Show Update Available Toast
	let showUpdateToast = true;
	let showChangelog = true;

	let showEmojiInCall = false;
	let voiceInterruption = false;
	let hapticFeedback = false;

	let webSearch = null;

	const toggleExpandDetails = (v: boolean) => {
		expandDetails = v;
		saveSettings({ expandDetails });
	};

	const toggleCollapseCodeBlocks = (v: boolean) => {
		collapseCodeBlocks = v;
		saveSettings({ collapseCodeBlocks });
	};

	const toggleSplitLargeChunks = async (v: boolean) => {
		splitLargeChunks = v;
		saveSettings({ splitLargeChunks: splitLargeChunks });
	};

	const togglePromptAutocomplete = async (v: boolean) => {
		promptAutocomplete = v;
		saveSettings({ promptAutocomplete: promptAutocomplete });
	};

	const togglesScrollOnBranchChange = async (v: boolean) => {
		scrollOnBranchChange = v;
		saveSettings({ scrollOnBranchChange: scrollOnBranchChange });
	};

	const toggleWidescreenMode = async (v: boolean) => {
		widescreenMode = v;
		saveSettings({ widescreenMode: widescreenMode });
	};

	const toggleChatBubble = async (v: boolean) => {
		chatBubble = v;
		saveSettings({ chatBubble: chatBubble });
	};

	const toggleLandingPageMode = async () => {
		landingPageMode = landingPageMode === '' ? 'chat' : '';
		saveSettings({ landingPageMode: landingPageMode });
	};

	const toggleShowUpdateToast = async (v: boolean) => {
		showUpdateToast = v;
		saveSettings({ showUpdateToast: showUpdateToast });
	};

	const toggleNotificationSound = async (v: boolean) => {
		notificationSound = v;
		saveSettings({ notificationSound: notificationSound });
	};

	const toggleShowChangelog = async (v: boolean) => {
		showChangelog = v;
		saveSettings({ showChangelog: showChangelog });
	};

	const toggleShowUsername = async (v: boolean) => {
		showUsername = v;
		saveSettings({ showUsername: showUsername });
	};

	const toggleEmojiInCall = async (v: boolean) => {
		showEmojiInCall = v;
		saveSettings({ showEmojiInCall: showEmojiInCall });
	};

	const toggleVoiceInterruption = async (v: boolean) => {
		voiceInterruption = v;
		saveSettings({ voiceInterruption: voiceInterruption });
	};

	const toggleImageCompression = async (v: boolean) => {
		imageCompression = v;
		saveSettings({ imageCompression });
	};

	const toggleHapticFeedback = async (v: boolean) => {
		hapticFeedback = v;
		saveSettings({ hapticFeedback: hapticFeedback });
	};

	const toggleUserLocation = async (v: boolean) => {
		userLocation = v;

		if (userLocation) {
			const position = await getUserPosition().catch((error) => {
				toast.error(error.message);
				return null;
			});

			if (position) {
				await updateUserInfo(localStorage.token, { location: position });
				toast.success($i18n.t('User location successfully retrieved.'));
			} else {
				userLocation = false;
			}
		}

		saveSettings({ userLocation });
	};

	const toggleTitleAutoGenerate = async (v: boolean) => {
		titleAutoGenerate = v;
		saveSettings({
			title: {
				...$settings.title,
				auto: titleAutoGenerate
			}
		});
	};

	const toggleAutoTags = async (v: boolean) => {
		autoTags = v;
		saveSettings({ autoTags });
	};

	const toggleRichTextInput = async (v: boolean) => {
		richTextInput = v;
		saveSettings({ richTextInput });
	};

	const toggleLargeTextAsFile = async (v: boolean) => {
		largeTextAsFile = v;
		saveSettings({ largeTextAsFile });
	};

	const toggleResponseAutoCopy = async () => {
		const permission = await navigator.clipboard
			.readText()
			.then(() => {
				return 'granted';
			})
			.catch(() => {
				return '';
			});

		console.log(permission);

		if (permission === 'granted') {
			responseAutoCopy = !responseAutoCopy;
			saveSettings({ responseAutoCopy: responseAutoCopy });
		} else {
			toast.error(
				$i18n.t(
					'Clipboard write permission denied. Please check your browser settings to grant the necessary access.'
				)
			);
		}
	};

	const toggleChangeChatDirection = async () => {
		if (chatDirection === 'auto') {
			chatDirection = 'LTR';
		} else if (chatDirection === 'LTR') {
			chatDirection = 'RTL';
		} else if (chatDirection === 'RTL') {
			chatDirection = 'auto';
		}
		saveSettings({ chatDirection });
	};

	const togglectrlEnterToSend = async (v: boolean) => {
		ctrlEnterToSend = v;
		saveSettings({ ctrlEnterToSend });
	};

	const updateInterfaceHandler = async () => {
		saveSettings({
			models: [defaultModelId],
			imageCompressionSize: imageCompressionSize
		});
	};

	const toggleWebSearch = async () => {
		webSearch = webSearch === null ? 'always' : null;
		saveSettings({ webSearch: webSearch });
	};

	onMount(async () => {
		titleAutoGenerate = $settings?.title?.auto ?? true;
		autoTags = $settings.autoTags ?? true;

		responseAutoCopy = $settings.responseAutoCopy ?? false;

		showUsername = $settings.showUsername ?? false;
		showUpdateToast = $settings.showUpdateToast ?? true;
		showChangelog = $settings.showChangelog ?? true;

		showEmojiInCall = $settings.showEmojiInCall ?? false;
		voiceInterruption = $settings.voiceInterruption ?? false;

		richTextInput = $settings.richTextInput ?? true;
		promptAutocomplete = $settings.promptAutocomplete ?? false;
		largeTextAsFile = $settings.largeTextAsFile ?? false;

		collapseCodeBlocks = $settings.collapseCodeBlocks ?? false;
		expandDetails = $settings.expandDetails ?? false;

		landingPageMode = $settings.landingPageMode ?? '';
		chatBubble = $settings.chatBubble ?? true;
		widescreenMode = $settings.widescreenMode ?? false;
		splitLargeChunks = $settings.splitLargeChunks ?? false;
		scrollOnBranchChange = $settings.scrollOnBranchChange ?? true;
		chatDirection = $settings.chatDirection ?? 'auto';
		userLocation = $settings.userLocation ?? false;

		notificationSound = $settings.notificationSound ?? true;

		hapticFeedback = $settings.hapticFeedback ?? false;
		ctrlEnterToSend = $settings.ctrlEnterToSend ?? false;

		imageCompression = $settings.imageCompression ?? false;
		imageCompressionSize = $settings.imageCompressionSize ?? { width: '', height: '' };

		defaultModelId = $settings?.models?.at(0) ?? '';
		if ($config?.default_models) {
			defaultModelId = $config.default_models.split(',')[0];
		}

		backgroundImageUrl = $settings.backgroundImageUrl ?? null;
		webSearch = $settings.webSearch ?? null;
	});
</script>

<form
	class="flex flex-col h-full justify-between space-y-3 text-sm"
	on:submit|preventDefault={() => {
		updateInterfaceHandler();
		dispatch('save');
	}}
>
	<input
		bind:this={filesInputElement}
		bind:files={inputFiles}
		type="file"
		hidden
		accept="image/*"
		on:change={() => {
			let reader = new FileReader();
			reader.onload = (event) => {
				let originalImageUrl = `${event.target.result}`;

				backgroundImageUrl = originalImageUrl;
				saveSettings({ backgroundImageUrl });
			};

			if (
				inputFiles &&
				inputFiles.length > 0 &&
				['image/gif', 'image/webp', 'image/jpeg', 'image/png'].includes(inputFiles[0]['type'])
			) {
				reader.readAsDataURL(inputFiles[0]);
			} else {
				console.log(`Unsupported File Type '${inputFiles[0]['type']}'.`);
				inputFiles = null;
			}
		}}
	/>

	<div
		class=" space-y-3 overflow-y-auto max-h-[28rem] lg:max-h-full dark:text-white/80 scrollbar-hidden"
	>
		<div class=" flex flex-col gap-5">
			<div class=" text-sm font-semibold">{$i18n.t('Interface')}</div>

			{#if $user?.role === 'admin'}
				<div>
					<div class=" flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Landing Page Mode')}</div>

						<button
							class="text-sm flex rounded-sm transition"
							on:click={() => {
								toggleLandingPageMode();
							}}
							type="button"
						>
							{#if landingPageMode === ''}
								<span class="ml-2 self-center">{$i18n.t('Default')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Chat')}</span>
							{/if}
						</button>
					</div>
				</div>
			{/if}

			<div>
				<div class=" flex w-full justify-between">
					<div class=" self-center text-sm">{$i18n.t('Chat Bubble UI')}</div>
					<Switch
						state={chatBubble}
						on:change={(e) => {
							toggleChatBubble(e.detail);
						}}
					/>
				</div>
			</div>

			{#if !$settings.chatBubble}
				<div>
					<div class=" flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Display the username instead of You in the Chat')}
						</div>
						<Switch
							state={showUsername}
							on:change={(e) => {
								toggleShowUsername(e.detail);
							}}
						/>
					</div>
				</div>
			{/if}

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">{$i18n.t('Widescreen Mode')}</div>

					<Switch
						state={widescreenMode}
						on:change={(e) => {
							toggleWidescreenMode(e.detail);
						}}
					/>
				</div>
			</div>

			{#if $user?.role === 'admin'}
				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Chat direction')}</div>

						<button
							class="p-1 px-3 text-sm flex rounded-sm transition"
							on:click={toggleChangeChatDirection}
							type="button"
						>
							{#if chatDirection === 'LTR'}
								<span class="ml-2 self-center">{$i18n.t('LTR')}</span>
							{:else if chatDirection === 'RTL'}
								<span class="ml-2 self-center">{$i18n.t('RTL')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Auto')}</span>
							{/if}
						</button>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Notification Sound')}
						</div>

						<Switch
							state={notificationSound}
							on:change={(e) => {
								toggleNotificationSound(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Toast notifications for new updates')}
						</div>

						<Switch
							state={showUpdateToast}
							on:change={(e) => {
								toggleShowUpdateToast(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t(`Show "What's New" modal on login`)}
						</div>

						<Switch
							state={showChangelog}
							on:change={(e) => {
								toggleShowChangelog(e.detail);
							}}
						/>
					</div>
				</div>
			{/if}

			<div class=" mt-2 text-sm font-semibold">{$i18n.t('Dialogue')}</div>

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">{$i18n.t('Title Auto-Generation')}</div>

					<Switch
						state={titleAutoGenerate}
						on:change={(e) => {
							toggleTitleAutoGenerate(e.detail);
						}}
					/>
				</div>
			</div>

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">{$i18n.t('Chat Tags Auto-Generation')}</div>

					<Switch
						state={autoTags}
						on:change={(e) => {
							toggleAutoTags(e.detail);
						}}
					/>
				</div>
			</div>

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">
						{$i18n.t('Auto-Copy Response to Clipboard')}
					</div>
					<Switch
						state={responseAutoCopy}
						on:change={(e) => {
							toggleResponseAutoCopy(e.detail);
						}}
					/>
				</div>
			</div>

			{#if $user?.role === 'admin'}
				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Rich Text Input for Chat')}
						</div>
						<Switch
							state={richTextInput}
							on:change={(e) => {
								toggleRichTextInput(e.detail);
							}}
						/>
					</div>
				</div>
			{/if}

			{#if $config?.features?.enable_autocomplete_generation && richTextInput}
				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Prompt Autocompletion')}
						</div>
						<Switch
							state={promptAutocomplete}
							on:change={(e) => {
								togglePromptAutocomplete(e.detail);
							}}
						/>
					</div>
				</div>
			{/if}

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">
						{$i18n.t('Paste Large Text as File')}
					</div>
					<Switch
						state={largeTextAsFile}
						on:change={(e) => {
							toggleLargeTextAsFile(e.detail);
						}}
					/>
				</div>
			</div>

			{#if $user?.role === 'admin'}
				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Always Collapse Code Blocks')}</div>
						<Switch
							state={collapseCodeBlocks}
							on:change={(e) => {
								toggleCollapseCodeBlocks(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Always Expand Details')}</div>

						<Switch
							state={expandDetails}
							on:change={(e) => {
								toggleExpandDetails(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Chat Background Image')}
						</div>

						<button
							class="p-1 px-3 text-sm flex rounded-sm transition"
							on:click={() => {
								if (backgroundImageUrl !== null) {
									backgroundImageUrl = null;
									saveSettings({ backgroundImageUrl });
								} else {
									filesInputElement.click();
								}
							}}
							type="button"
						>
							{#if backgroundImageUrl !== null}
								<span class="ml-2 self-center">{$i18n.t('Reset')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Upload')}</span>
							{/if}
						</button>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Allow User Location')}</div>
						<Switch
							state={userLocation}
							on:change={(e) => {
								toggleUserLocation(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Haptic Feedback')}</div>
						<Switch
							state={hapticFeedback}
							on:change={(e) => {
								toggleHapticFeedback(e.detail);
							}}
						/>
					</div>
				</div>
				<!-- <div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Fluidly stream large external response chunks')}
						</div>
	
						<button
							class="p-1 px-3 text-sm flex rounded-sm transition"
							on:click={() => {
								toggleSplitLargeChunks();
							}}
							type="button"
						>
							{#if splitLargeChunks === true}
								<span class="ml-2 self-center">{$i18n.t('On')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Off')}</span>
							{/if}
						</button>
					</div>
				</div> -->

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">
							{$i18n.t('Enter Key Behavior')}
						</div>

						<button
							class="p-1 px-3 text-sm flex rounded transition"
							on:click={() => {
								togglectrlEnterToSend(!ctrlEnterToSend);
							}}
							type="button"
						>
							{#if ctrlEnterToSend === true}
								<span class="ml-2 self-center">{$i18n.t('Ctrl+Enter to Send')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Enter to Send')}</span>
							{/if}
						</button>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Web Search in Chat')}</div>

						<button
							class="p-1 px-3 text-sm flex rounded-sm transition"
							on:click={() => {
								toggleWebSearch();
							}}
							type="button"
						>
							{#if webSearch === 'always'}
								<span class="ml-2 self-center">{$i18n.t('Always')}</span>
							{:else}
								<span class="ml-2 self-center">{$i18n.t('Default')}</span>
							{/if}
						</button>
					</div>
				</div>
			{/if}

			<div>
				<div class="  flex w-full justify-between">
					<div class=" self-center text-sm">
						{$i18n.t('Scroll to bottom when switching between branches')}
					</div>
					<Switch
						state={scrollOnBranchChange}
						on:change={(e) => {
							togglesScrollOnBranchChange(e.detail);
						}}
					/>
				</div>
			</div>

			{#if featureSupported && $user?.role === 'admin'}
				<div class=" my-1.5 text-sm font-medium">{$i18n.t('Voice')}</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Allow Voice Interruption in Call')}</div>
						<Switch
							state={voiceInterruption}
							on:change={(e) => {
								toggleVoiceInterruption(e.detail);
							}}
						/>
					</div>
				</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Display Emoji in Call')}</div>
						<Switch
							state={showEmojiInCall}
							on:change={(e) => {
								toggleEmojiInCall(e.detail);
							}}
						/>
					</div>
				</div>

				<div class=" my-1.5 text-sm font-medium">{$i18n.t('File')}</div>

				<div>
					<div class="  flex w-full justify-between">
						<div class=" self-center text-sm">{$i18n.t('Image Compression')}</div>

						<Switch
							state={imageCompression}
							on:change={(e) => {
								toggleImageCompression(e.detail);
							}}
						/>
					</div>
				</div>

				{#if imageCompression}
					<div>
						<div class="  flex w-full justify-between text-sm">
							<div class=" self-center text-sm">{$i18n.t('Image Max Compression Size')}</div>

							<div>
								<input
									bind:value={imageCompressionSize.width}
									type="number"
									class="w-20 bg-transparent outline-hidden text-center"
									min="0"
									placeholder="Width"
								/>x
								<input
									bind:value={imageCompressionSize.height}
									type="number"
									class="w-20 bg-transparent outline-hidden text-center"
									min="0"
									placeholder="Height"
								/>
							</div>
						</div>
					</div>
				{/if}
			{/if}
		</div>
	</div>

	<div class="flex justify-end text-sm font-medium">
		<button
			class="px-4 py-2 text-sm font-medium button-gradient hover:bg-gray-900 text-white dark:bg-[#484A58] dark:hover:bg-[#5A5C68] rounded-lg {$mobile
				? 'w-full'
				: ''}"
			type="submit"
		>
			{$i18n.t('Save')}
		</button>
	</div>
</form>
