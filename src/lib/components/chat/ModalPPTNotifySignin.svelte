<script lang="ts">
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';
	import type { i18n as i18nType } from 'i18next';
	import { showModalPPTNotifySignin } from '$lib/stores'; // Assuming a new store for this modal
	import Modal from '../common/Modal.svelte';

	const i18n = getContext<Writable<i18nType>>('i18n');

	const redirectToAuth = () => {
		$showModalPPTNotifySignin = false;
		location.replace('/auth');
	};

	const closeModal = () => {
		$showModalPPTNotifySignin = false;
	};
</script>

<Modal
	bind:show={$showModalPPTNotifySignin}
	size="sm"
	containerClassName="p-3 ppt-notify-signin-modal"
>
	<div
		class="flex flex-col justify-center items-center p-8 md:p-10 relative bg-white dark:bg-gray-800 rounded-lg"
	>
		<button
			on:click={closeModal}
			class="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
			aria-label="Close"
		>
			<svg
				class="w-6 h-6"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
				xmlns="http://www.w3.org/2000/svg"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M6 18L18 6M6 6l12 12"
				></path>
			</svg>
		</button>

		<div
			class="text-center text-[28px] max-md:text-[24px] font-bold text-gradient mb-2 whitespace-pre-wrap"
		>
			{$i18n.t('Create Presentations with AI')}
		</div>
		<div class="text-center text-sm max-md:text-xs text-black/60 dark:text-gray-300 mb-6">
			{$i18n.t('Sign in to create AI-powered presentations and publish online')}
		</div>

		<!-- Placeholder for image, as per user instruction -->
		<div class="w-full h-50 flex items-center justify-center rounded mb-8">
			<img src="/static/ppt_signin.png" alt="Welcome to z.ai" class="max-h-full max-w-full" />
		</div>

		<div class="w-full flex flex-col gap-3">
			<button
				on:click={redirectToAuth}
				class="w-full py-3 text-center button-gradient dark:bg-[#484A58] text-white hover:opacity-90 transition rounded-lg font-medium text-sm h-12"
			>
				{$i18n.t('Sign in')}
			</button>
			<button
				on:click={closeModal}
				class="w-full py-3 text-sm text-center border border-black/10 dark:border-white/10 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition rounded-lg font-medium h-12"
			>
				{$i18n.t('Skip for now')}
			</button>
		</div>
	</div>
</Modal>

<style>
	:global(.ppt-notify-signin-modal) {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* Ensuring the modal content itself is centered if the parent Modal component doesn't handle it for 'sm' size */
	:global(.ppt-notify-signin-modal > div) {
		/* Potentially add transform or margin auto if needed, but often the flex on parent is enough */
	}

	/* Custom styles for this specific modal if needed, otherwise rely on Tailwind and common Modal styles */
	.text-gradient {
		background: linear-gradient(160.13deg, #191a1d -3.97%, #747689 107.25%, #191a1d 218.47%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}
	:global(.dark) .text-gradient {
		background: linear-gradient(136.4deg, #ffffff -13.32%, #acaebd 58.94%, #ffffff 150.59%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
	}
</style>
