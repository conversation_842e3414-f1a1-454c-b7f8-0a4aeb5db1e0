<script lang="ts">
	import Bolt from '$lib/components/icons/Bolt.svelte';
	import { onMount, getContext, createEventDispatcher, onDestroy, tick, afterUpdate } from 'svelte';
	import { eventBus, showModalPPTNotifySignin, user, WEBUI_NAME } from '$lib/stores';
	import { WEBUI_VERSION } from '$lib/constants';
	import { mobile } from '$lib/stores';
	import { page } from '$app/stores';

	const i18n = getContext('i18n');
	const dispatch = createEventDispatcher();

	export let suggestionPrompts = [];
	export let className = '';
	export let inputValue = '';
	export let selectedGroupName = '';
	export let selectedGroup = '';
	export let selectedModels = [];

	let subPromptContainerRef: HTMLDivElement | null = null;

	let prevInputValue = inputValue;
	let selectedPromptName = '';

	$: forceSelectEffect(suggestionPrompts);
	async function forceSelectEffect(suggestionPrompts: any[]) {
		const force = suggestionPrompts?.find((i) => i.force_selected);
		if (force) {
			await tick();
			const target = document.querySelector(`.promptCardForce`);
			target?.click();
			// selectedGroup = force;
			// selectedGroupName = force.group_name;
		}
	}

	$: if (selectedGroup && prevInputValue !== inputValue) {
		!selectedGroup?.force_selected && resetToFirstLevel();
		prevInputValue = inputValue;
	}

	$: prevInputValue = inputValue;

	$: if (selectedGroupName === '') {
		selectedGroup = '';
	}

	// 导出重置函数供外部调用
	export const resetToFirstLevel = () => {
		selectedGroup = '';
		selectedGroupName = '';
	};

	// 处理外部点击事件
	function handleOutsideClick(event: MouseEvent) {
		if (event.target?.id === 'chat-input') return;
		if (subPromptContainerRef && !subPromptContainerRef.contains(event.target as Node)) {
			dispatch('outside_click');
			if (!selectedGroup?.force_selected) {
				resetToFirstLevel();
			}
		}
	}

	afterUpdate(() => {
		if (suggestionPrompts.length > 0 && selectedModels[0]) {
			$eventBus.emit('selected_group');
		}
	});

	onMount(() => {
		// 添加点击事件监听器
		document.addEventListener('click', handleOutsideClick);
		const suggest = $page.url.searchParams.get('suggest');
		if (suggest) {
			$eventBus.on('selected_group', () => {
				if (suggestionPrompts?.length > 0) {
					const suggest = $page.url.searchParams.get('suggest');
					if (suggest) {
						const group = suggestionPrompts.find((i) => i.group_name_en === suggest);
						if (group) {
							if (!group.prompts?.length) {
								// 单层
								const {
									prompt = '',
									prompt_en = '',
									flags,
									features = [],
									group_name = '',
									group_name_en = ''
								} = group;

								dispatch('select_group', {
									text: $i18n.language === 'en-US' ? prompt_en : prompt,
									group: $i18n.language === 'en-US' ? group_name_en : group_name,
									flags,
									features
								});
							} else {
								// 二级
								const { group_name = '', group_name_en = '', flags, features = [] } = group;
								dispatch('select_group', {
									group: $i18n.language === 'en-US' ? group_name_en : group_name,
									flags,
									features
								});
							}
							selectedGroup = group;
						}
					}
				}
				$eventBus.off('selected_group');
			});
		}
	});

	onDestroy(() => {
		// 移除点击事件监听器
		document.removeEventListener('click', handleOutsideClick);
	});
</script>

<div class=" suggestionContainer relative">
	{#if selectedGroup === ''}
		<div
			class="{$mobile
				? ''
				: 'absolute top-0 left-0 z-1'} w-full overflow-auto scrollbar-none {className} items-start flex justify-center gap-2 {$mobile
				? 'flex-nowrap'
				: 'flex-wrap'} mt-4 text-sm"
		>
			{#each suggestionPrompts as group, idx (group.group_name || group.group_name_en)}
				<button
					class="{`promptCard${group.force_selected ? 'Force' : ''}`} waterfall flex flex-col shrink-0 bg-white/80 dark:bg-black/20
									px-3 py-1.5 rounded-lg hover:bg-[#DAEEFF] dark:hover:bg-[#DAEEFF] transition group
									border-b-2 border-black/10 dark:border-white/10 dark:hover:text-black
									items-center justify-center text-center"
					style="animation-delay: {idx * 60}ms"
					on:click|stopPropagation={() => {
						if ($user?.role === 'guest' && group.flags?.includes('ppt_composer')) {
							if (!group.force_selected) {
								showModalPPTNotifySignin.set(true);
								return;
							}
						}

						if (!group.prompts?.length) {
							// 单层
							const {
								prompt = '',
								prompt_en = '',
								flags,
								features = [],
								group_name = '',
								group_name_en = ''
							} = group;
							dispatch('select_group', {
								text: $i18n.language === 'en-US' ? prompt_en : prompt,
								group: $i18n.language === 'en-US' ? group_name_en : group_name,
								flags,
								features
							});
						} else {
							// 二级
							const { group_name = '', group_name_en = '', flags, features = [] } = group;
							dispatch('select_group', {
								group: $i18n.language === 'en-US' ? group_name_en : group_name,
								flags,
								features
							});
							selectedGroup = group;
							document.addEventListener('click', handleOutsideClick);
						}
					}}
				>
					<div class="w-full flex items-center justify-between gap-2 h-6">
						<!-- 左侧：图标和名称 -->
						<div
							class="transition flex flex-row items-center gap-2 flex-1 min-w-0 text-black/60 dark:text-white/60"
						>
							<div class="size-4 flex items-center justify-center">
								{@html group.icon || ''}
							</div>
							<div class="truncate">
								{$i18n.language === 'en-US' ? group.group_name_en : group.group_name}
							</div>
						</div>

						<!-- 右侧：Tag 标签或占位符 -->
						{#if group.tag || group.tag_en}
							<div class="">
								{@html $i18n?.language === 'en-US'
									? group.tag_en || group.tag
									: group.tag || group.tag_en}
							</div>
						{/if}
					</div>
				</button>
			{/each}
		</div>
	{:else}
		<div
			bind:this={subPromptContainerRef}
			class="absolute {$mobile
				? 'bottom-0'
				: 'top-0'} left-0 z-1 max-h-[163px] w-full overflow-auto scrollbar-none items-start flex flex-col mt-3 text-sm leading-4"
		>
			{#each selectedGroup.prompts as prompt, idx (prompt.name || prompt.name_en)}
				<button
					class="promptCard waterfall w-full flex justify-start text-[rgb(49,49,50)] hover:text-[#47,48,48] dark:text-white/60 bg-[#F4F6F8] dark:bg-[#141618]
								p-3 hover:font-medium hover:bg-[#ECEEF0] dark:hover:bg-black/30 transition group
								border-b-1 border-black/10 dark:border-white/10 dark:hover:text-white
								 {selectedPromptName === prompt.name || selectedPromptName === prompt.name_en}
					? 'bg-[#C0E0FF] dark:text-black'
					: 'bg-white/80 dark:bg-[#26282A]'}"
					style="animation-delay: {idx * 60}ms"
					on:click={async () => {
						if ($user?.role === 'guest' && selectedGroup.flags?.includes('ppt_composer')) {
							showModalPPTNotifySignin.set(true);
							return;
						}
						dispatch('select_prompt', {
							text: $i18n.language === 'en-US' ? prompt.prompt_en : prompt.prompt,
							flags: prompt.flags,
							features: prompt.features
						});
						selectedPromptName = prompt.name || '';
						await tick();
						document.getElementById('send-message-button')?.click();
					}}
				>
					<div class=" truncate">
						{@html $i18n.language === 'en-US' ? prompt.name_en : prompt.name}
					</div>
				</button>
			{/each}
		</div>
	{/if}
</div>

<style>
	/* Waterfall animation for the suggestions */
	@keyframes fadeInUp {
		0% {
			opacity: 0;
			transform: translateY(20px);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.waterfall {
		opacity: 0;
		animation-name: fadeInUp;
		animation-duration: 200ms;
		animation-fill-mode: forwards;
		animation-timing-function: ease;
	}
</style>
