<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { v4 as uuidv4 } from 'uuid';
	import { createPicker, getAuthToken } from '$lib/utils/google-drive-picker';
	import { pickAndDownloadFile } from '$lib/utils/onedrive-file-picker';

	import { onMount, tick, getContext, createEventDispatcher, onDestroy } from 'svelte';
	const dispatch = createEventDispatcher();

	import {
		type Model,
		mobile,
		settings,
		showSidebar,
		models,
		config,
		showCallOverlay,
		tools,
		user as _user,
		showControls,
		TTSWorker,
		showArtifacts,
		autoArtifacts,
		showNotifySignInModal,
		NotifySignInModelType,
		SignInModalType,
		chatId,
		SignInModalSource
	} from '$lib/stores';

	import {
		blobToFile,
		compressImage,
		createMessagesList,
		extractCurlyBraceWords
	} from '$lib/utils';
	import { validateFile } from '$lib/utils/fileValidator';
	import { getFileAcceptAttribute } from '$lib/utils/fileValidator';
	import { transcribeAudio } from '$lib/apis/audio';
	import { uploadFile } from '$lib/apis/files';
	import { generateAutoCompletion } from '$lib/apis';
	import { deleteFileById } from '$lib/apis/files';

	import { WEBUI_BASE_URL, WEBUI_API_BASE_URL, PASTED_TEXT_CHARACTER_LIMIT } from '$lib/constants';

	import InputMenu from './MessageInput/InputMenu.svelte';
	import VoiceRecording from './MessageInput/VoiceRecording.svelte';
	import FilesOverlay from './MessageInput/FilesOverlay.svelte';
	import Commands from './MessageInput/Commands.svelte';
	import MCPServerModal from './MessageInput/MCPServerModal.svelte';
	import ToolSelector from './ToolSelector.svelte';

	import RichTextInput from '../common/RichTextInput.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import FileItem from '../common/FileItem.svelte';
	import Image from '../common/Image.svelte';

	import XMark from '../icons/XMark.svelte';
	import Headphone from '../icons/Headphone.svelte';
	import GlobeAlt from '../icons/GlobeAlt.svelte';
	import PhotoSolid from '../icons/PhotoSolid.svelte';
	import Photo from '../icons/Photo.svelte';
	import CommandLine from '../icons/CommandLine.svelte';
	import { KokoroWorker } from '$lib/workers/KokoroWorker';
	import Cube from '../icons/Cube.svelte';
	import Server from '../icons/Server.svelte';
	import ToolServersModal from './ToolServersModal.svelte';
	import Wrench from '../icons/Wrench.svelte';
	import { trackButtonClick } from '$lib/utils/analytics';
	import InputFlags from './InputFlags.svelte';
	import Hamburger from '../icons/Hamburger.svelte';

	import ArrowDown from '../icons/ArrowDown.svelte';
	import ArrowUp from '../icons/ArrowUp.svelte';
	import SendArrorIcon from '../icons/SendArrorIcon.svelte';
	const i18n = getContext('i18n');

	export let transparentBackground = false;

	export let onChange: Function = () => {};
	export let createMessagePair: Function;
	export let stopResponse: Function;

	export let autoScroll = false;

	export let atSelectedModel: Model | undefined = undefined;
	export let selectedModels: [''];

	let selectedModelIds = [];
	$: selectedModelIds = atSelectedModel ? [atSelectedModel.id] : selectedModels;

	export let history;

	export let prompt = '';
	export let files = [];

	export let toolServers = [];

	export let selectedToolIds = [];
	export let selectedMCPServers = [];

	export let imageGenerationEnabled = false;
	export let webSearchEnabled = false;
	export let codeInterpreterEnabled = false;
	export let mcpServerEnabled = false;
	export let showPreviewButton = false;
	export let resetSuggestions: Function;

	export let flags: string[] = [];
	export let features: any[] | undefined;
	export let disabledMCPServerMap: Record<string, boolean> = {};
	export let selectedGroup = '';
	export let showMessages = false;

	function handleFeaturesChange(features?: any[]) {
		if (!features) {
			return;
		}
		// 先清理掉之前的状态
		disabledMCPServerMap = {};
		selectedMCPServers = [];

		features.forEach(({ type, server, status }) => {
			if (type === 'mcp') {
				switch (status) {
					case 'pinned':
						disabledMCPServerMap[server] = true;
						selectedMCPServers = [...new Set([...selectedMCPServers, server])];
						break;
					case 'selected':
						selectedMCPServers = [...new Set([...selectedMCPServers, server])];
						break;
					case 'hidden':
						selectedMCPServers = selectedMCPServers.filter((s) => s !== server);
						disabledMCPServerMap[server] = true;
						break;
					default:
						break;
				}
			} else if (type === 'web_search') {
				switch (status) {
					case 'pinned':
						webSearchEnabled = true;
						break;
					case 'selected':
						webSearchEnabled = true;
						break;
					case 'hidden':
						webSearchEnabled = false;
						break;
					default:
						break;
				}
			}
		});
	}

	$: handleFeaturesChange(features);

	let flagsIndent = 0;
	let textAreaScrollHeight = 0;

	let showMCPServerModal = false;

	$: onChange({
		prompt,
		files,
		selectedToolIds,
		selectedMCPServers,
		flags,
		features,
		imageGenerationEnabled,
		webSearchEnabled,
		mcpServerEnabled
	});

	// 隐藏掉一些暂未开放的功能
	const featureSupported = false;

	let showTools = false;

	let loaded = false;
	let recording = false;

	let isComposing = false;

	let chatInputContainerElement;
	let chatInputElement;

	let filesInputElement;
	let commandsElement;

	let inputFiles;
	let dragged = false;

	let emittedInputEvent = false;

	let user = null;
	export let placeholder = '';

	let visionCapableModels = [];
	$: visionCapableModels = [...(atSelectedModel ? [atSelectedModel] : selectedModels)].filter(
		(model) => $models.find((m) => m.id === model)?.info?.meta?.capabilities?.vision ?? true
	);

	let webSearchCapableModels = [];
	$: webSearchCapableModels = [...(atSelectedModel ? [atSelectedModel] : selectedModels)].filter(
		(model) => $models.find((m) => m.id === model)?.info?.meta?.capabilities?.web_search ?? false
	);

	let previewModeCapableModels = [];

	$: {
		previewModeCapableModels = [...(atSelectedModel ? [atSelectedModel] : selectedModels)].filter(
			(model) =>
				$models.find((m) => m.id === model)?.info?.meta?.capabilities?.preview_mode ?? false
		);
	}

	let mcpCapableModels = [];
	$: mcpCapableModels = [...(atSelectedModel ? [atSelectedModel] : selectedModels)].filter(
		(model) => $models.find((m) => m.id === model)?.info?.meta?.capabilities?.mcp ?? false
	);

	let fileQACapableModels = [];
	$: fileQACapableModels = [...(atSelectedModel ? [atSelectedModel] : selectedModels)].filter(
		(model) => $models.find((m) => m.id === model)?.info?.meta?.capabilities?.file_qa ?? false
	);

	// 计算可用的 MCP Server（与 ToolSelector 组件逻辑一致）
	function getAvailableMCPServers(
		selectedModelIds: string[],
		allMCPServers: any[],
		allModels: any[]
	) {
		if (!selectedModelIds.length || !allMCPServers.length) {
			return [];
		}

		// 获取所有选择模型的 MCP Server 配置
		const modelMCPServerIds = new Set<string>();
		let hasModelWithMCPConfig = false;

		selectedModelIds.forEach((modelId) => {
			const model = allModels.find((m) => m.id === modelId);
			if (model?.info?.meta?.mcpServerIds && model.info.meta.mcpServerIds.length > 0) {
				// 如果模型配置了特定的 MCP Server，只添加这些
				hasModelWithMCPConfig = true;
				model.info.meta.mcpServerIds.forEach((serverId: string) => modelMCPServerIds.add(serverId));
			}
		});

		// 如果没有任何模型配置了特定的 MCP Server，则不显示任何工具
		if (!hasModelWithMCPConfig) {
			return [];
		}

		// 过滤出可用的 MCP Server
		return allMCPServers.filter((server) => modelMCPServerIds.has(server.name));
	}

	$: availableMCPServers = getAvailableMCPServers(
		selectedModels,
		($config as any)?.mcp_servers ?? [],
		$models
	);
	$: hasMCPServers = availableMCPServers.length > 0;

	const scrollToBottom = () => {
		const element = document.getElementById('messages-container');
		element.scrollTo({
			top: element.scrollHeight,
			behavior: 'smooth'
		});
	};

	const screenCaptureHandler = async () => {
		try {
			// Request screen media
			const mediaStream = await navigator.mediaDevices.getDisplayMedia({
				video: { cursor: 'never' },
				audio: false
			});
			// Once the user selects a screen, temporarily create a video element
			const video = document.createElement('video');
			video.srcObject = mediaStream;
			// Ensure the video loads without affecting user experience or tab switching
			await video.play();
			// Set up the canvas to match the video dimensions
			const canvas = document.createElement('canvas');
			canvas.width = video.videoWidth;
			canvas.height = video.videoHeight;
			// Grab a single frame from the video stream using the canvas
			const context = canvas.getContext('2d');
			context.drawImage(video, 0, 0, canvas.width, canvas.height);
			// Stop all video tracks (stop screen sharing) after capturing the image
			mediaStream.getTracks().forEach((track) => track.stop());

			// bring back focus to this current tab, so that the user can see the screen capture
			window.focus();

			// Convert the canvas to a Base64 image URL
			const imageUrl = canvas.toDataURL('image/png');
			// Add the captured image to the files array to render it
			files = [...files, { type: 'image', url: imageUrl }];
			// Clean memory: Clear video srcObject
			video.srcObject = null;
		} catch (error) {
			// Handle any errors (e.g., user cancels screen sharing)
			console.error('Error capturing screen:', error);
		}
	};

	const uploadFileHandler = async (file, fullContext: boolean = false) => {
		if (!$_user || $_user?.role === 'guest') {
			// Trigger sign-in modal for file upload
			NotifySignInModelType.set([SignInModalType.FileUpload, SignInModalSource.Home]);
			showNotifySignInModal.set(true);
			return null;
		}

		if ($_user?.role !== 'admin' && !($_user?.permissions?.chat?.file_upload ?? true)) {
			toast.error($i18n.t('You do not have permission to upload files.'));
			return null;
		}
		if (!fileQACapableModels.length) {
			toast.error($i18n.t('This model does not support file upload.'));
			return null;
		}
		// Validate file using centralized validator
		const validation = validateFile(file);
		if (!validation.isValid) {
			toast.error(validation.error);
			return null;
		}

		const tempItemId = uuidv4();

		// Determine file type based on extension
		const fileExtension = file.name.split('.').pop()?.toLowerCase();
		const isImage = ['png', 'jpg', 'jpeg', 'bmp', 'gif'].includes(fileExtension);

		// Create temporary URL for image preview during upload
		const tempUrl = isImage ? URL.createObjectURL(file) : '';

		const fileItem = {
			type: 'file',
			file: '',
			id: null,
			url: tempUrl,
			name: file.name,
			collection_name: '',
			status: 'uploading',
			size: file.size,
			error: '',
			itemId: tempItemId,
			isImage: isImage,
			...(fullContext ? { context: 'full' } : {})
		};

		try {
			// During the file upload, file content is automatically extracted.
			const uploadedFile = await uploadFile(localStorage.token, file);

			if (uploadedFile) {
				console.log('File upload completed:', {
					id: uploadedFile.id,
					name: fileItem.name,
					collection: uploadedFile?.meta?.collection_name
				});

				if (uploadedFile.error) {
					console.warn('File upload warning:', uploadedFile.error);
					toast.warning(uploadedFile.error);
				}

				// Clean up temporary blob URL if it exists
				if (fileItem.url && fileItem.url.startsWith('blob:')) {
					URL.revokeObjectURL(fileItem.url);
				}
				fileItem.status = 'uploaded';
				fileItem.file = uploadedFile;
				fileItem.id = uploadedFile.id;
				fileItem.collection_name =
					uploadedFile?.meta?.collection_name || uploadedFile?.collection_name;
				fileItem.url = fileItem.isImage
					? `${WEBUI_API_BASE_URL}/files/${uploadedFile.id}/content`
					: `${WEBUI_API_BASE_URL}/files/${uploadedFile.id}`;

				files = files;
			} else {
				files = files.filter((item) => item?.itemId !== tempItemId);
			}
		} catch (e) {
			toast.error(`${e}`);
			files = files.filter((item) => item?.itemId !== tempItemId);
		}
		files = [...files, fileItem];
	};

	const inputFilesHandler = async (inputFiles) => {
		console.log('Input files handler called with:', inputFiles);

		// Check if the number of files exceeds the maximum allowed
		if (
			($config?.file?.max_count ?? null) !== null &&
			inputFiles.length > $config?.file?.max_count
		) {
			toast.error(
				$i18n.t(`You can only chat with a maximum of {{maxCount}} file(s) at a time.`, {
					maxCount: $config?.file?.max_count
				})
			);
			// Clear all files from input
			files = [];
			return;
		}

		// Check if adding these files would exceed the limit (considering existing files)
		if (
			($config?.file?.max_count ?? null) !== null &&
			files.length + inputFiles.length > $config?.file?.max_count
		) {
			toast.error(
				$i18n.t(`You can only chat with a maximum of {{maxCount}} file(s) at a time.`, {
					maxCount: $config?.file?.max_count
				})
			);
			// Clear all files from input
			files = [];
			return;
		}

		inputFiles.forEach((file) => {
			console.log('Processing file:', {
				name: file.name,
				type: file.type,
				size: file.size,
				extension: file.name.split('.').pop()?.toLowerCase()
			});
			// 简化为单次调用，避免重复上传
			uploadFileHandler(file, false);
		});
	};

	const handleKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Escape') {
			console.log('Escape');
			dragged = false;
		}
	};

	const onDragOver = (e) => {
		e.preventDefault();

		// Check if a file is being dragged.
		if (e.dataTransfer?.types?.includes('Files')) {
			dragged = true;
		} else {
			dragged = false;
		}
	};

	const onDragLeave = () => {
		dragged = false;
	};

	const onDrop = async (e) => {
		e.preventDefault();
		console.log(e);

		if (e.dataTransfer?.files) {
			const inputFiles = Array.from(e.dataTransfer?.files);
			if (inputFiles && inputFiles.length > 0) {
				console.log(inputFiles);
				inputFilesHandler(inputFiles);
			}
		}

		dragged = false;
	};

	onMount(async () => {
		loaded = true;

		window.setTimeout(() => {
			const chatInput = document.getElementById('chat-input');
			!$mobile && chatInput?.focus();
		}, 0);

		window.addEventListener('keydown', handleKeyDown);

		await tick();

		const dropzoneElement = document.getElementById('chat-container');

		dropzoneElement?.addEventListener('dragover', onDragOver);
		dropzoneElement?.addEventListener('drop', onDrop);
		dropzoneElement?.addEventListener('dragleave', onDragLeave);
	});

	onDestroy(() => {
		// console.log('destroy');
		window.removeEventListener('keydown', handleKeyDown);
		showControls.set('');
		const dropzoneElement = document.getElementById('chat-container');

		if (dropzoneElement) {
			dropzoneElement?.removeEventListener('dragover', onDragOver);
			dropzoneElement?.removeEventListener('drop', onDrop);
			dropzoneElement?.removeEventListener('dragleave', onDragLeave);
		}
	});
</script>

<FilesOverlay show={dragged} />

<ToolServersModal bind:show={showTools} {selectedToolIds} />

{#if loaded}
	<div class="w-full font-primary">
		<div class=" mx-auto inset-x-0 bg-transparent flex justify-center">
			<div
				class="flex flex-col px-3 {($settings?.widescreenMode ?? null)
					? 'max-w-full'
					: 'max-w-6xl'} w-full"
			>
				<div class="relative">
					{#if autoScroll === false && history?.currentId}
						<div
							class=" absolute -top-12 left-0 right-0 flex justify-center z-30 pointer-events-none"
						>
							<button
								class=" bg-white border border-gray-100 dark:border-none dark:bg-white/20 p-1.5 rounded-full pointer-events-auto"
								on:click={() => {
									autoScroll = true;
									scrollToBottom();
								}}
							>
								<ArrowDown className="size-5" />
							</button>
						</div>
					{/if}
				</div>

				<div class="w-full relative">
					{#if featureSupported && (atSelectedModel !== undefined || selectedToolIds.length > 0 || webSearchEnabled || ($settings?.webSearch ?? false) === 'always' || imageGenerationEnabled || codeInterpreterEnabled || selectedMCPServers.length > 0)}
						<div
							class="px-3 pb-0.5 pt-1.5 text-left w-full flex flex-col absolute bottom-0 left-0 right-0 bg-linear-to-t from-white dark:from-gray-900 z-10"
						>
							{#if selectedMCPServers.length > 0}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-purple-500" />
											</span>
										</div>
										<div class="text-ellipsis line-clamp-1 flex">
											{#each selectedMCPServers as server, serverIdx}
												<Tooltip
													content={$config?.mcp_servers?.find((s) => s.name === server)
														?.description ?? ''}
													className="{serverIdx !== 0 ? 'pl-0.5' : ''} shrink-0"
													placement="top"
												>
													{$config?.mcp_servers?.find((s) => s.name === server)?.name ?? server}
												</Tooltip>

												{#if serverIdx !== selectedMCPServers.length - 1}
													<span>, </span>
												{/if}
											{/each}
										</div>
									</div>
								</div>
							{/if}

							{#if selectedToolIds.length > 0}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-yellow-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-yellow-500" />
											</span>
										</div>
										<div class="  text-ellipsis line-clamp-1 flex">
											{#each selectedToolIds.map((id) => {
												return $tools ? $tools.find((t) => t.id === id) : { id: id, name: id };
											}) as tool, toolIdx (toolIdx)}
												<Tooltip
													content={tool?.meta?.description ?? ''}
													className=" {toolIdx !== 0 ? 'pl-0.5' : ''} shrink-0"
													placement="top"
												>
													{tool.name}
												</Tooltip>

												{#if toolIdx !== selectedToolIds.length - 1}
													<span>, </span>
												{/if}
											{/each}
										</div>
									</div>
								</div>
							{/if}

							{#if webSearchEnabled || ($config?.features?.enable_web_search && ($settings?.webSearch ?? false)) === 'always'}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-blue-500" />
											</span>
										</div>
										<div class=" translate-y-[0.5px]">{$i18n.t('Search the internet')}</div>
									</div>
								</div>
							{/if}

							{#if imageGenerationEnabled}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-teal-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-teal-500" />
											</span>
										</div>
										<div class=" translate-y-[0.5px]">{$i18n.t('Generate an image')}</div>
									</div>
								</div>
							{/if}

							{#if codeInterpreterEnabled}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-green-500" />
											</span>
										</div>
										<div class=" translate-y-[0.5px]">{$i18n.t('Execute code for analysis')}</div>
									</div>
								</div>
							{/if}

							{#if $autoArtifacts}
								<div class="flex items-center justify-between w-full">
									<div class="flex items-center gap-2.5 text-sm dark:text-gray-500">
										<div class="pl-1">
											<span class="relative flex size-2">
												<span
													class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"
												/>
												<span class="relative inline-flex rounded-full size-2 bg-green-500" />
											</span>
										</div>
										<div class=" translate-y-[0.5px]">
											{$i18n.t('Change system_prompt and show artifacts panel')}
										</div>
									</div>
								</div>
							{/if}

							{#if atSelectedModel !== undefined}
								<div class="flex items-center justify-between w-full">
									<div class="pl-[1px] flex items-center gap-2 text-sm dark:text-gray-500">
										<img
											crossorigin="anonymous"
											alt="model profile"
											class="size-3.5 max-w-[28px] object-cover rounded-full"
											src={$models.find((model) => model.id === atSelectedModel.id)?.info?.meta
												?.profile_image_url ??
												($i18n.language === 'dg-DG' ? `/doge.png` : `/static/logoLight.svg`)}
										/>
										<div class="translate-y-[0.5px]">
											Talking to <span class=" font-medium">{atSelectedModel.name}</span>
										</div>
									</div>
									<div>
										<button
											class="flex items-center dark:text-gray-500"
											on:click={() => {
												atSelectedModel = undefined;
											}}
										>
											<XMark />
										</button>
									</div>
								</div>
							{/if}
						</div>
					{/if}

					<Commands
						bind:this={commandsElement}
						bind:prompt
						bind:files
						on:upload={(e) => {
							dispatch('upload', e.detail);
						}}
						on:select={(e) => {
							const data = e.detail;

							if (data?.type === 'model') {
								atSelectedModel = data.data;
							}

							const chatInputElement = document.getElementById('chat-input');
							chatInputElement?.focus();
						}}
					/>
				</div>
			</div>
		</div>

		<div class="transparent">
			<div
				class="{($settings?.widescreenMode ?? null)
					? 'max-w-[960px]'
					: 'max-w-[960px]'} bg-transparent mx-auto inset-x-0"
			>
				<div class="">
					<input
						bind:this={filesInputElement}
						bind:files={inputFiles}
						type="file"
						hidden
						multiple
						accept={getFileAcceptAttribute()}
						on:change={async () => {
							if (inputFiles && inputFiles.length > 0) {
								const _inputFiles = Array.from(inputFiles);
								inputFilesHandler(_inputFiles);
							} else {
								toast.error($i18n.t(`File not found.`));
							}

							filesInputElement.value = '';
						}}
					/>

					{#if recording}
						<VoiceRecording
							bind:recording
							on:cancel={async () => {
								recording = false;

								await tick();
								document.getElementById('chat-input')?.focus();
							}}
							on:confirm={async (e) => {
								const { text, filename } = e.detail;
								prompt = `${prompt}${text} `;

								recording = false;

								await tick();
								document.getElementById('chat-input')?.focus();

								if ($settings?.speechAutoSend ?? false) {
									dispatch('submit', prompt);
								}
							}}
						/>
					{:else}
						<form
							class="w-full flex gap-1.5"
							on:submit|preventDefault={() => {
								// check if selectedModels support image input
								if ($mobile) {
									document.getElementById('chat-input')?.blur();
								}
								dispatch('submit', prompt);
							}}
						>
							<div
								class="flex-1 flex flex-col relative w-full rounded-xl border-b-2 border-black/20 dark:border-white/10 transition px-1 bg-white/90 dark:bg-[#26282A] dark:text-gray-100"
								dir={$settings?.chatDirection ?? 'auto'}
							>
								{#if files.length > 0}
									<div class="mx-2 mt-2.5 -mb-1 flex items-center flex-wrap gap-2">
										{#each files as file, fileIdx}
											{#if file.isImage}
												<button
													class="relative group p-1.5 w-60 flex items-center gap-1 bg-white dark:bg-gray-850 border border-gray-50 dark:border-white/5 rounded-2xl text-left"
													type="button"
													on:click={() => {
														console.log(file);
													}}
												>
													<div class="flex items-center justify-center">
														<div
															class="w-12 h-12 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700"
														>
															<Image
																src={file.url}
																alt="input"
																imageClassName="w-full h-full object-cover"
															/>
														</div>
														{#if atSelectedModel ? visionCapableModels.length === 0 : selectedModels.length !== visionCapableModels.length}
															<Tooltip
																className="absolute top-1 left-1"
																content={$i18n.t('{{ models }}', {
																	models: [
																		...(atSelectedModel ? [atSelectedModel] : selectedModels)
																	]
																		.filter((id) => !visionCapableModels.includes(id))
																		.join(', ')
																})}
															>
																<svg
																	xmlns="http://www.w3.org/2000/svg"
																	viewBox="0 0 24 24"
																	fill="currentColor"
																	class="size-4 fill-yellow-300"
																>
																	<path
																		fill-rule="evenodd"
																		d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
																		clip-rule="evenodd"
																	/>
																</svg>
															</Tooltip>
														{/if}
													</div>
													<div class="flex flex-col justify-center -space-y-0.5 px-2.5 w-full">
														<div class="dark:text-gray-100 text-sm font-medium line-clamp-1 mb-1">
															{decodeURIComponent(file.name)}
														</div>
														<div class="flex justify-between text-gray-500 text-xs line-clamp-1">
															<span>文件</span>
															{#if file.size}
																<span class="capitalize">{(file.size / 1024).toFixed(1)} KB</span>
															{/if}
														</div>
													</div>
													<div class="absolute -top-1 -right-1">
														<button
															class="bg-white text-black border border-white rounded-full group-hover:visible invisible transition"
															type="button"
															on:click|stopPropagation={() => {
																// Clean up blob URL if it exists
																if (file.url && file.url.startsWith('blob:')) {
																	URL.revokeObjectURL(file.url);
																}
																files.splice(fileIdx, 1);
																files = files;
															}}
														>
															<svg
																xmlns="http://www.w3.org/2000/svg"
																viewBox="0 0 20 20"
																fill="currentColor"
																class="w-4 h-4"
															>
																<path
																	d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"
																/>
															</svg>
														</button>
													</div>
												</button>
											{:else}
												<FileItem
													item={file}
													name={file.name}
													type={file.type}
													size={file?.size}
													loading={file.status === 'uploading'}
													dismissible={true}
													edit={true}
													on:dismiss={async () => {
														if (file.type !== 'collection' && !file?.collection) {
															if (file.id) {
																// This will handle both file deletion and Chroma cleanup
																await deleteFileById(localStorage.token, file.id);
															}
														}

														// Remove from UI state
														files.splice(fileIdx, 1);
														files = files;
													}}
													on:click={() => {
														console.log(file);
													}}
												/>
											{/if}
										{/each}
									</div>
								{/if}

								<div class="px-2.5 relative overflow-hidden">
									{#if featureSupported && $settings?.richTextInput}
										<div
											class="scrollbar-hidden text-left bg-transparent dark:text-gray-100 outline-hidden w-full pt-3 px-1 resize-none h-fit max-h-80 overflow-auto"
											id="chat-input-container"
										>
											<RichTextInput
												bind:this={chatInputElement}
												bind:value={prompt}
												id="chat-input"
												messageInput={true}
												shiftEnter={!($settings?.ctrlEnterToSend ?? false) &&
													(!$mobile ||
														!(
															'ontouchstart' in window ||
															navigator.maxTouchPoints > 0 ||
															navigator.msMaxTouchPoints > 0
														))}
												placeholder={placeholder ? placeholder : $i18n.t('Send a Message')}
												largeTextAsFile={featureSupported && $settings?.largeTextAsFile}
												autocomplete={$config?.features?.enable_autocomplete_generation &&
													($settings?.promptAutocomplete ?? false)}
												generateAutoCompletion={async (text) => {
													if (selectedModelIds.length === 0 || !selectedModelIds.at(0)) {
														toast.error($i18n.t('Please select a model first.'));
													}

													const res = await generateAutoCompletion(
														localStorage.token,
														selectedModelIds.at(0),
														text,
														history?.currentId
															? createMessagesList(history, history.currentId)
															: null
													).catch((error) => {
														console.log(error);

														return null;
													});

													// console.log(res);
													return res;
												}}
												oncompositionstart={() => (isComposing = true)}
												oncompositionend={() => (isComposing = false)}
												on:keydown={async (e) => {
													e = e.detail.event;

													const isCtrlPressed = e.ctrlKey || e.metaKey; // metaKey is for Cmd key on Mac
													const commandsContainerElement =
														document.getElementById('commands-container');

													if (e.key === 'Escape') {
														stopResponse();
													}

													// Command/Ctrl + Shift + Enter to submit a message pair
													if (isCtrlPressed && e.key === 'Enter' && e.shiftKey) {
														e.preventDefault();
														createMessagePair(prompt);
													}

													// Check if Ctrl + R is pressed
													if (
														prompt.trim() === '' &&
														isCtrlPressed &&
														e.key.toLowerCase() === 'r'
													) {
														e.preventDefault();
														console.log('regenerate');

														const regenerateButton = [
															...document.getElementsByClassName('regenerate-response-button')
														]?.at(-1);

														regenerateButton?.click();
													}

													if (prompt.trim() === '' && e.key == 'ArrowUp') {
														e.preventDefault();

														const userMessageElement = [
															...document.getElementsByClassName('user-message')
														]?.at(-1);

														if (userMessageElement) {
															userMessageElement.scrollIntoView({ block: 'center' });
															const editButton = [
																...document.getElementsByClassName('edit-user-message-button')
															]?.at(-1);

															editButton?.click();
														}
													}

													if (commandsContainerElement) {
														if (commandsContainerElement && e.key === 'ArrowUp') {
															e.preventDefault();
															commandsElement.selectUp();

															const commandOptionButton = [
																...document.getElementsByClassName('selected-command-option-button')
															]?.at(-1);
															commandOptionButton.scrollIntoView({ block: 'center' });
														}

														if (commandsContainerElement && e.key === 'ArrowDown') {
															e.preventDefault();
															commandsElement.selectDown();

															const commandOptionButton = [
																...document.getElementsByClassName('selected-command-option-button')
															]?.at(-1);
															commandOptionButton.scrollIntoView({ block: 'center' });
														}

														if (commandsContainerElement && e.key === 'Tab') {
															e.preventDefault();

															const commandOptionButton = [
																...document.getElementsByClassName('selected-command-option-button')
															]?.at(-1);

															commandOptionButton?.click();
														}

														if (commandsContainerElement && e.key === 'Enter') {
															e.preventDefault();

															const commandOptionButton = [
																...document.getElementsByClassName('selected-command-option-button')
															]?.at(-1);

															if (commandOptionButton) {
																commandOptionButton?.click();
															} else {
																document.getElementById('send-message-button')?.click();
															}
														}
													} else {
														if (
															!$mobile ||
															!(
																'ontouchstart' in window ||
																navigator.maxTouchPoints > 0 ||
																navigator.msMaxTouchPoints > 0
															)
														) {
															if (isComposing) {
																return;
															}

															// Uses keyCode '13' for Enter key for chinese/japanese keyboards.
															//
															// Depending on the user's settings, it will send the message
															// either when Enter is pressed or when Ctrl+Enter is pressed.
															const enterPressed =
																($settings?.ctrlEnterToSend ?? false)
																	? (e.key === 'Enter' || e.keyCode === 13) && isCtrlPressed
																	: (e.key === 'Enter' || e.keyCode === 13) && !e.shiftKey;

															if (enterPressed) {
																e.preventDefault();
																if (prompt.trim() !== '') {
																	dispatch('submit', prompt);
																}
															}
														}
													}

													if (e.key === 'Escape') {
														console.log('Escape');
														atSelectedModel = undefined;
														selectedToolIds = [];
														webSearchEnabled = false;
														imageGenerationEnabled = false;
													}
												}}
												on:paste={async (e) => {
													e = e.detail.event;

													const clipboardData = e.clipboardData || window.clipboardData;

													if (clipboardData && clipboardData.items) {
														let hasHandledFile = false;

														// 首先检查是否有文件或图片
														for (let i = 0; i < clipboardData.items.length; i++) {
															const item = clipboardData.items[i];
															if (item.type.indexOf('image') !== -1 || item.kind === 'file') {
																// 如果有文件或图片，立即阻止默认行为
																e.preventDefault();
																break;
															}
														}

														for (const item of clipboardData.items) {
															if (item.type.indexOf('image') !== -1) {
																// 直接处理图片
																const blob = item.getAsFile();
																if (blob) {
																	// Create a File object from the blob
																	const file = new File([blob], `pasted_image_${Date.now()}.png`, {
																		type: blob.type
																	});
																	// Upload the file using the file upload system
																	await uploadFileHandler(file, false);
																	hasHandledFile = true;
																}
															} else if (item.kind === 'file') {
																const blob = item.getAsFile();
																if (blob) {
																	// Preserve the original filename if available
																	let fileName = blob.name;

																	// If no filename is available, generate one based on mime type
																	if (!fileName || fileName === '') {
																		// Get appropriate extension based on MIME type
																		const mimeType = blob.type || 'application/octet-stream';
																		const fileExt = mimeType.split('/').pop() || 'bin';

																		// Generate a timestamp-based filename with the correct extension
																		fileName = `pasted_file_${Date.now()}.${fileExt}`;
																	}

																	// Create a File object from the blob with the original or generated filename
																	const file = new File([blob], fileName, {
																		type: blob.type || 'application/octet-stream'
																	});

																	// Upload the file using the file upload system
																	await uploadFileHandler(file);
																	hasHandledFile = true;
																}
															} else if (item.type === 'text/plain' && !hasHandledFile) {
																if (featureSupported && $settings?.largeTextAsFile) {
																	const text = clipboardData.getData('text/plain');

																	if (text.length > PASTED_TEXT_CHARACTER_LIMIT) {
																		continue;
																	}
																}
															}
														}
													}
												}}
											/>
										</div>
									{:else}
										<InputFlags
											bind:flags
											onResize={(v) => (flagsIndent = v)}
											scrollHeight={textAreaScrollHeight}
											closable={!showMessages ||
												(typeof selectedGroup === 'object' &&
													selectedGroup?.force_selected === true)}
											onClose={() => {
												flags = [];
												features = [];
											}}
										/>
										<textarea
											id="chat-input"
											dir="auto"
											rows={$mobile ? ($_user?.role === 'guest' ? '1' : '2') : '3'}
											bind:this={chatInputElement}
											style="text-indent: {flagsIndent}px;"
											class="scrollbar-hidden bg-transparent dark:text-gray-100 outline-hidden w-full pt-3 px-1 resize-none dark:placeholder:text-white/40 text-sm"
											placeholder={placeholder ? placeholder : $i18n.t('Send a Message')}
											bind:value={prompt}
											on:compositionstart={() => (isComposing = true)}
											on:compositionend={() => (isComposing = false)}
											on:keydown={async (e) => {
												const isCtrlPressed = e.ctrlKey || e.metaKey; // metaKey is for Cmd key on Mac

												const commandsContainerElement =
													document.getElementById('commands-container');

												if (e.key === 'Escape') {
													stopResponse();
												}

												if (e.key === 'Backspace' && !e.repeat && !prompt && !$chatId) {
													if (selectedGroup?.force_selected) return;
													flags = [];
													features = [];
													selectedMCPServers = [];
													disabledMCPServerMap = {};
													resetSuggestions?.();
												}

												// if (e.key === 'Backspace' && !flags.length && !prompt) {
												// 	features = [];
												// 	selectedMCPServers = [];
												// 	disabledMCPServerMap = {};
												// 	resetSuggestions?.();
												// }

												// Command/Ctrl + Shift + Enter to submit a message pair
												if (isCtrlPressed && e.key === 'Enter' && e.shiftKey) {
													e.preventDefault();
													createMessagePair(prompt);
												}

												// Check if Ctrl + R is pressed
												if (prompt.trim() === '' && isCtrlPressed && e.key.toLowerCase() === 'r') {
													e.preventDefault();
													console.log('regenerate');

													const regenerateButton = [
														...document.getElementsByClassName('regenerate-response-button')
													]?.at(-1);

													regenerateButton?.click();
												}

												if (prompt.trim() === '' && e.key == 'ArrowUp') {
													e.preventDefault();

													const userMessageElement = [
														...document.getElementsByClassName('user-message')
													]?.at(-1);

													const editButton = [
														...document.getElementsByClassName('edit-user-message-button')
													]?.at(-1);

													console.log(userMessageElement);

													userMessageElement.scrollIntoView({ block: 'center' });
													editButton?.click();
												}

												if (commandsContainerElement) {
													if (commandsContainerElement && e.key === 'ArrowUp') {
														e.preventDefault();
														commandsElement.selectUp();

														const commandOptionButton = [
															...document.getElementsByClassName('selected-command-option-button')
														]?.at(-1);
														commandOptionButton.scrollIntoView({ block: 'center' });
													}

													if (commandsContainerElement && e.key === 'ArrowDown') {
														e.preventDefault();
														commandsElement.selectDown();

														const commandOptionButton = [
															...document.getElementsByClassName('selected-command-option-button')
														]?.at(-1);
														commandOptionButton.scrollIntoView({ block: 'center' });
													}

													if (commandsContainerElement && e.key === 'Enter') {
														e.preventDefault();

														const commandOptionButton = [
															...document.getElementsByClassName('selected-command-option-button')
														]?.at(-1);

														if (e.shiftKey) {
															prompt = `${prompt}\n`;
														} else if (commandOptionButton) {
															commandOptionButton?.click();
														} else {
															document.getElementById('send-message-button')?.click();
														}
													}

													if (commandsContainerElement && e.key === 'Tab') {
														e.preventDefault();

														const commandOptionButton = [
															...document.getElementsByClassName('selected-command-option-button')
														]?.at(-1);

														commandOptionButton?.click();
													}
												} else {
													if (
														!$mobile ||
														!(
															'ontouchstart' in window ||
															navigator.maxTouchPoints > 0 ||
															navigator.msMaxTouchPoints > 0
														)
													) {
														if (isComposing) {
															return;
														}

														// Prevent Enter key from creating a new line
														const isCtrlPressed = e.ctrlKey || e.metaKey;
														const enterPressed =
															($settings?.ctrlEnterToSend ?? false)
																? (e.key === 'Enter' || e.keyCode === 13) && isCtrlPressed
																: (e.key === 'Enter' || e.keyCode === 13) && !e.shiftKey;

														// console.log('Enter pressed:', enterPressed);

														if (enterPressed) {
															e.preventDefault();
														}

														// Submit the prompt when Enter key is pressed
														if (prompt.trim() !== '' && enterPressed) {
															dispatch('submit', prompt);
														}
													}
												}

												if (e.key === 'Tab') {
													const words = extractCurlyBraceWords(prompt);

													if (words.length > 0) {
														const word = words.at(0);
														const fullPrompt = prompt;

														prompt = prompt.substring(0, word?.endIndex + 1);
														await tick();

														e.target.scrollTop = e.target.scrollHeight;
														prompt = fullPrompt;
														await tick();

														e.preventDefault();
														e.target.setSelectionRange(word?.startIndex, word.endIndex + 1);
													}

													e.target.style.height = '';
													e.target.style.height = Math.min(e.target.scrollHeight, 320) + 'px';
												}

												if (e.key === 'Escape') {
													console.log('Escape');
													atSelectedModel = undefined;
													selectedToolIds = [];
													webSearchEnabled = false;
													imageGenerationEnabled = false;
												}
											}}
											on:input={async (e) => {
												if (!emittedInputEvent) {
													emittedInputEvent = true;
													trackButtonClick('chatbox', 'chatbox_click');
												}
												e.target.style.height = '';
												e.target.style.height = Math.min(e.target.scrollHeight, 320) + 'px';
											}}
											on:focus={async (e) => {
												e.target.style.height = '';
												e.target.style.height = Math.min(e.target.scrollHeight, 320) + 'px';
											}}
											on:paste={async (e) => {
												const clipboardData = e.clipboardData || window.clipboardData;

												if (clipboardData && clipboardData.items) {
													let hasHandledFile = false;

													// 首先检查是否有文件或图片
													for (let i = 0; i < clipboardData.items.length; i++) {
														const item = clipboardData.items[i];
														if (item.type.indexOf('image') !== -1 || item.kind === 'file') {
															// 如果有文件或图片，立即阻止默认行为
															e.preventDefault();
															break;
														}
													}

													for (const item of clipboardData.items) {
														if (item.type.indexOf('image') !== -1) {
															// 直接处理图片
															const blob = item.getAsFile();
															if (blob) {
																// Create a File object from the blob
																const file = new File([blob], `pasted_image_${Date.now()}.png`, {
																	type: blob.type
																});
																// Upload the file using the file upload system
																await uploadFileHandler(file, false);
																hasHandledFile = true;
															}
														} else if (item.kind === 'file') {
															const blob = item.getAsFile();
															if (blob) {
																// Preserve the original filename if available
																let fileName = blob.name;

																// If no filename is available, generate one based on mime type
																if (!fileName || fileName === '') {
																	// Get appropriate extension based on MIME type
																	const mimeType = blob.type || 'application/octet-stream';
																	const fileExt = mimeType.split('/').pop() || 'bin';

																	// Generate a timestamp-based filename with the correct extension
																	fileName = `pasted_file_${Date.now()}.${fileExt}`;
																}

																// Create a File object from the blob with the original or generated filename
																const file = new File([blob], fileName, {
																	type: blob.type || 'application/octet-stream'
																});

																// Upload the file using the file upload system
																await uploadFileHandler(file);
																hasHandledFile = true;
															}
														} else if (item.type === 'text/plain' && !hasHandledFile) {
															// 如果已经处理了文件，不要再处理文本
															const text = clipboardData.getData('text/plain');
															if (text.length > PASTED_TEXT_CHARACTER_LIMIT) {
																continue;
															}

															// 这里不需要阻止默认行为，让文本正常粘贴到输入框

															if (featureSupported && $settings?.largeTextAsFile) {
																const text = clipboardData.getData('text/plain');

																if (text.length > PASTED_TEXT_CHARACTER_LIMIT) {
																	continue;
																	// e.preventDefault();
																	// const blob = new Blob([text], { type: 'text/plain' });
																	// const file = new File([blob], `Pasted_Text_${Date.now()}.txt`, {
																	// 	type: 'text/plain'
																	// });

																	// await uploadFileHandler(file, true);
																}
															}
														}
													}
												}
											}}
											on:scroll={(e) => {
												textAreaScrollHeight = e.target.scrollTop;
											}}
										/>
									{/if}
								</div>

								<div
									class=" flex justify-between items-center mt-1.5 mb-2.5 mx-0.5 max-w-full"
									dir="ltr"
								>
									<div class="self-end flex items-center flex-1 max-w-[80%] gap-1.5 ml-1">
										{#if true}
											<InputMenu
												uploadFilesHandler={() => {
													filesInputElement.click();
												}}
											>
												<button
													class="border rounded-lg border-black/10 bg-transparent transition p-1.5 outline-hidden focus:outline-hidden {fileQACapableModels.length ===
													0
														? 'text-gray-400 dark:text-gray-600 cursor-not-allowed opacity-50'
														: 'hover:bg-gray-100 text-gray-800 dark:text-white dark:hover:bg-gray-800'}"
													type="button"
													aria-label="More"
													disabled={fileQACapableModels.length === 0}
												>
													<svg
														xmlns="http://www.w3.org/2000/svg"
														viewBox="0 0 20 20"
														fill="currentColor"
														class="size-5"
													>
														<path
															d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z"
														/>
													</svg>
												</button>
											</InputMenu>
										{/if}

										{#if selectedModelIds.includes('deep-research')}
											<Tooltip
												content={$i18n.t(
													'In deep research mode, network search is enabled by default.'
												)}
											>
												<button
													class="px-2 @xl:px-3 py-1.5 flex gap-1.5 items-center text-sm rounded-lg transition-colors duration-300 focus:outline-hidden cursor-default max-w-full overflow-hidden text-[#0068E0]"
												>
													<GlobeAlt className="size-5" strokeWidth="1.75" />
													<span
														class="hidden @xl:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px]"
														>{$i18n.t('Web Search')}</span
													>
												</button>
											</Tooltip>
										{/if}
										<div class="flex gap-[8px] items-center overflow-x-auto scrollbar-none flex-1">
											{#if toolServers.length + selectedToolIds.length > 0}
												<Tooltip
													content={$i18n.t('{{COUNT}} Available Tools', {
														COUNT: toolServers.length + selectedToolIds.length
													})}
												>
													<button
														class="translate-y-[0.5px] flex gap-1 items-center text-gray-600 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 rounded-lg p-1 self-center transition"
														aria-label="Available Tools"
														type="button"
														on:click={() => {
															showTools = !showTools;
														}}
													>
														<Wrench className="size-4" strokeWidth="1.75" />

														<span class="text-sm font-medium text-gray-600 dark:text-gray-300">
															{toolServers.length + selectedToolIds.length}
														</span>
													</button>
												</Tooltip>
											{/if}

											{#if $_user && (previewModeCapableModels.length > 0 || showPreviewButton)}
												<Tooltip
													content={$i18n.t('Auto-render HTML and SVG code as interactive pages')}
													placement="top"
												>
													<button
														on:click|preventDefault={() => {
															autoArtifacts.set(!$autoArtifacts);
															trackButtonClick(
																'chatbox',
																'chatbox_click',
																`artifacts_${$autoArtifacts ? 'on' : 'off'}`
															);
														}}
														type="button"
														class="px-2 @xl:px-3 py-1.5 flex gap-1.5 items-center text-sm rounded-lg border transition-colors duration-300 focus:outline-hidden max-w-full overflow-hidden {$autoArtifacts
															? 'bg-[#DAEEFF] dark:bg-transparent border-[#0068E00A] dark:border-[#0068E04A] text-[#0068E0]'
															: 'bg-transparent  dark:text-gray-300 border-[#E5E5E5] dark:border-[#3C3E3F] hover:bg-gray-100 dark:hover:bg-gray-800 '}"
													>
														<Cube className=" size-5" strokeWidth="1.75" />
														<span
															class="hidden @xl:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px] mr-0.5"
															>{$i18n.t('Artifacts')}</span
														>
													</button>
												</Tooltip>
											{/if}
											{#if webSearchCapableModels.length > 0}
												<Tooltip content={$i18n.t('Web Search')} placement="top">
													<button
														on:click|preventDefault={() => {
															webSearchEnabled = !webSearchEnabled;
															trackButtonClick(
																'chatbox',
																'chatbox_click',
																`search_${webSearchEnabled ? 'on' : 'off'}`
															);
														}}
														type="button"
														class="px-2 @xl:px-3 py-1.5 flex gap-1.5 items-center text-sm rounded-lg border transition-colors duration-300 focus:outline-hidden max-w-full overflow-hidden {webSearchEnabled ||
														($settings?.webSearch ?? false) === 'always'
															? 'bg-[#DAEEFF] dark:bg-transparent border-[#0068E00A] dark:border-[#0068E04A] text-[#0068E0]'
															: 'bg-transparent  dark:text-gray-300 border-[#E5E5E5] dark:border-[#3C3E3F] hover:bg-gray-100 dark:hover:bg-gray-800 '}"
													>
														<GlobeAlt className="size-5" strokeWidth="1.75" />
														<span
															class="hidden @xl:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px]"
															>{$i18n.t('Web Search')}</span
														>
													</button>
												</Tooltip>
											{/if}
											{#if mcpCapableModels.length > 0 && $config?.features?.enable_mcp && $config?.mcp_servers?.length > 0 && hasMCPServers}
												<ToolSelector
													bind:selectedServers={selectedMCPServers}
													{availableMCPServers}
													disabledServersMap={disabledMCPServerMap}
												>
													<button
														on:click|preventDefault={() => {
															trackButtonClick('chatbox', 'chatbox_click', 'mcp');
														}}
														type="button"
														disabled={!($config?.features?.enable_mcp ?? false)}
														class="px-2 @xl:px-3 py-1.5 flex gap-1.5 items-center text-sm rounded-lg border transition-colors duration-300 focus:outline-hidden max-w-full overflow-hidden bg-transparent dark:text-gray-300 border-[#E5E5E5] dark:border-[#3C3E3F] hover:bg-gray-100 dark:hover:bg-gray-800"
													>
														<Hamburger className=" size-4" strokeWidth="2" />
														<span
															class="hidden @sm:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px] mr-0.5 font-medium"
															>{selectedMCPServers.length || ''} {$i18n.t('Tools')}</span
														>
													</button>
												</ToolSelector>
											{/if}
											{#if $config?.features?.enable_image_generation && ($_user.role === 'admin' || $_user?.permissions?.features?.image_generation)}
												<Tooltip content={$i18n.t('Generate an image')} placement="top">
													<button
														on:click|preventDefault={() =>
															(imageGenerationEnabled = !imageGenerationEnabled)}
														type="button"
														class="px-1.5 @xl:px-2.5 py-1.5 flex gap-1.5 items-center text-sm rounded-full font-medium transition-colors duration-300 focus:outline-hidden max-w-full overflow-hidden {imageGenerationEnabled
															? 'bg-gray-100 dark:bg-gray-500/20 text-gray-600 dark:text-gray-400'
															: 'bg-transparent text-gray-600 dark:text-gray-300 border-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 '}"
													>
														<Photo className="size-5" strokeWidth="1.75" />
														<span
															class="hidden @xl:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px]"
															>{$i18n.t('Image')}</span
														>
													</button>
												</Tooltip>
											{/if}

											{#if featureSupported && $config?.features?.enable_code_interpreter && ($_user.role === 'admin' || $_user?.permissions?.features?.code_interpreter)}
												<Tooltip content={$i18n.t('Execute code for analysis')} placement="top">
													<button
														on:click|preventDefault={() =>
															(codeInterpreterEnabled = !codeInterpreterEnabled)}
														type="button"
														class="px-1.5 @xl:px-2.5 py-1.5 flex gap-1.5 items-center text-sm rounded-full font-medium transition-colors duration-300 focus:outline-hidden max-w-full overflow-hidden {codeInterpreterEnabled
															? 'bg-gray-100 dark:bg-gray-500/20 text-gray-600 dark:text-gray-400'
															: 'bg-transparent text-gray-600 dark:text-gray-300 border-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 '}"
													>
														<CommandLine className="size-5" strokeWidth="1.75" />
														<span
															class="hidden @xl:block whitespace-nowrap overflow-hidden text-ellipsis translate-y-[0.5px]"
															>{$i18n.t('Code Interpreter')}</span
														>
													</button>
												</Tooltip>
											{/if}
										</div>
									</div>

									<div class="self-end flex space-x-1 mr-1 shrink-0">
										{#if featureSupported && (!history?.currentId || history.messages[history.currentId]?.done == true)}
											<Tooltip content={$i18n.t('Record voice')}>
												<button
													id="voice-input-button"
													class=" text-gray-600 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 transition rounded-full p-1.5 mr-0.5 self-center"
													type="button"
													on:click={async () => {
														try {
															let stream = await navigator.mediaDevices
																.getUserMedia({ audio: true })
																.catch(function (err) {
																	toast.error(
																		$i18n.t(
																			`Permission denied when accessing microphone: {{error}}`,
																			{
																				error: err
																			}
																		)
																	);
																	return null;
																});

															if (stream) {
																recording = true;
																const tracks = stream.getTracks();
																tracks.forEach((track) => track.stop());
															}
															stream = null;
														} catch {
															toast.error($i18n.t('Permission denied when accessing microphone'));
														}
													}}
													aria-label="Voice Input"
												>
													<svg
														xmlns="http://www.w3.org/2000/svg"
														viewBox="0 0 20 20"
														fill="currentColor"
														class="w-5 h-5 translate-y-[0.5px]"
													>
														<path d="M7 4a3 3 0 016 0v6a3 3 0 11-6 0V4z" />
														<path
															d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5h-1.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z"
														/>
													</svg>
												</button>
											</Tooltip>
										{/if}

										{#if !history.currentId || history.messages[history.currentId]?.done == true}
											{#if featureSupported && prompt.trim() === ''}
												<div class=" flex items-center">
													<Tooltip content={$i18n.t('Call')}>
														<button
															class=" {webSearchEnabled ||
															($settings?.webSearch ?? false) === 'always'
																? 'bg-blue-500 text-white hover:bg-blue-400 '
																: 'bg-black text-white hover:bg-gray-900 dark:bg-white dark:text-black dark:hover:bg-gray-100'} transition rounded-full p-1.5 self-center"
															type="button"
															on:click={async () => {
																if (selectedModels.length > 1) {
																	toast.error($i18n.t('Select only one model to call'));

																	return;
																}

																if ($config.audio.stt.engine === 'web') {
																	toast.error(
																		$i18n.t(
																			'Call feature is not supported when using Web STT engine'
																		)
																	);

																	return;
																}
																// check if user has access to getUserMedia
																try {
																	let stream = await navigator.mediaDevices.getUserMedia({
																		audio: true
																	});
																	// If the user grants the permission, proceed to show the call overlay

																	if (stream) {
																		const tracks = stream.getTracks();
																		tracks.forEach((track) => track.stop());
																	}

																	stream = null;

																	if ($settings.audio?.tts?.engine === 'browser-kokoro') {
																		// If the user has not initialized the TTS worker, initialize it
																		if (!$TTSWorker) {
																			await TTSWorker.set(
																				new KokoroWorker({
																					dtype: $settings.audio?.tts?.engineConfig?.dtype ?? 'fp32'
																				})
																			);

																			await $TTSWorker.init();
																		}
																	}

																	// showCallOverlay.set(true);
																	// showControls.set(true);
																} catch (err) {
																	// If the user denies the permission or an error occurs, show an error message
																	toast.error(
																		$i18n.t('Permission denied when accessing media devices')
																	);
																}
															}}
															aria-label="Call"
														>
															<Headphone className="size-5" />
														</button>
													</Tooltip>
												</div>
											{:else}
												<div class=" flex items-center">
													<Tooltip content={$i18n.t('Send message')}>
														<button
															id="send-message-button"
															class="sendMessageButton {prompt.trim() !== ''
																? 'button-gradient dark:bg-[#484A58] dark:hover:bg-[#5A5C68] text-white  dark:text-white'
																: 'text-white bg-gray-200 dark:text-white/40 dark:bg-gray-700 disabled'} transition rounded-lg p-1.5 self-center"
															type="submit"
															disabled={prompt.trim() === ''}
														>
															<SendArrorIcon className="size-4" />
														</button>
													</Tooltip>
												</div>
											{/if}
										{:else}
											<div class="">
												<Tooltip content={$i18n.t('Stop')}>
													<button
														class="flex justify-center items-center bg-white hover:bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-800 transition rounded-full"
														on:click={() => {
															stopResponse();
														}}
													>
														<svg
															xmlns="http://www.w3.org/2000/svg"
															viewBox="0 0 24 24"
															fill="currentColor"
															class="size-7"
														>
															<path
																fill-rule="evenodd"
																d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm6-2.438c0-.724.588-1.312 1.313-1.312h4.874c.725 0 1.313.588 1.313 1.313v4.874c0 .725-.588 1.313-1.313 1.313H9.564a1.312 1.312 0 01-1.313-1.313V9.564z"
																clip-rule="evenodd"
															/>
														</svg>
													</button>
												</Tooltip>
											</div>
										{/if}
									</div>
								</div>
							</div>
						</form>
					{/if}
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- <MCPServerModal
	bind:show={showMCPServerModal}
	bind:selectedServers={selectedMCPServers}
	bind:disabledServersMap={disabledMCPServerMap}
	on:close={() => {
		showMCPServerModal = false;
	}}
/> -->
