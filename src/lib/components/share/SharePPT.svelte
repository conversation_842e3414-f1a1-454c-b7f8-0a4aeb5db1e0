<script lang="ts">
	import { chatId, showControls, eventBus } from '$lib/stores';
	import Spinner from '../common/Spinner.svelte';
	import XMark from '../icons/XMark.svelte';
	import PPTListRender from '$lib/components/chat/Messages/Markdown/GLMBlockRender/PPTListRender.svelte';
	import { getSharedPPT } from '$lib/apis/mcp';
	import { getContext, onDestroy, onMount } from 'svelte';
	import type { PPTPages } from '$lib/types';
	import FaceSmile from '../icons/FaceSmile.svelte';
	import { EventBus } from '$lib/constants';
	import type { Writable } from 'svelte/store';
	import type { i18n } from 'i18next';
	import Divider from '../common/Divider.svelte';
	import SlidesIcon from '../icons/SlidesIcon.svelte';
	import { DropdownMenu } from 'bits-ui';
	import { mcpData } from '$lib/stores/mcp';
	import dayjs from 'dayjs';
	import utc from 'dayjs/plugin/utc';
	import timezone from 'dayjs/plugin/timezone';
	import relativeTime from 'dayjs/plugin/relativeTime';
	import CheckBoxIndicator from '../icons/CheckBoxIndicator.svelte';
	import SildeVersionSelector from '../chat/SildeVersionSelector.svelte';
	import { getUserTimezone } from '$lib/utils';

	dayjs.extend(relativeTime);
	dayjs.extend(utc);
	dayjs.extend(timezone);
	const i18n: Writable<i18n> = getContext('i18n');

	let pptPages: PPTPages | null = null;
	let versions: { version: number; created_at: number }[] = [];
	let showVersionMenu = false;
	let selectedVersion = -1;

	let loading = false;
	let abortController: AbortController | null = null;

	const init = async (version?: number) => {
		if (loading) return;
		loading = true;
		abortController = new AbortController();
		try {
			const res = await getSharedPPT({
				chatId: $chatId,
				version: version ?? $mcpData?.ppt?.version
			});

			versions = res.versions ?? [];

			if (res.slides) {
				pptPages = res.slides;
				if (pptPages && pptPages.pages) {
					pptPages.pages = pptPages.pages.filter((page) => typeof page === 'string');
					selectedVersion = version ?? (versions.length > 0 ? versions[0].version : -1);
				}
			}
		} catch (error: any) {
			console.error(error.message);
		} finally {
			loading = false;
		}
	};

	// 创建一个类型兼容的事件处理函数
	const handlePPTUpdate = () => {
		init($mcpData?.ppt?.version);
	};

	onMount(() => {
		init($mcpData?.ppt?.version);
		$eventBus.on(EventBus.PPT_UPDATE, handlePPTUpdate); // 监听ppt更新事件
	});

	onDestroy(() => {
		if (abortController) {
			abortController.abort();
			$eventBus.off(EventBus.PPT_UPDATE, handlePPTUpdate);
			pptPages = null;
		}
	});
</script>

<div class=" pptPreviewer w-full h-full relative flex flex-col bg-white dark:bg-[#26282A]">
	<div
		class=" pptPreviewerHeadBar max-w-full flex justify-between overflow-hidden items-center-safe py-3.5 px-5 border-b-1 border-black/10"
	>
		<div
			class="flex-1 max-w-full overflow-hidden pointer-events-none flex gap-2 items-center justify-start whitespace-nowrap font-semibold"
		>
			<SlidesIcon className="size-5 inline" />
			<SildeVersionSelector
				title={pptPages?.title ?? ''}
				{versions}
				bind:selectedVersion
				bind:showVersionMenu
				hidden={selectedVersion === -1}
				onVersionSelect={async (version) => await init(version)}
			/>
		</div>

		<div class=" shrink-0 flex items-center gap-4 justify-end">
			<Divider className="shrink-0 h-6" orientation="vertical" />
			<button
				class="self-center pointer-events-auto p-1 rounded bg-white dark:bg-gray-850 hover:bg-black/5 dark:hover:bg-white/5"
				on:click={() => {
					showControls.set('');
				}}
			>
				<XMark className="size-5 text-gray-900 dark:text-white" />
			</button>
		</div>
	</div>
	{#if loading && !pptPages?.pages.length}
		<div class=" flex flex-1 justify-center items-center gap-2">
			<Spinner className="size-6" />
			<span>{$i18n.t('loading...')}</span>
		</div>
	{:else if !pptPages?.pages.length}
		<div class=" flex flex-1 flex-col justify-center items-center gap-2">
			<FaceSmile className="size-6" />
			<div>{$i18n.t('No PPT found')}</div>
		</div>
	{:else}
		<div class=" mcpSidePanelContent flex-1 relative overflow-x-hidden overflow-y-auto p-4">
			<PPTListRender pptRawList={pptPages.pages} editable={false} />
		</div>
	{/if}
</div>
