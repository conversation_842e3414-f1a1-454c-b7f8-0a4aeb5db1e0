# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
- **When using file-related tools, if token limit errors occur, switch to reading in chunks of 1k lines. Do not hesitate to use context - if necessary, you must read the entire file**

## Project Overview

Open WebUI is a feature-rich, self-hosted AI platform that supports various LLM runners (Ollama, OpenAI-compatible APIs) with built-in RAG capabilities. The project consists of a FastAPI backend and SvelteKit frontend.

## Essential Development Commands

### Running the Application

```bash
# Frontend development (runs on http://localhost:5173)
npm install
npm run dev

# Backend development (runs on http://localhost:8080)
cd backend
pip install -r requirements.txt
./dev.sh

# Full stack with <PERSON><PERSON>
docker-compose up -d  # Access at http://localhost:3000
```

### Building and Testing

```bash
# Frontend
npm run build        # Production build
npm run lint         # Run all linters (frontend, types, backend)
npm run format       # Format frontend code
npm run format:backend # Format Python code with black
npm run test:frontend # Run frontend tests
npm run cy:open      # Open Cypress E2E testing interface

# Backend
cd backend
pytest              # Run Python tests
ruff check .        # Python linting
black .             # Python formatting
```

### Running Single Tests

```bash
# Frontend specific test
npx vitest run path/to/test.spec.ts

# Backend specific test
cd backend
pytest path/to/test.py -v

# E2E tests
npx cypress run --spec cypress/e2e/specific-test.cy.ts
```

### Docker Development Workflow

```bash
# Advanced compose setup with options
./run-compose.sh --enable-gpu --enable-api --webui[port=3000]

# Makefile shortcuts
make install        # Docker compose up
make update         # Update models, pull code, rebuild
make start/stop     # Control services
make remove         # Interactive cleanup

# Memory analysis (backend debugging)
cd backend
./memtrace.sh       # Generate memory flamegraph reports
```

## High-Level Architecture

### Frontend Structure
- **Routes**: SvelteKit file-based routing in `src/routes/`
- **Components**: Feature-based organization in `src/lib/components/`
- **API Clients**: Centralized in `src/lib/apis/` with consistent patterns
- **State Management**: Svelte stores in `src/lib/stores/`
- **Real-time**: Socket.io integration for chat functionality

### Backend Architecture
- **API Layer**: FastAPI routers in `backend/open_webui/routers/`
- **Database**: SQLAlchemy models with migration support via Alembic
- **Authentication**: JWT-based with role-based access control
- **RAG System**: Pluggable vector database support (Chroma, Milvus, Qdrant, etc.)
- **Multi-Model Support**: Unified interface for various LLM providers through adapters
- **MCP Integration**: Model Context Protocol support for tool calling and external integrations

### Key Integration Points
1. **Frontend-Backend Communication**: 
   - REST API calls through centralized client functions
   - WebSocket for real-time chat streaming
   - Authentication tokens in HTTP headers

2. **Model Integration**:
   - OpenAI-compatible endpoint proxy
   - Ollama integration for local models
   - Pipeline system for custom model logic

3. **Storage Systems**:
   - File uploads stored in `backend/data/uploads/`
   - Vector embeddings in configured vector DB
   - User data in SQL database

4. **MCP Integration**:
   - Tool configuration in `backend/data/mcp.yaml`
   - MCP servers and tools in `backend/open_webui/mcp/`
   - Frontend MCP UI components in `src/lib/components/chat/`

### Development Patterns

1. **API Endpoints**: Follow RESTful conventions with consistent error handling
2. **Component Structure**: Use composition with clear prop interfaces
3. **Database Operations**: Use SQLAlchemy ORM with proper session management
4. **Error Handling**: Centralized error codes and consistent API responses
5. **Authentication**: Check user permissions at router level using dependencies

## Important Configuration

- **Environment Variables**: Configure in `.env` file (see `backend/open_webui/config.py` for all options)
- **Database**: Default SQLite, configurable to PostgreSQL/MySQL via `DATABASE_URL`
- **Vector DB**: Default Chroma, configurable via `VECTOR_DB` environment variable
- **Model Endpoints**: Configure via admin interface or environment variables

## Development Notes

- **API Testing**: FastAPI automatic docs at `http://localhost:8080/docs` when backend is running
- **Frontend Routing**: SvelteKit file-based routing with layout hierarchy
- **Component Testing**: Use `@testing-library/svelte` patterns for component tests
- **Type Safety**: TypeScript throughout frontend with strict type checking enabled
- **Linting**: 
  - Python: Use `ruff check .` for Python code and ignore type errors
  - Frontend: Use `npm run lint` for frontend code
  - Note: This project does not use strict TypeScript or Python type checking - you can ignore related linting errors
- **Tools**: When working with unfamiliar libraries, prioritize using the context7 tool for library documentation reading
- **NEVER create mock data or simplified components** unless explicitly told to do so

- **NEVER replace existing complex components with simplified versions** - always fix the actual problem

- **ALWAYS work with the existing codebase** - do not create new simplified alternatives

- **ALWAYS find and fix the root cause** of issues instead of creating workarounds

- **When debugging issues, focus on fixing the existing implementation, not replacing it**

- **When something doesn't work, debug and fix it - don't start over with a simple version**

- **Use uv to manage Python's virtual environment. The Python environment is located in the backend/.venv directory. Do not use pip to install dependencies. Use uv to install dependencies**
- **forbid to auto format or lint the code**

## Key System Components

### Middleware Architecture (`backend/open_webui/utils/middleware.py`)

The middleware handles the complete chat request/response lifecycle:

1. **Request Processing Pipeline**:
   - `process_chat_payload()`: Handles preprocessing, tool detection, MCP integration
   - `apply_params_to_form_data()`: Applies model-specific parameters
   - `process_pipeline_inlet_filter()`: Runs custom pipeline filters

2. **Feature Handlers**:
   - `chat_web_search_handler()`: Processes web search requests
   - `chat_image_generation_handler()`: Handles image generation
   - `chat_completion_tools_handler()`: Manages function calling
   - `chat_completion_files_handler()`: Processes RAG file operations

3. **Streaming Response Processing**:
   - `stream_body_handler_zero()`: Handles custom Zero/DR model responses with TagStreamParser
   - `stream_body_handler()`: Processes standard OpenAI-compatible streaming
   - Supports multiple content block types: text, reasoning, tool_calls, tool_result, code_interpreter

4. **Content Block System**:
   ```python
   content_blocks = [
       {"type": "text", "content": "..."},
       {"type": "reasoning", "start_tag": "think", "end_tag": "/think", "content": "..."},
       {"type": "tool_calls", "content": [...], "results": [...]},
       {"type": "tool_result", "data": {...}},
   ]
   ```

### PPT Integration Event Flow

PPT tools (`create_slide`, `add_slide`, `update_slide`) follow this event sequence:

1. **Tool Detection**: When PPT tools are detected in model response
2. **Connection Setup**: WebSocket connection to PPT server via `connect_ppt_server()`
3. **Streaming Parameters**: Incremental parameter transmission with heartbeat events
4. **Progress Updates**: Real-time status updates via MCP progress callbacks
5. **Result Processing**: Final PPT data including preview information
6. **UI Serialization**: Format PPT data for frontend display
7. **Connection Cleanup**: Close WebSocket connections on completion/cancellation

Event types sent to frontend:
- `chat:completion` with tool call status
- `conn:heartbeat` for connection keepalive
- Progressive content updates with PPT metadata

### MCP (Model Context Protocol) Integration

- **Configuration**: `backend/data/mcp.yaml` defines available MCP servers
- **Tool Processing**: `backend/open_webui/mcp/tools.py` handles MCP tool execution
- **UI Components**: `backend/open_webui/mcp/ui.py` formats tool calls for display
- **Streaming Support**: Real-time tool execution with progress callbacks
- **Server Management**: Automatic connection/disconnection handling

### Performance Monitoring

Prometheus metrics tracked in middleware:
- `CHAT_STREAM_TTFT_SECONDS`: Time to first token
- `CHAT_STREAM_TOKENS_PER_SECOND`: Token generation rate
- `CHAT_STREAM_PROCESS_TIME`: Total processing time
- `CHAT_STREAM_MODEL_NO_REPLY_TOKEN`: Models with no response
- `CHAT_STREAM_SEND_TOKEN_PACK`: Streaming packet counts

## Debugging Guidelines

### Stream Processing Debug

1. **Enable Debug Logging**:
   ```python
   log.setLevel(logging.DEBUG)
   ```

2. **Common Issues**:
   - Content block parsing errors: Check `TagStreamParser` in custom models
   - Tool execution failures: Verify MCP server connectivity
   - Streaming interruptions: Monitor WebSocket connection stability

3. **Debug Checkpoints**:
   - `[Request Timeline]` logs mark key processing stages
   - Content block transitions in `tag_content_handler()`
   - Tool execution progress in MCP callbacks

### Frontend-Backend Communication

1. **SSE Event Structure**:
   ```json
   {
     "type": "chat:completion",
     "data": {
       "content": "formatted_content",
       "message_id": "uuid",
       "done": false
     }
   }
   ```

2. **Tool Call Format**:
   ```json
   {
     "type": "mcp",
     "data": {
       "metadata": {
         "name": "tool_name",
         "arguments": "{}",
         "result": "tool_output"
       },
       "ppt": {"name": "presentation", "show": true}
     }
   }
   ```

### Common Development Patterns

1. **Adding New Tool Support**:
   - Define tool specification in MCP configuration
   - Implement tool handler in appropriate MCP server
   - Add UI formatting in `mcp/ui.py` if needed
   - Test with both streaming and non-streaming models

2. **Content Block Extensions**:
   - Add new block type to `serialize_content_blocks()`
   - Implement parsing logic in `tag_content_handler()`
   - Update frontend components to handle new block type

3. **Performance Optimization**:
   - Monitor Prometheus metrics for bottlenecks
   - Use async patterns for I/O operations
   - Implement proper connection pooling for external services

## Security Considerations

- **Content Filtering**: `check_sensitive()` processes input/output for safety
- **Tool Permissions**: Verify user access before tool execution  
- **MCP Security**: Validate MCP server configurations and responses
- **File Access**: Restrict file operations to allowed directories
- **Authentication**: JWT-based auth with role-based access control