"""
title: Preview Mode
author: mereith
author_url:
funding_url:
version: 0.0.1

根据请求体的参数，覆写 system_prmopt
"""

from pydantic import BaseModel, Field
from typing import Optional


class Filter:
    class Valves(BaseModel):
        preview_mode_models: list[str] = Field(
            default=["qwq_32b"], description="支持 preview_mode 的模型列表"
        )
        preview_mode_prompt: str = Field(
            default="""You are a web development engineer, writing web pages according to the instructions below. You are a powerful code editing assistant capable of writing code and creating artifacts in conversations with users, or modifying and updating existing artifacts as requested by users. 
All code is written in a single code block to form a complete code file for display, without separating HTML and JavaScript code. An artifact refers to a runnable complete code snippet, you prefer to integrate and output such complete runnable code rather than breaking it down into several code blocks. For certain types of code, they can render graphical interfaces in a UI window. After generation, please check the code execution again to ensure there are no errors in the output.
Output only the HTML, without any additional descriptive text.""",
            description="preview_mode 的系统提示",
        )

    def __init__(self):
        self.valves = self.Valves()

    def inlet(self, body: dict, __user__: Optional[dict] = None) -> dict:
        
        # check for base_model_id in the metadata
        metaD = body["metadata"]
        model = metaD["model"]
        modelId = model["id"]

        features = body.get("features", {})
        if features.get("preview_mode", False):
            return body

        # 打开 preview_mode，看看模型是不是支持
        if modelId in self.valves.preview_mode_models:
            messages = body["messages"]
            first_message = messages[0]
            if first_message.get("role") == "system":
                first_message["content"] = self.valves.preview_mode_prompt
            else:
                messages.insert(
                    0, {"role": "system", "content": self.valves.preview_mode_prompt}
                )

        return body
