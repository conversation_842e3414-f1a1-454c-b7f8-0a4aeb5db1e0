# MCP 异常处理修复

## 问题描述

在 MCP 服务器初始化过程中，当出现 `asyncio.CancelledError` 异常时，cleanup 过程中的异常会掩盖原始的 `CancelledError`，导致上层函数的 `except Exception as e:` 无法正确捕获到 `CancelledError`。

### 具体表现

1. **原始问题**：
   - `initialize` 方法中发生 `CancelledError`
   - 在 cleanup 过程中又发生新的异常
   - 新异常掩盖了原始的 `CancelledError`
   - 上层代码无法正确处理取消操作

2. **错误日志示例**：
   ```
   ERROR | open_webui.mcp.mcp:cleanup:180 | [MCP] Error during cleanup of server deep-web-search: unhandled errors in a TaskGroup (1 sub-exception)
   ```

## 修复方案

### 1. 改进 `Server.initialize` 方法

**修复前**：
```python
except Exception as e:
    log.error(f"[MCP] Error initializing server {self.name}: {e}")
    await self.cleanup()
    raise Exception(f"[MCP] Error initializing server {self.name}: {e}")
except asyncio.CancelledError:
    log.error(f"[MCP] Server {self.name} initialization cancelled")
    await self.cleanup()
    raise
```

**修复后**：
```python
except asyncio.CancelledError:
    log.error(f"[MCP] Server {self.name} initialization cancelled")
    # 安全地进行清理，不让清理异常掩盖原始的 CancelledError
    try:
        await self.cleanup()
    except Exception as cleanup_error:
        log.error(f"[MCP] Error during cleanup after cancellation for server {self.name}: {cleanup_error}")
    raise
except Exception as e:
    log.error(f"[MCP] Error initializing server {self.name}: {e}")
    # 安全地进行清理，不让清理异常掩盖原始异常
    try:
        await self.cleanup()
    except Exception as cleanup_error:
        log.error(f"[MCP] Error during cleanup after initialization failure for server {self.name}: {cleanup_error}")
    raise Exception(f"[MCP] Error initializing server {self.name}: {e}")
```

### 2. 改进 `Server.cleanup` 方法

**修复前**：
```python
async def cleanup(self) -> None:
    """Clean up server resources."""
    async with self._cleanup_lock:
        try:
            try:
                await self.exit_stack.aclose()
            except (asyncio.CancelledError, asyncio.InvalidStateError) as e:
                log.warning(f"[MCP] Async context cleanup issue for server {self.name}: {e}. This may be due to task context changes.")
            self.session = None
            self.stdio_context = None
        except Exception as e:
            log.error(f"[MCP] Error during cleanup of server {self.name}: {e}")
```

**修复后**：
```python
async def cleanup(self) -> None:
    """Clean up server resources."""
    async with self._cleanup_lock:
        # 分别处理每个清理步骤，确保一个步骤失败不会影响其他步骤
        cleanup_errors = []
        
        # 清理 exit_stack
        try:
            await self.exit_stack.aclose()
        except (asyncio.CancelledError, asyncio.InvalidStateError) as e:
            log.warning(f"[MCP] Async context cleanup issue for server {self.name}: {e}. This may be due to task context changes.")
        except Exception as e:
            cleanup_errors.append(f"exit_stack cleanup: {e}")
            log.error(f"[MCP] Error closing exit_stack for server {self.name}: {e}")
        
        # 清理 session
        try:
            self.session = None
        except Exception as e:
            cleanup_errors.append(f"session cleanup: {e}")
            log.error(f"[MCP] Error clearing session for server {self.name}: {e}")
        
        # 清理 stdio_context
        try:
            self.stdio_context = None
        except Exception as e:
            cleanup_errors.append(f"stdio_context cleanup: {e}")
            log.error(f"[MCP] Error clearing stdio_context for server {self.name}: {e}")
        
        # 如果有清理错误，记录但不抛出异常
        if cleanup_errors:
            log.warning(f"[MCP] Cleanup completed with errors for server {self.name}: {'; '.join(cleanup_errors)}")
        else:
            log.debug(f"[MCP] Cleanup completed successfully for server {self.name}")
```

### 3. 改进 `initialize_mcp_servers` 方法

**修复前**：
```python
except Exception as e:
    log.error(f"[MCP] Failed to initialize server {server_config.id}: {e}")
    # 记录单个服务器初始化失败
    mcp_individual_server_initialization_total.labels(
        server_id=server_config.id,
        status="failure",
        env=ENV
    ).inc()
    raise
```

**修复后**：
```python
except asyncio.CancelledError as e:
    log.error(f"[MCP] Server {server_config.id} initialization cancelled: {e}")
    # 记录单个服务器初始化失败
    mcp_individual_server_initialization_total.labels(
        server_id=server_config.id,
        status="failure",
        env=ENV
    ).inc()
    raise
except Exception as e:
    log.error(f"[MCP] Failed to initialize server {server_config.id}: {e}")
    # 记录单个服务器初始化失败
    mcp_individual_server_initialization_total.labels(
        server_id=server_config.id,
        status="failure",
        env=ENV
    ).inc()
    raise
```

## 修复效果

### 1. 异常传播正确性
- ✅ `CancelledError` 能够正确传播到上层调用者
- ✅ 一般异常能够正确传播，包含原始错误信息
- ✅ cleanup 过程中的异常不会掩盖原始异常

### 2. 清理过程健壮性
- ✅ cleanup 方法不会抛出异常，即使内部步骤失败
- ✅ 每个清理步骤独立处理，一个失败不影响其他步骤
- ✅ 详细的错误日志记录，便于调试

### 3. 日志记录改进
- ✅ 区分不同类型的异常（取消 vs 一般错误）
- ✅ 清理过程的详细错误记录
- ✅ 成功和失败的明确状态记录

## 测试验证

修复后通过了以下测试场景：

1. **CancelledError 传播测试**：验证取消异常能够正确传播
2. **一般异常传播测试**：验证普通异常能够正确传播
3. **cleanup 健壮性测试**：验证清理过程不会抛出异常
4. **正常 cleanup 流程测试**：验证正常清理流程工作正常
5. **集成测试**：验证在真实场景下的异常处理

## 关键改进点

1. **异常处理顺序**：先处理 `CancelledError`，再处理一般异常
2. **安全清理**：使用 try-except 包装清理调用，防止清理异常掩盖原始异常
3. **分步清理**：将清理过程分解为独立步骤，提高健壮性
4. **详细日志**：提供更详细的错误信息，便于问题诊断

这些修复确保了 MCP 服务器在异常情况下能够正确处理和传播异常，同时保证资源得到适当清理。
