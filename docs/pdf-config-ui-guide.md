# PDF配置UI使用指南

## 🎯 功能概述

我已经为您的转换器测试页面添加了完整的PDF配置选项，帮助您调试PDF生成效果。新增的配置包括：

### 📋 配置选项

#### 页面设置
- **页面格式**: A4, A3, Letter, Legal, Ledger, Tabloid 或自动检测
- **页面方向**: 纵向/横向
- **自定义尺寸**: 精确的页面宽度和高度 (cm)

#### 边距设置
- **上下左右边距**: 0-50mm 范围内调整
- **默认值**: 全部设为0以消除白边

#### 渲染选项
- **缩放级别**: 0.1-3.0 范围内调整
- **CSS媒体类型**: screen (屏幕) / print (打印)
- **等待条件**: 网络空闲、页面加载等
- **等待时间**: 额外等待时间 (ms)
- **渲染背景**: 是否包含背景样式

### 🎯 快速预设

为了方便调试，提供了5个预设配置：

#### 1. 零边距配置
```json
{
  "pageOrientation": "landscape",
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "zoom": 1.0,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "printBackground": true
}
```

#### 2. PPT优化配置
```json
{
  "pageWidth": 33.87,
  "pageHeight": 19.05,
  "pageOrientation": "landscape",
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "zoom": 1.0,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "printBackground": true
}
```

#### 3. A4横向配置
```json
{
  "pageFormat": "a4",
  "pageOrientation": "landscape",
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "zoom": 0.85,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "printBackground": true
}
```

#### 4. 自定义尺寸配置
```json
{
  "pageWidth": 25.4,
  "pageHeight": 14.3,
  "pageOrientation": "landscape",
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "zoom": 1.0,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "printBackground": true
}
```

#### 5. 重置默认
恢复到基础配置状态。

## 🚀 使用方法

### 1. 启动服务器
```bash
cd /Users/<USER>/cosmos_converter
npm start
# 或
node src/server.js
```

### 2. 访问测试页面
打开浏览器访问: `http://localhost:3000`

### 3. 使用PDF配置
1. 选择输出格式为 "PDF"
2. 点击 "📋 PDF 高级配置" 展开配置选项
3. 手动调整参数或点击快速预设按钮
4. 输入HTML内容
5. 点击 "开始转换" 测试效果

### 4. 调试流程
1. **使用零边距预设** - 先测试基础的白边消除
2. **使用PPT优化预设** - 测试1280×720px的精确匹配
3. **手动微调参数** - 根据实际效果调整zoom、边距等
4. **保存有效配置** - 记录效果好的参数组合

## 🔧 调试技巧

### 解决白边问题
1. **首先设置所有边距为0**
2. **启用背景渲染** (`printBackground: true`)
3. **使用screen媒体类型** (`cssMediaType: "screen"`)
4. **调整缩放级别** (通常0.8-1.2之间)

### 针对不同内容调整
- **PPT内容**: 使用PPT优化预设
- **文档内容**: 使用A4横向预设
- **自定义内容**: 先用零边距，再微调尺寸

### 性能优化
- **等待条件**: `networkidle0` 确保完全加载
- **等待时间**: 1500-3000ms 适合大多数情况
- **缩放级别**: 避免过小或过大的值

## 📊 测试页面

我还创建了一个独立的测试页面 `test-config-ui.html`，您可以：

1. 直接在浏览器中打开测试配置效果
2. 查看不同预设的JSON输出
3. 验证配置收集逻辑

## 🐛 常见问题

### 配置不生效
- 确保选择了PDF输出格式
- 检查浏览器控制台是否有错误
- 验证服务器是否正确接收配置

### 转换失败
- 检查CloudConvert API密钥
- 验证HTML内容格式
- 查看服务器日志错误信息

### 效果不理想
- 尝试不同的预设配置
- 逐步调整单个参数
- 检查HTML内容的CSS样式

## 📝 配置参数说明

| 参数 | 类型 | 说明 | 推荐值 |
|------|------|------|--------|
| `pageFormat` | string | 标准页面格式 | 'a4', 'ledger' |
| `pageWidth` | number | 页面宽度(cm) | 33.87 (PPT) |
| `pageHeight` | number | 页面高度(cm) | 19.05 (PPT) |
| `marginTop` | number | 上边距(mm) | 0 |
| `marginBottom` | number | 下边距(mm) | 0 |
| `marginLeft` | number | 左边距(mm) | 0 |
| `marginRight` | number | 右边距(mm) | 0 |
| `zoom` | number | 缩放级别 | 1.0 |
| `cssMediaType` | string | CSS媒体类型 | 'screen' |
| `waitUntil` | string | 等待条件 | 'networkidle0' |
| `waitTime` | number | 等待时间(ms) | 1500 |
| `printBackground` | boolean | 渲染背景 | true |

现在您可以通过这个增强的UI界面方便地测试和调试各种PDF转换配置了！
