# 多节点配置同步问题修复

## 问题描述

在多节点部署的 Open WebUI 环境中，当一个节点更新配置时，其他节点无法立即获取到最新的配置，导致配置不一致的问题。

## 问题根本原因

### 1. CONFIG_DATA 缓存机制
- `CONFIG_DATA` 全局变量在应用启动时只从数据库加载一次
- 后续所有配置读取都使用这个缓存值，不会重新查询数据库

### 2. get_config_value 函数依赖过时缓存
```python
# 修改前的代码
def get_config_value(config_path: str):
    path_parts = config_path.split(".")
    cur_config = CONFIG_DATA  # 使用启动时的缓存
    # ...
```

### 3. PersistentConfig 更新机制不完整
- PersistentConfig 类依赖 `get_config_value` 函数
- 当其他节点更新配置时，本节点的 PersistentConfig 实例仍然使用旧值

## 解决方案

### 核心修改：让 get_config_value 每次都读取数据库

修改 `backend/open_webui/config.py` 中的 `get_config_value` 函数：

```python
def get_config_value(config_path: str):
    # 每次都从数据库读取最新配置，确保多节点配置同步
    current_config = get_config()
    path_parts = config_path.split(".")
    cur_config = current_config
    for key in path_parts:
        if key in cur_config:
            cur_config = cur_config[key]
        else:
            return None
    return cur_config
```

### 辅助修改：优化 save_config 函数

确保保存配置后从数据库重新读取：

```python
def save_config(config):
    global CONFIG_DATA
    global PERSISTENT_CONFIG_REGISTRY
    try:
        save_to_db(config)
        # 从数据库重新读取最新配置，确保一致性
        CONFIG_DATA = get_config()

        # Trigger updates on all registered PersistentConfig entries
        for config_item in PERSISTENT_CONFIG_REGISTRY:
            config_item.update()
    except Exception as e:
        log.exception(e)
        return False
    return True
```

## 配置管理系统架构

### 管理的配置项类型

1. **UI 配置**
   - `ENABLE_SIGNUP`: 是否允许用户注册
   - `DEFAULT_PROMPT_SUGGESTIONS`: 默认提示建议
   - `WEBUI_BANNERS`: 横幅配置
   - `MODEL_ORDER_LIST`: 模型排序列表

2. **API 配置**
   - `ENABLE_OLLAMA_API`: Ollama API 开关
   - `ENABLE_OPENAI_API`: OpenAI API 开关
   - `OLLAMA_BASE_URLS`: Ollama 服务地址
   - `OPENAI_API_CONFIGS`: OpenAI API 配置

3. **功能配置**
   - `ENABLE_DIRECT_CONNECTIONS`: 直连功能
   - `TOOL_SERVER_CONNECTIONS`: 工具服务器连接
   - `ENABLE_CODE_EXECUTION`: 代码执行功能
   - `ENABLE_PPT_EXPORT`: PPT 导出功能
   - `ENABLE_PDF_EXPORT`: PDF 导出功能

4. **RAG 配置**
   - `RAG_EMBEDDING_MODEL`: 嵌入模型
   - `RAG_RERANKING_MODEL`: 重排序模型
   - `RAG_TOP_K`: 检索数量
   - `CONTENT_EXTRACTION_ENGINE`: 内容提取引擎

5. **音频配置**
   - `WHISPER_MODEL`: 语音识别模型
   - `DEEPGRAM_API_KEY`: Deepgram API 密钥
   - `AUDIO_STT_OPENAI_API_KEY`: OpenAI 语音转文本 API 密钥

## 优势

1. **实时同步**: 每次配置读取都从数据库获取最新值
2. **最小改动**: 只修改核心函数，不影响现有架构
3. **向后兼容**: 不破坏现有的配置管理机制
4. **简单可靠**: 避免复杂的缓存失效和通知机制

## 性能考虑

- 每次配置读取都会查询数据库，可能增加数据库负载
- 对于高频配置读取场景，可以考虑添加短期缓存（如 1-5 秒）
- 配置读取通常不是高频操作，性能影响可接受

## 测试验证

运行测试脚本验证修改效果：

```bash
cd backend
python test_config_sync.py
```

测试覆盖：
- 配置值读取逻辑
- 多节点配置更新模拟
- 嵌套配置路径处理
- 不存在配置路径的处理

## 部署建议

1. 在测试环境验证修改效果
2. 监控数据库查询性能
3. 如有性能问题，可考虑添加短期缓存机制
4. 建议在低峰期部署到生产环境
