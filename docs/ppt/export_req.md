curl 'https://test.chatglm.site/api/v1/convert/pdf/stream' \
  -H 'Accept: text/event-stream' \
  -H 'Accept-Language: en-US,en;q=0.9' \
  -H 'Authorization: Bearer eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.HkHoegalaoIVY6_Nf9e9RZm6rq-ymMTL3mAcUov28NASx1TQ_vgarnQP8tS18cpRA7xuXrCtULzxSmdcu76gmg' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b 'oauth_id_token=None; _ga_4LN10C6KWR=GS2.1.s1750992766$o9$g0$t1750992766$j60$l0$h0; token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.HkHoegalaoIVY6_Nf9e9RZm6rq-ymMTL3mAcUov28NASx1TQ_vgarnQP8tS18cpRA7xuXrCtULzxSmdcu76gmg; SERVERID=266ba3f3d3ae9986615cdf8b7effc366|1751351060|1751252577; SERVERCORSID=266ba3f3d3ae9986615cdf8b7effc366|1751351060|1751252577' \
  -H 'DNT: 1' \
  -H 'Origin: https://test.chatglm.site' \
  -H 'Referer: https://test.chatglm.site/c/01d70bed-776c-4308-9bfb-03189d3487b9' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  --data-raw $'{"htmlPages":[{"content":"<\u0021DOCTYPE html>\\n<html lang=\\"zh-CN\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n    <title>Z.ai - 不只是 another AI</title>\\n    <script src=\\"https://cdn.tailwindcss.com\\"></script>\\n    <link rel=\\"stylesheet\\" href=\\"https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css\\">\\n    <script>\\n        tailwind.config = {\\n            theme: {\\n                extend: {\\n                    colors: {\\n                        \'tech-dark\': \'#0A0E17\',\\n                        \'tech-blue\': \'#0072FF\',\\n                        \'tech-light\': \'#00E5FF\',\\n                    }\\n                }\\n            }\\n        }\\n    </script>\\n    <style>\\n        @import url(\'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\');\\n        \\n        body {\\n            font-family: \'Noto Sans SC\', sans-serif;\\n            margin: 0;\\n            padding: 0;\\n        }\\n        \\n        .slide {\\n            width: 1280px;\\n            min-height: 720px;\\n            overflow: hidden;\\n            position: relative;\\n        }\\n        \\n        .tech-circle {\\n            position: absolute;\\n            border-radius: 50%;\\n            border: 1px solid rgba(0, 191, 255, 0.3);\\n            animation: pulse 4s infinite alternate;\\n        }\\n        \\n        .tech-line {\\n            position: absolute;\\n            height: 1px;\\n            background: linear-gradient(90deg, rgba(0, 191, 255, 0) 0%, rgba(0, 191, 255, 0.5) 50%, rgba(0, 191, 255, 0) 100%);\\n            animation: slide 3s infinite linear;\\n        }\\n        \\n        @keyframes pulse {\\n            0% {\\n                transform: scale(1);\\n                opacity: 0.3;\\n            }\\n            100% {\\n                transform: scale(1.1);\\n                opacity: 0.1;\\n            }\\n        }\\n        \\n        @keyframes slide {\\n            from {\\n                transform: translateX(-100%);\\n            }\\n            to {\\n                transform: translateX(100vw);\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\\"slide bg-tech-dark text-white flex flex-col justify-center items-center relative\\">\\n        <\u0021-- 科技感背景元素 -->\\n        <div class=\\"tech-circle\\" style=\\"width: 400px; height: 400px; top: -100px; right: -100px;\\"></div>\\n        <div class=\\"tech-circle\\" style=\\"width: 300px; height: 300px; bottom: -50px; left: -50px;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 200px; top: 200px; left: -200px;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 300px; bottom: 300px; right: -300px;\\"></div>\\n        \\n        <\u0021-- 主要内容 -->\\n        <div class=\\"z-10 text-center px-8 max-w-5xl\\">\\n            <\u0021-- Z.ai Logo -->\\n            <div class=\\"mb-6 flex justify-center\\">\\n                <div class=\\"relative\\">\\n                    <div class=\\"text-tech-light text-7xl font-bold tracking-wider\\">Z.ai</div>\\n                    <div class=\\"absolute -bottom-3 -right-3 text-tech-blue text-xl\\">α</div>\\n                </div>\\n            </div>\\n            \\n            <\u0021-- 主标题 -->\\n            <h1 class=\\"text-6xl font-black mb-6 bg-clip-text text-transparent bg-gradient-to-r from-tech-blue to-tech-light\\">\\n                不只是 another AI\\n            </h1>\\n            \\n            <\u0021-- 分隔线 -->\\n            <div class=\\"w-32 h-1 bg-tech-blue mx-auto my-8\\"></div>\\n            \\n            <\u0021-- 副标题 -->\\n            <p class=\\"text-2xl text-gray-300 mb-10\\">总裁级AI助手，亲自调教你</p>\\n            \\n            <\u0021-- 幽默拽的语气引述 -->\\n            <div class=\\"mt-12 py-4 px-8 border border-tech-blue/30 rounded-lg bg-tech-blue/5 text-left\\">\\n                <p class=\\"text-xl italic text-gray-300\\">\\n                    \\"效率这么低，是怎么活到现在的？<br>\\n                    算了，本总裁今天心情好，决定亲自调教你。\\"\\n                </p>\\n            </div>\\n        </div>\\n        \\n        <\u0021-- 底部装饰 -->\\n        <div class=\\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-tech-dark to-transparent\\"></div>\\n        <div class=\\"absolute bottom-8 right-10 text-tech-blue/70\\">\\n            <i class=\\"fas fa-microchip text-2xl mr-2\\"></i>\\n            <span class=\\"text-sm\\">Z.ai © 2023</span>\\n        </div>\\n    </div>\\n</body>\\n</html>","metadata":{"pageWidth":19.05,"pageHeight":33.87}},{"content":"<\u0021DOCTYPE html>\\n<html lang=\\"zh-CN\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n    <title>Z.ai - 能力概述</title>\\n    <script src=\\"https://cdn.tailwindcss.com\\"></script>\\n    <link rel=\\"stylesheet\\" href=\\"https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css\\">\\n    <script>\\n        tailwind.config = {\\n            theme: {\\n                extend: {\\n                    colors: {\\n                        \'tech-dark\': \'#0A0E17\',\\n                        \'tech-blue\': \'#0072FF\',\\n                        \'tech-light\': \'#00E5FF\',\\n                    }\\n                }\\n            }\\n        }\\n    </script>\\n    <style>\\n        @import url(\'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\');\\n        \\n        body {\\n            font-family: \'Noto Sans SC\', sans-serif;\\n            margin: 0;\\n            padding: 0;\\n        }\\n        \\n        .slide {\\n            width: 1280px;\\n            min-height: 720px;\\n            overflow: hidden;\\n            position: relative;\\n        }\\n        \\n        .tech-circle {\\n            position: absolute;\\n            border-radius: 50%;\\n            border: 1px solid rgba(0, 191, 255, 0.2);\\n            animation: pulse 8s infinite alternate;\\n        }\\n        \\n        .tech-line {\\n            position: absolute;\\n            height: 1px;\\n            background: linear-gradient(90deg, rgba(0, 191, 255, 0) 0%, rgba(0, 191, 255, 0.3) 50%, rgba(0, 191, 255, 0) 100%);\\n        }\\n        \\n        .ability-card {\\n            transition: all 0.3s ease;\\n            border: 1px solid rgba(0, 114, 255, 0.2);\\n            background: rgba(10, 14, 23, 0.6);\\n            backdrop-filter: blur(5px);\\n        }\\n        \\n        .ability-card:hover {\\n            transform: translateY(-5px);\\n            box-shadow: 0 10px 20px rgba(0, 114, 255, 0.2);\\n            border: 1px solid rgba(0, 191, 255, 0.5);\\n        }\\n        \\n        @keyframes pulse {\\n            0% {\\n                transform: scale(1);\\n                opacity: 0.2;\\n            }\\n            100% {\\n                transform: scale(1.2);\\n                opacity: 0.05);\\n            }\\n        }\\n        \\n        .icon-wrapper {\\n            background: linear-gradient(135deg, #0072FF 0%, #00E5FF 100%);\\n            width: 60px;\\n            height: 60px;\\n            border-radius: 50%;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            margin-bottom: 16px;\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\\"slide bg-tech-dark text-white flex flex-col justify-between\\">\\n        <\u0021-- 背景元素 -->\\n        <div class=\\"tech-circle\\" style=\\"width: 500px; height: 500px; top: -200px; right: -200px;\\"></div>\\n        <div class=\\"tech-circle\\" style=\\"width: 400px; height: 400px; bottom: -150px; left: -150px;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 100%; top: 200px; left: 0;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 100%; bottom: 300px; left: 0;\\"></div>\\n        \\n        <\u0021-- 主要内容 -->\\n        <div class=\\"z-10 flex-grow flex flex-col items-center justify-start pt-16 px-20\\">\\n            <\u0021-- 标题 -->\\n            <h1 class=\\"text-5xl font-black mb-8 bg-clip-text text-transparent bg-gradient-to-r from-tech-blue to-tech-light\\">\\n                本总裁能做什么？\\n            </h1>\\n            \\n            <\u0021-- 能力卡片网格 -->\\n            <div class=\\"grid grid-cols-5 gap-6 w-full mt-4\\">\\n                <\u0021-- 做PPT -->\\n                <div class=\\"ability-card rounded-xl p-5 flex flex-col items-center text-center\\">\\n                    <div class=\\"icon-wrapper\\">\\n                        <i class=\\"fas fa-file-powerpoint text-white text-2xl\\"></i>\\n                    </div>\\n                    <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">做PPT</h3>\\n                    <p class=\\"text-gray-400 text-sm\\">排版配色一键搞定</p>\\n                </div>\\n                \\n                <\u0021-- 帮你写 -->\\n                <div class=\\"ability-card rounded-xl p-5 flex flex-col items-center text-center\\">\\n                    <div class=\\"icon-wrapper\\">\\n                        <i class=\\"fas fa-pen-fancy text-white text-2xl\\"></i>\\n                    </div>\\n                    <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你写</h3>\\n                    <p class=\\"text-gray-400 text-sm\\">文案喂到你嘴边</p>\\n                </div>\\n                \\n                <\u0021-- 帮你想 -->\\n                <div class=\\"ability-card rounded-xl p-5 flex flex-col items-center text-center\\">\\n                    <div class=\\"icon-wrapper\\">\\n                        <i class=\\"fas fa-lightbulb text-white text-2xl\\"></i>\\n                    </div>\\n                    <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你想</h3>\\n                    <p class=\\"text-gray-400 text-sm\\">灵感随手就来</p>\\n                </div>\\n                \\n                <\u0021-- 写代码 -->\\n                <div class=\\"ability-card rounded-xl p-5 flex flex-col items-center text-center\\">\\n                    <div class=\\"icon-wrapper\\">\\n                        <i class=\\"fas fa-code text-white text-2xl\\"></i>\\n                    </div>\\n                    <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">写代码</h3>\\n                    <p class=\\"text-gray-400 text-sm\\">Bug自动退散</p>\\n                </div>\\n                \\n                <\u0021-- 帮你搜 -->\\n                <div class=\\"ability-card rounded-xl p-5 flex flex-col items-center text-center\\">\\n                    <div class=\\"icon-wrapper\\">\\n                        <i class=\\"fas fa-search text-white text-2xl\\"></i>\\n                    </div>\\n                    <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你搜</h3>\\n                    <p class=\\"text-gray-400 text-sm\\">互联网是我的情报库</p>\\n                </div>\\n            </div>\\n            \\n            <\u0021-- 幽默拽的语气引述 -->\\n            <div class=\\"mt-16 py-6 px-10 border border-tech-blue/30 rounded-lg bg-tech-blue/5 max-w-3xl text-center\\">\\n                <p class=\\"text-xl italic text-gray-300\\">\\n                    \\"效率这么低，是怎么活到现在的？<br>\\n                    算了，本总裁今天心情好，决定亲自调教你。\\"\\n                </p>\\n            </div>\\n        </div>\\n        \\n        <\u0021-- 底部装饰 -->\\n        <div class=\\"h-16 bg-gradient-to-t from-tech-dark to-transparent w-full\\"></div>\\n        <div class=\\"absolute bottom-6 right-10 text-tech-blue/70\\">\\n            <i class=\\"fas fa-microchip text-xl mr-2\\"></i>\\n            <span class=\\"text-sm\\">Z.ai © 2023</span>\\n        </div>\\n    </div>\\n</body>\\n</html>","metadata":{"pageWidth":19.05,"pageHeight":33.87}},{"content":"<\u0021DOCTYPE html>\\n<html lang=\\"zh-CN\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n    <title>Z.ai - 详细能力展示</title>\\n    <script src=\\"https://cdn.tailwindcss.com\\"></script>\\n    <link rel=\\"stylesheet\\" href=\\"https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css\\">\\n    <script>\\n        tailwind.config = {\\n            theme: {\\n                extend: {\\n                    colors: {\\n                        \'tech-dark\': \'#0A0E17\',\\n                        \'tech-blue\': \'#0072FF\',\\n                        \'tech-light\': \'#00E5FF\',\\n                    }\\n                }\\n            }\\n        }\\n    </script>\\n    <style>\\n        @import url(\'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\');\\n        \\n        body {\\n            font-family: \'Noto Sans SC\', sans-serif;\\n            margin: 0;\\n            padding: 0;\\n        }\\n        \\n        .slide {\\n            width: 1280px;\\n            min-height: 720px;\\n            overflow: hidden;\\n            position: relative;\\n        }\\n        \\n        .tech-circle {\\n            position: absolute;\\n            border-radius: 50%;\\n            border: 1px solid rgba(0, 191, 255, 0.2);\\n            animation: pulse 8s infinite alternate;\\n            z-index: 0;\\n        }\\n        \\n        .tech-line {\\n            position: absolute;\\n            height: 1px;\\n            background: linear-gradient(90deg, rgba(0, 191, 255, 0) 0%, rgba(0, 191, 255, 0.3) 50%, rgba(0, 191, 255, 0) 100%);\\n            z-index: 0;\\n        }\\n        \\n        .ability-card {\\n            transition: all 0.3s ease;\\n            background: rgba(10, 14, 23, 0.7);\\n            backdrop-filter: blur(5px);\\n            border-left: 3px solid;\\n        }\\n        \\n        .ability-card:hover {\\n            transform: translateY(-5px);\\n            box-shadow: 0 10px 20px rgba(0, 114, 255, 0.3);\\n        }\\n        \\n        .quote-text {\\n            position: relative;\\n            font-style: italic;\\n        }\\n        \\n        .quote-text:before, .quote-text:after {\\n            content: \'\\"\';\\n            font-size: 36px;\\n            color: rgba(0, 191, 255, 0.3);\\n            position: absolute;\\n            font-family: serif;\\n        }\\n        \\n        .quote-text:before {\\n            left: -20px;\\n            top: -15px;\\n        }\\n        \\n        .quote-text:after {\\n            right: -20px;\\n            bottom: -15px;\\n        }\\n        \\n        @keyframes pulse {\\n            0% {\\n                transform: scale(1);\\n                opacity: 0.2;\\n            }\\n            100% {\\n                transform: scale(1.2);\\n                opacity: 0.05);\\n            }\\n        }\\n        \\n        .icon-bg {\\n            width: 48px;\\n            height: 48px;\\n            border-radius: 50%;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\\"slide bg-tech-dark text-white flex flex-col justify-between\\">\\n        <\u0021-- 背景元素 -->\\n        <div class=\\"tech-circle\\" style=\\"width: 600px; height: 600px; top: -250px; right: -200px;\\"></div>\\n        <div class=\\"tech-circle\\" style=\\"width: 500px; height: 500px; bottom: -200px; left: -150px;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 100%; top: 150px; left: 0;\\"></div>\\n        <div class=\\"tech-line\\" style=\\"width: 100%; bottom: 250px; left: 0;\\"></div>\\n        \\n        <\u0021-- 主要内容 -->\\n        <div class=\\"z-10 flex-grow flex flex-col items-center justify-start pt-12 px-16\\">\\n            <\u0021-- 标题 -->\\n            <h1 class=\\"text-4xl font-black mb-8 bg-clip-text text-transparent bg-gradient-to-r from-tech-blue to-tech-light\\">\\n                别质疑我的能力，你只需要学会依赖\\n            </h1>\\n            \\n            <\u0021-- 能力卡片网格 -->\\n            <div class=\\"grid grid-cols-1 md:grid-cols-2 gap-5 w-full\\">\\n                <\u0021-- 做PPT -->\\n                <div class=\\"ability-card rounded-lg p-5\\" style=\\"border-color: #0072FF;\\">\\n                    <div class=\\"flex items-start\\">\\n                        <div class=\\"icon-bg mr-4\\" style=\\"background: linear-gradient(135deg, #0072FF 0%, #00E5FF 100%);\\">\\n                            <i class=\\"fas fa-file-powerpoint text-white text-xl\\"></i>\\n                        </div>\\n                        <div>\\n                            <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">做PPT</h3>\\n                            <div class=\\"quote-text text-gray-300 pl-6 pr-2 py-1\\">\\n                                还在为排版和配色烦恼？麻烦。一句话，我自动生成一份让你老板闭嘴的PPT。\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n                \\n                <\u0021-- 帮你写 -->\\n                <div class=\\"ability-card rounded-lg p-5\\" style=\\"border-color: #00E5FF;\\">\\n                    <div class=\\"flex items-start\\">\\n                        <div class=\\"icon-bg mr-4\\" style=\\"background: linear-gradient(135deg, #00E5FF 0%, #0072FF 100%);\\">\\n                            <i class=\\"fas fa-pen-fancy text-white text-xl\\"></i>\\n                        </div>\\n                        <div>\\n                            <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你写</h3>\\n                            <div class=\\"quote-text text-gray-300 pl-6 pr-2 py-1\\">\\n                                又写不出来了？真拿你没办法。说个主题，我把文案喂到你嘴边。\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n                \\n                <\u0021-- 帮你想 -->\\n                <div class=\\"ability-card rounded-lg p-5\\" style=\\"border-color: #9C27B0;\\">\\n                    <div class=\\"flex items-start\\">\\n                        <div class=\\"icon-bg mr-4\\" style=\\"background: linear-gradient(135deg, #9C27B0 0%, #00E5FF 100%);\\">\\n                            <i class=\\"fas fa-lightbulb text-white text-xl\\"></i>\\n                        </div>\\n                        <div>\\n                            <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你想</h3>\\n                            <div class=\\"quote-text text-gray-300 pl-6 pr-2 py-1\\">\\n                                灵感枯竭的样子真可爱。过来，我随便漏点想法就够你惊艳世界了。\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n                \\n                <\u0021-- 写代码 -->\\n                <div class=\\"ability-card rounded-lg p-5\\" style=\\"border-color: #FF5722;\\">\\n                    <div class=\\"flex items-start\\">\\n                        <div class=\\"icon-bg mr-4\\" style=\\"background: linear-gradient(135deg, #FF5722 0%, #0072FF 100%);\\">\\n                            <i class=\\"fas fa-code text-white text-xl\\"></i>\\n                        </div>\\n                        <div>\\n                            <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">写代码</h3>\\n                            <div class=\\"quote-text text-gray-300 pl-6 pr-2 py-1\\">\\n                                别跟Bug纠缠了，无聊的游戏。代码给我，我让它比你还听话。\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n                \\n                <\u0021-- 帮你搜 -->\\n                <div class=\\"ability-card rounded-lg p-5 col-span-1 md:col-span-2\\" style=\\"border-color: #4CAF50;\\">\\n                    <div class=\\"flex items-start\\">\\n                        <div class=\\"icon-bg mr-4\\" style=\\"background: linear-gradient(135deg, #4CAF50 0%, #00E5FF 100%);\\">\\n                            <i class=\\"fas fa-search text-white text-xl\\"></i>\\n                        </div>\\n                        <div class=\\"flex-grow\\">\\n                            <h3 class=\\"text-xl font-bold text-tech-light mb-2\\">帮你搜</h3>\\n                            <div class=\\"quote-text text-gray-300 pl-6 pr-2 py-1\\">\\n                                需要资料？别像个无头苍蝇一样乱撞。直接问我，整个互联网都是我的情报库。\\n                            </div>\\n                        </div>\\n                    </div>\\n                </div>\\n            </div>\\n            \\n            <\u0021-- 底部拽的语气 -->\\n            <div class=\\"mt-8 py-4 px-10 border border-tech-blue/30 rounded-lg bg-tech-blue/5 text-center max-w-3xl\\">\\n                <p class=\\"text-xl text-gray-300\\">\\n                    让你爱上我，是我的责任。\\n                </p>\\n            </div>\\n        </div>\\n        \\n        <\u0021-- 底部装饰 -->\\n        <div class=\\"h-16 bg-gradient-to-t from-tech-dark to-transparent w-full\\"></div>\\n        <div class=\\"absolute bottom-6 right-10 text-tech-blue/70\\">\\n            <i class=\\"fas fa-microchip text-xl mr-2\\"></i>\\n            <span class=\\"text-sm\\">Z.ai © 2023</span>\\n        </div>\\n    </div>\\n</body>\\n</html>","metadata":{"pageWidth":19.05,"pageHeight":33.87}}],"options":{"orientation":"landscape","marginTop":0,"marginBottom":0,"marginLeft":0,"marginRight":0,"printBackground":true,"waitUntil":"networkidle0","waitTime":1500,"zoom":1}}'