# WebUI 转换代理更新文档

## 概述

根据需求，修改了 webui 转发前端的 `/pdf/stream` 和 `/ppt/stream` 请求构造逻辑，实现以下功能：

1. 前端不再直传 `htmlPages`，由后端来构造 `htmlPages` 的 `content`
2. 后端请求 PPT server 获取 slides 数据
3. 在请求外层参数 `options` 中添加 `title`、`slide_name` 等字段
4. 前端请求 webui 后端时带上 `chatId`

## 修改内容

### 1. 后端修改 (`backend/open_webui/routers/converter.py`)

#### 1.1 更新请求模型
```python
class ConversionRequest(BaseModel):
    chatId: str  # 改为必需的chatId字段
    options: Optional[Dict[str, Any]] = {}
    pageMetadata: Optional[list] = []  # 前端计算的页面尺寸数据
```

#### 1.2 新增获取PPT slides数据的函数
```python
async def get_ppt_slides(chat_id: str, user_id: str) -> Dict[str, Any]:
    """从PPT服务器获取slides数据"""
    # 参考 get_chat_product 的实现
    # 请求 localhost:8000/get_slides/{chat_id}
    # 返回包含 title, slide_name, desc, page_num, pages 的数据
```

#### 1.3 修改PDF转换函数
- 从PPT服务器获取slides数据
- 构造htmlPages（包含content和前端传递的metadata）
- 使用前端计算的页面尺寸数据，如果没有则使用默认值
- 在options中添加title、slide_name等字段
- 详细的请求日志记录

#### 1.4 修改PPT转换函数
- 同PDF转换函数的修改逻辑

### 2. 前端修改

#### 2.1 转换服务更新 (`src/lib/apis/conversion/index.ts`)

新增支持chatId的方法：
```typescript
// 新的API - 使用chatId和页面尺寸数据
async convertToPdf(chatId: string, options: ConversionOptions, callbacks: ConversionCallbacks, pageMetadata?: any[])
async convertToPpt(chatId: string, options: ConversionOptions, callbacks: ConversionCallbacks, pageMetadata?: any[])

// 兼容旧版本 - 使用htmlPages
async convertToPdfWithPages(htmlPages: HtmlPage[], options: ConversionOptions, callbacks: ConversionCallbacks)
async convertToPptWithPages(htmlPages: HtmlPage[], options: ConversionOptions, callbacks: ConversionCallbacks)
```

新增请求方法：
```typescript
private async performConversionWithChatId(
    endpoint: string,
    chatId: string,
    options: ConversionOptions,
    callbacks: ConversionCallbacks,
    pageMetadata?: any[]
): Promise<void>
```

#### 2.2 PPTExportButton组件更新 (`src/lib/components/chat/PPTExportButton.svelte`)

- 新增 `chatId` 参数
- 修改PDF和PPT导出函数，使用新的API
- 保留页面尺寸计算逻辑，将计算结果传递给后端
- 前端计算页面尺寸，后端使用前端传递的尺寸数据

#### 2.3 PPTPreviewer组件更新 (`src/lib/components/chat/PPTPreviewer.svelte`)

- 传递 `chatId` 给 `PPTExportButton` 组件

## 请求流程

### 原流程
```
前端 -> 计算页面尺寸 -> 构造htmlPages -> webui后端 -> converter服务
```

### 新流程
```
前端 -> 计算页面尺寸 -> 发送chatId+pageMetadata -> webui后端 -> 请求PPT server -> 构造htmlPages(使用前端尺寸) -> converter服务
```

## API变更

### 前端请求格式
```json
// 旧格式
{
  "htmlPages": [...],
  "options": {...}
}

// 新格式
{
  "chatId": "chat-id-string",
  "options": {...},
  "pageMetadata": [
    {
      "pageWidth": 33.87,
      "pageHeight": 19.05
    },
    ...
  ]
}
```

### 后端转发格式
```json
{
  "htmlPages": [
    {
      "content": "HTML内容",
      "metadata": {
        "pageWidth": 33.87,
        "pageHeight": 19.05
      }
    }
  ],
  "options": {
    "title": "A Nice Slide",
    "slide_name": "slide.pptx",
    "desc": "A Nice Slide",
    "page_num": 3,
    // 其他原有选项...
  }
}
```

## PPT Server API

使用的PPT server接口：
```bash
curl localhost:8000/get_slides/{chat_id} -H 'X-User-Id: user-id'
```

返回结构：
```json
{
    "title": "slide.meta.get('title', 'A Nice Slide')",
    "slide_name": "slide.meta.get('slide_name', 'slide.pptx')",
    "desc": "slide.meta.get('desc', 'A Nice Slide')",
    "page_num": "len(slide.pages)",
    "pages": ["HTML内容1", "HTML内容2", ...]
}
```

## 日志记录

后端增加了详细的日志记录：
- PPT server请求URL和headers
- 获取到的slides数据
- 构造的htmlPages数量
- 完整的转发请求数据

## 兼容性

- 前端保留了旧版本的API方法（`convertToPdfWithPages`, `convertToPptWithPages`）
- 后端接口保持向下兼容
- 下游converter服务接口定义保持不变

## 测试建议

1. 测试新的chatId流程是否正常工作
2. 验证PPT server数据获取是否正确
3. 检查options中的新字段是否正确传递
4. 确认日志记录是否完整
5. 测试错误处理（无效chatId、PPT server不可用等）
