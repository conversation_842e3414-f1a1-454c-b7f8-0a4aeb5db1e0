# File Upload API Implementation

This document describes the implementation of file upload functionality in the chat system, integrating with Alibaba Cloud OSS (Object Storage Service) and Zhipuai's file management system.

Below are the reference APIs from Zhipuai and Alibaba Cloud OSS that we've integrated into our implementation.
## 文件管理

## 上传文件

上传用于模型微调、知识库、Batch、文件抽取等功能所使用的文件。

#### 接口请求

| 类型 | 描述 |
| --- | --- |
| 传输方式 | https |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/files |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | JSON |
| 响应格式 | JSON |
| 接口请求类型 | POST |

### 请求参数

| 参数名称 | 类型 | 是否必填 | 参数说明 |
| --- | --- | --- | --- |
| file | File | 是 | 输入要上传的文件的完整路径 |
| purpose | String | 是 | **上传文件的用途**:   **batch** ：用于批量任务处理，支持 `.jsonl` 文件格式，，单个文件大小限制为100 MB，文件数不超过 1000 个。 [Batch指南](https://bigmodel.cn/dev/howuse/batchapi)   **retrieval** ：用于知识库检索，支持 `doc` 、 `docx` 、 `pdf` 、 `xlsx` 等文件格式，，单个文件大小限制为 50 M。总容量不超过1G。   **file-extract** ：用于文档内容抽取，支持的格式包括： `pdf` 、 `docx` 、 `doc` 、 `xls` 、 `xlsx` 、 `ppt` 、 `pptx` 、 `png` 、 `jpg` 、 `jpeg` 、 `csv` ，单个文件大小限制为 50 M，图片大小不超过5M，文件数不超过 100 个。   **code-interpreter** ：文件上传给代码沙盒CI使用，支持的格式包括： `pdf` 、 `docx` 、 `doc` 、 `xls` 、 `xlsx` 、 `txt` 、 `png` 、 `jpg` 、 `jpeg` 、 `csv` ，单个文件大小限制为 20 M，图片大小不超过5M，文件数不超过 100 个。   **微调**   **fine-tune** ：用于语言模型微调，支持 `.jsonl` 文件格式，单个文件大小限制为 512 MB。 [微调指南](https://bigmodel.cn/dev/api#fine-tuning)   **fine-tune-function-calling** ：用于语言模型函数调用能力微调，支持 `.jsonl` 文件格式，单个文件大小限制为 512 MB。   **fine-tune-vision-cogview** ：用于文生图模型微调，支持 `.jsonl` 文件格式，单个文件大小限制为 512 MB。   **fine-tune-vision-cogvlm** ：用于图生文模型微调，支持 `.jsonl` 文件格式，单个文件大小限制为 512 MB。 |
| knowledge\_id | String | 否 | 当上传目的为 `retrieval` 时可用，必须指定 `knowledge_id`,访问 [知识库](https://open.bigmodel.cn/knowledge) 查询 `knowledge_id` 。 |

### 请求示例

```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

result = client.files.create(
    file=open("product_reviews.jsonl", "rb"),
    purpose="batch"    #支持retrieval、batch、fine-tune、file-extract、code-interpreter
)
print(result.id)
```


## 删除文件

#### 接口请求

| 类型 | 描述 |
| --- | --- |
| 传输方式 | https |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/files/{fileID} |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | JSON |
| 响应格式 | JSON |
| 接口请求类型 | DELETE |

### 请求参数

| 参数名称 | 类型 | 是否必填 | 参数说明 |
| --- | --- | --- | --- |
| file\_id | string | 是 | 文件 id |

### 请求示例

```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

result = client.files.delete(
    file_id="文件id"         #支持retrieval、batch、fine-tune、file-extract文件
)
```


## 查询文件列表

#### 接口请求

| 传输方式 | https |
| --- | --- |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/files |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | url拼参数 |
| 响应格式 | JSON |
| 接口请求类型 | GET |

### 请求参数

| 参数名称 | 类型 | 是否必填 | 参数说明 |
| --- | --- | --- | --- |
| purpose | String | 是 | 文件用途，支持batch、file-extract、fine-tune |
| knowledge\_id | String | 否 | 当文件用途为 `retrieval` 时，需要提供查询的知识库ID。 |
| page | Integer | 否 | 页面、默认 1 |
| limit | Integer | 否 | 查询文件列表数，默认10 |
| after | String | 否 | 查询指定fileID之后的文件列表（当文件用途为 fine-tune 时需要） |
| order | String | 否 | 排序规则，可选值\[desc，asc\]，默认desc（当文件用途为 fine-tune 时需要） |

### 请求示例

```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

# 请求文件列表
result = client.files.list(
    purpose="batch",    #支持batch、file-extract、fine-tune
)
print(result)
```


```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

# 请求知识库文件列表
resp = client.knowledge.document.list(
    purpose="retrieval",   #支持retrieval
    knowledge_id="1798330146986561536"
)
print(resp)
```


## 知识库文件详情

| 类型 | 详情 |
| --- | --- |
| 传输方式 | https |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/document/{id} |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | PATH |
| 响应格式 | JSON |
| 接口请求类型 | GET |

### 请求参数

| 参数名称 | 类型 | 是否必填 | 参数说明 |
| --- | --- | --- | --- |
| id | string | 是 | 文件 id |

### 请求示例

```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

resp = client.knowledge.document.retrieve(
    document_id="1803049612567781376"  #支持retrieval
)
print(resp)
```



## 编辑知识库文件

| 类型 | 详情 |
| --- | --- |
| 传输方式 | https |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/document/{id} |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | PUT |
| 响应格式 | JSON |
| 接口请求类型 | PUT |

### 请求参数

| 参数名称 | 类型 | 是否必填 | 参数说明 |
| --- | --- | --- | --- |
| document\_id | string | 是 | 文件id、只支持purpose为 **retrieval的文件** |
| knowledge\_type | int | 是 | 知识类型:1:文章知识: 支持pdf,url,docx2.问答知识-文档: 支持pdf,url,docx3.问答知识-表格: 支持xlsx4.商品库-表格: 支持xlsx5.自定义: 支持pdf,url,docx |
| custom\_separator | List<string> | 否 | 当前知识类型为自定义(konwledge\_type=5)的时候传 切片规则才会生效, 默认\\n |
| sentence\_size | int | 否 | 当前知识类型为自定义(konwledge\_type=5)的时候传切片字数才会生效,取值范围: 20-2000,切片大小 默认300 |

### 请求示例

```
from zhipuai import ZhipuAI

client = ZhipuAI(api_key="") # 请填写您自己的APIKey

resp = client.knowledge.document.edit(
    document_id="1803049612567781376",  #支持retrieval
    knowledge_type="1",
    sentence_size=204,
    custom_separator=["\n"]
)
print(resp)
```





---
title: "智谱AI开放平台"
source: "https://bigmodel.cn/dev/api/knowlage-manage/queryextract"
author:
  - "[[北京智谱华章科技股份有限公司]]"
published:
created: 2025-05-20
description: "智谱大模型开放平台-新一代国产自主通用AI大模型开放平台，是国内大模型排名前列的大模型网站，研发了多款LLM模型，多模态视觉模型产品，致力于将AI产品技术与行业场景双轮驱动的中国先进的认知智能技术和千行百业应用相结合，构建更高精度、高效率、通用化的AI开发新模式和企业级解决方案，实现智谱大模型的产业化，将AI的好处带给每个人。"
tags:
  - "clippings"
---
## 文件内容抽取

从文件中提取文本信息作为上下文进行问答，需要与文件上传功能一起使用。建议参考 [使用指南](https://open.bigmodel.cn/dev/howuse/fileqa) 。

## 接口请求

| 类型 | 详情 |
| --- | --- |
| 传输方式 | https |
| 请求地址 | https://open.bigmodel.cn/api/paas/v4/files/{file\_id}/content |
| 调用方式 | 同步调用，等待返回结果 |
| 字符编码 | UTF-8 |
| 接口请求格式 | JSON |
| 响应格式 | JSON |
| 接口请求类型 | GET |

## 调用示例

```
from pathlib import Path
from zhipuai import ZhipuAI

client = ZhipuAI(
    api_key="您的API Key",
    base_url="https://open.bigmodel.cn/api/paas/v4"
)

# 用于上传文件
# 格式限制：.PDF .DOCX .DOC .XLS .XLSX .PPT .PPTX .PNG .JPG .JPEG .CSV .PY .TXT .MD .BMP .GIF
# 文件大小不超过50M，图片大小不超过5M、总数限制为100个文件
file_object = client.files.create(file=Path("本地文件地址"), purpose="file-extract")

# 文件内容抽取
file_content = client.files.content(file_id=file_object.id).content.decode()
print(file_content)
```



#### 响应示例

```
{"content":"文档内容","file_type":"application/pdf","filename":"文档名称.pdf","title":"","type":"file"}
```




Here's a neatly formatted Markdown version of the guide for quickly getting started with the OSS Python SDK:

# Python Quick Start Guide

**Last Updated**: 2025-04-29 10:30:49

## Product Details

This guide introduces how to quickly use the OSS Python SDK to perform common operations such as creating a storage space (Bucket), uploading files (Object), downloading files, etc.

## Important Notes

-  For the correspondence between OSS-supported Regions and Endpoints, refer to [OSS Regions and Access Domains](#).
-  This document uses environment variables to read access credentials as an example. For more examples on configuring access credentials, see [Configuring Access Credentials](#).
-  The Python SDK is initialized using the default example. For more scenario examples, see [Initialization](#).

## Prerequisites

-  Registered Alibaba Cloud account.
-  Completed personal or enterprise real-name authentication.
-  Enabled OSS service.

## Configuring Credentials

1. **Create a RAM User AccessKey with OSS Management Permissions.**
2. **Use a ROS script to quickly create a RAM User AccessKey with OSS Management Permissions.**
3. **Configure environment variables using the RAM User AccessKey.**

### Linux/macOS/Windows

Add the environment variable settings to the `~/.bashrc` file using the following commands:

```bash
echo "export OSS_ACCESS_KEY_ID='YOUR_ACCESS_KEY_ID'" >> ~/.bashrc
echo "export OSS_ACCESS_KEY_SECRET='YOUR_ACCESS_KEY_SECRET'" >> ~/.bashrc
```

Activate the changes:

```bash
source ~/.bashrc
```

Verify the environment variables:

```bash
echo $OSS_ACCESS_KEY_ID
echo $OSS_ACCESS_KEY_SECRET
```

After modifying the system environment variables, restart or refresh your runtime environment, including IDEs, command line interfaces, other desktop applications, and background services to ensure the latest system environment variables are successfully loaded.

## Installing the SDK

-  Ensure a suitable version of the Python runtime environment is installed. Check the Python version with:

```bash
python -version
```

-  If Python is not installed, download and install it.
-  Install the OSS Python SDK:

```bash
pip install oss2
```

## Running the Example

Run the following code example to experience the complete OSS usage process: create a Bucket, upload a file, download a file, list files, and delete files and the Bucket.

```python
# -*- coding: utf-8 -*-
import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider
from itertools import islice
import os
import logging
import time
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Check if environment variables are set
required_env_vars = ['OSS_ACCESS_KEY_ID', 'OSS_ACCESS_KEY_SECRET']
for var in required_env_vars:
    if var not in os.environ:
        logging.error(f"Environment variable {var} is not set.")
        exit(1)

# Get credentials from environment variables
auth = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())

# Set Endpoint and Region
endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
region = "cn-hangzhou"

def generate_unique_bucket_name():
    # Get current timestamp
    timestamp = int(time.time())
    # Generate a random number between 0 and 9999
    random_number = random.randint(0, 9999)
    # Construct a unique Bucket name
    bucket_name = f"demo-{timestamp}-{random_number}"
    return bucket_name

# Generate a unique Bucket name
bucket_name = generate_unique_bucket_name()
bucket = oss2.Bucket(auth, endpoint, bucket_name, region=region)

def create_bucket(bucket):
    try:
        bucket.create_bucket(oss2.models.BUCKET_ACL_PRIVATE)
        logging.info("Bucket created successfully")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to create bucket: {e}")

def upload_file(bucket, object_name, data):
    try:
        result = bucket.put_object(object_name, data)
        logging.info(f"File uploaded successfully, status code: {result.status}")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to upload file: {e}")

def download_file(bucket, object_name):
    try:
        file_obj = bucket.get_object(object_name)
        content = file_obj.read().decode('utf-8')
        logging.info("File content:")
        logging.info(content)
        return content
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to download file: {e}")

def list_objects(bucket):
    try:
        objects = list(islice(oss2.ObjectIterator(bucket), 10))
        for obj in objects:
            logging.info(obj.key)
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to list objects: {e}")

def delete_objects(bucket):
    try:
        objects = list(islice(oss2.ObjectIterator(bucket), 100))
        if objects:
            for obj in objects:
                bucket.delete_object(obj.key)
                logging.info(f"Deleted object: {obj.key}")
        else:
            logging.info("No objects to delete")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to delete objects: {e}")

def delete_bucket(bucket):
    try:
        bucket.delete_bucket()
        logging.info("Bucket deleted successfully")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to delete bucket: {e}")

# Main process
if __name__ == '__main__':
    # 1. Create Bucket
    create_bucket(bucket)
    # 2. Upload File
    upload_file(bucket, 'test-string-file', b'Hello OSS, this is a test string.')
    # 3. Download File
    download_file(bucket, 'test-string-file')
    # 4. List Objects in Bucket
    list_objects(bucket)
    # 5. Delete Objects in Bucket
    delete_objects(bucket)
    # 6. Delete Bucket
    delete_bucket(bucket)
```

This guide provides a comprehensive walkthrough for using the OSS Python SDK, from setup to executing basic operations.

# Implementation Details

## Overview

Our implementation integrates Alibaba Cloud OSS and Zhipuai's file management system to provide a comprehensive file upload and content extraction solution for the chat system. The implementation includes:

1. A storage provider for Alibaba Cloud OSS
2. A client for Zhipuai's file management and extraction APIs
3. API endpoints for uploading files to chats and messages
4. On-demand content extraction during chat completions

**Important**: File contents are not stored in the database. Only the OSS URLs are stored, and file content is extracted on-demand during chat completions.

## Components

### 1. OSS Storage Provider

We've implemented an `OSSStorageProvider` class that extends the base `StorageProvider` interface to handle file storage in Alibaba Cloud OSS. This provider:

- Uploads files to OSS
- Downloads files from OSS
- Deletes files from OSS
- Manages file paths and URLs

### 2. Zhipuai Client

We've created a `ZhipuAIClient` class that interacts with Zhipuai's file management and extraction APIs. This client:

- Uploads files to Zhipuai
- Extracts content from files
- Manages file metadata

### 3. File Upload API Endpoints

We've implemented two main API endpoints:

- `/api/v1/file-upload/chat/{chat_id}/upload`: Uploads files for a chat
- `/api/v1/file-upload/chat/{chat_id}/message/{message_id}/upload`: Uploads files for a specific message

### 4. On-demand Content Extraction

Content extraction is performed on-demand during the chat completions process, not when files are uploaded. This approach:
- Reduces immediate processing load during file upload
- Only extracts content when it's actually needed for a conversation
- Ensures file contents are not stored in the database

## Message Format

When a message includes file attachments, the message structure will look like:

```json
{
  "author": "user",
  "content": {
    "text": "Here's a document I'd like to discuss",
    "attachments": [
      {
        "id": "file_id",
        "name": "document.pdf",
        "url": "https://bucket-name.oss-endpoint/file_path",
        "type": "document",
        "size": 1024,
        "content_type": "application/pdf"
      }
    ]
  }
}
```

## Security Considerations

1. **File Size Limits**: Files larger than 50MB are rejected
2. **Content Length Limits**: Extracted content is truncated if it exceeds 400K characters
3. **File Type Validation**: File types are validated and categorized as "document" or "image"
4. **Error Handling**: Robust error handling for file upload and content extraction failures

## Configuration

### Environment Variables

The following environment variables need to be set:

#### Alibaba Cloud OSS Configuration
```
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_ENDPOINT=your_endpoint (e.g., oss-cn-beijing.aliyuncs.com)
OSS_BUCKET_NAME=your_bucket_name
OSS_KEY_PREFIX=optional_prefix (e.g., chat-files/)
```

#### Zhipuai Configuration
```
ZHIPUAI_API_KEY=your_zhipuai_api_key
ZHIPUAI_API_BASE_URL=https://open.bigmodel.cn/api/paas/v4 (default)
```

### Storage Provider Configuration

To use Alibaba Cloud OSS as the storage provider, set:
```
STORAGE_PROVIDER=oss
```

## Usage Example

### Frontend Implementation

```javascript
// Example of uploading files to a chat
const uploadFiles = async (chatId, files) => {
  const formData = new FormData();

  for (const file of files) {
    formData.append('files', file);
  }

  // No need to specify extract_content as it's done on-demand during chat completions

  const response = await fetch(`/api/v1/file-upload/chat/${chatId}/upload`, {
    method: 'POST',
    body: formData,
  });

  return await response.json();
};
```

## Limitations and Future Improvements

1. Currently, only text extraction is supported. Future versions could add support for image analysis.
2. The system truncates very large extracted content. A more sophisticated chunking strategy could be implemented.
3. Add support for more file types and specialized extraction methods.
