openapi: 3.0.0
info:
  title: Sandbox API
  description: 通过API Gateway访问的代码沙盒执行服务
  version: 1.0.0
servers:
  - url: https://api.zhipuai-infra.cn
    description: API Gateway 服务器
paths:
  /sandbox:
    post:
      summary: 创建沙盒环境
      tags:
        - Sandbox
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSandboxRequest'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSandboxResponse'
      parameters:
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox

  /sandbox/{id}/execute:
    post:
      summary: 在沙盒中执行代码
      tags:
        - Sandbox
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 沙盒ID
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExecuteCodeRequest'
      responses:
        '200':
          description: 执行成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecuteCodeResponse'

  /sandbox/{id}/ping:
    get:
      summary: 检查沙盒状态
      tags:
        - Sandbox
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 沙盒ID
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      responses:
        '200':
          description: 沙盒状态
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PingSandboxResponse'

  /sandbox/{id}:
    delete:
      summary: 删除沙盒
      tags:
        - Sandbox
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 沙盒ID
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      responses:
        '200':
          description: 删除成功

  /execute:
    post:
      summary: 一次性执行代码（无需创建沙盒）
      tags:
        - Sandbox
      security:
        - bearerAuth: []
      parameters:
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OneTimeExecuteRequest'
      responses:
        '200':
          description: 执行成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecuteCodeResponse'

  /sandbox/{id}/files/upload/-{path}:
    post:
      summary: 上传文件到沙盒
      tags:
        - Files
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 沙盒ID
        - in: path
          name: path
          required: true
          schema:
            type: string
          description: 目标文件路径, 必须以/mnt/data开头
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      requestBody:
        content:
          application/octet-stream:
            schema:
              type: string
              format: binary
      responses:
        '200':
          description: 上传成功

  /sandbox/{id}/files/download/-{path}:
    get:
      summary: 从沙盒下载文件
      tags:
        - Files
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 沙盒ID
        - in: path
          name: path
          required: true
          schema:
            type: string
          description: 文件路径, 必须以/mnt/data开头
        - in: header
          name: Model
          schema:
            type: string
            default: sandbox
          required: true
          description: 固定值为sandbox
      responses:
        '200':
          description: 文件内容
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary

components:
  schemas:
    CreateSandboxRequest:
      type: object
      required:
        - lang
      properties:
        lang:
          type: string
          description: 编程语言
          example: python3
    
    CreateSandboxResponse:
      type: object
      properties:
        id:
          type: string
          description: 沙盒ID
          example: 86b9b313-4e63-48e2-a837-895da97ab933
    
    ExecuteCodeRequest:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          description: 要执行的代码
          example: "print('hello world' + str(1+2))"
        timeout_secs:
          type: integer
          description: 执行超时时间(秒)
          example: 60
    
    ExecuteCodeResponse:
      type: object
      properties:
        status:
          type: string
          description: 执行状态
          example: ok
        events:
          type: array
          items:
            $ref: '#/components/schemas/ExecutionEvent'
    
    ExecutionEvent:
      type: object
      properties:
        type:
          type: string
          description: 事件类型
          example: stream
        timestamp:
          type: string
          format: date-time
          description: 时间戳
          example: 2024-01-04T07:25:34.780809Z
        data:
          type: object
          properties:
            stream:
              type: string
              description: 输出流类型
              example: stdout
            text:
              type: string
              description: 输出内容
              example: "hello world3\n"
    
    PingSandboxResponse:
      type: object
      properties:
        last_activity:
          type: string
          format: date-time
          description: 最后活动时间
          example: 2024-01-04T07:25:34.784575Z
    
    OneTimeExecuteRequest:
      type: object
      required:
        - code
        - lang
      properties:
        code:
          type: string
          description: 要执行的代码
          example: "print('hello world' + str(1+2))"
        timeout_secs:
          type: integer
          description: 执行超时时间(秒)
          example: 60
        lang:
          type: string
          description: 编程语言
          example: python3
  
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT