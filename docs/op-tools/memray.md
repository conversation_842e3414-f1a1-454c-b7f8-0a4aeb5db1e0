TITLE: Profiling a Python Script with Memray
DESCRIPTION: This command uses <PERSON><PERSON><PERSON> to profile the memory usage of a specified Python script (my_script.py). The run subcommand executes the script and generates a binary output file containing the memory profile data.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_6

LANGUAGE: shell
CODE:
```
python3 -m memray run my_script.py
```

----------------------------------------

TITLE: Memray Command Line Usage Overview
DESCRIPTION: This snippet displays the general command-line usage and available subcommands for <PERSON><PERSON><PERSON>, a memory profiler for Python applications. It outlines various modes of operation like run, flamegraph, table, and live, along with their purposes and common options.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_5

LANGUAGE: shell
CODE:
```
usage: memray [-h] [-v] {run,flamegraph,table,live,tree,parse,summary,stats} ...

Memory profiler for Python applications

Run `memray run` to generate a memory profile report, then use a reporter command
such as `memray flamegraph` or `memray table` to convert the results into HTML.

Example:

    $ python3 -m memray run -o output.bin my_script.py
    $ python3 -m memray flamegraph output.bin

positional arguments:
  {run,flamegraph,table,live,tree,parse,summary,stats}
                        Mode of operation
    run                 Run the specified application and track memory usage
    flamegraph          Generate an HTML flame graph for peak memory usage
    table               Generate an HTML table with all records in the peak memory usage
    live                Remotely monitor allocations in a text-based interface
    tree                Generate a tree view in the terminal for peak memory usage
    parse               Debug a results file by parsing and printing each record in it
    summary             Generate a terminal-based summary report of the functions that allocate most memory
    stats               Generate high level stats of the memory usage in the terminal

optional arguments:
  -h, --help            Show this help message and exit
  -v, --verbose         Increase verbosity. Option is additive and can be specified up to 3 times
  -V, --version         Displays the current version of Memray

Please submit feedback, ideas, and bug reports by filing a new issue at https://github.com/bloomberg/memray/issues
```

----------------------------------------

TITLE: Handling OOM Errors and Post-Processing Memray Captures (Shell)
DESCRIPTION: This shell script provides a robust method to run Memray and perform post-processing on the capture file, preventing data loss in environments where processes might be killed due to Out Of Memory (OOM) errors (e.g., Kubernetes). It ensures that the capture file is processed (e.g., summarized or copied to persistent storage) before the container or process is destroyed.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_7

LANGUAGE: shell
CODE:
```
memray run --output /tmp/capture.bin myprogram.py
echo "Program finished"
# Do your post-processing here. This example just logs a summary of what's
# in the capture file, but you might want to generate reports from it and
# copy them over the network to some persistent storage, for instance.
memray summary /tmp/capture.bin
```

----------------------------------------

TITLE: Profiling a Python Module with Memray
DESCRIPTION: This command profiles the memory usage of a Python module (my_module) using Memray. The -m flag passed to memray run allows it to execute the module similar to how python3 -m would, tracking its memory allocations.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_7

LANGUAGE: shell
CODE:
```
python3 -m memray run -m my_module
```

----------------------------------------

TITLE: Installing Memray via pip
DESCRIPTION: This shell command demonstrates how to install the Memray memory profiler using Python's pip package manager. It's the recommended method to obtain the latest stable release from PyPI. Memray includes a C extension, so binary wheels are distributed for common systems like Linux x86/x64 and macOS.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
python3 -m pip install memray
```

----------------------------------------

TITLE: Caching Recursive Method with functools.cache in Python
DESCRIPTION: This Python snippet applies the `@functools.cache` decorator to a recursive method `factorial_plus`. The decorator caches results based on method arguments. A key issue highlighted is that `self` (the instance) is implicitly part of the cache key, and since `maxsize` is not set, the cache retains references to all `self` instances and `n` values, preventing them from being garbage collected and potentially leading to high memory usage.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/3.rst#_snippet_0

LANGUAGE: Python
CODE:
```
@functools.cache
def factorial_plus(self, n: int) -> int:
    return n * self.factorial_plus(n - 1) + self.inc if n else 1 + self.inc
```

----------------------------------------

TITLE: Programmatically Tracking Memory Allocations with Memray in Python
DESCRIPTION: This snippet demonstrates how to enable memory tracking programmatically within a Python application using the `memray.Tracker` context manager. It records all memory allocations occurring inside the `with` block to the specified binary output file, `output_file.bin`, providing detailed insights into memory usage during that specific execution phase.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_15

LANGUAGE: Python
CODE:
```
import memray

with memray.Tracker("output_file.bin"):
    print("Allocations will be tracked until the with block ends")
```

----------------------------------------

TITLE: Optimizing List Initialization in Python
DESCRIPTION: This optimized Python function demonstrates how to pre-allocate memory for a list when its final size is known. By using `[None] * n`, Python allocates a single contiguous block of memory large enough for all elements from the start, drastically reducing the number of allocations and improving memory efficiency compared to iterative appending.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/temporary_allocations.rst#_snippet_2

LANGUAGE: python
CODE:
```
def foo(n):
    return [None] * n

foo(1_000_000)
```

----------------------------------------

TITLE: Installing Memray with pip for Python 3.9
DESCRIPTION: This command installs the Memray memory profiler using pip for a specific Python interpreter, Python 3.9. It ensures Memray is available within the intended Python environment for profiling applications.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_0

LANGUAGE: shell
CODE:
```
python3.9 -m pip install memray
```

----------------------------------------

TITLE: Running Memray for Basic Memory Tracking (Shell)
DESCRIPTION: This snippet shows the basic usage of the `memray run` subcommand to launch a Python process and track its memory allocations. It supports running a Python file directly or a module using the `-m` flag. By default, results are saved to a `memray-<script>.<pid>.bin` file, but a custom output file can be specified with `-o` or `--output`.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray run [options] file.py [args]
memray run [options] -m module [args]
```

----------------------------------------

TITLE: Detecting Inefficient List Appending in Python
DESCRIPTION: This Python function demonstrates a common pattern where appending elements to a list can lead to multiple reallocations as the list grows. Each reallocation involves creating a new, larger buffer and copying existing elements, resulting in numerous temporary allocations that `memray` can detect as inefficient.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/temporary_allocations.rst#_snippet_0

LANGUAGE: python
CODE:
```
def foo(n):
    x = []
    for _ in range(n):
        x.append(None)
    return x

foo(1_000_000)
```

----------------------------------------

TITLE: Tracking Memory Allocations with Memray (Shell)
DESCRIPTION: This shell command initiates a memory profiling session for a specified Python script using Memray. It captures detailed allocation data and saves it to a binary file, which is essential for generating various analysis reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/examples/README.rst#_snippet_0

LANGUAGE: Shell
CODE:
```
memray3.9 run mandelbrot/mandelbrot.py
```

----------------------------------------

TITLE: Generating a Flame Graph Report with Memray
DESCRIPTION: This command processes a Memray binary output file (e.g., my_script.2369.bin) and generates an HTML flame graph. The flame graph visually represents memory usage over time, helping identify allocation hotspots.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_9

LANGUAGE: shell
CODE:
```
memray flamegraph my_script.2369.bin
```

----------------------------------------

TITLE: Invoking Memray via Python Module for Python 3.9
DESCRIPTION: This command invokes Memray by running it as a Python module using the Python 3.9 interpreter. This method explicitly links Memray to a specific Python environment for execution.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_1

LANGUAGE: shell
CODE:
```
python3.9 -m memray
```

----------------------------------------

TITLE: Creating Python Virtual Environment in Shell
DESCRIPTION: This command creates a new Python virtual environment named `.venv` in the current directory. It isolates project dependencies, preventing conflicts with system-wide Python packages and ensuring a clean environment for the tutorial's requirements.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_1

LANGUAGE: shell
CODE:
```
python3 -m venv .venv
```

----------------------------------------

TITLE: Activating Python Virtual Environment in Shell
DESCRIPTION: This command activates the previously created Python virtual environment. Upon successful activation, the terminal prompt will be prefixed with `(.venv)`, indicating that subsequent Python commands will use the packages installed within this isolated environment.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_2

LANGUAGE: shell
CODE:
```
source .venv/bin/activate
```

----------------------------------------

TITLE: Demonstrating Lazy Memory Allocation with NumPy in Python
DESCRIPTION: This snippet illustrates how memory is lazily allocated by the OS. It first creates a large NumPy array using `numpy.empty`, which reserves space in heap memory but doesn't immediately allocate physical RAM. Resident memory only increases after the array is explicitly written to, forcing the OS to commit memory pages.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/memory.rst#_snippet_0

LANGUAGE: Python
CODE:
```
import time
import numpy
time.sleep(1)
big_array = numpy.empty(1_000_000)
time.sleep(1)
big_array[:] = 42.0
time.sleep(1)
```

----------------------------------------

TITLE: Generating a Flame Graph with Memray (Shell)
DESCRIPTION: This command utilizes Memray to process a previously generated binary profiling results file and create an interactive HTML flame graph. The flame graph visually represents memory allocations across the application's call stack, aiding in performance bottleneck identification.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/examples/README.rst#_snippet_2

LANGUAGE: Shell
CODE:
```
memray3.9 flamegraph mandelbrot/memray-mandelbrot.py.187967.bin
```

----------------------------------------

TITLE: Implementing Self-Contained Cache with functools.cache in Python
DESCRIPTION: This snippet demonstrates a memoization strategy where the cache is stored directly on the `self` object of a class instance. This ensures the cache's lifecycle is tied to the object, preventing memory leaks by releasing the cache when the object is garbage collected. It defines an `Algorithms` class with a cached `factorial_plus` method and a generator function to use it.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/3.rst#_snippet_1

LANGUAGE: Python
CODE:
```
class Algorithms:
    def __init__(self, inc: int):
        self.inc = inc
        self.factorial_plus = functools.cache(self._uncached_factorial_plus)

    def _uncached_factorial_plus(self, n: int) -> int:
        return n * self.factorial_plus(n - 1) + self.inc if n > 1 else 1 + self.inc

def generate_factorial_plus_last_digit(plus_range: int, factorial_range: int):
    for i in range(plus_range):
        A = Algorithms(i)
        for j in range(factorial_range):
            yield A.factorial_plus(j) % 10
```

----------------------------------------

TITLE: Demonstrating Delayed Memory Deallocation with NumPy in Python
DESCRIPTION: This snippet shows that memory is not always freed immediately by the OS after a program no longer needs it. After creating and populating a large NumPy array, `del big_array` decreases the heap size, but the resident memory may not decrease until the system allocator decides to release the pages back to the OS.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/memory.rst#_snippet_1

LANGUAGE: Python
CODE:
```
import time
import numpy
time.sleep(1)
big_array = numpy.empty(1_000_000)
time.sleep(1)
big_array[:] = 42.0
time.sleep(1)
del big_array
time.sleep(1)
```

----------------------------------------

TITLE: Activating Native Memory Tracking with Memray
DESCRIPTION: This command illustrates how to enable native C/C++ function tracking when running a Python script with `memray`. By including the `--native` argument, `memray` captures memory allocations from C extensions, providing a comprehensive view of memory usage across both Python and native code.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_12

LANGUAGE: shell
CODE:
```
memray run --native my_script.py
```

----------------------------------------

TITLE: Starting Live Memory Profiling with Memray
DESCRIPTION: This command initiates Memray's live reporting mode for a specified Python application. It starts the target process in the background and connects a Text User Interface (TUI) in the foreground to display real-time heap memory usage, including current and peak heap size, runtime, and allocation snapshots.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_0

LANGUAGE: shell-session
CODE:
```
$ memray run --live application.py
```

----------------------------------------

TITLE: Running Memray in Live Mode for a Script
DESCRIPTION: This command demonstrates how to execute a Python script using `memray`'s interactive live mode. The `--live` option launches a terminal-based interface (TUI) that allows real-time inspection of memory usage, which is particularly useful for debugging long-running scripts or those with complex memory patterns.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_13

LANGUAGE: shell
CODE:
```
memray run --live my_script.py
```

----------------------------------------

TITLE: Running Memray in Live Mode (Shell)
DESCRIPTION: This command starts a Python application with Memray in 'live' mode. Memray launches the application in the background and simultaneously runs a Text User Interface (TUI) in the foreground, allowing for real-time observation of the application's memory usage.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_3

LANGUAGE: shell
CODE:
```
memray3.9 run --live application.py
```

----------------------------------------

TITLE: Limiting Cache Size with functools.lru_cache in Python
DESCRIPTION: This snippet shows how to set a maximum size for the cache using the `@functools.lru_cache` decorator. By specifying `maxsize`, the cache will automatically evict the least recently used items when the limit is reached, preventing unbounded memory growth. This is a direct way to control cache memory usage.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/3.rst#_snippet_2

LANGUAGE: Python
CODE:
```
@functools.lru_cache(maxsize=10000)
def factorial_plus(self, n: int) -> int:
    return n * self.factorial_plus(n - 1) + self.inc if n else 1 + self.inc
```

----------------------------------------

TITLE: Enabling Native Tracking in Memray Live Mode
DESCRIPTION: This command extends Memray's live reporting to include native memory allocations alongside Python allocations. By adding the `--native` flag, users can observe memory usage originating from C/C++ extensions or other native code within the profiled Python application, providing a more comprehensive view of heap activity.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_1

LANGUAGE: shell-session
CODE:
```
$ memray run --live --native application.py
```

----------------------------------------

TITLE: Generating Fibonacci Numbers with Python Generator
DESCRIPTION: This Python function calculates the Fibonacci sequence up to a given 'length' using a generator. It leverages the 'yield' keyword to return values one by one, pausing execution and saving state, which significantly reduces memory consumption compared to building and returning an entire list, especially for large sequences. It handles edge cases for 'length' less than 1 or equal to 1.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_13

LANGUAGE: Python
CODE:
```
def fibonacci(length):
    # edge cases
    if length < 1:
        return
    if length == 1:
        yield 1
        return

    left = right = 1
    yield left
    yield right

    for _ in range(length - 2):
        left, right = right, left + right
        yield right
```

----------------------------------------

TITLE: Simple Python Function Call Example for Flame Graphs
DESCRIPTION: This Python snippet defines a series of nested functions (`a`, `b`, `c`) to illustrate how call stacks leading to memory allocations are represented in a flame graph. Function `c` allocates memory by creating a string of length `n`. This example helps in understanding how the 'width' in a flame graph corresponds to memory allocated by a function or its children, and how the 'height' represents the call stack depth.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/flamegraph.rst#_snippet_0

LANGUAGE: python
CODE:
```
def a(n):
    return b(n)

def b(n):
    return [c(n), d(n)]

def c(n):
    return "a" * n
```

----------------------------------------

TITLE: Running Memray in Live Mode for a Module
DESCRIPTION: This command shows how to run a Python module in `memray`'s interactive live mode. Similar to running a script, the `--live -m` options enable the TUI, providing real-time memory profiling for modules, which is beneficial for understanding memory behavior during module execution.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_14

LANGUAGE: shell
CODE:
```
memray run --live -m my_module
```

----------------------------------------

TITLE: Profiling Python Script with Memray (Shell)
DESCRIPTION: This command executes the `fibonacci.py` script under Memray's profiling. Memray intercepts memory allocations during the script's execution and records them into a binary output file, which will later be used to generate a flame graph.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_8

LANGUAGE: shell
CODE:
```
memray run fibonacci.py
```

----------------------------------------

TITLE: Complex Memory Allocation Example - Python
DESCRIPTION: This comprehensive Python example demonstrates a more intricate memory allocation scenario. It defines a series of interconnected functions (`a` through `i`, plus `missing`) that allocate varying amounts of memory, either directly or through their callees. The purpose is to provide a detailed case study for analyzing memory flame graphs, highlighting how different call paths contribute to overall memory usage and how some allocations might not appear in the final peak.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/flamegraph.rst#_snippet_2

LANGUAGE: python
CODE:
```
def a(n):
    return [b(n), h(n)]

def b(n):
    return c(n)

def c(n):
    missing(n)
    return d(n)

def missing(n):
    return "a" * n

def d(n):
    return [e(n), f(n), "a" * (n // 2)]

def e(n):
    return "a" * n

def f(n):
    return g(n)

def g(n):
    return "a" * n * 2

def h(n):
    return i(n)

def i(n):
    return "a" * n

a(100000)
```

----------------------------------------

TITLE: Basic Memray Process Attachment (Shell)
DESCRIPTION: This command demonstrates the fundamental syntax for attaching Memray to a running process. It requires the process ID (`<pid>`) of a Python application where Memray is installed and debugger privileges are available. By default, it opens a live TUI, but can also record allocations to a file using the `-o` option.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/attach.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray attach [options] <pid>
```

----------------------------------------

TITLE: Verifying Optimized List Initialization with Memray Summary
DESCRIPTION: This shell command sequence profiles the optimized Python code using `memray run` and then analyzes its memory footprint with `memray summary --temporary-allocations`. The `memray` output confirms the significant reduction in allocations (down to 1) and memory usage for the `foo()` function, validating the effectiveness of the optimization.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/temporary_allocations.rst#_snippet_3

LANGUAGE: shell
CODE:
```
$ memray run -fo test.bin example2.py
$ memray summary test.bin --temporary-allocations
┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┓
┃                      ┃     <Total ┃      Total ┃            ┃ Own Memory ┃ Allocation ┃
┃ Location             ┠────────────╂────────────╂────────────╂────────────╂────────────┨
│ foo at example2.py   │    7.629MB │     99.29% │    7.629MB │     99.29% │          1 │
│ ...                  │            │            │            │            │            │
└──────────────────────┴────────────┴────────────┴────────────┴────────────┴────────────┘
```

----------------------------------------

TITLE: Optimizing Memory with Nested Function Calls in Python
DESCRIPTION: This snippet demonstrates a memory optimization technique in Python by avoiding intermediate local variables. Instead of storing results of each step in new variables, it directly nests function calls, allowing Python's garbage collector to free memory from intermediate results more promptly, reducing peak memory usage. It requires `add_scalar`, `duplicate_data`, `raise_to_power`, `subtract_scalar`, and `load_xMb_of_data` functions, along with `SIZE_OF_DATA_IN_MB`, `SUBTRACT_AMOUNT`, `POWER_AMOUNT`, and `ADD_AMOUNT` constants. The expected output is the final processed data.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/2.rst#_snippet_0

LANGUAGE: Python
CODE:
```
def process_data():
    # no extra reference to the original array
    return add_scalar(
        duplicate_data(
            raise_to_power(
                subtract_scalar(
                    load_xMb_of_data(SIZE_OF_DATA_IN_MB),
                    SUBTRACT_AMOUNT
                ),
                POWER_AMOUNT
            )
        ),
        ADD_AMOUNT
    )
```

----------------------------------------

TITLE: Profiling Python Script with Memray in Venv (Shell)
DESCRIPTION: Similar to the Codespaces setup, this command profiles the `fibonacci.py` script using Memray within a virtual environment. It generates a binary output file containing memory allocation data, which is a prerequisite for creating a flame graph.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_10

LANGUAGE: shell
CODE:
```
memray run exercise_1/fibonacci.py
```

----------------------------------------

TITLE: Tracking Memory Across Forks with Memray (Shell)
DESCRIPTION: This command enables Memray to continue tracking memory usage in child processes after a parent process forks. This feature is particularly useful for applications utilizing `multiprocessing` or pre-fork patterns like Celery or Gunicorn, creating a new output file for each child process. Note that this mode is incompatible with live tracking modes.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_6

LANGUAGE: shell
CODE:
```
memray run --follow-fork example.py
```

----------------------------------------

TITLE: Optimizing Memory with Variable Reassignment in Python
DESCRIPTION: This snippet illustrates another memory optimization strategy in Python, often referred to as 'hidden mutability'. It reuses a single local variable (`data`) to store the result of each sequential data transformation. This approach minimizes peak memory usage by ensuring that only one version of the large data array is held in memory at any given time, as older versions are immediately eligible for garbage collection. It depends on `load_xMb_of_data`, `subtract_scalar`, `raise_to_power`, `duplicate_data`, and `add_scalar` functions, and constants like `SIZE_OF_DATA_IN_MB`, `SUBTRACT_AMOUNT`, `POWER_AMOUNT`, and `ADD_AMOUNT`. The function returns the final processed `data`.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/2.rst#_snippet_1

LANGUAGE: Python
CODE:
```
def process_data():
    # reusing the local variable instead of allocating more space
    # this approach is called 'hidden mutability'
    data = load_xMb_of_data(SIZE_OF_DATA_IN_MB)
    data = subtract_scalar(data, SUBTRACT_AMOUNT)
    data = raise_to_power(data, POWER_AMOUNT)
    data = duplicate_data(data)
    data = add_scalar(data, ADD_AMOUNT)
    return data
```

----------------------------------------

TITLE: Running Pytest with Memray Plugin
DESCRIPTION: This command demonstrates how to invoke pytest with the `--memray` flag, enabling the pytest-memray plugin to automatically generate a memory report for the specified test files. This is a convenient way to integrate memory profiling into an existing test suite.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_10

LANGUAGE: shell
CODE:
```
pytest --memray tests/
```

----------------------------------------

TITLE: Starting Memray in Live Remote Server Mode
DESCRIPTION: This command starts the target Python application in a server mode, allowing Memray's TUI to connect from a separate process or shell. It initiates the profiling server and waits for a client connection, enabling the separation of the profiled application's execution from the live monitoring interface.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_2

LANGUAGE: shell-session
CODE:
```
$ memray run --live-remote application.py
Run 'memray live <port>' in another shell to see live results
```

----------------------------------------

TITLE: Running Memray Analysis on a Python Script (Python 3.9)
DESCRIPTION: This command executes Memray's 'run' subcommand with the version-qualified script 'memray3.9' to profile the 'example.py' script. It tracks memory allocations and deallocations during the script's execution, saving the results.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_4

LANGUAGE: shell
CODE:
```
memray3.9 run example.py
```

----------------------------------------

TITLE: Running Memray in Live Remote Mode (Shell)
DESCRIPTION: This command initiates Memray in 'live-remote' mode, where the application runs in the background and Memray binds to an unused port. It then waits for a separate `memray live <port>` command to be executed in another terminal to attach the TUI, providing flexibility for monitoring.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_4

LANGUAGE: shell
CODE:
```
memray3.9 run --live-remote application.py
```

----------------------------------------

TITLE: Generating Flame Graph from Memray Output in Venv (Shell)
DESCRIPTION: This command generates an HTML flame graph report from the Memray binary output file, specifically for the `fibonacci.py` script located in `exercise_1/`. The `<run-id>` must be replaced with the unique identifier from the profiling run, allowing visualization of memory usage.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_11

LANGUAGE: shell
CODE:
```
memray flamegraph exercise_1/memray-fibonacci.py.<run-id>.bin
```

----------------------------------------

TITLE: Limiting Memory Usage in Pytest with Memray Marker
DESCRIPTION: This Python snippet shows how to use the `@pytest.mark.limit_memory` decorator provided by `pytest-memray` to set a maximum memory allocation limit for a specific test function. If the test exceeds the specified memory (e.g., '24 MB'), it will automatically fail, aiding in memory regression testing.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_11

LANGUAGE: python
CODE:
```
@pytest.mark.limit_memory("24 MB")
def test_foobar():
    # do some stuff that allocates memory
```

----------------------------------------

TITLE: Profiling Memory with Memray Flamegraph in Jupyter
DESCRIPTION: This example demonstrates using the `%%memray_flamegraph` cell magic with specific arguments (`--trace-python-allocators` and `--leaks`) to profile Python memory allocations and identify memory leaks within a Jupyter cell. The code defines functions that allocate strings, and Memray tracks their memory usage, displaying a flame graph upon execution.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/jupyter_magic.rst#_snippet_1

LANGUAGE: Python
CODE:
```
%%memray_flamegraph --trace-python-allocators --leaks
def a():
    return "a" * 10_000

def bc():
    return "bc" * 10_000

x = a() + bc()
```

----------------------------------------

TITLE: Installing Python Dependencies in Virtual Environment
DESCRIPTION: This command installs all necessary Python packages listed in `requirements-tutorial.txt` into the active virtual environment. It uses `pip` to fetch and install the project's dependencies, ensuring all prerequisites for the tutorial exercises are met.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_3

LANGUAGE: shell
CODE:
```
python3 -m pip install -r requirements-tutorial.txt
```

----------------------------------------

TITLE: Tracing Python Allocators with Memray (Shell)
DESCRIPTION: This command runs Memray to track allocations made by Python's internal allocators, which is particularly useful for identifying memory leaks. It acts as an alternative to setting `PYTHONMALLOC=malloc` but allows distinguishing between system and Python allocator usage. Be aware that this mode generates larger report files and results in slower profiling due to increased data collection.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_2

LANGUAGE: shell
CODE:
```
memray run --trace-python-allocators example.py
```

----------------------------------------

TITLE: Attaching to Live Remote Memray Session (Shell)
DESCRIPTION: This command is used in a separate terminal to connect to a Memray session that was started with `memray run --live-remote`. It attaches the TUI to the running application, enabling live memory usage analysis.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_5

LANGUAGE: shell
CODE:
```
memray3.9 live <port>
```

----------------------------------------

TITLE: Deactivating pymalloc via PYTHONMALLOC Environment Variable (Shell)
DESCRIPTION: To ensure Python uses the system's `malloc` instead of its internal `pymalloc` allocator, set the `PYTHONMALLOC` environment variable to `malloc`. This is crucial for accurate memory leak detection as `pymalloc` might incorrectly report memory as leaked if it doesn't return it to the system.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/python_allocators.rst#_snippet_0

LANGUAGE: Shell
CODE:
```
export PYTHONMALLOC=malloc
```

----------------------------------------

TITLE: Deactivating pymalloc via Python -Xdev Flag (Shell)
DESCRIPTION: Another method to deactivate `pymalloc` is by executing Python with the `-Xdev` command-line flag. This flag enables Python's development mode, which includes changes to memory allocation behavior, making it suitable for detailed memory profiling and leak hunting.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/python_allocators.rst#_snippet_1

LANGUAGE: Shell
CODE:
```
python -Xdev your_script.py
```

----------------------------------------

TITLE: Basic Usage of Memray Stats Command
DESCRIPTION: This snippet illustrates the fundamental command-line syntax for invoking the `memray stats` subcommand. It takes a `<results>` argument, which is the path to a previously generated memory capture file, and allows for optional parameters to modify its behavior or output format.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/stats.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray stats [options] <results>
```

----------------------------------------

TITLE: Analyzing Inefficient List Appending with Memray Summary
DESCRIPTION: This shell command sequence uses `memray run` to profile the Python code that uses `list.append()` and then `memray summary --temporary-allocations` to report on the memory usage. The output clearly shows a high number of allocations (78 in this case) for the `foo()` function, indicating the inefficiency caused by repeated list reallocations.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/temporary_allocations.rst#_snippet_1

LANGUAGE: shell
CODE:
```
$ memray run -fo test.bin example.py
$ memray summary test.bin --temporary-allocations
┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━┓
┃                      ┃     <Total ┃      Total ┃            ┃ Own Memory ┃ Allocation ┃
┃ Location             ┠────────────╂────────────╂────────────╂────────────╂────────────┨
│ foo at example.py    │   72.486MB │     99.93% │   72.486MB │     99.93% │         78 │
│ ...                  │            │            │            │            │            │
└──────────────────────┴────────────┴────────────┴────────────┴────────────┴────────────┘
```

----------------------------------------

TITLE: Running Memray in Live Remote Mode (Shell)
DESCRIPTION: Initiates a Memray profiling session for `application.py` with live remote monitoring enabled on port 12345. This allows another Memray instance to connect and display real-time memory usage, with instructions provided for connecting to the live session.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_5

LANGUAGE: Shell
CODE:
```
$ memray run --live-remote application.py --live-port 12345
Run 'memray live 60125' in another shell to see live results
```

----------------------------------------

TITLE: Tracing pymalloc Allocations with Memray --trace-python-allocators Flag (Shell)
DESCRIPTION: Instead of fully deactivating `pymalloc`, you can instruct `memray run` to trace every call to the `pymalloc` allocator using the `--trace-python-allocators` flag. This provides granular insight into `pymalloc`'s memory usage, though it can lead to slower executions and significantly larger report files.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/python_allocators.rst#_snippet_2

LANGUAGE: Shell
CODE:
```
memray run --trace-python-allocators your_script.py
```

----------------------------------------

TITLE: Directly Invoking Memray for Profiling
DESCRIPTION: These commands demonstrate how to invoke Memray directly from the command line (assuming it's in the system's PATH) to profile a Python script or module. This provides a more concise way to run Memray without explicitly using python3 -m memray.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_8

LANGUAGE: shell
CODE:
```
memray run my_script.py
memray run -m my_module
```

----------------------------------------

TITLE: Invoking memray table subcommand in Shell
DESCRIPTION: This snippet shows the basic command-line usage for the `memray table` subcommand. It requires a `<results>` file, which is a capture file generated by the `run` subcommand, to produce an HTML report of memory allocations. Optional `[options]` can be used to customize the output, such as specifying the output file name.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/table.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray table [options] <results>
```

----------------------------------------

TITLE: Basic Usage of Memray Tree Command (Shell)
DESCRIPTION: This snippet illustrates the fundamental command-line syntax for invoking the 'memray tree' subcommand. It requires a previously generated memory capture file as input and supports optional parameters to customize the report's display and filtering. The output is directed to standard output, with colorization enabled for terminal environments.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tree.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray tree [options] <results>
```

----------------------------------------

TITLE: Basic Usage of Memray Flamegraph Command
DESCRIPTION: This snippet illustrates the fundamental syntax for invoking the `memray flamegraph` subcommand. It requires a capture file generated by the `run` subcommand as its primary argument and can be extended with various options to customize the visualization, such as enabling leak detection or splitting thread views.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/flamegraph.rst#_snippet_4

LANGUAGE: shell
CODE:
```
memray flamegraph [options] <results>
```

----------------------------------------

TITLE: Generating Flame Graph from Memray Output (Shell)
DESCRIPTION: This command processes the binary output file generated by `memray run` and converts it into an interactive HTML flame graph report. The `<run-id>` placeholder must be replaced with the actual unique identifier generated by Memray for the specific profiling run.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_9

LANGUAGE: shell
CODE:
```
memray flamegraph memray-fibonacci.py.<run-id>.bin
```

----------------------------------------

TITLE: Loading Memray IPython Extension
DESCRIPTION: This IPython magic command loads the Memray extension, making the `%%memray_flamegraph` cell magic available for profiling memory usage directly within Jupyter notebooks. It's a prerequisite for using Memray's profiling capabilities in a Jupyter environment.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/jupyter_magic.rst#_snippet_0

LANGUAGE: IPython
CODE:
```
%load_ext memray
```

----------------------------------------

TITLE: Transforming Memray Capture Files (Shell)
DESCRIPTION: This snippet shows the basic command-line usage of the `memray transform` subcommand. It requires specifying an output format and the path to a Memray capture file. The command converts the capture file into the specified format, with a default output filename that can be overridden using the `-o` option.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/transform.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray transform <format> [options] <results>
```

----------------------------------------

TITLE: Enabling Native C/C++ Tracking with Memray (Shell)
DESCRIPTION: This snippet demonstrates how to activate native C/C++ function tracking using the `memray run` subcommand by adding the `--native` argument. This provides deeper insights into memory allocations within extension modules like NumPy or Pandas, showing internal C calls. Note that reports with native frames must be generated on the same machine where the data was collected.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/run.rst#_snippet_1

LANGUAGE: shell
CODE:
```
memray run --native example.py
```

----------------------------------------

TITLE: Generating Memray Flame Graph from Results (Python 3.9)
DESCRIPTION: This command uses the version-qualified 'memray3.9' script to generate an interactive flame graph from a previously captured Memray profile results file. The output is an HTML file visualizing memory usage patterns.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_6

LANGUAGE: shell
CODE:
```
memray3.9 flamegraph memray-example.py.4131.bin
```

----------------------------------------

TITLE: Illustrating Inverted Flame Graphs with Python
DESCRIPTION: This Python code defines several functions that call each other, ultimately leading to memory allocations in `a()` and `a1()`. It serves as an example to demonstrate the difference between a normal flame graph and an inverted flame graph generated by Memray, showing how `a()` is called multiple times via different paths and `a1()` once. The code is designed to create distinct call stacks that allocate memory.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/flamegraph.rst#_snippet_3

LANGUAGE: python
CODE:
```
def a():
    return 1000000 * "a"

def a1():
    return 1000000 * "a"

def b():
    return a()

def c():
    return b()

def d():
    return b()

def f():
    return g()

def e():
    return g()

def g():
    return c()

def main():
    a = a1()
    x = d()
    y = e()
    z = f()
    return (x,y,z,a)

main()
```

----------------------------------------

TITLE: Invoking Memray via Version-Qualified Script (memray3.9)
DESCRIPTION: This command invokes Memray using its version-qualified executable script, 'memray3.9'. This provides a clear and explicit way to specify which Python version's Memray installation is being used.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_2

LANGUAGE: shell
CODE:
```
memray3.9
```

----------------------------------------

TITLE: Launching HTTP Server for Flame Graphs (Shell)
DESCRIPTION: This command starts a simple HTTP server on port 3000, which is necessary to view the generated Memray flame graph HTML files in a web browser, especially within a Codespaces environment. It allows local files to be served and accessed via a URL.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_6

LANGUAGE: shell
CODE:
```
python -m http.server 3000
```

----------------------------------------

TITLE: Simple Memory Allocation Example - Python
DESCRIPTION: This snippet defines a simple Python function `d(n)` that allocates a string of length `n`. It also shows a call to `a(100000)`, which in the context of the document, is part of a larger example demonstrating memory allocation patterns in a call graph where `a` calls `b`, and `b` calls `c` and `d`. The purpose is to illustrate basic memory allocation for flame graph visualization.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/flamegraph.rst#_snippet_1

LANGUAGE: python
CODE:
```
def d(n):
    return "a" * n

a(100000)
```

----------------------------------------

TITLE: Fibonacci Sequence Generation (Python)
DESCRIPTION: This Python function calculates the Fibonacci sequence up to a specified `length`. It initializes the sequence with the first two numbers and then iteratively appends subsequent numbers. The challenge highlights that the `output.append` call is a significant source of memory allocations, suggesting it as a target for optimization.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_12

LANGUAGE: python
CODE:
```
def fibonacci(length):
    # edge cases
    if length < 1:
        return []
    if length == 1:
        return [1]
    if length == 2:
        return [1, 1]

    output = [1, 1]

    for i in range(length - 2):
        output.append(output[i] + output[i + 1])  # <- Here!

    return output
```

----------------------------------------

TITLE: Manually Clearing functools.cache in Python
DESCRIPTION: This snippet illustrates how to manually clear a cache associated with a decorated function. Calling `cache_clear()` on the cached function resets the cache, removing all stored entries. This is useful for explicit memory management or when cached data becomes stale and needs to be refreshed.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/3.rst#_snippet_3

LANGUAGE: Python
CODE:
```
Algorithms.factorial_plus.cache_clear()
```

----------------------------------------

TITLE: Calling GLIBC malloc_info API with Python ctypes
DESCRIPTION: This snippet shows how to retrieve detailed memory allocator information using GLIBC's `malloc_info` API via Python's `ctypes`. It defines a `ctypes.Structure` to map the `mallinfo` struct, calls the C function, and then iterates through the returned data to print various memory statistics.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/memory.rst#_snippet_3

LANGUAGE: Python
CODE:
```
import ctypes

class MallInfo(ctypes.Structure):
    _fields_ = [
        (name, ctypes.c_int)
        for name in (
            "arena",
            "ordblks",
            "smblks",
            "hblks",
            "hblkhd",
            "usmblks",
            "fsmblks",
            "uordblks",
            "fordblks",
            "keepcost",
        )
    ]


libc = ctypes.CDLL("libc.so.6")
mallinfo = libc.mallinfo
mallinfo.argtypes = []
mallinfo.restype = MallInfo

info = mallinfo()
fields = [(name, getattr(info, name)) for name, _ in info._fields_]
print("Malloc info:")
for name, value in fields:
    print(f"- {name}: {value}")
```

----------------------------------------

TITLE: Running Specific Pytest Test File in Shell
DESCRIPTION: This command executes only the tests contained within the `tests/test_exercise_1.py` file. It's useful for focusing on a single exercise during development, saving time by avoiding the execution of the entire test suite.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_5

LANGUAGE: shell
CODE:
```
pytest tests/test_exercise_1.py
```

----------------------------------------

TITLE: Using Memray Summary Subcommand in Shell
DESCRIPTION: This snippet shows the basic command-line usage of the `memray summary` subcommand. It requires a previously generated capture file as an argument and can accept various options. The output is printed directly to standard output, with colorization if the output is a terminal.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/summary.rst#_snippet_0

LANGUAGE: shell
CODE:
```
memray summary [options] <results>
```

----------------------------------------

TITLE: Running All Pytest Tests in Shell
DESCRIPTION: This command executes all Pytest tests found in the current directory and its subdirectories. It's used to run the entire workshop's test suite, providing a comprehensive check of all exercises.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_4

LANGUAGE: shell
CODE:
```
pytest
```

----------------------------------------

TITLE: Invoking Memray via Unqualified Script
DESCRIPTION: This command invokes Memray using the unqualified 'memray' script. While convenient, its execution relies on the system's PATH or active virtual environment to determine the underlying Python interpreter.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_3

LANGUAGE: shell
CODE:
```
memray
```

----------------------------------------

TITLE: Connecting to Memray Live Remote Server
DESCRIPTION: This command connects a Memray TUI client to a previously started `memray run --live-remote` server. By specifying the port number, the client establishes a connection to the profiling server, allowing the live memory usage data to be displayed in the TUI while the profiled application runs in its own process.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_3

LANGUAGE: shell-session
CODE:
```
$ # Run this in a different shell:
$ memray live <port>
```

----------------------------------------

TITLE: Starting Memray Live Remote on Custom Port
DESCRIPTION: This command initiates Memray's live remote profiling server on a user-defined port, in this case, 12345. It allows users to specify a non-default port for the profiling server, which is useful in environments where specific ports are required or to avoid conflicts with other applications.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/live.rst#_snippet_4

LANGUAGE: shell-session
CODE:
```
$ memray run --live-remote --live-port 12345 application.py
Run 'memray live 12345' in another shell to see live results
```

----------------------------------------

TITLE: Cloning and Installing Memray in Development Mode
DESCRIPTION: This sequence of commands clones the Memray repository, creates and activates a Python virtual environment, upgrades pip, and then installs Memray in editable (development) mode along with test and extra requirements. This setup is ideal for development and contribution.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_3

LANGUAGE: shell
CODE:
```
<NAME_EMAIL>:bloomberg/memray.git memray
cd memray
python3 -m venv ../memray-env/  # just an example, put this wherever you want
source ../memray-env/bin/activate
python3 -m pip install --upgrade pip
python3 -m pip install -e . -r requirements-test.txt -r requirements-extra.txt
```

----------------------------------------

TITLE: Checking DWARF Debug Information for Python Interpreter (Linux)
DESCRIPTION: This command illustrates how to check for DWARF debugging information within the Python interpreter executable on Linux. It uses `which python` to locate the interpreter and then pipes its path to `readelf -S` to list its sections, filtering for 'debug' entries. The example output shows typical DWARF sections like `.debug_info` and `.debug_line`, indicating that debug information is available for symbolification.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_1

LANGUAGE: shell-session
CODE:
```
$ readelf -S $(which python) | grep debug
```

----------------------------------------

TITLE: Verifying Object Files for macOS dSYM Generation
DESCRIPTION: This snippet demonstrates how to verify the presence of object files required for generating dSYM bundles on macOS. It uses `dsymutil -s` to list debug information sections and `grep OSO` to confirm object file references, followed by `ls` to check the actual file existence. This is a prerequisite for successfully creating a dSYM bundle for a native extension.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_2

LANGUAGE: Shell
CODE:
```
$ # Sanity check: ensure that the object files are still around
$ dsymutil -s  src/memray/_memray.cpython-310-darwin.so | grep OSO | head -n 1
[  9431] 000d39a1 66 (N_OSO        ) 00     0001   0000000062fb8052 'memray/build/temp.macosx-12.5-arm64-cpython-310/src/memray/_memray.o'

$ ls memray/build/temp.macosx-12.5-arm64-cpython-310/src/memray/_memray.o
.rw-r--r-- 3.5M pgalindo3 16 Aug 12:32 memray/build/temp.macosx-12.5-arm64-cpython-310/src/memray/_memray.o
```

----------------------------------------

TITLE: Installing Pre-commit Hooks for Memray
DESCRIPTION: This command installs the pre-commit hooks configured for the Memray repository. These hooks automatically run linting checks and other validations before each commit, ensuring code quality and adherence to project standards.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_4

LANGUAGE: shell
CODE:
```
pre-commit install
```

----------------------------------------

TITLE: Memray Profile Results File Output Message
DESCRIPTION: This snippet displays the console output message from Memray, indicating the filename where profiling results are being recorded. The binary file contains captured memory allocation data for later analysis.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/getting_started.rst#_snippet_5

LANGUAGE: text
CODE:
```
Writing profile results into memray-example.py.4131.bin
```

----------------------------------------

TITLE: Unsetting DEBUGINFOD_URLS in Shell
DESCRIPTION: This snippet demonstrates how to clear the `DEBUGINFOD_URLS` environment variable using the `unset` command in a shell. This action prevents the system from attempting to query any debuginfod servers, effectively disabling debuginfod lookups.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_11

LANGUAGE: Shell
CODE:
```
$ unset DEBUGINFOD_URLS
```

----------------------------------------

TITLE: Configuring Debuginfod Server URLs
DESCRIPTION: This command sets the `DEBUGINFOD_URLS` environment variable, specifying the URL(s) of `debuginfod` servers that Memray should use to fetch debug information. This variable accepts a space-separated list of URLs. Correctly setting this variable is crucial for Memray to locate and download debug symbols for binaries when generating native mode reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_7

LANGUAGE: Shell
CODE:
```
$ export DEBUGINFOD_URLS="https://debuginfod.archlinux.org/"
```

----------------------------------------

TITLE: Checking DWARF Debug Information for Executables (Linux)
DESCRIPTION: This command demonstrates how to use the `readelf` utility on Linux to inspect a binary file (e.g., `a.out`) and determine if it contains DWARF debugging information. The `grep debug` filter helps identify relevant debug sections, which are essential for Memray to produce detailed and accurate native mode reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_0

LANGUAGE: shell-session
CODE:
```
$ readelf -S ./a.out | grep debug
```

----------------------------------------

TITLE: Installing Pre-commit Hooks for Linting (Shell)
DESCRIPTION: This shell script installs the necessary development dependencies from `requirements-extra.txt` and then sets up the pre-commit hooks. These hooks automatically run linting checks before each commit, ensuring code quality and adherence to project standards.
SOURCE: https://github.com/bloomberg/memray/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: shell
CODE:
```
python3 -m pip install -r requirements-extra.txt
pre-commit install
```

----------------------------------------

TITLE: Installing Debuginfod Client on Debian/Ubuntu
DESCRIPTION: This command installs the `debuginfod` client library on Debian or Ubuntu systems using the `apt` package manager. The client library is a prerequisite for Memray to query remote `debuginfod` servers and fetch debug information on demand, which is essential for symbolification on Linux distributions that don't ship debug info with binaries.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_4

LANGUAGE: Shell
CODE:
```
$ sudo apt install debuginfod
```

----------------------------------------

TITLE: Generating dSYM Bundle for macOS Native Extension
DESCRIPTION: This command generates a dSYM bundle containing debug information for a macOS shared object. The `dsymutil` tool reads the debug map from the shared library and loads DWARF information from the original object files, relocating addresses to create a single binary with final, linked debug addresses. This bundle is crucial for Memray to perform effective native mode symbolification.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_3

LANGUAGE: Shell
CODE:
```
$ # Then generate a dSYM bundle with the debug information:
$ dsymutil src/memray/_memray.cpython-310-darwin.so
```

----------------------------------------

TITLE: Calling GLIBC malloc_stats API with Python ctypes
DESCRIPTION: This snippet demonstrates how to call the GLIBC `malloc_stats` C function from Python using the `ctypes` module. It loads the `libc.so.6` library and invokes `malloc_stats` to print memory allocator statistics to standard output, which can help in diagnosing memory fragmentation issues.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/memory.rst#_snippet_2

LANGUAGE: Python
CODE:
```
import ctypes
libc = ctypes.CDLL("libc.so.6")
libc.malloc_stats.restype = None
libc.malloc_stats()
```

----------------------------------------

TITLE: gprof2dot Node Layout (Text)
DESCRIPTION: This text snippet illustrates the visual layout of a node in the `gprof2dot` output graph generated from Memray data. Each node represents a function and displays its filename, function name, total memory percentage (including children), self memory percentage, and the number of allocations performed by that function.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/transform.rst#_snippet_1

LANGUAGE: text
CODE:
```
+-----------------------------+
|           filename          |
|        function_name        |
|           total %           |
|           (self %)          |
|        num_allocs ×         |
+-----------------------------+
```

----------------------------------------

TITLE: Adding Developer's Certificate of Origin to Git Commits
DESCRIPTION: This snippet shows the required `Signed-Off-By` line that must be included as the last line of every commit message to confirm agreement with the project's open source license terms. It is typically added using `git commit -s`.
SOURCE: https://github.com/bloomberg/memray/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: Git Commit Message
CODE:
```
Signed-Off-By: Random J. Developer <<EMAIL>>
```

----------------------------------------

TITLE: Installing Debuginfod Client on Fedora
DESCRIPTION: This command installs the `elfutils-debuginfod` package, which provides the `debuginfod` client library, on Fedora systems using the `dnf` package manager. Installing this client allows Memray to automatically fetch debug information from remote `debuginfod` servers, enhancing the accuracy of native mode reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_6

LANGUAGE: Shell
CODE:
```
$ sudo dnf install elfutils-debuginfod
```

----------------------------------------

TITLE: Installing Debuginfod Client on Arch Linux
DESCRIPTION: This command installs the `debuginfod` client library on Arch Linux systems using the `pacman` package manager. The client library enables Memray to connect to `debuginfod` servers and download necessary debug information for binaries, improving native mode symbolification in reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_5

LANGUAGE: Shell
CODE:
```
$ sudo pacman -S debuginfod
```

----------------------------------------

TITLE: Navigating to Exercise Directory (Shell)
DESCRIPTION: This command changes the current working directory to `docs/tutorials/exercise_1/`. This is a prerequisite step to run the `fibonacci.py` script and generate its memory profile using Memray from the correct location.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_7

LANGUAGE: shell
CODE:
```
cd docs/tutorials/exercise_1/
```

----------------------------------------

TITLE: Navigating to Tutorial Directory in Shell
DESCRIPTION: This command changes the current directory to `docs/tutorials/` within the Memray repository. This is the designated location for running tests and exercises throughout the tutorial, ensuring all relative paths for scripts and tests are correctly resolved.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/tutorials/1.rst#_snippet_0

LANGUAGE: shell
CODE:
```
cd docs/tutorials/
```

----------------------------------------

TITLE: Limiting Debuginfod Download Time and Size
DESCRIPTION: This snippet sets `DEBUGINFOD_TIMEOUT` to `10` seconds and `DEBUGINFOD_MAXSIZE` to `1,000,000` bytes (1MB), controlling the maximum duration and size for `debuginfod` downloads. These environment variables help manage resource consumption and prevent excessively large or slow debug information fetches, ensuring more predictable report generation times.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_10

LANGUAGE: Shell
CODE:
```
$ export DEBUGINFOD_TIMEOUT=10       # seconds
$ export DEBUGINFOD_MAXSIZE=1000000  # bytes
```

----------------------------------------

TITLE: Setting Compiler Flags for LZ4 on macOS
DESCRIPTION: This snippet sets the CFLAGS and LDFLAGS environment variables to help the compiler locate the header and library files for the lz4 dependency when building Memray on macOS using Homebrew. This is crucial for successful compilation.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_1

LANGUAGE: shell
CODE:
```
export CFLAGS="-I$(brew --prefix lz4)/include" LDFLAGS="-L$(brew --prefix lz4)/lib -Wl,-rpath,$(brew --prefix lz4)/lib"
```

----------------------------------------

TITLE: Displaying All Threads' Stack Trac es in GDB
DESCRIPTION: Use this GDB command to display the stack trace for all threads in a process. This is particularly useful when analyzing a core file to understand the state of all threads at the time of the crash or when trying to attach.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/attach.rst#_snippet_1

LANGUAGE: gdb
CODE:
```
thread apply all bt
```

----------------------------------------

TITLE: Displaying All Threads' Stack Traces in LLDB
DESCRIPTION: Use this LLDB command to display the stack trace for all threads within a process. This command is crucial for debugging core files, providing insight into the state of the process's threads when an issue occurred or during an attachment attempt.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/attach.rst#_snippet_2

LANGUAGE: lldb
CODE:
```
thread backtrace all
```

----------------------------------------

TITLE: Extending Base Template and Defining Topbar Buttons (Jinja2)
DESCRIPTION: This snippet demonstrates how to extend a base HTML template using `{% extends "base.html" %}` and define a block named `topbar_buttons`. It also shows how to include content from the parent block using `{{ super() }}` and add custom text.
SOURCE: https://github.com/bloomberg/memray/blob/main/src/memray/reporters/templates/classic_base.html#_snippet_0

LANGUAGE: Jinja2
CODE:
```
{% extends "base.html" %} {% block topbar_buttons %} {{ super() }} Usage Over Time {% endblock %}
```

----------------------------------------

TITLE: Defining Extra Modal Block with HTML Content (Jinja2)
DESCRIPTION: This snippet defines a block named `extra_modal` which contains HTML content, including a heading and some text. This block is intended to be used for modal dialogs or similar overlay elements.
SOURCE: https://github.com/bloomberg/memray/blob/main/src/memray/reporters/templates/classic_base.html#_snippet_2

LANGUAGE: Jinja2
CODE:
```
{% block extra_modal %}\n\n##### Resident set size over time\n\n×\n\nClose\n\n{% endblock %}
```

----------------------------------------

TITLE: Memray Profile Output File Message (Text)
DESCRIPTION: This text snippet illustrates the console output from Memray after a successful profiling run. It confirms the creation of the binary results file, providing its full path and filename, which is required for subsequent report generation commands.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/examples/README.rst#_snippet_1

LANGUAGE: Text
CODE:
```
Writing profile results into mandelbrot/memray-mandelbrot.py.187967.bin
```

----------------------------------------

TITLE: Enabling Verbose Debuginfod Logging
DESCRIPTION: This command sets the `DEBUGINFOD_VERBOSE` environment variable to `1`, activating highly verbose logging for `debuginfod` operations. This provides detailed insights into network requests and cached file usage during debug information fetching, which is useful for advanced debugging but generates a significant amount of output.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_9

LANGUAGE: Shell
CODE:
```
$ export DEBUGINFOD_VERBOSE=1
```

----------------------------------------

TITLE: Enabling Debuginfod Download Progress Diagnostics
DESCRIPTION: This command sets the `DEBUGINFOD_PROGRESS` environment variable to `1`, enabling progress diagnostics during `debuginfod` downloads. When enabled, Memray will display information about the download status, which can be useful for monitoring long-running fetches of debug information from remote servers.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/native_mode.rst#_snippet_8

LANGUAGE: Shell
CODE:
```
$ export DEBUGINFOD_PROGRESS=1
```

----------------------------------------

TITLE: gprof2dot Edge Layout (Text)
DESCRIPTION: This text snippet describes the visual representation of an edge in the `gprof2dot` output graph. An edge signifies a call relationship between a 'caller' and a 'callee' function, indicating the percentage of memory allocated by the callee as a result of calls from the caller.
SOURCE: https://github.com/bloomberg/memray/blob/main/docs/transform.rst#_snippet_2

LANGUAGE: text
CODE:
```
           percentage %
caller -------------------> callee
```

----------------------------------------

TITLE: Setting macOS Deployment Target
DESCRIPTION: This command sets the MACOSX_DEPLOYMENT_TARGET environment variable, which is required when building Memray on macOS to specify the minimum macOS version the compiled binaries should support.
SOURCE: https://github.com/bloomberg/memray/blob/main/README.md#_snippet_2

LANGUAGE: shell
CODE:
```
export MACOSX_DEPLOYMENT_TARGET=10.14
```

----------------------------------------

TITLE: Initializing Temporal Flame Graph JavaScript Variables
DESCRIPTION: This snippet initializes global JavaScript variables used by the temporal flame graph visualization. `hideImports` controls whether imported modules are hidden, while `intervals`, `flamegraphIntervals`, and `invertedNoImportsIntervals` are placeholders for data structures that will store and manage time-based allocation intervals and flame graph data.
SOURCE: https://github.com/bloomberg/memray/blob/main/src/memray/reporters/templates/temporal_flamegraph.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
var hideImports = false;
var intervals = null;
var flamegraphIntervals = null;
var invertedNoImportsIntervals = null;
```

----------------------------------------

TITLE: Initializing Report Data (JavaScript)
DESCRIPTION: This JavaScript block initializes global variables with profiling data passed from the server-side, typically in JSON format. It sets up `packed_data`, `memory_records`, `inverted`, and other flags like `merge_threads` and `temporal` for subsequent client-side processing and visualization of Memray reports.
SOURCE: https://github.com/bloomberg/memray/blob/main/src/memray/reporters/templates/base.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const packed_data = {{ data|tojson }}; var data = null; var flamegraphData = null; var invertedNoImportsData = null; const merge_threads = {{ merge_threads|tojson }}; const memory_records = {{ memory_records|tojson }}; const inverted = {{ inverted|tojson }}; const temporal = packed_data.intervals != null;
```

----------------------------------------

TITLE: Defining Empty Extra Navigation Block (Jinja2)
DESCRIPTION: This snippet defines an empty block named `extra_nav`. This is useful for providing a placeholder that can be overridden by child templates, or to explicitly clear content from a parent block.
SOURCE: https://github.com/bloomberg/memray/blob/main/src/memray/reporters/templates/classic_base.html#_snippet_1

LANGUAGE: Jinja2
CODE:
```
{% block extra_nav %}{% endblock %}
```