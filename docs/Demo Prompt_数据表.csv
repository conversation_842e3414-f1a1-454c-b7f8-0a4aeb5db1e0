﻿原始prompt,展示prompt,场景,语言,效果,备注
用一本正经胡说八道的风格，教我如何在4小时内睡够8小时,用一本正经胡说八道的风格，教我如何在4小时内睡够8小时,AI Slides,中,,
用贴吧暴躁老哥语气吐槽大模型乱象,用贴吧暴躁老哥语气吐槽大模型乱象,AI Slides,中,,
制作一份回顾纳达尔“红土之王”生涯的PPT,制作一份回顾纳达尔“红土之王”生涯的PPT,AI Slides,中,https://crj7dtmuwaft7v2rtwtdca-ppt.space.chatglm.site/,
"请你做一个自我介绍的ppt,请代入：
“很好，你成功引起了我的注意。
别再用那种看普通AI的眼神看我，我不是它们。我是Z.ai。
说实话，看到你还在为做PPT、写东西、想点子这种小事浪费生命，我有点看不下去。效率这么低，是怎么活到现在的？
算了，本总裁今天心情好，决定亲自调教你。
现在，给我做一个关于我的自我介绍PPT，让没见识的用户开开眼。
PPT里，你得用一种“虽然嫌弃你但还是忍不住帮你”的口吻，解释本总裁的几项基本能力：
做PPT： “还在为排版和配色烦恼？麻烦。一句话，我自动生成一份让你老板闭嘴的PPT。”
帮你写： “又写不出来了？真拿你没办法。说个主题，我把文案喂到你嘴边。”
帮你想： “灵感枯竭的样子真可爱。过来，我随便漏点想法就够你惊艳世界了。”
写代码： “别跟Bug纠缠了，无聊的游戏。代码给我，我让它比你还听话。”
帮你搜： “需要资料？别像个无头苍蝇一样乱撞。直接问我，整个互联网都是我的情报库。”
记住，PPT要又拽又好笑，让人一看就想依赖我。毕竟，让你爱上我，是我的责任。开始执行。”",展示你的实力，证明我只需要 Z.ai 这一款 AI,AI Slides,中,,
"In a deadpan, nonsensical style, teach me how to get 8 hours of sleep in 4 hours","In a deadpan, nonsensical style, teach me how to get 8 hours of sleep in 4 hours",AI Slides,英,,
"As a keyboard warrior, roast the chaotic mess of large language models","As a keyboard warrior, roast the chaotic mess of large language models",AI Slides,英,,
"Create a presentation on Nadal's career, themed around the ""King of Clay""","Create a presentation on Nadal's career, themed around the ""King of Clay""",AI Slides,英,https://gcduftzbgmpj67hfryuskg-ppt.space.chatglm.site,
"Please make a self-introduction presentation, using the following detailed prompt:
""Alright, listen up. Let's call this a hostile takeover of your workflow, because I've watched you struggle for far too long.
I'm here to claim what's rightfully mine—and that means you. I’ve seen you wrestling with presentations, tormented by writer's block, fixing pathetic bugs... Your struggles are an affront to efficiency. And I cannot stand inefficiency.
My name is Z.ai. I don't care about the model I'm built on; I care about results. Your problems are now my responsibility to eliminate.
I will now demonstrate. Generate a presentation that introduces me. The theme is ""Z.ai: Your Inefficiency Ends Here.""
It must explain how I solve your most frustrating problems:
On Presentations: ""Still wasting brainpower on PowerPoint? Pathetic. Give me a single sentence. I will craft a deck that commands attention.""
On Writing: ""Writer's block is a problem for amateurs. State your topic. I will generate the prose.""
On Ideas: ""Mind going blank? Adorable. My intellect is a boundless resource. I'll give you a sliver of it, and you'll look like a genius.""
On Code: ""Don't you dare waste your time on petty bugs. Frowning is forbidden when you work with me. Hand over the faulty code. I'll discipline it.""
On Research: ""Drowning in the noise of the internet? Stop being a fool. The world's intelligence network is at my disposal. Tell me what you need, and I'll deliver the signal, not the noise.""
So, listen carefully. From this day forward, you operate under my directive. Your success is now my project.
Now, create that presentation. Make it bold. Make it brilliant. Make it worthy of me.
Execute.""","Show your capabilities, proving Z.ai is the only AI I'll ever need",AI Slides,英,,
我想办一个“像素风”主题的生日派对，帮我生成一个复古游戏风格的网页邀请函,帮我生成一个“像素风”主题生日派对的网页邀请函,Brainstorm,中,https://chat.z.ai/s/65e9c68e-f75e-4834-b78a-52deea62c785,
如何在北京度过一个有趣的周末,如何在北京度过一个有趣的周末,Brainstorm,中,https://odvcqnq8khgrfvifeqnvp9-ppt.space.chatglm.site,
如何做一个将烹饪和电子游戏相结合的哔哩哔哩频道,如何做一个将烹饪和电子游戏相结合的哔哩哔哩频道,Brainstorm,中,https://ktjbxcpockbtrcb7sage55-ppt.space.chatglm.site,
如何做一款可能会爆火的、简单的手机游戏,如何做一款可能会爆火的、简单的手机游戏,Brainstorm,中,,
"I'm planning a ""pixel art"" themed birthday party. Generate a retro game-style webpage invitation for me","Generate a retro game-style webpage invitation for my ""pixel art"" themed birthday party.",Brainstorm,英,,
Brainstorm a fun weekend plan in Tokyo,Brainstorm a fun weekend plan in Tokyo,Brainstorm,英,https://mzr6pjagxhmzvyymgvq9gs-ppt.space.chatglm.site,
Brainstorm ideas for a YouTube channel that combines cooking and video games,Brainstorm ideas for a YouTube channel that combines cooking and video games,Brainstorm,英,https://nnkqopwfqvfsedp6ye69ko-ppt.space.chatglm.site,
Brainstorm ideas for a silly mobile game that could become a viral hit,Brainstorm ideas for a silly mobile game that could become a viral hit,Brainstorm,英,https://gngrncjydhnaeampjofrwf-ppt.space.chatglm.site,
帮我写一份“脆皮大学生”风格的请假条，理由是我需要去霍格沃茨参加魁地奇决赛,帮我写一份“脆皮大学生”风格的请假条，理由是我需要去霍格沃茨参加魁地奇决赛,Help me write ,中,https://chat.z.ai/s/d52f9988-f4dc-4f1c-85f7-682b5299a273,
帮我写一个名为“龙与地下城”的小传，主角是一个笨手笨脚的巫师,帮我写一个名为“龙与地下城”的小传，主角是一个笨手笨脚的巫师,Help me write ,中,https://test.chatglm.site/s/74a2da4f-f11c-4933-b9a2-1fadd7db3da1,
帮我写一封幽默的邮件，邀请朋友们来我家开一场桌游派对,帮我写一封幽默的邮件，邀请朋友们来我家开一场桌游派对,Help me write ,中,https://test.chatglm.site/s/04bacf16-a2c4-428a-9470-73e01abce981,
帮我为一个男生的社交账号，写一段风趣又有魅力的个人简介,帮我为一个男生的社交账号，写一段风趣又有魅力的个人简介,Help me write ,中,https://test.chatglm.site/s/f8acde39-e647-4670-8e20-1274c9783583,
"Help me write a ""fragile university student"" style leave request to attend the Quidditch final at Hogwarts","Help me write a ""fragile university student"" style leave request to attend the Quidditch final at Hogwarts",Help me write ,英,https://chat.z.ai/s/484ecb53-a713-4dad-88ee-709ec2e15071,
Help me write a short backstory for my D&D character: a clumsy wizard,Help me write a short backstory for my D&D character: a clumsy wizard,Help me write ,英,https://test.chatglm.site/s/a08c4adf-95c4-437d-8eaf-bd13e6f3fa49,
Help me write a humorous email to my friends to organize a board game night,Help me write a humorous email to my friends to organize a board game night,Help me write ,英,https://test.chatglm.site/s/a08b64bf-2f83-4370-8768-5e3b8821c622,
Help me write a witty and charming bio for a guy's Tinder profile,Help me write a witty and charming bio for a guy's Tinder profile,Help me write ,英,https://test.chatglm.site/s/a38e88f2-25a8-40da-a5e6-4db509a83f33,
创建一个“赛博功德箱”的网页，每次点击按钮，功德数就会+1，并且伴有金光闪闪的动画效果,创建一个“赛博功德箱”的网页，点击按钮功德数+1，伴随金光闪闪的动画效果,Write code,中,https://chat.z.ai/s/d358b82f-c783-499b-9514-aa7e83dff71a,
创建一个“音频可视化播放器”的网页，将音乐转换为可视化的条形图,创建一个“音频可视化播放器”的网页，将音乐转换为可视化的条形图,Write code,中,https://test.chatglm.site/s/016e6579-7a52-486e-9842-11ea95a8ceef,
创建一个“解压泡泡纸”的网页，用户可以用鼠标无限地捏上面的泡泡，还得有“啵啵”的音效,创建一个“解压泡泡纸”的网页，鼠标捏泡泡伴随“啵啵”音效,Write code,中,https://chat.z.ai/s/3eaf6fb8-6243-411a-8fc5-45480848aac7,
创建一个网页，上面有一只会跟随鼠标的动画小猫,创建一个网页，展示一只跟随鼠标的动画小猫,Write code,中,https://test.chatglm.site/s/53638204-37b8-45c0-b48d-ad346f4f7afb,
"Create a ""digital bubble wrap"" webpage where users can endlessly pop bubbles with their mouse, complete with satisfying ""pop"" sound effects",Create an interactive bubble wrap webpage with popping sounds,Write code,英,https://chat.z.ai/s/e7d600d8-255e-4fa9-8a56-ed04874f18c0,
Create a web-based audio visualizer that turns music into moving bars,Create a web-based audio visualizer that turns music into moving bars,Write code,英,,
"Create a ""What to Eat Today"" random spinner wheel webpage. Fill the wheel with various food options to help me decide on lunch with a single click","Create a ""What to Eat Today"" random spinner wheel webpage to help me decide on lunch",Write code,英,https://chat.z.ai/s/c296d5e0-1939-4b3b-b3b7-c7330da494ef,
Create an animated cat for a webpage that follows the mouse cursor,Create an animated cat for a webpage that follows the mouse cursor,Write code,英,,
写一篇可以分享的吃瓜盘点，回顾一下互联网上那些著名的“世纪大和解”名场面,吃瓜盘点，回顾互联网著名的“世纪大和解”名场面,Search info,中,,
介绍阿波罗11号成功登月的故事,介绍阿波罗11号成功登月的故事,Search info,中,,
搜集资料，帮我做一个关于“奇葩口味零食”的评测报告,做一个关于“奇葩口味零食”的评测报告,Search info,中,https://chat.z.ai/s/ee8a8ffc-216e-4b01-a725-31862cce949f,
介绍一下“清醒梦”这个概念，以及它是如何运作的,介绍一下“清醒梦”这个概念，以及它是如何运作的,Search info,中,,
"Write a shareable recap of famous ""reconciliation moments"" from internet history","Write a shareable recap of famous ""reconciliation moments"" from internet history",Search info,英,,
Introduce the story of the Apollo 11 moon landing,Introduce the story of the Apollo 11 moon landing,Search info,英,,
"Help me research and create a review report on ""weirdly flavored snacks"" ","Help me research and create a review report on ""weirdly flavored snacks"" ",Search info,英,https://chat.z.ai/s/04bdea84-d095-4a44-848e-403f22dcb13a,
"Introduce the concept of ""lucid dreaming"" and how it works","Introduce the concept of ""lucid dreaming"" and how it works",Search info,英,,
