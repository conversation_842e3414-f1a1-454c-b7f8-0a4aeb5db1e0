# 转换服务配置指南

## 概述

SSE 转换服务支持灵活的配置方式，可以根据不同环境自动选择合适的服务端点，也支持手动配置。

## 配置方式

### 1. 自动配置（推荐）

系统会根据当前环境自动选择合适的配置：

- **开发环境** (`dev = true`): 使用本地服务 `http://localhost:3000`
- **生产环境** (`dev = false`): 使用远程服务 `https://cosmos-converter.chatglm.site`

```typescript
import { sseConversionService } from '$lib/apis/conversion';

// 使用默认配置（自动根据环境选择）
await sseConversionService.convertToPdf(htmlPages, options, callbacks);
```

### 2. 环境变量配置

通过设置 `PUBLIC_COSMOS_CONVERTER_BASE_URL` 环境变量来覆盖默认配置：

```bash
# 在 .env.local 文件中设置
PUBLIC_COSMOS_CONVERTER_BASE_URL=http://localhost:3000

# 或者在部署时设置
PUBLIC_COSMOS_CONVERTER_BASE_URL=https://your-custom-converter.com
```

**注意**：
- 环境变量必须以 `PUBLIC_` 开头，这是 SvelteKit 的约定
- 以 `PUBLIC_` 开头的变量会暴露给客户端（浏览器），因此不要包含敏感信息
- SvelteKit 使用 `$env/dynamic/public` 来访问这些变量

### 3. 代码中手动配置

#### 方式一：创建自定义服务实例

```typescript
import { createConversionService } from '$lib/apis/conversion';

// 创建使用自定义 URL 的服务实例
const customService = createConversionService({
  baseUrl: 'https://your-custom-converter.com'
});

await customService.convertToPdf(htmlPages, options, callbacks);
```

#### 方式二：更新现有实例的配置

```typescript
import { sseConversionService } from '$lib/apis/conversion';

// 动态更新服务 URL
sseConversionService.updateBaseUrl('https://your-custom-converter.com');

await sseConversionService.convertToPdf(htmlPages, options, callbacks);
```

## 配置优先级

配置的优先级从高到低为：

1. 代码中手动指定的 `baseUrl`
2. 环境变量 `PUBLIC_COSMOS_CONVERTER_BASE_URL`
3. 自动环境检测配置

## 工具函数

### 获取当前配置信息

```typescript
import { getDefaultConversionConfig } from '$lib/apis/conversion';

const config = getDefaultConversionConfig();
console.log('当前配置:', config);
// 输出示例:
// {
//   baseUrl: "http://localhost:8080",
//   apiBaseUrl: "http://localhost:8080/api/v1"
// }
```

### 检查当前服务 URL

```typescript
import { sseConversionService } from '$lib/apis/conversion';

const currentUrl = sseConversionService.getBaseUrl();
console.log('当前服务 URL:', currentUrl);
```

## 使用场景

### 本地开发

在本地开发时，确保 cosmos-converter 服务在 `http://localhost:3000` 运行：

```bash
cd cosmos_converter
npm start
```

系统会自动使用本地服务。

### 生产部署

在生产环境中，系统会自动使用 `https://cosmos-converter.chatglm.site`。

### 自定义部署

如果你有自己的转换服务实例，可以通过环境变量配置：

```bash
PUBLIC_COSMOS_CONVERTER_BASE_URL=https://your-converter.example.com
```

### 多环境切换

在某些特殊场景下，你可能需要在运行时动态切换服务：

```typescript
import { sseConversionService } from '$lib/apis/conversion';

// 切换到本地服务
sseConversionService.updateBaseUrl('http://localhost:8080');

// 切换到远程服务
sseConversionService.updateBaseUrl('https://your-remote-service.com');

// 切换到自定义服务
sseConversionService.updateBaseUrl('https://your-custom-service.com');
```

## SvelteKit 环境变量说明

SvelteKit 有一套独特的环境变量处理机制：

### 静态 vs 动态环境变量

- **静态环境变量** (`$env/static/public`): 在构建时替换，适用于构建时已知的值
- **动态环境变量** (`$env/dynamic/public`): 在运行时读取，适用于部署时配置的值

本项目使用动态环境变量，支持在不同部署环境中灵活配置。

### PUBLIC_ 前缀的意义

- 以 `PUBLIC_` 开头的环境变量会暴露给客户端（浏览器）
- 不以 `PUBLIC_` 开头的变量只能在服务端访问
- 转换服务 URL 需要在客户端使用，因此必须使用 `PUBLIC_` 前缀

## 注意事项

1. **环境变量必须以 `PUBLIC_` 开头**，这样才能在客户端代码中访问
2. **安全考虑**：由于 `PUBLIC_` 变量会暴露给客户端，不要在其中包含敏感信息
3. **CORS 配置**：确保自定义的转换服务正确配置了 CORS，允许来自你的前端域名的请求
4. **服务兼容性**：自定义的转换服务应该与标准的 API 接口兼容
5. **网络安全**：在生产环境中使用 HTTPS URL 