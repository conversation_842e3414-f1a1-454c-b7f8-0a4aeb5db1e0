# Converter 导出监控功能

## 概述

为 Open WebUI 的 converter 路由添加了 Prometheus 监控功能，用于统计和跟踪 PDF 和 PPT 导出的各种指标。

## 功能特性

### Prometheus 监控指标

使用 Prometheus 指标收集以下数据：

- **导出请求总数** (`converter_export_requests_total`)
  - 标签：`export_type`, `status`, `user_id`, `env`

- **导出页面总数** (`converter_export_pages_total`)
  - 标签：`export_type`, `user_id`, `env`

- **导出耗时分布** (`converter_export_duration_seconds`)
  - 标签：`export_type`, `env`
  - 桶：1s, 5s, 10s, 30s, 1min, 2min, 5min, 10min, 30min

- **导出文件大小分布** (`converter_export_file_size_bytes`)
  - 标签：`export_type`, `env`
  - 桶：1KB, 10KB, 100KB, 1MB, 10MB, 100MB, 1GB

## 监控的关键指标

- **导出次数统计**: 按类型(PDF/PPT)、状态(成功/失败)、用户分组
- **页数统计**: 总导出页数，按导出类型分组
- **性能指标**: 导出耗时分布、文件大小分布
- **质量指标**: 成功率、失败统计

## 使用方式

### 自动监控

监控功能已集成到现有的导出路由中：
- `/api/v1/converter/pdf/stream`
- `/api/v1/converter/ppt/stream`

每次导出请求都会自动记录相关指标。

### 查看 Prometheus 指标

指标可通过 `/metrics` 端点获取（如果启用了 Prometheus 监控）。

示例查询：
```promql
# 导出请求总数
converter_export_requests_total

# 按类型分组的导出次数
sum by (export_type) (converter_export_requests_total)

# 成功率
sum(converter_export_requests_total{status="success"}) / sum(converter_export_requests_total) * 100

# 平均导出时间
rate(converter_export_duration_seconds_sum[5m]) / rate(converter_export_duration_seconds_count[5m])
```

## 实现细节

### 监控器类 (`ConverterMonitor`)

- 记录导出开始和结束
- 上报 Prometheus 指标
- 轻量级设计，对性能影响极小

### 集成方式

- 在导出开始时调用 `record_export_start()`
- 在导出结束时调用 `record_export_end()`
- 异常处理中也会记录失败状态

## 配置

监控功能默认启用，无需额外配置。相关环境变量：

- `ENV`: 环境标识，用于 Prometheus 标签

## 注意事项

1. Prometheus 指标会占用少量内存
2. 所有统计都是按用户隔离的，保护用户隐私
3. 监控失败不影响导出功能正常运行
