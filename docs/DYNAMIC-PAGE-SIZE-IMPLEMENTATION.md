# 动态页面尺寸配置功能实现

## 🎯 功能概述

基于PPT渲染过程中每页宽高比不同的特点，实现了动态页面尺寸配置功能，支持每页独立的PDF页面尺寸配置。

## 🔧 实现内容

### 1. 类型定义更新

**文件**: `src/types/index.ts`

- 新增 `PageMetadata` 接口，支持页面尺寸信息
- 更新 `HtmlPage` 接口，metadata支持对象格式

```typescript
// 页面元数据结构
export interface PageMetadata {
  pageWidth?: number;  // 页面宽度 (cm)
  pageHeight?: number; // 页面高度 (cm)
  [key: string]: any;  // 支持其他元数据
}

// HTML Page 数据结构
export interface HtmlPage {
  content: string; // 完整的 HTML 内容
  metadata?: string | PageMetadata; // 支持字符串或对象格式
}
```

### 2. 后端服务更新

**文件**: `src/services/cloudConvertService.ts`

#### 主要修改：

1. **createTempHtmlFiles方法**
   - 返回文件列表和元数据列表
   - 解析每页的metadata，支持字符串和对象格式
   - 错误处理和兜底机制

2. **createHtmlToPdfJob方法**
   - 支持pageMetadata参数
   - 为每个转换任务单独设置页面尺寸
   - 合并全局选项和页面特定选项

3. **createHtmlToPptJob方法**
   - 同样支持每页不同的尺寸配置
   - 保持与PDF转换一致的逻辑

#### 核心逻辑：

```typescript
// 为每个HTML文件创建转换任务
for (let i = 0; i < htmlFiles.length; i++) {
  // 获取当前页面的元数据
  const currentPageMetadata = pageMetadata[i];
  
  // 合并全局选项和页面特定选项
  const pageOptions = { ...options };
  if (currentPageMetadata?.pageWidth && currentPageMetadata?.pageHeight) {
    pageOptions.pageWidth = currentPageMetadata.pageWidth;
    pageOptions.pageHeight = currentPageMetadata.pageHeight;
  }
  
  tasks[`convert-html-${i}`] = {
    operation: 'convert',
    input: `upload-html-${i}`,
    output_format: 'pdf',
    engine: 'chrome',
    ...this.getPdfOptions(pageOptions)
  };
}
```

### 3. 前端导出逻辑更新

**文件**: `src/lib/components/chat/PPTExportButton.svelte`

#### 新增功能：

1. **页面尺寸计算函数**
   ```typescript
   const calculatePageDimensions = async (htmlContent: string): Promise<{ width: number; height: number }>
   ```
   - 创建临时iframe测量页面实际尺寸
   - 应用与PPTRender.svelte相同的兜底逻辑
   - 像素到厘米的精确转换 (96 DPI标准)

2. **导出流程优化**
   - 在导出前计算每页的实际尺寸
   - 将尺寸信息嵌入到每页的metadata中
   - 支持异步并行计算提高性能

#### 尺寸计算逻辑：

```typescript
// 应用与PPTRender.svelte相同的兜底逻辑
if (width < 1280) {
  width = 1280;
  height = height > 1280 ? 720 : height;
}

// 转换像素到厘米 (96 DPI: 1英寸 = 96像素 = 2.54厘米)
const widthCm = (width / 96) * 2.54;
const heightCm = (height / 96) * 2.54;
```

### 4. 前端服务接口更新

**文件**: `src/lib/apis/conversion/index.ts`

- 更新 `HtmlPage` 接口，支持metadata字段
- 保持与后端类型定义的一致性

## 🎯 技术特点

### 1. 精确尺寸计算
- 基于96 DPI标准进行像素到厘米的转换
- 与PPTRender.svelte保持一致的尺寸计算逻辑
- 支持1280px宽度兜底机制

### 2. 错误处理机制
- 尺寸计算失败时使用默认值 (33.87cm x 19.05cm)
- metadata解析失败时的兜底处理
- 临时DOM元素的正确清理

### 3. 性能优化
- 异步并行计算多页面尺寸
- 临时iframe的高效创建和销毁
- 最小化DOM操作影响

### 4. 向后兼容
- 支持原有的字符串metadata格式
- 保持现有API接口不变
- 渐进式功能增强

## 🧪 测试验证

创建了测试文件 `test-dynamic-page-size.js`，包含：
- 不同尺寸的测试页面 (1280x720, 1600x900, 800x600)
- 完整的转换流程测试
- 作业状态监控

## 📋 使用示例

```typescript
// 前端使用示例
const htmlPages = await Promise.all(
  pptPages.pages.map(async (page, index) => {
    const dimensions = await calculatePageDimensions(page as string);
    return {
      content: page as string,
      metadata: {
        pageWidth: dimensions.width,
        pageHeight: dimensions.height
      }
    };
  })
);

// 后端会自动为每页应用对应的尺寸配置
await sseConversionService.convertToPdf(htmlPages, options, callbacks);
```

## 🔄 工作流程

1. **前端计算**: 遍历每个PPT页面，计算实际渲染尺寸
2. **元数据嵌入**: 将尺寸信息嵌入到每页的metadata中
3. **后端解析**: 后端解析每页的metadata，提取尺寸信息
4. **动态配置**: 为每个转换任务单独设置页面尺寸参数
5. **PDF生成**: CloudConvert根据每页的尺寸配置生成PDF
6. **页面合并**: 将不同尺寸的PDF页面合并为最终文档

## ✅ 预期效果

- ✅ 消除PDF输出时的白边问题
- ✅ 保持每页的原始宽高比
- ✅ 支持混合尺寸的PPT文档
- ✅ 提高PDF输出质量和准确性
