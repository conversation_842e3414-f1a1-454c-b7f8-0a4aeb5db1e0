# PPT HTML 转 PDF 优化配置指南

## 前端PPT渲染分析

### 当前PPT规格
- **固定宽度**: 1280px
- **固定高度**: 720px
- **宽高比**: 16:9 (标准演示文稿比例)
- **DPI基准**: 96 DPI (Web标准)

### 前端渲染逻辑
```typescript
// PPTViewer.svelte 中的关键配置
const targetWidth = 1280;
const targetHeight = 720;

// iframe 固定尺寸
class="absolute w-[1280px] h-[720px]"

// 自适应缩放
const scale = Math.min(scaleX, scaleY);
node.style.transform = `scale(${scale})`;
```

## 最佳PDF转换配置

### 配置1：精确尺寸配置（推荐）

基于96 DPI精确计算的页面尺寸：

```json
{
  "pageWidth": 33.87,
  "pageHeight": 19.05,
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "printBackground": true,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "zoom": 1.0,
  "pageOrientation": "landscape"
}
```

**计算依据**:
- 宽度: 1280px ÷ 96 DPI × 2.54cm = 33.87cm
- 高度: 720px ÷ 96 DPI × 2.54cm = 19.05cm

### 配置2：实用尺寸配置

稍微调整以获得更好的打印效果：

```json
{
  "pageWidth": 33.8,
  "pageHeight": 19.0,
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "printBackground": true,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "zoom": 1.0,
  "pageOrientation": "landscape"
}
```

### 配置3：标准演示文稿尺寸

使用常见的10英寸×5.6英寸尺寸：

```json
{
  "pageWidth": 25.4,
  "pageHeight": 14.3,
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "printBackground": true,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "zoom": 1.0,
  "pageOrientation": "landscape"
}
```

### 配置4：A4横向兼容配置

适用于需要A4打印的场景：

```json
{
  "pageFormat": "a4",
  "pageOrientation": "landscape",
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "printBackground": true,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "waitTime": 1500,
  "zoom": 0.85
}
```

## 参数详解

### 关键参数说明

| 参数 | 值 | 说明 |
|------|----|----|
| `pageWidth` | 33.87cm | 基于1280px精确计算的宽度 |
| `pageHeight` | 19.05cm | 基于720px精确计算的高度 |
| `marginTop/Bottom/Left/Right` | 0 | 消除所有白边 |
| `printBackground` | true | 保持PPT背景样式 |
| `cssMediaType` | "screen" | 使用屏幕样式而非打印样式 |
| `waitUntil` | "networkidle0" | 等待所有网络请求完成 |
| `waitTime` | 1500ms | 额外等待时间确保渲染完成 |
| `zoom` | 1.0 | 保持原始缩放比例 |
| `pageOrientation` | "landscape" | 横向布局匹配16:9比例 |

### DPI计算说明

**Web标准 (96 DPI)**:
- 1280px = 1280 ÷ 96 × 2.54 = 33.87cm
- 720px = 720 ÷ 96 × 2.54 = 19.05cm

**印刷标准 (72 DPI)**:
- 1280px = 1280 ÷ 72 × 2.54 = 45.16cm
- 720px = 720 ÷ 72 × 2.54 = 25.4cm

CloudConvert使用Chrome引擎，默认采用96 DPI，因此推荐使用Web标准计算。

## 使用示例

### JavaScript调用示例

```javascript
const response = await axios.post('/api/convert/pdf', {
  htmlPages: [
    {
      content: pptHtmlContent,
      metadata: "PPT页面"
    }
  ],
  options: {
    pageWidth: 33.87,
    pageHeight: 19.05,
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0,
    printBackground: true,
    cssMediaType: "screen",
    waitUntil: "networkidle0",
    waitTime: 1500,
    zoom: 1.0,
    pageOrientation: "landscape"
  }
});
```

### 流式转换示例

```javascript
const response = await axios.post('/api/convert/pdf/stream', {
  htmlPages: [{ content: pptHtmlContent }],
  options: configurations.exactSize
}, {
  responseType: 'stream'
});
```

## 测试验证

### 快速测试

```bash
# 使用精确尺寸配置测试
node test-ppt-conversion.js

# 测试所有配置
node examples/ppt-to-pdf-example.js
```

### 验证清单

- [ ] PDF无上下白边
- [ ] PDF无左右白边  
- [ ] 内容比例正确
- [ ] 背景样式保持
- [ ] 文字清晰可读
- [ ] 图片正常显示

## 故障排除

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 仍有白边 | 边距设置不为0 | 确保所有margin参数为0 |
| 内容被裁剪 | 页面尺寸太小 | 使用精确尺寸配置或调整zoom |
| 背景丢失 | 背景渲染关闭 | 设置printBackground: true |
| 样式异常 | 使用打印样式 | 设置cssMediaType: "screen" |
| 加载不完整 | 等待时间不足 | 增加waitTime或使用networkidle0 |

### 微调建议

如果使用推荐配置后仍有问题，可以尝试：

1. **调整zoom值**: 0.95-1.05之间微调
2. **调整页面尺寸**: ±0.5cm范围内调整
3. **增加等待时间**: waitTime设置为2000-3000ms
4. **使用不同等待条件**: 尝试"domcontentloaded"或"load"

## 前端CSS优化建议

当前前端实现已经很好，但可以考虑以下优化：

```css
/* 确保PPT内容完全填充容器 */
.ppt-container {
  width: 1280px;
  height: 720px;
  overflow: hidden;
  background: transparent;
}

/* 确保所有元素使用box-sizing: border-box */
.ppt-content * {
  box-sizing: border-box;
}
```

这些配置应该能够完全解决PPT HTML转PDF的白边问题，同时保持最佳的视觉效果。
