# Stream Body Handler 首响时间计算修复

## 问题描述

在多轮 tool call（循环的模型请求）场景下，`stream_body_handler` 对于首响时间（TTFT - Time To First Token）的计算方式存在问题。

### 问题原因

在多轮 tool call 的循环中，每次调用 `stream_body_handler` 时都会创建一个新的 `metrics_tracker`，但是使用的是最初的 `request_start_time`。这导致：

1. **第一轮请求**：首响时间计算正确（从用户请求开始到第一个 token）
2. **后续轮次**：首响时间计算错误（仍然从最初的用户请求开始计算，而不是从当前轮次的模型请求开始）

### 具体场景

```
用户请求 -> 模型响应（包含 tool call）-> 执行工具 -> 模型再次响应
         ^                                    ^
         |                                    |
    request_start_time                  应该从这里开始计算首响时间
                                       但之前从 request_start_time 开始计算
```

## 修复方案

### 1. 修改 `stream_body_handler` 函数签名

在 `backend/open_webui/utils/middleware.py` 中，为 `stream_body_handler` 添加 `round_start_time` 参数：

```python
async def stream_body_handler(response, round_start_time=None):
    # 创建指标跟踪器 - 对于多轮tool call，使用当前轮次的开始时间
    current_round_start_time = (
        round_start_time
        if round_start_time is not None
        else request_start_time
    )
    metrics_tracker = create_metrics_tracker(
        selected_model_id, metadata, current_round_start_time
    )
```

### 2. 在多轮 tool call 循环中传递正确的开始时间

在多轮 tool call 的循环中，记录每轮请求的开始时间并传递给 `stream_body_handler`：

```python
try:
    # 记录当前轮次的开始时间，用于准确计算首响时间
    current_round_start_time = time.time()
    
    res = await generate_chat_completion(...)
    
    if isinstance(res, StreamingResponse):
        # 传递当前轮次的开始时间，确保首响时间计算正确
        await stream_body_handler(res, current_round_start_time)
```

### 3. 保持第一轮请求的兼容性

第一轮请求调用 `stream_body_handler(response)` 时不传递 `round_start_time` 参数，函数会自动使用原始的 `request_start_time`，保持向后兼容。

## 修复效果

修复后，每轮 tool call 的首响时间计算都是准确的：

1. **第一轮请求**：从用户请求开始到第一个 token 的时间
2. **后续轮次**：从当前轮次模型请求开始到第一个 token 的时间

这样可以更准确地监控每轮模型请求的性能，而不是累积的总时间。

## 相关文件

- `backend/open_webui/utils/middleware.py` - 主要修改文件
- `backend/open_webui/utils/handlers/metrics_handler.py` - 指标跟踪器实现
- `backend/open_webui/utils/handlers/zero_stream_handler.py` - Zero 模型处理器（无需修改，因为不涉及多轮 tool call）

## 测试建议

1. 测试单轮对话的首响时间计算是否正常
2. 测试多轮 tool call 场景下每轮的首响时间是否准确
3. 验证指标上报到 Prometheus 的数据是否正确
