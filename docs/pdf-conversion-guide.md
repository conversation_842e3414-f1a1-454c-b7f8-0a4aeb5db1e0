# PDF 转换配置指南

## 解决 PPT HTML 转 PDF 白边问题

当从 PPT HTML 转换为 PDF 时，经常会遇到白边问题。这通常是由于页面尺寸、边距或缩放设置不当造成的。以下是解决方案：

## 推荐配置

### 1. 零边距配置（推荐用于 PPT）

```json
{
  "htmlPages": [...],
  "options": {
    "marginTop": 0,
    "marginBottom": 0,
    "marginLeft": 0,
    "marginRight": 0,
    "printBackground": true,
    "cssMediaType": "screen",
    "waitUntil": "networkidle0",
    "zoom": 1.0
  }
}
```

### 2. 自定义页面尺寸配置

```json
{
  "htmlPages": [...],
  "options": {
    "pageWidth": 25.4,
    "pageHeight": 19.05,
    "marginTop": 0,
    "marginBottom": 0,
    "marginLeft": 0,
    "marginRight": 0,
    "printBackground": true,
    "cssMediaType": "screen",
    "zoom": 1.0
  }
}
```

### 3. 标准演示文稿尺寸配置

```json
{
  "htmlPages": [...],
  "options": {
    "pageFormat": "a4",
    "pageOrientation": "landscape",
    "marginTop": 5,
    "marginBottom": 5,
    "marginLeft": 5,
    "marginRight": 5,
    "printBackground": true,
    "cssMediaType": "screen",
    "waitUntil": "load",
    "zoom": 0.8
  }
}
```

## 参数说明

### 页面尺寸参数

- `pageWidth`: 页面宽度（厘米）
- `pageHeight`: 页面高度（厘米）
- `pageFormat`: 标准页面格式（a4, a3, letter 等）
- `pageOrientation`: 页面方向（portrait, landscape）

### 边距参数（单位：毫米）

- `marginTop`: 上边距
- `marginBottom`: 下边距
- `marginLeft`: 左边距
- `marginRight`: 右边距

### 渲染参数

- `zoom`: 缩放级别（0.1-3.0，默认 1.0）
- `printBackground`: 是否渲染背景（true/false）
- `cssMediaType`: CSS媒体类型（screen/print）

### 等待参数

- `waitUntil`: 等待条件
  - `load`: 等待页面加载完成
  - `domcontentloaded`: 等待DOM加载完成
  - `networkidle0`: 等待网络空闲（0个连接）
  - `networkidle2`: 等待网络空闲（最多2个连接）
- `waitForElement`: 等待特定元素（CSS选择器）
- `waitTime`: 额外等待时间（毫秒）

## 常见问题解决方案

### 问题1：PDF有白边
**解决方案**：设置所有边距为0
```json
{
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0
}
```

### 问题2：内容被裁剪
**解决方案**：调整缩放级别
```json
{
  "zoom": 0.8
}
```

### 问题3：背景丢失
**解决方案**：启用背景渲染
```json
{
  "printBackground": true,
  "cssMediaType": "screen"
}
```

### 问题4：内容加载不完整
**解决方案**：增加等待时间
```json
{
  "waitUntil": "networkidle0",
  "waitTime": 2000
}
```

## PPT 特定建议

对于 PPT HTML 转 PDF，推荐使用以下配置：

```json
{
  "marginTop": 0,
  "marginBottom": 0,
  "marginLeft": 0,
  "marginRight": 0,
  "printBackground": true,
  "cssMediaType": "screen",
  "waitUntil": "networkidle0",
  "zoom": 1.0,
  "pageOrientation": "landscape"
}
```

这个配置可以最大程度地保持 PPT 的原始外观，避免白边问题。
