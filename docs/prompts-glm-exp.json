[{"group_name": "AI Slides", "group_name_en": "AI Slides", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M13.6666 2.20001H2.33325V11.5333H13.6666V2.20001Z\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linejoin=\"round\"/><path d=\"M5.33325 14.2L7.99992 11.5333L10.6666 14.2\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M4.64136 8.5876L6.52219 6.75166L8.00282 8.19996L11.3224 4.8736\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M1.33325 2.20001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "分析《老友记》人物性格", "name_en": "Analyze 'Friends' Character Personalities", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "制作一份分析经典美剧《老友记》中的人物MBTI的PPT", "prompt_en": "Create a presentation analyzing the personality types in the TV show \"Friends\""}, {"name": "都市夜景摄影作品集", "name_en": "Urban Nightscapes Photography Portfolio", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "制作一份以“都市夜景”为主题的摄影师作品集", "prompt_en": "Create a portfolio PPT for a photographer with the theme \"Urban Nightscapes\""}, {"name": "回顾纳达尔“红土之王”生涯", "name_en": "<PERSON><PERSON>'s 'King of Clay' Career", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "制作一份回顾纳达尔“红土之王”生涯的PPT", "prompt_en": "Create a presentation on <PERSON><PERSON>'s career, themed around the \"King of Clay\""}, {"name": "苹果公司季度业绩回顾", "name_en": "Apple's Quarterly Performance Review", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "制作一份关于苹果公司上一季度的业绩表现PPT", "prompt_en": "Create a PPT for the performance review of Apple last quarter"}], "flags": ["ppt_composer"], "features": [{"type": "mcp", "server": "ppt-maker", "status": "pinned"}, {"type": "mcp", "server": "deep-web-search", "status": "pinned"}]}, {"group_name": "帮我写", "group_name_en": "Help me write", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M2.33331 14H14.3333\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M3.66669 8.90663V11.3333H6.10575L13 4.43603L10.5651 2L3.66669 8.90663Z\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "抖音短视频脚本", "name_en": "TikTok Video Script", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一个简短、有趣的抖音视频脚本", "prompt_en": "Help me write a short, funny script for a TikTok video"}, {"name": "龙与地下城角色背景", "name_en": "D&D Character Backstory", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一个名为“龙与地下城”的小传，主角是一个笨手笨脚的巫师", "prompt_en": "Help me write a short backstory for my D&D character: a clumsy wizard"}, {"name": "桌游派对邀请函", "name_en": "Board Game Night Invitation", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一封幽默的邮件，邀请朋友们来我家开一场桌游派对", "prompt_en": "Help me write a humorous email to my friends to organize a board game night"}, {"name": "Tinder个人简介", "name_en": "Tinder Bio", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我为一个男生的Tinder账号，写一段风趣又有魅力的个人简介", "prompt_en": "Help me write a witty and charming bio for a guy's Tinder profile"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}, {"type": "mcp", "server": "ppt-maker", "status": "off"}]}, {"group_name": "头脑风暴", "group_name_en": "Brainstorm", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M13.3334 6.66668C13.3334 8.93591 11.9162 10.8741 9.91852 11.6445H8.00002H6.08152C4.08389 10.8741 2.66669 8.93591 2.66669 6.66668C2.66669 3.72114 5.05449 1.33334 8.00002 1.33334C10.9456 1.33334 13.3334 3.72114 13.3334 6.66668Z\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9.91858 11.6445L9.69221 14.361C9.67781 14.5338 9.53338 14.6667 9.36001 14.6667H6.64011C6.46674 14.6667 6.32231 14.5338 6.30794 14.361L6.08154 11.6445\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M6 5.66666L8 7.50003M8 7.50003L10 5.66666M8 7.50003V9.50003\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "小客厅装饰点子", "name_en": "Small Living Room Decor Ideas", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何装饰一间小客厅", "prompt_en": "Brainstorm ideas for decorating a small living room"}, {"name": "有趣的周末计划", "name_en": "Fun Weekend Plan Ideas", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何在北京度过一个有趣的周末", "prompt_en": "Brainstorm a fun weekend plan in Tokyo"}, {"name": "跨界视频频道点子", "name_en": "Cross-Genre Channel Ideas", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何做一个将烹饪和电子游戏相结合的哔哩哔哩频道", "prompt_en": "Brainstorm ideas for a YouTube channel that combines cooking and video games"}, {"name": "病毒式手游点子", "name_en": "Viral Mobile Game Ideas", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何做一款可能会爆火的、简单的手机游戏", "prompt_en": "Brainstorm ideas for a silly mobile game that could become a viral hit"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}, {"type": "mcp", "server": "ppt-maker", "status": "off"}]}, {"group_name": "写代码", "group_name_en": "Write code", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M14 2.66666H1.99998C1.63179 2.66666 1.33331 2.96513 1.33331 3.33332V12.6667C1.33331 13.0348 1.63179 13.3333 1.99998 13.3333H14C14.3682 13.3333 14.6666 13.0348 14.6666 12.6667V3.33332C14.6666 2.96513 14.3682 2.66666 14 2.66666Z\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linejoin=\"round\"/><path d=\"M4 6L6.33333 8L4 10\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M7.66669 10.6667H12\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "复古贪吃蛇游戏", "name_en": "Retro Snake Game", "prompt": "创建一个经典的复古“贪吃蛇”游戏", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt_en": "Create a classic retro \"Snake\" game"}, {"name": "音频可视化播放器", "name_en": "Audio Visualizer Player", "prompt": "创建一个“音频可视化播放器”，将音乐转换为可视化的条形图", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt_en": "Create an \"audio visualization player\" that turns music into visualized bars"}, {"name": "动态星空场景", "name_en": "Dynamic Starry Night Scene", "prompt": "创建一个动态的“星空之夜”场景，其中有闪烁的星星和移动的月亮", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt_en": "Create a dynamic \"starry night\" scene, featuring twinkling stars and a moving moon"}, {"name": "跟随鼠标的动画猫", "name_en": "Cursor-Following Animated Cat", "prompt": "创建一个网页，上面有一只会跟随鼠标的动画小猫", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt_en": "Create a webpage with an animated cat that follows the mouse cursor"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "off"}, {"type": "mcp", "server": "ppt-maker", "status": "off"}]}, {"group_name": "搜索信息", "group_name_en": "Search Info", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompts": [{"name": "第一代iPhone", "name_en": "The First iPhone", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍第一代iPhone的发布及其带来的影响", "prompt_en": "Introduce the launch of the first iPhone and its impact"}, {"name": "阿波罗11号登月", "name_en": "Apollo 11 Moon Landing", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍阿波罗11号成功登月的故事", "prompt_en": "Introduce the story of the Apollo 11 moon landing"}, {"name": "巴拿马运河", "name_en": "The Panama Canal", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍巴拿马运河的建造过程与关键的工程挑战", "prompt_en": "Introduce the construction process and key engineering challenges of the Panama Canal"}, {"name": "清醒梦", "name_en": "<PERSON>id Dreaming", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍一下“清醒梦”这个概念，以及它是如何运作的", "prompt_en": "Introduce the concept of \"lucid dreaming\" and how it works"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}, {"type": "mcp", "server": "ppt-maker", "status": "off"}]}]