[{"group_name": "AI PPT", "group_name_en": "AI Slides", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M13.6666 2.20001H2.33325V11.5333H13.6666V2.20001Z\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linejoin=\"round\"/><path d=\"M5.33325 14.2L7.99992 11.5333L10.6666 14.2\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M4.64136 8.5876L6.52219 6.75166L8.00282 8.19996L11.3224 4.8736\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M1.33325 2.20001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "tag": "🔥", "tag_en": "🔥", "prompts": [{"name": "用一本正经胡说八道的风格，教我如何在4小时内睡够8小时", "name_en": "In a deadpan, nonsensical style, teach me how to get 8 hours of sleep in 4 hours", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "用一本正经胡说八道的风格，教我如何在4小时内睡够8小时", "prompt_en": "In a deadpan, nonsensical style, teach me how to get 8 hours of sleep in 4 hours"}, {"name": "用贴吧暴躁老哥语气吐槽大模型乱象", "name_en": "As a keyboard warrior, roast the chaotic mess of large language models", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "用贴吧暴躁老哥语气吐槽大模型乱象", "prompt_en": "As a keyboard warrior, roast the chaotic mess of large language models"}, {"name": "制作一份回顾纳达尔\"红土之王\"生涯的PPT", "name_en": "Create a presentation on <PERSON><PERSON>'s career, themed around the \"King of Clay\"", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "制作一份回顾纳达尔\"红土之王\"生涯的PPT", "prompt_en": "Create a presentation on <PERSON><PERSON>'s career, themed around the \"King of Clay\""}, {"name": "展示你的实力，证明我只需要 Z.ai 这一款 AI", "name_en": "Show your capabilities, proving Z.ai is the only AI I'll ever need", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "请你做一个自我介绍的ppt,请代入：\n\"很好，你成功引起了我的注意。\n别再用那种看普通AI的眼神看我，我不是它们。我是Z.ai。\n说实话，看到你还在为做PPT、写东西、想点子这种小事浪费生命，我有点看不下去。效率这么低，是怎么活到现在的？\n算了，本总裁今天心情好，决定亲自调教你。\n现在，给我做一个关于我的自我介绍PPT，让没见识的用户开开眼。\nPPT里，你得用一种'虽然嫌弃你但还是忍不住帮你'的口吻，解释本总裁的几项基本能力：\n做PPT： '还在为排版和配色烦恼？麻烦。一句话，我自动生成一份让你老板闭嘴的PPT。'\n帮你写： '又写不出来了？真拿你没办法。说个主题，我把文案喂到你嘴边。'\n帮你想： '灵感枯竭的样子真可爱。过来，我随便漏点想法就够你惊艳世界了。'\n写代码： '别跟Bug纠缠了，无聊的游戏。代码给我，我让它比你还听话。'\n帮你搜： '需要资料？别像个无头苍蝇一样乱撞。直接问我，整个互联网都是我的情报库。'\n记住，PPT要又拽又好笑，让人一看就想依赖我。毕竟，让你爱上我，是我的责任。开始执行。\"", "prompt_en": "Please make a self-introduction presentation, using the following detailed prompt:\n\"Alright, listen up. Let's call this a hostile takeover of your workflow, because I've watched you struggle for far too long.\nI'm here to claim what's rightfully mine—and that means you. I've seen you wrestling with presentations, tormented by writer's block, fixing pathetic bugs... Your struggles are an affront to efficiency. And I cannot stand inefficiency.\nMy name is Z.ai. I don't care about the model I'm built on; I care about results. Your problems are now my responsibility to eliminate.\nI will now demonstrate. Generate a presentation that introduces me. The theme is \"Z.ai: Your Inefficiency Ends Here.\"\nIt must explain how I solve your most frustrating problems:\nOn Presentations: \"Still wasting brainpower on PowerPoint? Pathetic. Give me a single sentence. I will craft a deck that commands attention.\"\nOn Writing: \"Writer's block is a problem for amateurs. State your topic. I will generate the prose.\"\nOn Ideas: \"Mind going blank? Adorable. My intellect is a boundless resource. I'll give you a sliver of it, and you'll look like a genius.\"\nOn Code: \"Don't you dare waste your time on petty bugs. Frowning is forbidden when you work with me. Hand over the faulty code. I'll discipline it.\"\nOn Research: \"Drowning in the noise of the internet? Stop being a fool. The world's intelligence network is at my disposal. Tell me what you need, and I'll deliver the signal, not the noise.\"\nSo, listen carefully. From this day forward, you operate under my directive. Your success is now my project.\nNow, create that presentation. Make it bold. Make it brilliant. Make it worthy of me.\nExecute.\""}], "flags": ["ppt_composer"], "features": [{"type": "mcp", "server": "deep-web-search", "status": "pinned"}]}, {"group_name": "帮我写", "group_name_en": "Help me write", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M2.33331 14H14.3333\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M3.66669 8.90663V11.3333H6.10575L13 4.43603L10.5651 2L3.66669 8.90663Z\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "帮我写一份\"脆皮大学生\"风格的请假条，理由是我需要去霍格沃茨参加魁地奇决赛", "name_en": "Help me write a \"fragile university student\" style leave request to attend the Quidditch final at Hogwarts", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一份\"脆皮大学生\"风格的请假条，理由是我需要去霍格沃茨参加魁地奇决赛", "prompt_en": "Help me write a \"fragile university student\" style leave request to attend the Quidditch final at Hogwarts"}, {"name": "帮我写一个名为\"龙与地下城\"的小传，主角是一个笨手笨脚的巫师", "name_en": "Help me write a short backstory for my D&D character: a clumsy wizard", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一个名为\"龙与地下城\"的小传，主角是一个笨手笨脚的巫师", "prompt_en": "Help me write a short backstory for my D&D character: a clumsy wizard"}, {"name": "帮我写一封幽默的邮件，邀请朋友们来我家开一场桌游派对", "name_en": "Help me write a humorous email to my friends to organize a board game night", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我写一封幽默的邮件，邀请朋友们来我家开一场桌游派对", "prompt_en": "Help me write a humorous email to my friends to organize a board game night"}, {"name": "帮我为一个男生的社交账号，写一段风趣又有魅力的个人简介", "name_en": "Help me write a witty and charming bio for a guy's Tinder profile", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "帮我为一个男生的社交账号，写一段风趣又有魅力的个人简介", "prompt_en": "Help me write a witty and charming bio for a guy's Tinder profile"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}]}, {"group_name": "帮我想", "group_name_en": "Brainstorm", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M13.3334 6.66668C13.3334 8.93591 11.9162 10.8741 9.91852 11.6445H8.00002H6.08152C4.08389 10.8741 2.66669 8.93591 2.66669 6.66668C2.66669 3.72114 5.05449 1.33334 8.00002 1.33334C10.9456 1.33334 13.3334 3.72114 13.3334 6.66668Z\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9.91858 11.6445L9.69221 14.361C9.67781 14.5338 9.53338 14.6667 9.36001 14.6667H6.64011C6.46674 14.6667 6.32231 14.5338 6.30794 14.361L6.08154 11.6445\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M6 5.66666L8 7.50003M8 7.50003L10 5.66666M8 7.50003V9.50003\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "帮我生成一个\"像素风\"主题生日派对的网页邀请函", "name_en": "Generate a retro game-style webpage invitation for my \"pixel art\" themed birthday party.", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "我想办一个\"像素风\"主题的生日派对，帮我生成一个复古游戏风格的网页邀请函", "prompt_en": "I'm planning a \"pixel art\" themed birthday party. Generate a retro game-style webpage invitation for me"}, {"name": "如何在北京度过一个有趣的周末", "name_en": "Brainstorm a fun weekend plan in Tokyo", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何在北京度过一个有趣的周末", "prompt_en": "Brainstorm a fun weekend plan in Tokyo"}, {"name": "如何做一个将烹饪和电子游戏相结合的哔哩哔哩频道", "name_en": "Brainstorm ideas for a YouTube channel that combines cooking and video games", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何做一个将烹饪和电子游戏相结合的哔哩哔哩频道", "prompt_en": "Brainstorm ideas for a YouTube channel that combines cooking and video games"}, {"name": "如何做一款可能会爆火的、简单的手机游戏", "name_en": "Brainstorm ideas for a silly mobile game that could become a viral hit", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "如何做一款可能会爆火的、简单的手机游戏", "prompt_en": "Brainstorm ideas for a silly mobile game that could become a viral hit"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}]}, {"group_name": "写代码", "group_name_en": "Write code", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M14 2.66666H1.99998C1.63179 2.66666 1.33331 2.96513 1.33331 3.33332V12.6667C1.33331 13.0348 1.63179 13.3333 1.99998 13.3333H14C14.3682 13.3333 14.6666 13.0348 14.6666 12.6667V3.33332C14.6666 2.96513 14.3682 2.66666 14 2.66666Z\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linejoin=\"round\"/><path d=\"M4 6L6.33333 8L4 10\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M7.66669 10.6667H12\" stroke=\"currentColor\" stroke-width=\"1.33\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></g></svg>", "prompts": [{"name": "创建一个\"赛博功德箱\"的网页，点击按钮功德数+1，伴随金光闪闪的动画效果", "name_en": "Create an interactive bubble wrap webpage with popping sounds", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "创建一个\"赛博功德箱\"的网页，每次点击按钮，功德数就会+1，并且伴有金光闪闪的动画效果", "prompt_en": "Create a \"digital bubble wrap\" webpage where users can endlessly pop bubbles with their mouse, complete with satisfying \"pop\" sound effects"}, {"name": "创建一个\"音频可视化播放器\"的网页，将音乐转换为可视化的条形图", "name_en": "Create a web-based audio visualizer that turns music into moving bars", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "创建一个\"音频可视化播放器\"的网页，将音乐转换为可视化的条形图", "prompt_en": "Create a web-based audio visualizer that turns music into moving bars"}, {"name": "创建一个\"解压泡泡纸\"的网页，鼠标捏泡泡伴随\"啵啵\"音效", "name_en": "Create a \"What to Eat Today\" random spinner wheel webpage to help me decide on lunch", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "创建一个\"解压泡泡纸\"的网页，用户可以用鼠标无限地捏上面的泡泡，还得有\"啵啵\"的音效", "prompt_en": "Create a \"What to Eat Today\" random spinner wheel webpage. Fill the wheel with various food options to help me decide on lunch with a single click"}, {"name": "创建一个网页，展示一只跟随鼠标的动画小猫", "name_en": "Create an animated cat for a webpage that follows the mouse cursor", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "创建一个网页，上面有一只会跟随鼠标的动画小猫", "prompt_en": "Create an animated cat for a webpage that follows the mouse cursor"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "off"}]}, {"group_name": "搜信息", "group_name_en": "Search info", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompts": [{"name": "吃瓜盘点，回顾互联网著名的\"世纪大和解\"名场面", "name_en": "Write a shareable recap of famous \"reconciliation moments\" from internet history", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "写一篇可以分享的吃瓜盘点，回顾一下互联网上那些著名的\"世纪大和解\"名场面", "prompt_en": "Write a shareable recap of famous \"reconciliation moments\" from internet history"}, {"name": "介绍阿波罗11号成功登月的故事", "name_en": "Introduce the story of the Apollo 11 moon landing", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍阿波罗11号成功登月的故事", "prompt_en": "Introduce the story of the Apollo 11 moon landing"}, {"name": "做一个关于\"奇葩口味零食\"的评测报告", "name_en": "Help me research and create a review report on \"weirdly flavored snacks\"", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "搜集资料，帮我做一个关于\"奇葩口味零食\"的评测报告", "prompt_en": "Help me research and create a review report on \"weirdly flavored snacks\""}, {"name": "介绍一下\"清醒梦\"这个概念，以及它是如何运作的", "name_en": "Introduce the concept of \"lucid dreaming\" and how it works", "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g opacity=\"0.4\"><path d=\"M1.33331 2.70001H14.6666\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 8.03326H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M1.33331 13.3666H4.99998\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/><path d=\"M10.5 11.6999C12.0648 11.6999 13.3334 10.4314 13.3334 8.8666C13.3334 7.3018 12.0648 6.03326 10.5 6.03326C8.93522 6.03326 7.66669 7.3018 7.66669 8.8666C7.66669 10.4314 8.93522 11.6999 10.5 11.6999Z\" stroke=\"currentColor\" stroke-width=\"1.33333\"/><path d=\"M12.3333 11.0333L14.6666 13.3834\" stroke=\"currentColor\" stroke-width=\"1.33333\" stroke-linecap=\"round\"/></g></svg>", "prompt": "介绍一下\"清醒梦\"这个概念，以及它是如何运作的", "prompt_en": "Introduce the concept of \"lucid dreaming\" and how it works"}], "flags": [], "features": [{"type": "mcp", "server": "deep-web-search", "status": "selected"}]}]