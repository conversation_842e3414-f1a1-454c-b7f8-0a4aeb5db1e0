# MCP 搜索缓存功能文档 (重构版)

## 功能概述

本功能为 McpContainer.svelte 组件实现了基于类的前端内存缓存管理，使用 Svelte 4 的 Context API 在 Messages.svelte 组件级别维护搜索结果缓存，用于存储 MCP search 搜索操作返回的 citations 数据，并在 visit_page 工具中实现 URL 到标题的映射替换。

## 架构改进

### 重构亮点

- **类化设计**: 将搜索缓存逻辑抽象为 `SearchCacheManager` 类
- **Context API**: 使用 Svelte 4 的 Context API 在 Messages 组件级别管理缓存
- **去除 chatId 映射**: 不再使用 chatId 映射，每个 Messages 组件实例维护独立缓存
- **更好的封装**: 提供完整的缓存管理 API，包括统计、搜索、导入导出等功能

## 核心功能

### 1. 前端内存缓存管理

- **组件级别缓存**: 每个 Messages 组件实例维护独立的搜索结果缓存
- **累积更新**: 支持多轮搜索的结果累积，避免重复数据
- **生命周期管理**: 缓存随组件创建和销毁自动管理

### 2. URL 到标题的映射替换

- **智能替换**: 当 visit_page 工具的 URL 参数在缓存中存在时，显示对应的标题
- **降级处理**: 如果找不到对应的标题，保持原始 URL 显示
- **仅影响显示**: 替换只影响显示层面，不影响实际的工具调用参数

### 3. URL 标准化

- **智能匹配**: 支持 URL 标准化匹配，处理尾部斜杠等差异
- **容错处理**: 同时支持精确匹配和标准化匹配

## 技术实现

### 数据结构

```typescript
// 搜索结果引用项
interface CitationItem {
    title: string;
    url: string;
    text: string;
    index: number;
    favicon?: string;
}

// 会话级别的搜索结果缓存
interface SearchCitationsCache {
    [chatId: string]: CitationItem[];
}
```

### 核心 API

#### `addSearchCitationsToCache(chatId: string, citations: CitationItem[])`
- 添加搜索结果到指定会话的缓存
- 自动去重，避免重复添加相同的 URL
- 支持累积更新

#### `findTitleByUrl(chatId: string, url: string): string | null`
- 根据 URL 查找对应的标题
- 支持精确匹配和标准化匹配
- 找不到时返回 null

#### `clearSearchCitationsCache(chatId: string)`
- 清除指定会话的搜索缓存

### 自动化集成

#### McpContainer.svelte 中的自动处理

1. **自动缓存**: 当检测到搜索工具返回结果时，自动添加到缓存
```typescript
$: if (data.browser?.search_result && data.browser.search_result.length > 0 && $chatId) {
    addSearchCitationsToCache($chatId, data.browser.search_result);
}
```

2. **智能显示**: 在 formatArguments 函数中自动处理 visit_page 工具的 URL 替换
```typescript
if (data.metadata.name === 'visit_page' && parsed.url && $chatId) {
    const title = findTitleByUrl($chatId, parsed.url);
    if (title) {
        return title;
    }
}
```

## 数据流向

```
search tool metadata 
    ↓
parse_mcp_search_citations_from_metadata (后端)
    ↓
browser.search_result (前端接收)
    ↓
addSearchCitationsToCache (自动缓存)
    ↓
findTitleByUrl (visit_page 工具显示优化)
    ↓
显示标题而非 URL
```

## 使用场景

### 典型工作流

1. 用户执行搜索操作
2. 搜索结果自动缓存到前端内存
3. 用户使用 visit_page 工具访问搜索结果中的某个链接
4. visit_page 工具参数显示为友好的标题而非原始 URL
5. 点击工具卡片仍然正确跳转到原始 URL

### 边界情况处理

- **URL 不在缓存中**: 显示原始 URL
- **缓存为空**: 显示原始 URL
- **URL 格式异常**: 使用容错匹配
- **会话切换**: 每个会话独立缓存

## 测试

### 测试页面

- `/mcp-search-cache-test`: 测试缓存功能的基本操作
- `/mcp-container-test`: 测试 McpContainer 组件的 URL 映射功能

### 测试用例

1. **基本缓存功能**
   - 添加搜索结果到缓存
   - 查找 URL 对应的标题
   - 重复添加去重测试
   - 清除缓存功能

2. **URL 映射功能**
   - visit_page 工具使用缓存中的 URL（应显示标题）
   - visit_page 工具使用不在缓存中的 URL（应显示原始 URL）
   - 其他工具不受影响

3. **边界情况**
   - 空缓存处理
   - 无效 URL 处理
   - 会话切换测试

## 性能考虑

- **内存使用**: 缓存数据仅在会话期间保存，页面刷新后清除
- **查找效率**: 使用数组查找，对于典型的搜索结果数量（<100）性能良好
- **自动清理**: 可考虑添加 LRU 缓存或定时清理机制

## 未来扩展

1. **持久化存储**: 可考虑使用 localStorage 实现跨页面刷新的持久化
2. **缓存大小限制**: 添加缓存大小限制和 LRU 淘汰策略
3. **更多工具支持**: 扩展到其他需要 URL 映射的 MCP 工具
4. **模糊匹配**: 支持域名级别的模糊匹配
