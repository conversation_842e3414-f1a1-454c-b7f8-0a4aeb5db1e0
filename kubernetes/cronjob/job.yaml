apiVersion: batch/v1
kind: CronJob
metadata:
  name: memory-check-cronjob
  namespace: app
spec:
  schedule: "0 * * * *"  # 每小时运行一次
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: memory-check-sa  # 需要创建具有适当权限的 ServiceAccount
          containers:
          - name: memory-check
            image: mereith/kubectl:latest
            imagePullPolicy: Always
            command:
            - /bin/sh
            - -c
            - |
              # 日志辅助函数
              log() {
                echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
              }
              
              # 记录任务开始时间
              log "开始执行内存检查任务"
              START_TIME=$(date +%s)
              
              # 获取 prod-open-webui deployment 的所有 pod
              log "正在获取 app 命名空间中标签为 app=open-webui,environment=production 的所有 pod"
              PODS=$(kubectl get pods -n app -l app=open-webui,environment=production --no-headers -o custom-columns=":metadata.name")
              
              # 统计 pod 数量
              POD_COUNT=$(echo "$PODS" | wc -l | xargs)
              log "找到 $POD_COUNT 个 pod 需要检查"
              
              # 初始化计数器
              OVER_LIMIT_COUNT=0
              DELETED_COUNT=0
              
              # 检查每个 pod 的内存使用情况
              for POD in $PODS; do
                log "正在检查 pod: $POD"
                
                # 获取 pod 的内存使用量
                MEMORY_USAGE=$(kubectl top pod $POD -n app --no-headers | awk '{print $3}')
                
                # 如果获取失败，记录错误并继续下一个
                if [ -z "$MEMORY_USAGE" ]; then
                  log "警告: 无法获取 $POD 的内存使用量，可能 metrics-server 未正常工作"
                  continue
                fi
                
                log "Pod $POD 当前内存使用量: $MEMORY_USAGE"
                
                # 提取数值和单位
                VALUE=$(echo $MEMORY_USAGE | sed 's/[^0-9]*//g')
                UNIT=$(echo $MEMORY_USAGE | sed 's/[0-9]*//g')
                
                # 转换为 MB 便于比较
                case $UNIT in
                  "Mi")
                    MEMORY_MB=$VALUE
                    log "转换: $VALUE$UNIT = ${MEMORY_MB}MB"
                    ;;
                  "Gi")
                    MEMORY_MB=$(( $VALUE * 1024 ))
                    log "转换: $VALUE$UNIT = ${MEMORY_MB}MB"
                    ;;
                  "Ki")
                    MEMORY_MB=$(( $VALUE / 1024 ))
                    log "转换: $VALUE$UNIT = ${MEMORY_MB}MB"
                    ;;
                  *)
                    MEMORY_MB=$VALUE
                    log "未知单位: $UNIT，假设值已经是 MB: $MEMORY_MB"
                    ;;
                esac
                
                # 检查是否超过 8G (8192MB)
                if [ $MEMORY_MB -gt 8192 ]; then
                  OVER_LIMIT_COUNT=$((OVER_LIMIT_COUNT + 1))
                  log "警告: Pod $POD 内存使用量 $MEMORY_USAGE 超过 8G 限制"
                  
                  # 在删除前先执行 memtrace.sh 脚本获取内存使用详情
                  log "正在执行内存追踪脚本: bash memtrace.sh"
                  MEMTRACE_OUTPUT=$(kubectl exec -n app $POD -- bash memtrace.sh 2>&1)
                  MEMTRACE_STATUS=$?
                  
                  if [ $MEMTRACE_STATUS -eq 0 ]; then
                    log "内存追踪脚本执行成功"
                    # 截取输出避免消息过长 (最多保留1000个字符)
                    if [ ${#MEMTRACE_OUTPUT} -gt 1000 ]; then
                      MEMTRACE_OUTPUT="${MEMTRACE_OUTPUT:0:1000}... (输出被截断)"
                    fi
                    log "内存追踪结果: $MEMTRACE_OUTPUT"
                  else
                    log "内存追踪脚本执行失败: $MEMTRACE_OUTPUT"
                    MEMTRACE_OUTPUT="脚本执行失败: $MEMTRACE_OUTPUT"
                  fi
                  
                  # 将输出内容写入临时文件
                  echo "$MEMTRACE_OUTPUT" > /tmp/memtrace_output.txt
                  
                  # 向飞书发送通知 (使用 jq 正确构造 JSON)
                  log "正在发送飞书通知..."
                  CURRENT_TIME=$(date +"%Y-%m-%d %H:%M:%S")
                  
                  # 使用 jq 构造 JSON 消息，正确使用字符串插值
                  jq -n --arg pod "$POD" \
                        --arg mem "$MEMORY_USAGE" \
                        --arg time "$CURRENT_TIME" \
                        --arg output "$(cat /tmp/memtrace_output.txt)" \
                        '{
                          "msg_type": "text",
                          "content": {
                            "text": "警告: Pod \($pod) 内存使用量 \($mem) 超过 8G 限制，已被自动删除。\n\n检测时间: \($time)\n\n内存追踪结果:\n\($output)"
                          }
                        }' > /tmp/feishu_message.json
                  
                  FEISHU_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d @/tmp/feishu_message.json https://open.feishu.cn/open-apis/bot/v2/hook/c22ec18e-d2a7-4d95-b921-d5d338c06d0a)
                  log "飞书响应: $FEISHU_RESPONSE"
                  
                  # 删除 pod
                  log "正在删除 pod $POD..."
                  DELETE_RESULT=$(kubectl delete pod $POD -n app 2>&1)
                  if [ $? -eq 0 ]; then
                    log "成功删除 pod $POD"
                    DELETED_COUNT=$((DELETED_COUNT + 1))
                  else
                    log "删除 pod $POD 失败: $DELETE_RESULT"
                  fi
                else
                  log "Pod $POD 内存使用正常，低于 8G 限制"
                fi
              done
              
              # 计算执行时间
              END_TIME=$(date +%s)
              DURATION=$((END_TIME - START_TIME))
              
              # 输出任务总结
              log "内存检查任务完成"
              log "总结: 检查了 $POD_COUNT 个 pod，发现 $OVER_LIMIT_COUNT 个超过内存限制，成功删除 $DELETED_COUNT 个"
              log "任务耗时: ${DURATION}秒"
              
              # 如果有删除操作，再次发送总结到飞书
              if [ $DELETED_COUNT -gt 0 ]; then
                # 使用 jq 构造总结消息
                jq -n --arg count "$POD_COUNT" \
                      --arg over "$OVER_LIMIT_COUNT" \
                      --arg deleted "$DELETED_COUNT" \
                      '{
                        "msg_type": "text",
                        "content": {
                          "text": "内存检查任务总结：检查了 \($count) 个 pod，发现 \($over) 个超过内存限制，成功删除 \($deleted) 个。"
                        }
                      }' > /tmp/summary_message.json
                
                curl -s -X POST -H "Content-Type: application/json" -d @/tmp/summary_message.json https://open.feishu.cn/open-apis/bot/v2/hook/c22ec18e-d2a7-4d95-b921-d5d338c06d0a
              fi
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: memory-check-sa
  namespace: app
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pod-manager-role
  namespace: app
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "delete"]
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods"]
  verbs: ["get", "list"] 
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: memory-check-rolebinding
  namespace: app
subjects:
- kind: ServiceAccount
  name: memory-check-sa
  namespace: app
roleRef:
  kind: Role
  name: pod-manager-role
  apiGroup: rbac.authorization.k8s.io
