apiVersion: batch/v1
kind: Job
metadata:
  name: memtrace-test-job
  namespace: app
spec:
  template:
    spec:
      serviceAccountName: memory-check-sa  # 使用相同的ServiceAccount
      containers:
      - name: memtrace-test
        image: mereith/kubectl:latest
        imagePullPolicy: Always
        command:
        - /bin/sh  # 使用sh而不是bash (curlimages/curl使用Alpine Linux)
        - -c
        - |
          # 日志辅助函数
          log() {
            echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1"
          }
          
          log "开始执行内存追踪测试任务"
          
          
          # 获取第一个匹配的 pod 作为测试对象
          log "正在获取 app 命名空间中标签为 app=open-webui,environment=production 的pod"
          POD=$(kubectl get pods -n app -l app=open-webui,environment=production --no-headers -o custom-columns=":metadata.name" | head -n 1)
          
          if [ -z "$POD" ]; then
            log "错误: 没有找到匹配的pod"
            exit 1
          fi
          
          log "选择 pod: $POD 进行测试"
          
          # 获取 pod 的内存使用量
          MEMORY_USAGE=$(kubectl top pod $POD -n app --no-headers | awk '{print $3}')
          
          if [ -z "$MEMORY_USAGE" ]; then
            log "警告: 无法获取 $POD 的内存使用量，可能 metrics-server 未正常工作"
            exit 1
          fi
          
          log "Pod $POD 当前内存使用量: $MEMORY_USAGE"
          
          # 在pod中执行 memtrace.sh 脚本
          log "正在执行内存追踪脚本: bash memtrace.sh"
          MEMTRACE_OUTPUT=$(kubectl exec -n app $POD -- bash memtrace.sh 2>&1)
          MEMTRACE_STATUS=$?
          
          if [ $MEMTRACE_STATUS -eq 0 ]; then
            log "内存追踪脚本执行成功"
            
            # 截取输出避免消息过长 (最多保留1000个字符)
            if [ ${#MEMTRACE_OUTPUT} -gt 1000 ]; then
              TRUNCATED_OUTPUT="${MEMTRACE_OUTPUT:0:1000}... (输出被截断)"
              log "内存追踪结果 (截断版): $TRUNCATED_OUTPUT"
            else
              log "内存追踪结果: $MEMTRACE_OUTPUT"
            fi
          else
            log "内存追踪脚本执行失败: $MEMTRACE_OUTPUT"
            MEMTRACE_OUTPUT="脚本执行失败: $MEMTRACE_OUTPUT"
          fi
          
          # 向飞书发送通知
          log "正在发送飞书通知..."
          
          # 使用jq正确构造JSON
          CURRENT_TIME=$(date +"%Y-%m-%d %H:%M:%S")
          
          # 将输出内容写入临时文件
          echo "$MEMTRACE_OUTPUT" > /tmp/memtrace_output.txt
          
          # 使用jq构造JSON消息，正确使用字符串插值
          jq -n --arg pod "$POD" \
                --arg mem "$MEMORY_USAGE" \
                --arg time "$CURRENT_TIME" \
                --arg output "$(cat /tmp/memtrace_output.txt)" \
                '{
                  "msg_type": "text",
                  "content": {
                    "text": "测试 - 内存追踪结果\n\nPod名称: \($pod)\n内存使用量: \($mem)\n检测时间: \($time)\n\n内存追踪结果:\n\($output)"
                  }
                }' > /tmp/feishu_message.json
          
          FEISHU_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" -d @/tmp/feishu_message.json https://open.feishu.cn/open-apis/bot/v2/hook/15822684-b440-4129-884c-58045f3e91f7)
          log "飞书响应: $FEISHU_RESPONSE"
          
          log "测试任务完成"
      restartPolicy: Never
  backoffLimit: 1
