#!/usr/bin/env python3
"""
快速创建超级管理员账号的脚本

使用方法:
python quick_admin.py

这个脚本会创建一个默认的超级管理员账号:
- 邮箱: admin@localhost
- 密码: admin123
- 姓名: Super Admin
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from open_webui.models.auths import Auths
from open_webui.models.users import Users
from open_webui.utils.auth import get_password_hash


def create_default_admin():
    """创建默认的超级管理员账号"""
    
    # 默认管理员信息
    email = "admin@localhost"
    password = "admin123"
    name = "Super Admin"
    profile_image_url = "/user.png"
    
    try:
        print("=== 快速创建超级管理员 ===\n")
        
        # 检查是否已存在
        existing_user = Users.get_user_by_email(email.lower())
        if existing_user:
            print(f"❌ 默认管理员账号已存在")
            print(f"   邮箱: {existing_user.email}")
            print(f"   姓名: {existing_user.name}")
            print(f"   角色: {existing_user.role}")
            
            if existing_user.role != "admin":
                print("\n正在升级为管理员...")
                updated_user = Users.update_user_role_by_id(existing_user.id, "admin")
                if updated_user:
                    print("✅ 用户已升级为管理员")
                    return True
                else:
                    print("❌ 升级失败")
                    return False
            else:
                print("   该账号已经是管理员")
                return True
        
        # 创建新管理员
        print("正在创建默认超级管理员...")
        hashed_password = get_password_hash(password)
        user = Auths.insert_new_auth(
            email=email.lower(),
            password=hashed_password,
            name=name,
            profile_image_url=profile_image_url,
            role="admin"
        )
        
        if user:
            print("✅ 超级管理员创建成功!\n")
            print("=== 登录信息 ===")
            print(f"邮箱: {email}")
            print(f"密码: {password}")
            print(f"姓名: {name}")
            print(f"角色: admin")
            print(f"\n⚠️  请登录后立即修改密码!")
            return True
        else:
            print("❌ 创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建管理员时发生错误: {str(e)}")
        return False


def main():
    success = create_default_admin()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
