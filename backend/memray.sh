#!/bin/bash

# Simple Memray memory profiling for Open WebUI
# One-click solution with minimal complexity

set -e

PORT="${PORT:-8080}"
REPORTS_DIR="./memray-reports"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✓${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }

# Check memray installation
if ! command -v memray &> /dev/null; then
    echo "❌ Memray not installed. Install with: pip install memray"
    exit 1
fi

# Create reports directory
mkdir -p "$REPORTS_DIR"

# Cleanup function
cleanup() {
    echo ""
    print_warning "Stopping and generating report..."
    
    # Find latest .bin file
    LATEST_FILE=$(find "$REPORTS_DIR" -name "*.bin" -type f 2>/dev/null | sort -r | head -1)
    
    if [ -n "$LATEST_FILE" ]; then
        FLAMEGRAPH_FILE="${LATEST_FILE%.bin}-flamegraph.html"
        print_status "Generating flame graph..."
        memray flamegraph "$LATEST_FILE" -o "$FLAMEGRAPH_FILE" 2>/dev/null
        print_status "Report saved: $FLAMEGRAPH_FILE"
        
        # Open in browser
        if command -v open >/dev/null 2>&1; then
            open "$FLAMEGRAPH_FILE"
        fi
    fi
    
    exit 0
}

# Trap Ctrl+C
trap cleanup SIGINT SIGTERM

# Show usage
show_usage() {
    echo "🧠 Open WebUI Memory Profiler"
    echo ""
    echo "Usage: $0 [mode]"
    echo ""
    echo "Modes:"
    echo "  auto    - Record to file, auto-generate report on stop (default)"
    echo "  live    - Real-time TUI monitoring"
    echo "  view    - Open latest report"
    echo "  clean   - Clean old reports"
    echo ""
    echo "Examples:"
    echo "  $0        # Auto mode (recommended)"
    echo "  $0 auto   # Same as above"
    echo "  $0 live   # Real-time monitoring"
    echo "  $0 view   # View latest report"
}

# Auto mode (default)
auto_mode() {
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    OUTPUT_FILE="$REPORTS_DIR/openwebui-${TIMESTAMP}.bin"
    
    print_info "Starting Open WebUI with memory profiling..."
    print_info "Server: http://localhost:$PORT"
    print_info "Profile: $OUTPUT_FILE"
    echo ""
    print_warning "Use the app normally, then press Ctrl+C to generate report"
    echo ""
    
    # Start with memory profiling
    memray run --output "$OUTPUT_FILE" -m uvicorn open_webui.main:app \
        --host 0.0.0.0 --forwarded-allow-ips '*' --reload --port $PORT
}

# Live mode
live_mode() {
    print_info "Starting real-time memory monitoring..."
    print_info "Press 'q' to quit the TUI"
    echo ""
    
    memray run --live -m uvicorn open_webui.main:app \
        --host 0.0.0.0 --forwarded-allow-ips '*' --reload --port $PORT
}

# View latest report
view_reports() {
    LATEST_HTML=$(find "$REPORTS_DIR" -name "*flamegraph.html" -type f 2>/dev/null | sort -r | head -1)
    
    if [ -z "$LATEST_HTML" ]; then
        echo "❌ No reports found. Run profiling first."
        exit 1
    fi
    
    print_status "Opening: $LATEST_HTML"
    
    if command -v open >/dev/null 2>&1; then
        open "$LATEST_HTML"
    elif command -v xdg-open >/dev/null 2>&1; then
        xdg-open "$LATEST_HTML"
    else
        echo "Open this file: file://$(realpath "$LATEST_HTML")"
    fi
}

# Clean reports
clean_reports() {
    if [ -d "$REPORTS_DIR" ] && [ "$(ls -A "$REPORTS_DIR")" ]; then
        read -p "Delete all reports in $REPORTS_DIR? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            rm -rf "$REPORTS_DIR"/*
            print_status "Reports cleaned"
        fi
    else
        print_info "No reports to clean"
    fi
}

# Main logic
case "${1:-auto}" in
    "auto"|"")
        auto_mode
        ;;
    "live")
        live_mode
        ;;
    "view")
        view_reports
        ;;
    "clean")
        clean_reports
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo "❌ Unknown mode: $1"
        show_usage
        exit 1
        ;;
esac