#!/usr/bin/env python3
"""
批量压力测试脚本

支持运行多个不同配置的压力测试，并生成对比报告
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
import argparse
import logging

# 导入主测试模块
from stress_test import StressTester, StressTestConfig

logger = logging.getLogger(__name__)


class BatchTestRunner:
    """批量测试运行器"""
    
    def __init__(self, config_file: str, output_dir: str = "batch_results"):
        self.config_file = config_file
        self.output_dir = Path(output_dir)
        self.test_configs = []
        self.results = []
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.test_scenarios = config_data.get('test_scenarios', [])
            self.default_headers = config_data.get('default_headers', {})
            self.monitoring = config_data.get('monitoring', {})
            
            logger.info(f"加载了 {len(self.test_scenarios)} 个测试场景")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    async def run_batch_tests(self):
        """运行批量测试"""
        batch_start_time = datetime.now()
        batch_dir = self.output_dir / f"batch_test_{batch_start_time.strftime('%Y%m%d_%H%M%S')}"
        batch_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"开始批量测试，结果保存到: {batch_dir}")
        
        for i, scenario in enumerate(self.test_scenarios, 1):
            logger.info(f"运行测试场景 {i}/{len(self.test_scenarios)}: {scenario['name']}")
            
            # 创建测试配置
            config = StressTestConfig(
                concurrency=scenario.get('concurrency', 10),
                server_url=scenario.get('server_url', 'http://localhost:8080'),
                template_file=scenario.get('template_file', 'curl.md'),
                output_dir=str(batch_dir / f"scenario_{i:02d}_{scenario['name'].replace(' ', '_')}"),
                timeout=scenario.get('timeout', 300)
            )
            
            try:
                # 运行单个测试
                async with StressTester(config) as tester:
                    await tester.run_stress_test()
                    summary = tester.generate_summary_report()
                    tester.save_summary_report(summary)
                    
                    # 保存结果
                    result = {
                        'scenario': scenario,
                        'summary': summary,
                        'config': config.__dict__
                    }
                    self.results.append(result)
                    
                    logger.info(f"场景 '{scenario['name']}' 完成")
                    
            except Exception as e:
                logger.error(f"场景 '{scenario['name']}' 执行失败: {e}")
                self.results.append({
                    'scenario': scenario,
                    'error': str(e),
                    'config': config.__dict__
                })
        
        # 生成批量测试报告
        await self._generate_batch_report(batch_dir)
        
        logger.info("批量测试完成")
        return self.results
    
    async def _generate_batch_report(self, batch_dir: Path):
        """生成批量测试对比报告"""
        report_file = batch_dir / "batch_comparison_report.json"
        text_report_file = batch_dir / "batch_comparison_report.txt"
        
        # JSON 报告
        batch_summary = {
            'test_time': datetime.now().isoformat(),
            'total_scenarios': len(self.test_scenarios),
            'successful_scenarios': len([r for r in self.results if 'error' not in r]),
            'failed_scenarios': len([r for r in self.results if 'error' in r]),
            'results': self.results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(batch_summary, f, indent=2, ensure_ascii=False)
        
        # 文本报告
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write("=== 批量压力测试对比报告 ===\n\n")
            f.write(f"测试时间: {batch_summary['test_time']}\n")
            f.write(f"总场景数: {batch_summary['total_scenarios']}\n")
            f.write(f"成功场景数: {batch_summary['successful_scenarios']}\n")
            f.write(f"失败场景数: {batch_summary['failed_scenarios']}\n\n")
            
            # 场景对比表格
            f.write("场景对比:\n")
            f.write("-" * 100 + "\n")
            f.write(f"{'场景名称':<20} {'并发数':<8} {'成功率':<10} {'平均响应时间':<15} {'平均TTFB':<12} {'状态':<10}\n")
            f.write("-" * 100 + "\n")
            
            for result in self.results:
                scenario_name = result['scenario']['name']
                concurrency = result['scenario']['concurrency']
                
                if 'error' in result:
                    f.write(f"{scenario_name:<20} {concurrency:<8} {'N/A':<10} {'N/A':<15} {'N/A':<12} {'失败':<10}\n")
                else:
                    summary = result['summary']
                    success_rate = summary['statistics']['success_rate']
                    avg_duration = summary['performance']['avg_duration']
                    avg_ttfb = summary['performance']['avg_ttfb']
                    
                    f.write(f"{scenario_name:<20} {concurrency:<8} {success_rate:<10.2f} {avg_duration:<15.3f} {avg_ttfb:<12.3f} {'成功':<10}\n")
            
            f.write("-" * 100 + "\n\n")
            
            # 详细结果
            for i, result in enumerate(self.results, 1):
                f.write(f"=== 场景 {i}: {result['scenario']['name']} ===\n")
                
                if 'error' in result:
                    f.write(f"错误: {result['error']}\n\n")
                else:
                    summary = result['summary']
                    stats = summary['statistics']
                    perf = summary['performance']
                    
                    f.write(f"并发数: {result['scenario']['concurrency']}\n")
                    f.write(f"服务器: {result['scenario']['server_url']}\n")
                    f.write(f"总请求数: {stats['total_requests']}\n")
                    f.write(f"成功请求数: {stats['successful_requests']}\n")
                    f.write(f"失败请求数: {stats['failed_requests']}\n")
                    f.write(f"成功率: {stats['success_rate']:.2f}%\n")
                    f.write(f"平均响应时间: {perf['avg_duration']:.3f}s\n")
                    f.write(f"平均TTFB: {perf['avg_ttfb']:.3f}s\n")
                    
                    if summary.get('errors'):
                        f.write("错误统计:\n")
                        for error_type, count in summary['errors'].items():
                            f.write(f"  {error_type}: {count} 次\n")
                    
                    f.write("\n")
        
        logger.info(f"批量测试报告已保存到: {report_file}")
        logger.info(f"批量测试文本报告已保存到: {text_report_file}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量压力测试工具')
    parser.add_argument('-c', '--config', type=str, default='config_example.json',
                        help='配置文件路径 (默认: config_example.json)')
    parser.add_argument('-o', '--output', type=str, default='batch_results',
                        help='输出目录 (默认: batch_results)')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='详细输出模式')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        logger.error(f"配置文件不存在: {args.config}")
        return 1
    
    try:
        # 运行批量测试
        runner = BatchTestRunner(args.config, args.output)
        results = await runner.run_batch_tests()
        
        # 打印简要结果
        successful = len([r for r in results if 'error' not in r])
        failed = len([r for r in results if 'error' in r])
        
        print(f"\n=== 批量测试完成 ===")
        print(f"总场景数: {len(results)}")
        print(f"成功场景数: {successful}")
        print(f"失败场景数: {failed}")
        
        if failed > 0:
            print("\n失败的场景:")
            for result in results:
                if 'error' in result:
                    print(f"  - {result['scenario']['name']}: {result['error']}")
        
        return 0 if failed == 0 else 1
        
    except Exception as e:
        logger.error(f"批量测试执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
