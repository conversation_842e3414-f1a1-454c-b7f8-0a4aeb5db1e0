#!/usr/bin/env python3
"""
简化版实时监控脚本

用于快速监控压力测试进度
"""

import os
import time
from pathlib import Path
from datetime import datetime
import argparse


def clear_screen():
    """清屏"""
    os.system('clear' if os.name == 'posix' else 'cls')


def analyze_log_file(log_file):
    """分析单个日志文件"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        result = {
            'completed': False,
            'success': False,
            'duration': None,
            'ttfb': None,
            'error': None
        }
        
        # 检查是否完成
        if "=== 请求结束 ===" in content:
            result['completed'] = True
            
            # 检查是否成功
            if "状态码: 200" in content:
                result['success'] = True
            else:
                result['success'] = False
                # 提取错误信息
                error_lines = [line for line in content.split('\n') if line.startswith('错误:')]
                if error_lines:
                    result['error'] = error_lines[0].replace('错误:', '').strip()
            
            # 提取耗时
            duration_lines = [line for line in content.split('\n') if line.startswith('总耗时:')]
            if duration_lines:
                duration_str = duration_lines[0].replace('总耗时:', '').replace('s', '').strip()
                try:
                    result['duration'] = float(duration_str)
                except ValueError:
                    pass
            
            # 提取 TTFB
            ttfb_lines = [line for line in content.split('\n') if line.startswith('TTFB:') and 'N/A' not in line]
            if ttfb_lines:
                ttfb_str = ttfb_lines[0].replace('TTFB:', '').replace('s', '').strip()
                try:
                    result['ttfb'] = float(ttfb_str)
                except ValueError:
                    pass
        
        return result
        
    except Exception as e:
        return {'error': str(e), 'completed': False, 'success': False}


def analyze_completed_test(results_dir):
    """分析已完成的测试"""
    results_path = Path(results_dir)
    
    if not results_path.exists():
        print(f"❌ 结果目录不存在: {results_path}")
        return
    
    # 查找汇总报告
    summary_file = results_path / "summary_report.txt"
    if summary_file.exists():
        print("=== 测试结果汇总 ===")
        with open(summary_file, 'r', encoding='utf-8') as f:
            print(f.read())
    else:
        print("未找到汇总报告，分析日志文件...")
        
        log_files = list(results_path.glob("request_*.log"))
        if not log_files:
            print("❌ 未找到日志文件")
            return
        
        print(f"找到 {len(log_files)} 个日志文件")
        
        # 详细统计
        total_requests = len(log_files)
        completed = 0
        successful = 0
        failed = 0
        durations = []
        ttfbs = []
        errors = {}
        
        for log_file in log_files:
            result = analyze_log_file(log_file)
            if result['completed']:
                completed += 1
                if result['success']:
                    successful += 1
                else:
                    failed += 1
                    if result['error']:
                        errors[result['error']] = errors.get(result['error'], 0) + 1
                
                if result['duration'] is not None:
                    durations.append(result['duration'])
                
                if result['ttfb'] is not None:
                    ttfbs.append(result['ttfb'])
        
        # 计算统计数据
        success_rate = (successful / completed * 100) if completed > 0 else 0
        avg_duration = sum(durations) / len(durations) if durations else 0
        avg_ttfb = sum(ttfbs) / len(ttfbs) if ttfbs else 0
        
        print("\n=== 测试结果分析 ===")
        print(f"总请求数: {total_requests}")
        print(f"已完成请求: {completed}")
        print(f"成功请求: {successful}")
        print(f"失败请求: {failed}")
        print(f"成功率: {success_rate:.1f}%")
        
        if durations:
            print(f"\n性能指标:")
            print(f"  平均响应时间: {avg_duration:.3f}s")
            print(f"  最大响应时间: {max(durations):.3f}s")
            print(f"  最小响应时间: {min(durations):.3f}s")
        
        if ttfbs:
            print(f"  平均 TTFB: {avg_ttfb:.3f}s")
            print(f"  最大 TTFB: {max(ttfbs):.3f}s")
            print(f"  最小 TTFB: {min(ttfbs):.3f}s")
        
        if errors:
            print(f"\n错误统计:")
            for error_type, count in errors.items():
                print(f"  {error_type}: {count} 次")


def monitor_directory(results_dir, refresh_interval=2):
    """监控目录"""
    results_path = Path(results_dir)
    
    print(f"开始监控: {results_path}")
    print("按 Ctrl+C 停止监控\n")
    
    try:
        while True:
            clear_screen()
            
            print("=== 压力测试实时监控 ===")
            print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"监控目录: {results_path}")
            print("-" * 60)
            
            if not results_path.exists():
                print("❌ 监控目录不存在")
                print("请检查路径是否正确")
                time.sleep(refresh_interval)
                continue
            
            # 查找日志文件
            log_files = list(results_path.glob("request_*.log"))
            
            if not log_files:
                print("📁 目录为空，等待测试开始...")
                time.sleep(refresh_interval)
                continue
            
            # 分析所有日志文件
            total_requests = len(log_files)
            completed_requests = 0
            successful_requests = 0
            failed_requests = 0
            running_requests = 0
            durations = []
            ttfbs = []
            errors = {}
            
            for log_file in log_files:
                result = analyze_log_file(log_file)
                
                if result['completed']:
                    completed_requests += 1
                    if result['success']:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        if result['error']:
                            errors[result['error']] = errors.get(result['error'], 0) + 1
                    
                    if result['duration'] is not None:
                        durations.append(result['duration'])
                    
                    if result['ttfb'] is not None:
                        ttfbs.append(result['ttfb'])
                else:
                    running_requests += 1
            
            # 计算统计数据
            success_rate = (successful_requests / completed_requests * 100) if completed_requests > 0 else 0
            avg_duration = sum(durations) / len(durations) if durations else 0
            avg_ttfb = sum(ttfbs) / len(ttfbs) if ttfbs else 0
            
            # 显示统计信息
            print(f"总请求数:     {total_requests}")
            print(f"已完成:       {completed_requests}")
            print(f"进行中:       {running_requests}")
            print(f"成功数:       {successful_requests}")
            print(f"失败数:       {failed_requests}")
            print(f"成功率:       {success_rate:.1f}%")
            print()
            
            # 性能指标
            print("性能指标:")
            print(f"  平均响应时间: {avg_duration:.3f}s")
            print(f"  平均 TTFB:    {avg_ttfb:.3f}s")
            
            if durations:
                print(f"  最大响应时间: {max(durations):.3f}s")
                print(f"  最小响应时间: {min(durations):.3f}s")
            
            if ttfbs:
                print(f"  最大 TTFB:    {max(ttfbs):.3f}s")
                print(f"  最小 TTFB:    {min(ttfbs):.3f}s")
            
            print()
            
            # 错误统计
            if errors:
                print("错误统计:")
                for error_type, count in errors.items():
                    print(f"  {error_type}: {count} 次")
            else:
                print("错误统计: 无错误")
            
            print()
            
            # 进度条
            if total_requests > 0:
                progress = completed_requests / total_requests
                bar_length = 40
                filled_length = int(bar_length * progress)
                bar = '█' * filled_length + '-' * (bar_length - filled_length)
                print(f"进度: [{bar}] {progress*100:.1f}%")
            
            print()
            
            # 状态指示
            if running_requests > 0:
                print("🔄 测试进行中...")
            elif completed_requests == total_requests:
                print("✅ 测试已完成")
            
            print()
            print("按 Ctrl+C 停止监控")
            
            time.sleep(refresh_interval)
            
    except KeyboardInterrupt:
        print("\n\n监控已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版压力测试监控工具')
    parser.add_argument('results_dir', help='测试结果目录')
    parser.add_argument('-i', '--interval', type=int, default=2,
                        help='刷新间隔，秒 (默认: 2)')
    parser.add_argument('-a', '--analyze', action='store_true',
                        help='分析已完成的测试结果')
    
    args = parser.parse_args()
    
    if args.analyze:
        analyze_completed_test(args.results_dir)
    else:
        monitor_directory(args.results_dir, args.interval)


if __name__ == "__main__":
    main()
