{"test_scenarios": [{"name": "轻量级测试", "description": "适用于开发环境的轻量级压力测试", "concurrency": 5, "timeout": 120, "server_url": "http://localhost:8080"}, {"name": "中等负载测试", "description": "适用于测试环境的中等负载测试", "concurrency": 20, "timeout": 300, "server_url": "http://test-server:8080"}, {"name": "高负载测试", "description": "适用于生产环境的高负载压力测试", "concurrency": 50, "timeout": 600, "server_url": "http://production-server:8080"}], "default_headers": {"X-FE-Version": "staging-fe-0.0.171", "User-Agent": "StressTest/1.0.0", "Content-Type": "application/json", "Accept": "*/*", "Connection": "keep-alive"}, "monitoring": {"enable_real_time": false, "log_level": "INFO", "save_raw_responses": true, "max_log_file_size": "100MB"}}