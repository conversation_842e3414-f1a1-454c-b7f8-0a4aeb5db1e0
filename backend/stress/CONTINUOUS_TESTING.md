# 持续压测功能使用指南

## 概述

持续压测功能允许您在指定时间内持续不断地发送并发请求，实现真正的循环并发测试。与传统的固定请求数量测试不同，持续压测会在设定时间内持续发送请求，一个请求完成后立即启动下一个请求。

## 功能特点

- **时间驱动**：基于持续时间而非请求数量
- **循环并发**：维持指定的并发数量，请求完成后立即发起新请求
- **实时监控**：提供实时进度反馈和统计信息
- **详细记录**：记录每个请求的完整生命周期
- **性能统计**：自动计算 RPS（每秒请求数）等性能指标

## 使用方法

### 1. 基本用法

```bash
# 启用持续压测，默认60秒
./run_test.sh --continuous

# 指定持续时间（秒）
./run_test.sh --continuous -d 120

# 指定并发数和持续时间
./run_test.sh --continuous -c 20 -d 300
```

### 2. 预设模式

```bash
# 快速测试（5个并发，标准模式）
./run_test.sh --quick

# 压力测试（50个并发，标准模式）
./run_test.sh --stress

# 持续压测（10个并发，持续5分钟）
./run_test.sh --endurance
```

### 3. 高级用法

```bash
# 持续压测 + 详细输出
./run_test.sh --continuous -d 180 -v

# 指定服务器地址和持续时间
./run_test.sh --continuous -s https://your-server.com -d 600

# 自定义所有参数
./run_test.sh --continuous -c 15 -d 240 -s https://api.example.com -v
```

### 4. Python 直接调用

```bash
# 使用 Python 脚本直接运行
python3 stress_test.py --continuous -d 120 -c 10

# 带详细输出
python3 stress_test.py --continuous -d 120 -c 10 -v
```

## 参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--continuous` | - | false | 启用持续压测模式 |
| `--duration` | `-d` | 60 | 持续时间（秒） |
| `--concurrency` | `-c` | 10 | 并发数量 |
| `--server` | `-s` | https://chat.z.ai | 目标服务器 |
| `--verbose` | `-v` | false | 详细输出模式 |

## 输出说明

### 实时监控

持续压测过程中会显示实时进度：

```
已完成 50 个请求，已运行 15.2s，剩余 44.8s
已完成 100 个请求，已运行 30.1s，剩余 29.9s
```

### 结果报告

测试完成后会显示详细统计：

```
=== 持续压测完成 ===
持续时间: 120s
总请求数: 456
成功请求数: 450
失败请求数: 6
成功率: 98.68%
平均响应时间: 2.345s
平均 TTFB: 1.234s
平均每秒请求数 (RPS): 3.80
详细结果保存在: results/stress_test_20240101_120000
```

## 性能指标

持续压测会自动计算以下关键指标：

- **RPS（每秒请求数）**：总请求数 ÷ 持续时间
- **成功率**：成功请求数 ÷ 总请求数 × 100%
- **平均响应时间**：所有请求响应时间的平均值
- **平均 TTFB**：首字节时间的平均值
- **并发效率**：实际并发度的维持情况

## 使用场景

### 1. 性能基准测试

```bash
# 测试系统在5分钟内的性能表现
./run_test.sh --continuous -d 300 -c 20
```

### 2. 容量规划

```bash
# 不同并发级别的长期测试
./run_test.sh --continuous -c 10 -d 600  # 低并发
./run_test.sh --continuous -c 50 -d 600  # 高并发
```

### 3. 稳定性测试

```bash
# 长时间稳定性测试
./run_test.sh --continuous -c 15 -d 3600  # 1小时测试
```

### 4. 负载模拟

```bash
# 模拟真实用户负载
./run_test.sh --continuous -c 8 -d 1800   # 模拟正常负载
```

## 注意事项

1. **资源消耗**：持续压测会产生大量日志文件，请确保有足够的磁盘空间
2. **网络稳定性**：长时间测试需要稳定的网络连接
3. **服务器影响**：请在测试环境中进行，避免影响生产系统
4. **监控系统**：建议同时监控目标服务器的资源使用情况
5. **合理设置**：根据系统能力合理设置并发数和持续时间

## 故障排除

### 1. 请求失败率过高

- 检查服务器负载和响应能力
- 适当降低并发数
- 检查网络连接稳定性

### 2. 响应时间过长

- 检查服务器性能瓶颈
- 分析请求处理链路
- 考虑优化请求体大小

### 3. 内存使用过高

- 适当降低并发数
- 定期清理结果文件
- 监控系统资源使用

## 结果分析

测试完成后，可以使用以下方式分析结果：

```bash
# 分析最新的测试结果
./run_test.sh -a results/stress_test_20240101_120000

# 启动实时监控
./run_test.sh -m results/stress_test_20240101_120000
```

结果文件包含：
- `summary_report.json`：JSON 格式的详细统计
- `summary_report.txt`：人类可读的报告
- `request_XXXX.log`：每个请求的详细日志 