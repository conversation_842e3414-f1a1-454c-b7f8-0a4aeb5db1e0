#!/usr/bin/env python3
"""
实时监控脚本

监控正在运行的压力测试，提供实时性能指标显示
"""

import asyncio
import json
import time
import os
import sys
from pathlib import Path
from datetime import datetime
import argparse
import logging

logger = logging.getLogger(__name__)


class RealTimeMonitor:
    """实时监控器"""
    
    def __init__(self, results_dir: str, refresh_interval: int = 2):
        self.results_dir = Path(results_dir)
        self.refresh_interval = refresh_interval
        self.last_check_time = 0
        self.request_stats = {}
        self.running = True
    
    async def start_monitoring(self):
        """开始监控"""
        print("=== 压力测试实时监控 ===")
        print(f"监控目录: {self.results_dir}")
        print(f"刷新间隔: {self.refresh_interval}秒")
        print("按 Ctrl+C 停止监控\n")

        try:
            while self.running:
                await self._update_stats()
                self._display_stats()
                await asyncio.sleep(self.refresh_interval)
        except KeyboardInterrupt:
            print("\n监控已停止")
    
    async def _update_stats(self):
        """更新统计信息"""
        if not self.results_dir.exists():
            return
        
        # 查找所有日志文件
        log_files = list(self.results_dir.glob("request_*.log"))
        
        current_stats = {
            'total_requests': len(log_files),
            'completed_requests': 0,
            'running_requests': 0,
            'failed_requests': 0,
            'avg_duration': 0,
            'avg_ttfb': 0,
            'success_rate': 0,
            'durations': [],
            'ttfbs': [],
            'errors': {}
        }
        
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查请求状态
                if "=== 请求结束 ===" in content:
                    current_stats['completed_requests'] += 1
                    
                    # 提取状态码
                    if "状态码: 200" in content:
                        pass  # 成功请求
                    else:
                        current_stats['failed_requests'] += 1
                        
                        # 提取错误信息
                        if "错误:" in content:
                            error_line = [line for line in content.split('\n') if line.startswith('错误:')]
                            if error_line:
                                error_msg = error_line[0].replace('错误:', '').strip()
                                current_stats['errors'][error_msg] = current_stats['errors'].get(error_msg, 0) + 1
                    
                    # 提取性能指标
                    duration_line = [line for line in content.split('\n') if line.startswith('总耗时:')]
                    if duration_line:
                        duration_str = duration_line[0].replace('总耗时:', '').replace('s', '').strip()
                        try:
                            duration = float(duration_str)
                            current_stats['durations'].append(duration)
                        except ValueError:
                            pass
                    
                    ttfb_line = [line for line in content.split('\n') if line.startswith('TTFB:') and 'N/A' not in line]
                    if ttfb_line:
                        ttfb_str = ttfb_line[0].replace('TTFB:', '').replace('s', '').strip()
                        try:
                            ttfb = float(ttfb_str)
                            current_stats['ttfbs'].append(ttfb)
                        except ValueError:
                            pass
                
                else:
                    current_stats['running_requests'] += 1
                    
            except Exception as e:
                logger.debug(f"读取日志文件 {log_file} 时出错: {e}")
        
        # 计算平均值
        if current_stats['durations']:
            current_stats['avg_duration'] = sum(current_stats['durations']) / len(current_stats['durations'])
        
        if current_stats['ttfbs']:
            current_stats['avg_ttfb'] = sum(current_stats['ttfbs']) / len(current_stats['ttfbs'])
        
        # 计算成功率
        if current_stats['completed_requests'] > 0:
            successful = current_stats['completed_requests'] - current_stats['failed_requests']
            current_stats['success_rate'] = (successful / current_stats['completed_requests']) * 100
        
        self.request_stats = current_stats
    
    def _display_stats(self):
        """显示统计信息"""
        # 清屏
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("=== 压力测试实时监控 ===")
        print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"监控目录: {self.results_dir}")
        print("-" * 60)
        
        stats = self.request_stats
        
        # 基本统计
        print(f"总请求数:     {stats['total_requests']}")
        print(f"已完成:       {stats['completed_requests']}")
        print(f"进行中:       {stats['running_requests']}")
        print(f"失败数:       {stats['failed_requests']}")
        print(f"成功率:       {stats['success_rate']:.2f}%")
        print()
        
        # 性能指标
        print("性能指标:")
        print(f"  平均响应时间: {stats['avg_duration']:.3f}s")
        print(f"  平均 TTFB:    {stats['avg_ttfb']:.3f}s")
        
        if stats['durations']:
            print(f"  最大响应时间: {max(stats['durations']):.3f}s")
            print(f"  最小响应时间: {min(stats['durations']):.3f}s")
        
        if stats['ttfbs']:
            print(f"  最大 TTFB:    {max(stats['ttfbs']):.3f}s")
            print(f"  最小 TTFB:    {min(stats['ttfbs']):.3f}s")
        
        print()
        
        # 错误统计
        if stats['errors']:
            print("错误统计:")
            for error_type, count in stats['errors'].items():
                print(f"  {error_type}: {count} 次")
        else:
            print("错误统计: 无错误")
        
        print()
        
        # 进度条
        if stats['total_requests'] > 0:
            progress = stats['completed_requests'] / stats['total_requests']
            bar_length = 40
            filled_length = int(bar_length * progress)
            bar = '█' * filled_length + '-' * (bar_length - filled_length)
            print(f"进度: [{bar}] {progress*100:.1f}%")
        
        print()
        print("按 Ctrl+C 停止监控")


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, results_dir: str):
        self.results_dir = Path(results_dir)
    
    def analyze_completed_test(self):
        """分析已完成的测试"""
        if not self.results_dir.exists():
            print(f"结果目录不存在: {self.results_dir}")
            return
        
        # 查找汇总报告
        summary_file = self.results_dir / "summary_report.json"
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                summary = json.load(f)
            
            self._display_summary(summary)
        else:
            print("未找到汇总报告，分析日志文件...")
            self._analyze_log_files()
    
    def _display_summary(self, summary: dict):
        """显示汇总信息"""
        print("=== 测试结果分析 ===")
        print()
        
        # 测试配置
        config = summary.get('test_config', {})
        print("测试配置:")
        print(f"  并发数: {config.get('concurrency', 'N/A')}")
        print(f"  服务器: {config.get('server_url', 'N/A')}")
        print(f"  测试时间: {config.get('test_time', 'N/A')}")
        print()
        
        # 统计信息
        stats = summary.get('statistics', {})
        print("统计信息:")
        print(f"  总请求数: {stats.get('total_requests', 0)}")
        print(f"  成功请求数: {stats.get('successful_requests', 0)}")
        print(f"  失败请求数: {stats.get('failed_requests', 0)}")
        print(f"  成功率: {stats.get('success_rate', 0):.2f}%")
        print()
        
        # 性能指标
        perf = summary.get('performance', {})
        print("性能指标:")
        print(f"  平均响应时间: {perf.get('avg_duration', 0):.3f}s")
        print(f"  最大响应时间: {perf.get('max_duration', 0):.3f}s")
        print(f"  最小响应时间: {perf.get('min_duration', 0):.3f}s")
        print(f"  平均 TTFB: {perf.get('avg_ttfb', 0):.3f}s")
        print(f"  最大 TTFB: {perf.get('max_ttfb', 0):.3f}s")
        print(f"  最小 TTFB: {perf.get('min_ttfb', 0):.3f}s")
        print()
        
        # 错误统计
        errors = summary.get('errors', {})
        if errors:
            print("错误统计:")
            for error_type, count in errors.items():
                print(f"  {error_type}: {count} 次")
        else:
            print("错误统计: 无错误")
    
    def _analyze_log_files(self):
        """分析日志文件"""
        log_files = list(self.results_dir.glob("request_*.log"))
        
        if not log_files:
            print("未找到日志文件")
            return
        
        print(f"找到 {len(log_files)} 个日志文件")
        
        # 简单分析
        completed = 0
        failed = 0
        
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "=== 请求结束 ===" in content:
                    completed += 1
                    if "状态码: 200" not in content:
                        failed += 1
            except Exception as e:
                print(f"读取 {log_file} 时出错: {e}")
        
        print(f"已完成请求: {completed}")
        print(f"失败请求: {failed}")
        print(f"成功率: {((completed - failed) / completed * 100) if completed > 0 else 0:.2f}%")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='压力测试监控工具')
    parser.add_argument('results_dir', help='测试结果目录')
    parser.add_argument('-i', '--interval', type=int, default=2,
                        help='刷新间隔，秒 (默认: 2)')
    parser.add_argument('-a', '--analyze', action='store_true',
                        help='分析已完成的测试结果')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='详细输出模式')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.basicConfig(
        level=logging.DEBUG if args.verbose else logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    if args.analyze:
        # 分析模式
        analyzer = LogAnalyzer(args.results_dir)
        analyzer.analyze_completed_test()
    else:
        # 监控模式
        monitor = RealTimeMonitor(args.results_dir, args.interval)
        await monitor.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
