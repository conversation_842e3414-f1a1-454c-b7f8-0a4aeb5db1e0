#!/usr/bin/env python3
"""
压力测试工具验证脚本

用于验证压力测试工具的各个组件是否正常工作
"""

import asyncio
import json
import os
import sys
import tempfile
import uuid
from pathlib import Path
import logging

# 导入测试模块
from stress_test import RequestTemplate, StressTestConfig, StressTester, RequestLogger

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestValidator:
    """测试验证器"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp(prefix="stress_test_validation_"))
        logger.info(f"创建临时测试目录: {self.temp_dir}")
        
        # 创建测试用的 curl.md 文件
        test_curl_content = """curl --location --request POST 'http://localhost:8080/api/chat/completions' \\
--header 'X-FE-Version: test-version' \\
--header 'Authorization: Bearer test-token' \\
--header 'Content-Type: application/json' \\
--data-raw '{
    "stream": true,
    "model": "test-model",
    "messages": [
        {
            "role": "user",
            "content": "测试消息"
        }
    ],
    "chat_id": "test-chat-id",
    "id": "test-id"
}'"""
        
        test_curl_file = self.temp_dir / "test_curl.md"
        with open(test_curl_file, 'w', encoding='utf-8') as f:
            f.write(test_curl_content)
        
        return test_curl_file
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        if self.temp_dir and self.temp_dir.exists():
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info(f"清理临时测试目录: {self.temp_dir}")
    
    def test_request_template_parsing(self, template_file):
        """测试请求模板解析"""
        logger.info("测试请求模板解析...")
        
        try:
            template = RequestTemplate(str(template_file))
            
            # 验证请求头解析
            expected_headers = {
                'X-FE-Version': 'test-version',
                'Authorization': 'Bearer test-token',
                'Content-Type': 'application/json'
            }
            
            for key, value in expected_headers.items():
                if template.headers.get(key) != value:
                    raise AssertionError(f"请求头解析错误: {key} = {template.headers.get(key)}, 期望 = {value}")
            
            # 验证请求体解析
            if template.request_body.get('model') != 'test-model':
                raise AssertionError(f"请求体解析错误: model = {template.request_body.get('model')}")
            
            if template.request_body.get('chat_id') != 'test-chat-id':
                raise AssertionError(f"请求体解析错误: chat_id = {template.request_body.get('chat_id')}")
            
            logger.info("✅ 请求模板解析测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 请求模板解析测试失败: {e}")
            return False
    
    def test_uuid_replacement(self, template_file):
        """测试 UUID 替换功能"""
        logger.info("测试 UUID 替换功能...")
        
        try:
            template = RequestTemplate(str(template_file))
            
            # 生成多个请求，验证 UUID 是否不同
            requests = []
            for i in range(5):
                url, headers, body = template.generate_request("http://test-server:8080")
                requests.append((url, headers, body))
            
            # 验证 URL 正确
            for url, _, _ in requests:
                if url != "http://test-server:8080/api/chat/completions":
                    raise AssertionError(f"URL 生成错误: {url}")
            
            # 验证 UUID 替换
            chat_ids = [body['chat_id'] for _, _, body in requests]
            ids = [body['id'] for _, _, body in requests]
            
            # 检查是否都是有效的 UUID
            for chat_id in chat_ids:
                uuid.UUID(chat_id)  # 如果不是有效 UUID 会抛出异常
            
            for id_val in ids:
                uuid.UUID(id_val)  # 如果不是有效 UUID 会抛出异常
            
            # 检查是否都不相同
            if len(set(chat_ids)) != len(chat_ids):
                raise AssertionError("chat_id 存在重复")
            
            if len(set(ids)) != len(ids):
                raise AssertionError("id 存在重复")
            
            # 验证其他字段未被修改
            for _, _, body in requests:
                if body['model'] != 'test-model':
                    raise AssertionError("model 字段被意外修改")
            
            logger.info("✅ UUID 替换功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ UUID 替换功能测试失败: {e}")
            return False
    
    def test_request_logger(self):
        """测试请求日志记录器"""
        logger.info("测试请求日志记录器...")
        
        try:
            # 创建日志记录器
            logger_instance = RequestLogger(self.temp_dir, 999)
            
            # 模拟请求生命周期
            test_url = "http://test-server:8080/api/chat/completions"
            test_headers = {"Content-Type": "application/json"}
            test_body = {"test": "data"}
            
            logger_instance.log_start(test_url, test_headers, test_body)
            logger_instance.log_ttfb()
            logger_instance.log_sse_event("data: test event 1")
            logger_instance.log_sse_event("data: test event 2")
            logger_instance.log_end(200)
            
            # 验证日志文件是否创建
            log_file = self.temp_dir / "request_0999.log"
            if not log_file.exists():
                raise AssertionError("日志文件未创建")
            
            # 验证日志内容
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_content = [
                "=== 请求 999 开始 ===",
                test_url,
                "TTFB:",
                "data: test event 1",
                "data: test event 2",
                "=== 请求结束 ===",
                "状态码: 200"
            ]
            
            for required in required_content:
                if required not in content:
                    raise AssertionError(f"日志内容缺失: {required}")
            
            # 验证摘要信息
            summary = logger_instance.get_summary()
            if summary['request_id'] != 999:
                raise AssertionError(f"摘要信息错误: request_id = {summary['request_id']}")
            
            if summary['status_code'] != 200:
                raise AssertionError(f"摘要信息错误: status_code = {summary['status_code']}")
            
            if summary['sse_events_count'] != 2:
                raise AssertionError(f"摘要信息错误: sse_events_count = {summary['sse_events_count']}")
            
            logger.info("✅ 请求日志记录器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 请求日志记录器测试失败: {e}")
            return False
    
    def test_config_creation(self):
        """测试配置创建"""
        logger.info("测试配置创建...")
        
        try:
            config = StressTestConfig(
                concurrency=5,
                server_url="http://test-server:8080",
                template_file="test.md",
                output_dir="test_output",
                timeout=120
            )
            
            # 验证配置参数
            if config.concurrency != 5:
                raise AssertionError(f"并发数配置错误: {config.concurrency}")
            
            if config.server_url != "http://test-server:8080":
                raise AssertionError(f"服务器URL配置错误: {config.server_url}")
            
            if config.timeout != 120:
                raise AssertionError(f"超时配置错误: {config.timeout}")
            
            # 验证结果目录创建
            if not config.results_dir.exists():
                raise AssertionError("结果目录未创建")
            
            logger.info("✅ 配置创建测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置创建测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始验证压力测试工具...")
        
        try:
            # 设置测试环境
            template_file = self.setup_test_environment()
            
            # 运行各项测试
            tests = [
                ("请求模板解析", lambda: self.test_request_template_parsing(template_file)),
                ("UUID 替换功能", lambda: self.test_uuid_replacement(template_file)),
                ("请求日志记录器", self.test_request_logger),
                ("配置创建", self.test_config_creation),
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                if test_func():
                    passed += 1
                    self.test_results.append((test_name, True, None))
                else:
                    self.test_results.append((test_name, False, "测试失败"))
            
            # 输出测试结果
            print("\n" + "="*60)
            print("压力测试工具验证结果")
            print("="*60)
            
            for test_name, success, error in self.test_results:
                status = "✅ 通过" if success else "❌ 失败"
                print(f"{test_name:<20} {status}")
                if error:
                    print(f"  错误: {error}")
            
            print("-"*60)
            print(f"总计: {passed}/{total} 个测试通过")
            
            if passed == total:
                print("🎉 所有测试通过！压力测试工具可以正常使用。")
                return True
            else:
                print("⚠️  部分测试失败，请检查相关功能。")
                return False
                
        finally:
            # 清理测试环境
            self.cleanup_test_environment()


async def main():
    """主函数"""
    validator = TestValidator()
    success = await validator.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
