#!/usr/bin/env python3
"""
Chat Completions API 压力测试工具

功能：
1. 基于 req_data/req_body.json 文件中的请求体进行压力测试
2. 支持配置化参数：并发数量、目标服务器地址
3. 自动将请求中的 chatid 和 id 字段替换为随机生成的 UUID
4. 详细记录每个并发请求的完整生命周期数据
"""

import asyncio
import aiohttp
import json
import uuid
import time
import argparse
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StressTestConfig:
    """压力测试配置类"""
    
    def __init__(self, 
                 concurrency: int = 10,
                 server_url: str = "http://**************:8080",
                 template_file: str = "req_data/req_body.json",
                 headers_file: str = "req_data/headers.json",
                 output_dir: str = "results",
                 timeout: int = 300,
                 continuous: bool = False,
                 duration: int = 60):
        self.concurrency = concurrency
        self.server_url = server_url
        self.template_file = template_file
        self.headers_file = headers_file
        self.output_dir = output_dir
        self.timeout = timeout
        self.continuous = continuous  # 是否启用持续压测模式
        self.duration = duration      # 持续压测时间（秒）
        self.test_start_time = datetime.now()
        
        # 创建输出目录
        self.results_dir = Path(output_dir) / f"stress_test_{self.test_start_time.strftime('%Y%m%d_%H%M%S')}"
        self.results_dir.mkdir(parents=True, exist_ok=True)


class RequestTemplate:
    """请求模板处理类"""
    
    def __init__(self, template_file: str, headers_file: str = "req_data/headers.json"):
        self.template_file = template_file
        self.headers_file = headers_file
        self.headers = {}
        self.request_body = {}
        self._load_headers()
        self._load_request_body()
    
    def _load_headers(self):
        """从 JSON 文件加载请求头"""
        try:
            with open(self.headers_file, 'r', encoding='utf-8') as f:
                self.headers = json.load(f)
                
            logger.info(f"成功加载请求头文件: {self.headers_file}")
            logger.info(f"加载了 {len(self.headers)} 个请求头")
            
        except Exception as e:
            logger.error(f"加载请求头文件失败: {e}")
            raise
    
    def _load_request_body(self):
        """从 JSON 文件加载请求体"""
        try:
            with open(self.template_file, 'r', encoding='utf-8') as f:
                self.request_body = json.load(f)
                
            logger.info(f"成功加载请求体文件: {self.template_file}")
            
        except Exception as e:
            logger.error(f"加载请求体文件失败: {e}")
            raise
    
    def generate_request(self, server_url: str) -> Tuple[str, Dict, Dict]:
        """生成一个新的请求，替换 UUID 字段"""
        # 深拷贝请求体
        body = json.loads(json.dumps(self.request_body))
        
        # 替换 chat_id 和 id 字段
        body['chat_id'] = str(uuid.uuid4())
        body['id'] = str(uuid.uuid4())
        
        # 构建完整的 URL
        url = f"{server_url}/api/chat/completions"
        
        # 复制请求头
        headers = self.headers.copy()
        
        return url, headers, body


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self, results_dir: Path, request_id: int):
        self.request_id = request_id
        self.log_file = results_dir / f"request_{request_id:04d}.log"
        self.start_time = None
        self.ttfb = None
        self.end_time = None
        self.status_code = None
        self.error = None
        self.sse_events = []
        
    def log_start(self, url: str, headers: Dict, body: Dict):
        """记录请求开始"""
        self.start_time = time.time()
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write(f"=== 请求 {self.request_id} 开始 ===\n")
            f.write(f"开始时间: {datetime.fromtimestamp(self.start_time)}\n")
            f.write(f"URL: {url}\n")
            f.write(f"请求头:\n{json.dumps(headers, indent=2, ensure_ascii=False)}\n")
            f.write(f"请求体:\n{json.dumps(body, indent=2, ensure_ascii=False)}\n")
            f.write("\n=== 响应信息 ===\n")
    
    def log_ttfb(self):
        """记录首次响应时间"""
        if self.start_time:
            self.ttfb = time.time() - self.start_time
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"TTFB: {self.ttfb:.3f}s\n")

    def log_response_headers(self, status_code: int, headers: Dict):
        """记录响应头信息"""
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"响应状态码: {status_code}\n")
            f.write(f"响应头:\n{json.dumps(dict(headers), indent=2, ensure_ascii=False)}\n")
            f.write("\n=== SSE 流数据 ===\n")
    
    def log_sse_event(self, event_data: str):
        """记录 SSE 事件"""
        timestamp = time.time()
        self.sse_events.append((timestamp, event_data))
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{datetime.fromtimestamp(timestamp)}] {event_data}\n")
    
    def log_end(self, status_code: int, error: Optional[str] = None):
        """记录请求结束"""
        self.end_time = time.time()
        self.status_code = status_code
        self.error = error
        
        duration = self.end_time - self.start_time if self.start_time else 0
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== 请求结束 ===\n")
            f.write(f"结束时间: {datetime.fromtimestamp(self.end_time)}\n")
            f.write(f"状态码: {status_code}\n")
            f.write(f"总耗时: {duration:.3f}s\n")
            f.write(f"TTFB: {self.ttfb:.3f}s\n" if self.ttfb else "TTFB: N/A\n")
            f.write(f"SSE 事件数量: {len(self.sse_events)}\n")
            if error:
                f.write(f"错误: {error}\n")
    
    def get_summary(self) -> Dict:
        """获取请求摘要信息"""
        duration = self.end_time - self.start_time if self.start_time and self.end_time else 0
        return {
            'request_id': self.request_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': duration,
            'ttfb': self.ttfb,
            'status_code': self.status_code,
            'sse_events_count': len(self.sse_events),
            'error': self.error
        }


class StressTester:
    """压力测试主类"""
    
    def __init__(self, config: StressTestConfig):
        self.config = config
        self.template = RequestTemplate(config.template_file, config.headers_file)
        self.results = []
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=self.config.concurrency * 2)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def execute_single_request(self, request_id: int) -> Dict:
        """执行单个请求"""
        logger_instance = RequestLogger(self.config.results_dir, request_id)

        try:
            # 生成请求
            url, headers, body = self.template.generate_request(self.config.server_url)

            # 记录请求开始
            logger_instance.log_start(url, headers, body)

            # 发送请求
            async with self.session.post(url, headers=headers, json=body, timeout=1200) as response:
                # 记录 TTFB
                logger_instance.log_ttfb()

                # 记录响应头
                logger_instance.log_response_headers(response.status, response.headers)

                # 处理 SSE 流
                if response.content_type == 'text/event-stream':
                    await self._process_sse_stream(response, logger_instance)
                else:
                    # 非流式响应
                    content = await response.text()
                    logger_instance.log_sse_event(f"Non-SSE Response: {content}")

                # 记录请求结束
                logger_instance.log_end(response.status)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"请求 {request_id} 失败: {error_msg}")
            logger_instance.log_end(500, error_msg)

        return logger_instance.get_summary()

    async def _process_sse_stream(self, response: aiohttp.ClientResponse, logger_instance: RequestLogger):
        """处理 SSE 流数据"""
        try:
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if line_str:
                    logger_instance.log_sse_event(line_str)

                    # 检查是否是结束标志
                    if line_str == "data: [DONE]":
                        break

        except Exception as e:
            logger.error(f"处理 SSE 流时出错: {e}")
            logger_instance.log_sse_event(f"SSE Stream Error: {e}")

    async def run_stress_test(self) -> List[Dict]:
        """运行压力测试"""
        if self.config.continuous:
            return await self.run_continuous_stress_test()
        else:
            return await self.run_standard_stress_test()

    async def run_standard_stress_test(self) -> List[Dict]:
        """运行标准压力测试（原有逻辑）"""
        logger.info(f"开始标准压力测试: 并发数={self.config.concurrency}")
        logger.info(f"目标服务器: {self.config.server_url}")
        logger.info(f"结果目录: {self.config.results_dir}")

        # 创建并发任务
        tasks = []
        for i in range(self.config.concurrency):
            task = asyncio.create_task(self.execute_single_request(i + 1))
            tasks.append(task)

        # 等待所有任务完成
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()

        # 处理结果
        self.results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"任务 {i+1} 异常: {result}")
                self.results.append({
                    'request_id': i + 1,
                    'error': str(result),
                    'status_code': 500
                })
            else:
                self.results.append(result)

        total_duration = end_time - start_time
        logger.info(f"标准压力测试完成，总耗时: {total_duration:.3f}s")

        return self.results

    async def run_continuous_stress_test(self) -> List[Dict]:
        """运行持续压测（新功能）"""
        logger.info(f"开始持续压力测试: 并发数={self.config.concurrency}, 持续时间={self.config.duration}s")
        logger.info(f"目标服务器: {self.config.server_url}")
        logger.info(f"结果目录: {self.config.results_dir}")

        start_time = time.time()
        end_time = start_time + self.config.duration
        request_counter = 0
        active_tasks = set()
        self.results = []

        # 创建一个锁来保护共享资源
        results_lock = asyncio.Lock()

        async def worker():
            """工作协程：持续发送请求直到时间结束"""
            nonlocal request_counter
            
            while time.time() < end_time:
                # 获取新的请求ID
                async with results_lock:
                    request_counter += 1
                    current_request_id = request_counter

                try:
                    # 执行请求
                    result = await self.execute_single_request(current_request_id)
                    
                    # 保存结果
                    async with results_lock:
                        self.results.append(result)
                        
                    # 记录进度
                    if current_request_id % 10 == 0:
                        elapsed = time.time() - start_time
                        remaining = end_time - time.time()
                        logger.info(f"已完成 {current_request_id} 个请求，已运行 {elapsed:.1f}s，剩余 {remaining:.1f}s")

                except Exception as e:
                    logger.error(f"请求 {current_request_id} 执行异常: {e}")
                    async with results_lock:
                        self.results.append({
                            'request_id': current_request_id,
                            'error': str(e),
                            'status_code': 500
                        })

        # 启动并发工作协程
        for i in range(self.config.concurrency):
            task = asyncio.create_task(worker())
            active_tasks.add(task)

        # 等待所有工作协程完成或超时
        try:
            await asyncio.wait_for(
                asyncio.gather(*active_tasks, return_exceptions=True),
                timeout=self.config.duration + 10  # 额外的10秒缓冲
            )
        except asyncio.TimeoutError:
            logger.warning("部分任务超时，正在强制终止...")
            for task in active_tasks:
                if not task.done():
                    task.cancel()

        actual_duration = time.time() - start_time
        logger.info(f"持续压力测试完成，实际耗时: {actual_duration:.3f}s，完成请求数: {len(self.results)}")

        return self.results

    def generate_summary_report(self) -> Dict:
        """生成汇总报告"""
        if not self.results:
            return {}

        # 统计数据
        total_requests = len(self.results)
        successful_requests = len([r for r in self.results if r.get('status_code') == 200])
        failed_requests = total_requests - successful_requests

        # 计算性能指标
        durations = [r.get('duration', 0) for r in self.results if r.get('duration')]
        ttfbs = [r.get('ttfb', 0) for r in self.results if r.get('ttfb')]

        avg_duration = sum(durations) / len(durations) if durations else 0
        avg_ttfb = sum(ttfbs) / len(ttfbs) if ttfbs else 0
        max_duration = max(durations) if durations else 0
        min_duration = min(durations) if durations else 0
        max_ttfb = max(ttfbs) if ttfbs else 0
        min_ttfb = min(ttfbs) if ttfbs else 0

        # 错误统计
        error_types = {}
        for result in self.results:
            if result.get('error'):
                error_type = result['error']
                error_types[error_type] = error_types.get(error_type, 0) + 1

        summary = {
            'test_config': {
                'concurrency': self.config.concurrency,
                'server_url': self.config.server_url,
                'test_time': self.config.test_start_time.isoformat(),
                'timeout': self.config.timeout,
                'continuous': self.config.continuous,
                'duration': self.config.duration if self.config.continuous else None
            },
            'statistics': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': successful_requests / total_requests * 100 if total_requests > 0 else 0
            },
            'performance': {
                'avg_duration': avg_duration,
                'max_duration': max_duration,
                'min_duration': min_duration,
                'avg_ttfb': avg_ttfb,
                'max_ttfb': max_ttfb,
                'min_ttfb': min_ttfb
            },
            'errors': error_types
        }

        return summary

    def save_summary_report(self, summary: Dict):
        """保存汇总报告"""
        report_file = self.config.results_dir / "summary_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # 同时生成可读的文本报告
        text_report_file = self.config.results_dir / "summary_report.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write("=== Chat Completions API 压力测试报告 ===\n\n")

            # 测试配置
            config = summary['test_config']
            f.write("测试配置:\n")
            f.write(f"  测试模式: {'持续压测' if config['continuous'] else '标准压测'}\n")
            f.write(f"  并发数: {config['concurrency']}\n")
            f.write(f"  目标服务器: {config['server_url']}\n")
            f.write(f"  测试时间: {config['test_time']}\n")
            if config['continuous']:
                f.write(f"  持续时间: {config['duration']}s\n")
            f.write(f"  超时设置: {config['timeout']}s\n\n")

            # 统计信息
            stats = summary['statistics']
            f.write("统计信息:\n")
            f.write(f"  总请求数: {stats['total_requests']}\n")
            f.write(f"  成功请求数: {stats['successful_requests']}\n")
            f.write(f"  失败请求数: {stats['failed_requests']}\n")
            f.write(f"  成功率: {stats['success_rate']:.2f}%\n\n")

            # 性能指标
            perf = summary['performance']
            f.write("性能指标:\n")
            f.write(f"  平均响应时间: {perf['avg_duration']:.3f}s\n")
            f.write(f"  最大响应时间: {perf['max_duration']:.3f}s\n")
            f.write(f"  最小响应时间: {perf['min_duration']:.3f}s\n")
            f.write(f"  平均 TTFB: {perf['avg_ttfb']:.3f}s\n")
            f.write(f"  最大 TTFB: {perf['max_ttfb']:.3f}s\n")
            f.write(f"  最小 TTFB: {perf['min_ttfb']:.3f}s\n\n")

            # 错误信息
            if summary['errors']:
                f.write("错误统计:\n")
                for error_type, count in summary['errors'].items():
                    f.write(f"  {error_type}: {count} 次\n")
            else:
                f.write("错误统计: 无错误\n")

        logger.info(f"汇总报告已保存到: {report_file}")
        logger.info(f"文本报告已保存到: {text_report_file}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Chat Completions API 压力测试工具')
    parser.add_argument('-c', '--concurrency', type=int, default=10,
                        help='并发请求数量 (默认: 10)')
    parser.add_argument('-s', '--server', type=str, default='http://**************:8080',
                        help='目标服务器地址 (默认: http://**************:8080)')
    parser.add_argument('-t', '--template', type=str, default='req_data/req_body.json',
                        help='请求模板文件路径 (默认: req_data/req_body.json)')
    parser.add_argument('-H', '--headers', type=str, default='req_data/headers.json',
                        help='请求头文件路径 (默认: req_data/headers.json)')
    parser.add_argument('-o', '--output', type=str, default='results',
                        help='输出目录 (默认: results)')
    parser.add_argument('--timeout', type=int, default=300,
                        help='请求超时时间，秒 (默认: 300)')
    parser.add_argument('--continuous', action='store_true',
                        help='启用持续压测模式')
    parser.add_argument('-d', '--duration', type=int, default=60,
                        help='持续压测时间，秒 (默认: 60，仅在持续模式下有效)')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='详细输出模式')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查模板文件是否存在
    if not os.path.exists(args.template):
        logger.error(f"请求体文件不存在: {args.template}")
        return 1

    # 检查请求头文件是否存在
    if not os.path.exists(args.headers):
        logger.error(f"请求头文件不存在: {args.headers}")
        return 1

    # 创建配置
    config = StressTestConfig(
        concurrency=args.concurrency,
        server_url=args.server,
        template_file=args.template,
        headers_file=args.headers,
        output_dir=args.output,
        timeout=args.timeout,
        continuous=args.continuous,
        duration=args.duration
    )

    try:
        # 运行压力测试
        async with StressTester(config) as tester:
            results = await tester.run_stress_test()

            # 生成报告
            summary = tester.generate_summary_report()
            tester.save_summary_report(summary)

            # 打印简要结果
            test_mode = "持续压测" if config.continuous else "标准压测"
            print(f"\n=== {test_mode}完成 ===")
            if config.continuous:
                print(f"持续时间: {config.duration}s")
            print(f"总请求数: {summary['statistics']['total_requests']}")
            print(f"成功请求数: {summary['statistics']['successful_requests']}")
            print(f"失败请求数: {summary['statistics']['failed_requests']}")
            print(f"成功率: {summary['statistics']['success_rate']:.2f}%")
            print(f"平均响应时间: {summary['performance']['avg_duration']:.3f}s")
            print(f"平均 TTFB: {summary['performance']['avg_ttfb']:.3f}s")
            if config.continuous:
                total_requests = summary['statistics']['total_requests']
                rps = total_requests / config.duration if config.duration > 0 else 0
                print(f"平均每秒请求数 (RPS): {rps:.2f}")
            print(f"详细结果保存在: {config.results_dir}")

            return 0 if summary['statistics']['failed_requests'] == 0 else 1

    except Exception as e:
        logger.error(f"压力测试执行失败: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
