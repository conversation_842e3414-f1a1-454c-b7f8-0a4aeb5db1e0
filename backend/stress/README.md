# Chat Completions API 压力测试工具

这是一个专门为测试 chat/completions 接口设计的压力测试工具，支持并发请求、SSE 流处理、详细日志记录和性能分析。

## 🛠️ 工具组件

1. **`stress_test.py`** - 核心压力测试工具
2. **`batch_test.py`** - 批量测试工具
3. **`monitor.py`** - 完整实时监控工具
4. **`simple_monitor.py`** - 简化版监控工具（推荐）
5. **`run_test.sh`** - 快速启动脚本
6. **`test_validation.py`** - 工具验证脚本
7. **`config_example.json`** - 配置示例文件

## 功能特性

- ✅ **基于模板的请求生成**: 从 `curl.md` 文件解析请求模板
- ✅ **UUID 自动替换**: 自动替换请求中的 `chat_id` 和 `id` 字段为随机 UUID
- ✅ **并发压力测试**: 支持配置并发数量进行压力测试
- ✅ **SSE 流处理**: 完整记录 Server-Sent Events 流数据
- ✅ **详细日志记录**: 每个请求独立日志文件，记录完整生命周期
- ✅ **响应头记录**: 记录完整的HTTP响应头信息
- ✅ **TTFB 测量**: 精确测量首次响应时间 (Time To First Byte)
- ✅ **性能统计**: 自动生成详细的性能分析报告
- ✅ **错误分析**: 统计和分类各种错误类型
- ✅ **实时监控**: 支持实时监控测试进度和性能指标

## 安装依赖

```bash
pip install aiohttp
```

## 使用方法

### 基本用法

```bash
# 使用默认配置运行压力测试
python stress_test.py

# 指定并发数量
python stress_test.py -c 20

# 指定目标服务器
python stress_test.py -s http://localhost:8080

# 指定输出目录
python stress_test.py -o my_test_results
```

### 完整参数说明

```bash
python stress_test.py [选项]

选项:
  -c, --concurrency INT    并发请求数量 (默认: 10)
  -s, --server URL         目标服务器地址 (默认: http://**************:8080)
  -t, --template FILE      请求模板文件路径 (默认: curl.md)
  -o, --output DIR         输出目录 (默认: results)
  --timeout INT            请求超时时间，秒 (默认: 300)
  -v, --verbose            详细输出模式
  -h, --help               显示帮助信息
```

### 示例命令

```bash
# 高并发测试
python stress_test.py -c 50 -s http://production-server:8080 -v

# 本地测试
python stress_test.py -c 5 -s http://localhost:8080 -o local_test

# 长时间测试
python stress_test.py -c 10 --timeout 600 -o long_test
```

## 📊 实时监控

### 方法一：简化版监控（推荐）

```bash
# 实时监控测试进度
python3 simple_monitor.py results/stress_test_20250613_161133

# 分析已完成的测试
python3 simple_monitor.py results/stress_test_20250613_161133 --analyze
```

### 方法二：完整版监控

```bash
# 监控最新的测试结果
./run_test.sh -m results/stress_test_20250612_143022

# 或者直接使用 Python
python3 monitor.py results/stress_test_20250612_143022
```

### 监控界面显示内容：
- 实时请求统计（总数、完成数、进行中、成功数、失败数）
- 性能指标（平均/最大/最小响应时间、TTFB）
- 成功率百分比
- 错误统计和分类
- 可视化进度条
- 测试状态指示

## 输出结果

### 目录结构

测试完成后，会在指定的输出目录下创建以下结构：

```
results/
└── stress_test_20250612_143022/
    ├── request_0001.log          # 请求1的详细日志
    ├── request_0002.log          # 请求2的详细日志
    ├── ...
    ├── request_0010.log          # 请求10的详细日志
    ├── summary_report.json       # JSON格式汇总报告
    └── summary_report.txt        # 文本格式汇总报告
```

### 单个请求日志格式

每个 `request_XXXX.log` 文件包含：

```
=== 请求 1 开始 ===
开始时间: 2025-06-13 16:11:33.844250
URL: http://**************:8080/api/chat/completions
请求头:
{
  "X-FE-Version": "staging-fe-0.0.171",
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "Content-Type": "application/json"
}
请求体:
{
  "stream": true,
  "model": "GLM-0530-SGLANG",
  "chat_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  ...
}

=== 响应信息 ===
TTFB: 63.625s
响应状态码: 200
响应头:
{
  "Content-Type": "text/event-stream",
  "Cache-Control": "no-cache",
  "Connection": "keep-alive",
  "X-Request-ID": "abc123",
  ...
}

=== SSE 流数据 ===
[2025-06-13 16:12:37.561906] data: {"type":"chat:completion","data":{"delta_content":"你好"}}
[2025-06-13 16:12:37.643468] data: {"type":"chat:completion","data":{"delta_content":"！我是"}}
[2025-06-13 16:12:37.643536] data: {"type":"chat:completion","data":{"delta_content":"AI助手"}}
[2025-06-13 16:16:34.609426] SSE Stream Error:

=== 请求结束 ===
结束时间: 2025-06-13 16:16:34.609616
状态码: 200
总耗时: 300.765s
TTFB: 63.625s
SSE 事件数量: 134
```

### 汇总报告格式

`summary_report.txt` 包含：

```
=== Chat Completions API 压力测试报告 ===

测试配置:
  并发数: 10
  目标服务器: http://**************:8080
  测试时间: 2025-06-12T14:30:22.123456
  超时设置: 300s

统计信息:
  总请求数: 10
  成功请求数: 9
  失败请求数: 1
  成功率: 90.00%

性能指标:
  平均响应时间: 2.345s
  最大响应时间: 4.567s
  最小响应时间: 1.234s
  平均 TTFB: 0.156s
  最大 TTFB: 0.234s
  最小 TTFB: 0.123s

错误统计:
  Connection timeout: 1 次
```

## 模板文件格式

工具会自动解析 `curl.md` 文件中的 curl 命令，提取：

1. **请求头**: 从 `--header` 参数中提取
2. **请求体**: 从 `--data-raw` 参数中提取 JSON 数据

确保 `curl.md` 文件包含完整的 curl 命令，包括所有必要的请求头和请求体。

## UUID 替换规则

工具会自动替换请求体中的以下字段：
- `chat_id`: 替换为新的 UUID
- `id`: 替换为新的 UUID

这确保每个并发请求都有唯一的标识符，避免冲突。

## 性能监控指标

### 关键指标

1. **TTFB (Time To First Byte)**: 从发送请求到收到第一个字节的时间
2. **总响应时间**: 从请求开始到完全结束的时间
3. **成功率**: 成功请求占总请求的百分比
4. **SSE 事件数量**: 每个请求收到的 SSE 事件总数

### 性能分析

- **平均值**: 了解整体性能水平
- **最大值**: 识别性能瓶颈
- **最小值**: 了解最佳性能表现
- **错误分布**: 分析失败原因

## 故障排除

### 常见问题

1. **模板文件解析失败**
   - 检查 `curl.md` 文件格式是否正确
   - 确保包含完整的 curl 命令

2. **连接超时**
   - 检查目标服务器是否可访问
   - 增加 `--timeout` 参数值

3. **权限错误**
   - 检查 Authorization token 是否有效
   - 确认用户权限设置

4. **并发限制**
   - 降低并发数量 `-c` 参数
   - 检查服务器并发限制设置

### 调试模式

使用 `-v` 参数启用详细输出模式，获取更多调试信息：

```bash
python stress_test.py -v -c 5
```

## 注意事项

1. **服务器负载**: 高并发测试可能对目标服务器造成较大负载，请谨慎使用
2. **网络带宽**: SSE 流数据可能消耗大量网络带宽
3. **磁盘空间**: 详细日志可能占用较多磁盘空间
4. **测试环境**: 建议在测试环境中进行压力测试，避免影响生产环境

## 扩展功能

如需添加更多功能，可以考虑：

1. **自定义请求模板**: 支持多种请求模板
2. **实时监控**: 添加实时性能监控界面
3. **分布式测试**: 支持多机器分布式压力测试
4. **更多统计指标**: 添加更详细的性能分析指标
