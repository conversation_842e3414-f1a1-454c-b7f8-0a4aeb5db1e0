#!/bin/bash

# Chat Completions API 压力测试快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_CONCURRENCY=10
DEFAULT_SERVER="https://chat.z.ai"
DEFAULT_OUTPUT="results"
DEFAULT_TEMPLATE="req_data/req_body.json"
DEFAULT_HEADERS="req_data/headers.json"

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：显示帮助信息
show_help() {
    echo "Chat Completions API 压力测试工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --concurrency NUM    并发数量 (默认: $DEFAULT_CONCURRENCY)"
    echo "  -s, --server URL         服务器地址 (默认: $DEFAULT_SERVER)"
    echo "  -o, --output DIR         输出目录 (默认: $DEFAULT_OUTPUT)"
    echo "  -t, --template FILE      请求体文件 (默认: $DEFAULT_TEMPLATE)"
    echo "  -H, --headers FILE       请求头文件 (默认: $DEFAULT_HEADERS)"
    echo "  --timeout SECONDS        超时时间 (默认: 300)"
    echo "  --continuous             启用持续压测模式"
    echo "  -d, --duration SECONDS   持续压测时间 (默认: 60)"
    echo "  -v, --verbose            详细输出"
    echo "  -m, --monitor            启动实时监控"
    echo "  -b, --batch              运行批量测试"
    echo "  -a, --analyze DIR        分析测试结果"
    echo "  --quick                  快速测试 (5个并发)"
    echo "  --stress                 压力测试 (50个并发)"
    echo "  --endurance              持续压测 (10个并发，持续5分钟)"
    echo "  --prod                   使用生产环境配置 (headers.json)"
    echo "  --test                   使用测试环境配置 (headers_test.json)"
    echo "  -h, --help               显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 使用默认配置运行测试"
    echo "  $0 -c 20 -v             # 20个并发，详细输出"
    echo "  $0 --continuous -d 120  # 持续压测2分钟"
    echo "  $0 --quick              # 快速测试"
    echo "  $0 --stress             # 压力测试"
    echo "  $0 --endurance          # 持续压测"
    echo "  $0 --prod               # 使用生产环境配置"
    echo "  $0 --test               # 使用测试环境配置"
    echo "  $0 -H req_data/custom.json  # 使用自定义请求头文件"
    echo "  $0 -m results/latest    # 监控测试结果"
    echo "  $0 -b                   # 运行批量测试"
    echo "  $0 -a results/test_001  # 分析测试结果"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查 aiohttp
    if ! python3 -c "import aiohttp" 2>/dev/null; then
        print_warning "aiohttp 未安装，正在安装..."
        pip3 install aiohttp
    fi
    
    # 检查模板文件
    if [ ! -f "$template_file" ]; then
        print_error "请求体文件不存在: $template_file"
        exit 1
    fi
    
    # 检查请求头文件
    if [ ! -f "$headers_file" ]; then
        print_error "请求头文件不存在: $headers_file"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：运行单次测试
run_single_test() {
    local test_type="标准压力测试"
    if [ "$continuous" = true ]; then
        test_type="持续压测"
    fi
    
    print_info "开始 $test_type..."
    print_info "配置: 并发=$concurrency, 服务器=$server, 输出=$output"
    print_info "请求头文件: $headers_file"
    if [ "$continuous" = true ]; then
        print_info "持续时间: ${duration}s"
    fi

    local cmd="python3 stress_test.py -c $concurrency -s $server -o $output -t $template_file -H $headers_file"
    
    if [ "$timeout" != "" ]; then
        cmd="$cmd --timeout $timeout"
    fi
    
    if [ "$continuous" = true ]; then
        cmd="$cmd --continuous -d $duration"
    fi
    
    if [ "$verbose" = true ]; then
        cmd="$cmd -v"
    fi
    
    echo "执行命令: $cmd"
    eval $cmd
    
    if [ $? -eq 0 ]; then
        print_success "$test_type 完成"
        
        # 查找最新的结果目录
        latest_dir=$(find $output -name "stress_test_*" -type d | sort | tail -1)
        if [ -n "$latest_dir" ]; then
            print_info "结果保存在: $latest_dir"
            
            # 显示简要结果
            if [ -f "$latest_dir/summary_report.txt" ]; then
                echo ""
                echo "=== 测试结果摘要 ==="
                head -20 "$latest_dir/summary_report.txt"
            fi
        fi
    else
        print_error "$test_type 失败"
        exit 1
    fi
}

# 函数：运行批量测试
run_batch_test() {
    print_info "开始批量测试..."
    
    local cmd="python3 batch_test.py -o $output"
    
    if [ "$verbose" = true ]; then
        cmd="$cmd -v"
    fi
    
    echo "执行命令: $cmd"
    eval $cmd
    
    if [ $? -eq 0 ]; then
        print_success "批量测试完成"
    else
        print_error "批量测试失败"
        exit 1
    fi
}

# 函数：启动监控
start_monitor() {
    local monitor_dir="$1"
    
    if [ -z "$monitor_dir" ]; then
        print_error "请指定要监控的目录"
        exit 1
    fi
    
    print_info "启动实时监控: $monitor_dir"
    python3 monitor.py "$monitor_dir"
}

# 函数：分析结果
analyze_results() {
    local analyze_dir="$1"
    
    if [ -z "$analyze_dir" ]; then
        print_error "请指定要分析的目录"
        exit 1
    fi
    
    print_info "分析测试结果: $analyze_dir"
    python3 monitor.py "$analyze_dir" --analyze
}

# 解析命令行参数
concurrency=$DEFAULT_CONCURRENCY
server=$DEFAULT_SERVER
output=$DEFAULT_OUTPUT
template_file=$DEFAULT_TEMPLATE
headers_file=$DEFAULT_HEADERS
timeout=""
continuous=false
duration=60
verbose=false
monitor=false
batch=false
analyze=""
quick=false
stress=false
endurance=false
prod=false
test=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--concurrency)
            concurrency="$2"
            shift 2
            ;;
        -s|--server)
            server="$2"
            shift 2
            ;;
        -o|--output)
            output="$2"
            shift 2
            ;;
        -t|--template)
            template_file="$2"
            shift 2
            ;;
        -H|--headers)
            headers_file="$2"
            shift 2
            ;;
        --timeout)
            timeout="$2"
            shift 2
            ;;
        --continuous)
            continuous=true
            shift
            ;;
        -d|--duration)
            duration="$2"
            shift 2
            ;;
        -v|--verbose)
            verbose=true
            shift
            ;;
        -m|--monitor)
            monitor=true
            if [[ $# -gt 1 && ! $2 =~ ^- ]]; then
                monitor_dir="$2"
                shift
            fi
            shift
            ;;
        -b|--batch)
            batch=true
            shift
            ;;
        -a|--analyze)
            analyze="$2"
            shift 2
            ;;
        --quick)
            quick=true
            concurrency=5
            shift
            ;;
        --stress)
            stress=true
            concurrency=50
            shift
            ;;
        --endurance)
            endurance=true
            continuous=true
            concurrency=10
            duration=300
            shift
            ;;
        --prod)
            prod=true
            headers_file="req_data/headers.json"
            shift
            ;;
        --test)
            test=true
            headers_file="req_data/headers_test.json"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
if [ "$monitor" = true ]; then
    start_monitor "$monitor_dir"
elif [ "$batch" = true ]; then
    check_dependencies
    run_batch_test
elif [ -n "$analyze" ]; then
    analyze_results "$analyze"
else
    check_dependencies

    # 显示当前配置信息
    if [ "$prod" = true ]; then
        print_info "使用生产环境配置 (headers.json)"
    elif [ "$test" = true ]; then
        print_info "使用测试环境配置 (headers_test.json)"
    fi

    if [ "$quick" = true ]; then
        print_info "运行快速测试模式 (5个并发)"
    elif [ "$stress" = true ]; then
        print_info "运行压力测试模式 (50个并发)"
    elif [ "$endurance" = true ]; then
        print_info "运行持续压测模式 (10个并发，持续5分钟)"
    elif [ "$continuous" = true ]; then
        print_info "运行持续压测模式 (${concurrency}个并发，持续${duration}秒)"
    fi

    run_single_test
fi
