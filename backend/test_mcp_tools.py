#!/usr/bin/env python3
"""
测试 MCP 工具转换为 OpenAI 工具格式的功能
"""

import json
import sys
import os

# 添加后端路径到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'open_webui'))

from open_webui.mcp.tools import convert_mcp_tool_to_openai_function

def test_mcp_tools_conversion():
    """测试 MCP 工具转换为 OpenAI 格式"""
    
    # 测试输入数据
    test_tools = [
        {
            "name": "visit_page",
            "description": "Opens a specific webpage in a browser for viewing. The URL provided points to the webpage to open. The tool loads the webpage for browsing and returns its main content for first page in Markdown format.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "url": {
                        "description": "The URL of the webpage to visit",
                        "title": "Url",
                        "type": "string"
                    }
                },
                "required": [
                    "url"
                ]
            }
        },
        {
            "name": "click",
            "description": "Clicks on a specific element in the current webpage. The reference number provided points to the element to click. Only elements clearly marked with reference number (ref=ref_id) are clickable. The tool returns the content of the webpage after clicking the element in Markdown format.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "ref": {
                        "description": "The reference number of the element to click",
                        "title": "Ref",
                        "type": "string"
                    }
                },
                "required": [
                    "ref"
                ]
            }
        },
        {
            "name": "page_up",
            "description": "Scrolls up one page in the browser. The tool will return the page content of the webpage in Markdown format after scrolling up.",
            "inputSchema": {
                "type": "object",
                "properties": {}
            }
        },
        {
            "name": "page_down",
            "description": "Scrolls down one page in the browser. The tool will return the page content of the webpage in Markdown format after scrolling down.",
            "inputSchema": {
                "type": "object",
                "properties": {}
            }
        },
        {
            "name": "find_on_page_ctrl_f",
            "description": "Finds a specific string on the current webpage. The search string provided is the string to search for in the current webpage. The tool will return the first page content containing the string.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "search_string": {
                        "description": "The string to search for",
                        "title": "Search String",
                        "type": "string"
                    }
                },
                "required": [
                    "search_string"
                ]
            }
        },
        {
            "name": "find_next",
            "description": "Locate the next instance of the search string on the current webpage. This tool returns the subsequent page content containing the search string, as identified by the latest 'find_on_page_ctrl_f' operation.",
            "inputSchema": {
                "type": "object",
                "properties": {}
            }
        },
        {
            "name": "go_back",
            "description": "Go back to the previous webpage in the browser. This tool will navigate the browser back to the last visited webpage and return the content of the previous page in Markdown format.",
            "inputSchema": {
                "type": "object",
                "properties": {}
            }
        },
        {
            "name": "search",
            "description": "Searches the web to retrieve information related to specific topics. The input is a list of queries, each representing a distinct aspect of the information needed. The tool performs web searches for all queries in parallel and returns relevant web pages for each, including the page title, URL, and a brief snippet summarizing its content.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "queries": {
                        "description": "List of search queries to execute",
                        "items": {
                            "type": "string"
                        },
                        "title": "Queries",
                        "type": "array"
                    }
                },
                "required": [
                    "queries"
                ]
            }
        },
        {
            "name": "python",
            "description": "Executes Python code strings. The code must be valid Python syntax and can include any standard library functions or operations. This tool is used to run Python scripts and return the output, including any errors or exceptions that occur during execution.",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "code": {
                        "description": "The python code to execute",
                        "title": "Code",
                        "type": "string"
                    }
                },
                "required": [
                    "code"
                ]
            }
        }
    ]
    
    print("开始测试 MCP 工具转换为 OpenAI 格式...")
    print("=" * 60)
    
    converted_tools = []
    
    for i, tool in enumerate(test_tools, 1):
        print(f"\n测试工具 {i}: {tool['name']}")
        print("-" * 40)
        
        # 转换工具
        try:
            openai_tool = convert_mcp_tool_to_openai_function(tool)
            converted_tools.append(openai_tool)
            
            print("✅ 转换成功!")
            print(f"原始工具名称: {tool['name']}")
            print(f"转换后工具名称: {openai_tool['function']['name']}")
            
            # 检查是否移除了 title 字段
            params_str = json.dumps(openai_tool['function']['parameters'], indent=2)
            if 'title' in params_str:
                print("❌ 警告: 参数中仍然包含 title 字段")
            else:
                print("✅ 成功移除所有 title 字段")
            
            # 显示转换后的结构
            print("转换后的工具结构:")
            print(json.dumps(openai_tool, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
            continue
    
    print("\n" + "=" * 60)
    print(f"测试完成! 成功转换 {len(converted_tools)}/{len(test_tools)} 个工具")
    
    # 保存转换结果到文件
    output_file = "converted_tools_output.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "original_tools_count": len(test_tools),
            "converted_tools_count": len(converted_tools),
            "converted_tools": converted_tools
        }, f, indent=2, ensure_ascii=False)
    
    print(f"转换结果已保存到: {output_file}")
    
    return converted_tools

if __name__ == "__main__":
    test_mcp_tools_conversion() 