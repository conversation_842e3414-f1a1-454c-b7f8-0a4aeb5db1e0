def contains_chinese_char(s):
    """
    检测字符串是否包含中文字符

    参数:
        s (str): 需要检测的字符串

    返回:
        bool: 如果包含中文字符返回True，否则返回False
    """
    for char in s:
        # 检查字符的Unicode编码是否在中文字符的范围内
        # 中文字符的Unicode范围主要在 \u4e00-\u9fff
        if '\u4e00' <= char <= '\u9fff':
            return True
    return False


# 测试代码
if __name__ == "__main__":
    test_strings = [
        "Hello world",
        "Hello 世界",
        "你好，世界！",
        "123456",
        "ABC中文DEF",
        ",，。"
    ]

    for string in test_strings:
        result = contains_chinese_char(string)
        print(f"字符串 \"{string}\" 包含中文字符: {result}")