"""
简化的连接监控模块
只负责记录连接事件和上报Prometheus指标
"""

import logging
import time
from enum import Enum
from typing import Optional

from open_webui.env import ENV
from open_webui.utils.metrics import (
    connection_disconnects_total,
    connection_timeout_total,
    connection_retry_total,
    connection_duration_seconds,
)

log = logging.getLogger(__name__)


class ConnectionType(Enum):
    """连接类型枚举"""
    OPENAI_API = "openai_api"
    OLLAMA_API = "ollama_api"
    ZHIPU_API = "zhipu_api"
    SSE_CLIENT = "sse_client"
    WEBSOCKET = "websocket"


class ErrorType(Enum):
    """错误类型枚举"""
    SERVER_DISCONNECTED = "server_disconnected"
    CONNECTION_TIMEOUT = "connection_timeout"
    NETWORK_ERROR = "network_error"
    SSL_ERROR = "ssl_error"
    DNS_ERROR = "dns_error"
    UNKNOWN_ERROR = "unknown_error"


class SimpleConnectionMonitor:
    """简化的连接监控器"""

    def __init__(self):
        # 简单的统计计数器
        self.stats = {
            'total_connections': 0,
            'total_disconnects': 0,
            'total_timeouts': 0,
            'total_retries': 0,
        }

    def record_connection_start(self,
                              connection_type: ConnectionType,
                              model_id: Optional[str] = None) -> str:
        """记录连接开始"""
        self.stats['total_connections'] += 1
        connection_id = f"{connection_type.value}_{model_id}_{time.time()}"
        log.debug(f"[SimpleConnectionMonitor] Connection started: {connection_id}")
        return connection_id

    def record_connection_end(self,
                            connection_id: str,
                            connection_type: ConnectionType,
                            model_id: Optional[str] = None,
                            duration: Optional[float] = None,
                            error_type: Optional[ErrorType] = None,
                            error_message: Optional[str] = None):
        """记录连接结束并上报指标"""

        # 更新Prometheus指标
        if error_type:
            connection_disconnects_total.labels(
                connection_type=connection_type.value,
                error_type=error_type.value,
                model_id=model_id or "unknown",
                env=ENV
            ).inc()

            self.stats['total_disconnects'] += 1
            log.warning(f"[SimpleConnectionMonitor] Connection error: {connection_type.value}, "
                       f"error: {error_type.value}, model: {model_id}")

        if duration:
            connection_duration_seconds.labels(
                connection_type=connection_type.value,
                model_id=model_id or "unknown",
                env=ENV
            ).observe(duration)

    def record_timeout(self,
                      connection_type: ConnectionType,
                      timeout_type: str,
                      model_id: Optional[str] = None):
        """记录超时事件"""
        connection_timeout_total.labels(
            connection_type=connection_type.value,
            timeout_type=timeout_type,
            model_id=model_id or "unknown",
            env=ENV
        ).inc()

        self.stats['total_timeouts'] += 1
        log.warning(f"[SimpleConnectionMonitor] Timeout: {connection_type.value}, "
                   f"type: {timeout_type}, model: {model_id}")

    def record_retry(self,
                    connection_type: ConnectionType,
                    retry_reason: str,
                    model_id: Optional[str] = None):
        """记录重试事件"""
        connection_retry_total.labels(
            connection_type=connection_type.value,
            retry_reason=retry_reason,
            model_id=model_id or "unknown",
            env=ENV
        ).inc()

        self.stats['total_retries'] += 1
        log.info(f"[SimpleConnectionMonitor] Retry: {connection_type.value}, "
                f"reason: {retry_reason}, model: {model_id}")

    def get_stats(self):
        """获取简单统计信息"""
        return self.stats.copy()


# 全局连接监控器实例
connection_monitor = SimpleConnectionMonitor()


def get_connection_monitor() -> SimpleConnectionMonitor:
    """获取全局连接监控器实例"""
    return connection_monitor
