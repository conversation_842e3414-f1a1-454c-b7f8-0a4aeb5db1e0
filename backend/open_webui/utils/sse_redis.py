import asyncio
import json
import logging
import time
from typing import Optional, AsyncGenerator, Dict, Any
import os
import redis.asyncio as redis
from redis.asyncio import Redis


log = logging.getLogger(__name__)


class SSERedisManager:
    """
    基于Redis的SSE消息分发管理器
    实现用户限流、消息pub/sub和状态管理
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_url = redis_url
        self.redis_client: Optional[Redis] = None
        self._connected = False
        
        # Redis Key模板
        # 改为按session区分的消息缓存
        self.CURRENT_MSG_KEY = "chat_current:{user_id}:{session_id}"
        # 改为用户活跃会话集合，存储session_id列表
        self.USER_SESSIONS_KEY = "user_sessions:{user_id}"
        # 会话详细信息，包含message_id, chat_id等
        self.SESSION_INFO_KEY = "session_info:{user_id}:{session_id}"
        # 改为按session区分的发布订阅频道
        self.CHANNEL_KEY = "chat_stream:{user_id}:{session_id}"
        
        # 配置参数
        self.MESSAGE_EXPIRE_TIME = 15 * 60  # 15分钟过期
        # 最后的清理等待订阅者时间
        self.MAX_WAIT_TIME = 3  # 最大等待时间（秒）
        self.CLEANUP_CHECK_INTERVAL = 1  # 清理检查间隔（秒）
        
        # 用户并发限制配置
        self.DEFAULT_USER_CONCURRENCY_LIMIT = int(os.getenv("USER_CONCURRENCY_LIMIT", "3"))
        
    async def _ensure_connected(self):
        """确保Redis连接"""
        if not self._connected or not self.redis_client:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            self._connected = True
            
    async def initialize(self):
        """初始化Redis连接"""
        await self._ensure_connected()
        
            
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            self._connected = False
            
    async def check_user_concurrency(self, user_id: str) -> tuple[bool, Optional[dict]]:
        """
        检查用户是否超过并发限制
        返回True表示可以继续，False表示有并发限制
        """
        
        if os.getenv("SKIP_USER_TASK_LIMIT_CHECK") == "true":
            return True, None
            
        await self._ensure_connected()
        user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
        
        # 获取用户当前活跃会话数量
        current_sessions_count = await self.redis_client.scard(user_sessions_key)
        
        if current_sessions_count >= self.DEFAULT_USER_CONCURRENCY_LIMIT:
            # 获取一些会话信息用于返回
            sessions = await self.redis_client.smembers(user_sessions_key)
            limit_info = {
                "current_sessions": current_sessions_count,
                "limit": self.DEFAULT_USER_CONCURRENCY_LIMIT,
                "active_sessions": list(sessions)[:3]  # 只返回前3个会话ID
            }
            return False, limit_info
            
        return True, None

        
    async def start_chat_session(self, user_id: str, message_id: str, chat_id: str) -> bool:
        """
        开始聊天会话，设置初始状态
        返回True表示成功开始，False表示已有进行中的会话超过限制
        """
        if not message_id:
            log.error(f"【pubsub】message_id 为空，{user_id}")
            raise ValueError("message_id 为空")
        if not chat_id:
            log.error(f"【pubsub】chat_id 为空，{user_id}")
            raise ValueError("chat_id 为空")
        await self._ensure_connected()
        
        # 检查并发
        can_continue, limit_value = await self.check_user_concurrency(user_id)
        if not can_continue:
            return False
        
        session_id = message_id
        
        user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
        session_info_key = self.SESSION_INFO_KEY.format(user_id=user_id, session_id=session_id)
        
        # 使用Redis事务确保原子性
        async with self.redis_client.pipeline(transaction=True) as pipe:
            # 添加会话到用户活跃会话集合
            pipe.sadd(user_sessions_key, session_id)
            # 设置会话过期时间
            pipe.expire(user_sessions_key, self.MESSAGE_EXPIRE_TIME)
            
            # 存储会话详细信息
            session_info = {
                "message_id": message_id,
                "chat_id": chat_id,
                "session_id": session_id,
                "timestamp": time.time()
            }
            pipe.setex(session_info_key, self.MESSAGE_EXPIRE_TIME, json.dumps(session_info))
            
            await pipe.execute()
        
        log.info(f"Started chat session for user {user_id}, session_id: {session_id}, message_id: {message_id}, chat_id: {chat_id}")
        return True
    
    async def delete_limit(self, user_id: str, session_id: str = None):
        """
        删除用户限流，在 completion 前面报错，还没开启 pubsub 的时候，直接删除限流 key 
        如果提供session_id，只删除特定会话；否则删除用户所有会话
        """
        await self._ensure_connected()
        
        if session_id:
            # 删除特定会话
            user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
            session_info_key = self.SESSION_INFO_KEY.format(user_id=user_id, session_id=session_id)
            
            async with self.redis_client.pipeline(transaction=True) as pipe:
                pipe.srem(user_sessions_key, session_id)
                pipe.delete(session_info_key)
                await pipe.execute()
        else:
            # 删除用户所有会话（兼容旧的调用方式）
            user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
            
            # 获取所有会话ID
            sessions = await self.redis_client.smembers(user_sessions_key)
            
            async with self.redis_client.pipeline(transaction=True) as pipe:
                # 删除会话集合
                pipe.delete(user_sessions_key)
                
                # 删除所有会话信息
                for session_id in sessions:
                    session_info_key = self.SESSION_INFO_KEY.format(user_id=user_id, session_id=session_id)
                    pipe.delete(session_info_key)
                    
                await pipe.execute()

    async def cache_current_message(self, user_id: str, session_id: str, full_chunk: str):
        """
        缓存当前消息
        """
        await self._ensure_connected()
        current_key = self.CURRENT_MSG_KEY.format(user_id=user_id, session_id=session_id)
        await self.redis_client.setex(current_key, self.MESSAGE_EXPIRE_TIME, full_chunk)
    
    
    # pub_sub 只发全量, 并且更新最后一个全量的 chunk，用来新连接进来的时候马上享受到效果。 这里传入的数据就是在 middleware 里 event_emitter 的 json.dumps 后
    async def update_and_publish_message(self, user_id: str, session_id: str, full_chunk: str):
        """
        更新当前消息并发布到频道【都是全量的】
        """
        await self._ensure_connected()
        
        channel = self.CHANNEL_KEY.format(user_id=user_id, session_id=session_id)
        
        await self.cache_current_message(user_id, session_id, full_chunk)
        

        # 发布消息到频道
        await self.redis_client.publish(channel, full_chunk)

        
    async def get_current_message(self, user_id: str, session_id: str) -> Optional[str]:
        """
        获取用户当前的消息状态
        """
        await self._ensure_connected()
        
        current_key = self.CURRENT_MSG_KEY.format(user_id=user_id, session_id=session_id)
        message_str = await self.redis_client.get(current_key)
        
        if message_str:
            try:
                return message_str
            except json.JSONDecodeError:
                log.error(f"Failed to decode message for user {user_id}, session {session_id}")
                return None
        return None
        
    async def subscribe_user_messages(self, user_id: str, session_id: str) -> AsyncGenerator[str, None]:
        """
        订阅用户消息流
        """
        await self._ensure_connected()
        
        channel = self.CHANNEL_KEY.format(user_id=user_id, session_id=session_id)
        pubsub = self.redis_client.pubsub()
        
        try:
            await pubsub.subscribe(channel)
            
            # 首先发送当前状态（如果存在）
            current_message = await self.get_current_message(user_id, session_id)
            if current_message:
                yield "data: " + current_message + "\n\n"
                
            # 监听实时消息
            async for message in pubsub.listen():
                if message["type"] == "message":
                    try:
                        if message["data"]:
                          yield "data: " + message["data"] + "\n\n"
                        else:
                          log.debug("【pubsub】没有消息了，结束订阅")
                          break

                            
                    except Exception as e:
                        log.error(f"Failed to decode published message for user {user_id}, session {session_id}: {e}")
                        break
                    except asyncio.CancelledError:
                        log.debug("【pubsub】订阅被取消 inner，结束订阅")
                        break
                        
        except Exception as e:
            log.error(f"【pubsub】Error in message subscription for user {user_id}, session {session_id}: {e}")
        except asyncio.CancelledError:
            log.debug("【pubsub】订阅被取消 outer，结束订阅")
        finally:
            await pubsub.unsubscribe(channel)
            await pubsub.close()
            
    async def get_channel_subscribers_count(self, user_id: str, session_id: str) -> int:
        """
        获取频道订阅者数量，最后后台任务的时候会等一下，如果不为 0 等 5s 强制关闭
        """
        await self._ensure_connected()
        
        channel = self.CHANNEL_KEY.format(user_id=user_id, session_id=session_id)
        result = await self.redis_client.pubsub_numsub(channel)
        
        # result是一个列表，格式为 [channel_name, subscriber_count]
        if result and len(result) >= 2:
            return result[1]
        return 0

    async def release_user_limit(self, user_id: str, session_id: str = None):
        """
        释放用户限流
        如果提供session_id，只释放特定会话；否则释放用户所有会话
        """
        await self.delete_limit(user_id, session_id)

    # 最终任务结束的时候，或者是 stopTask 接口调用的时候，会调用这个方法
    # 等待3s订阅者然后强制关闭
    async def wait_and_cleanup(self, user_id: str, session_id: str = None):
        """
        等待订阅者离开后清理会话
        如果提供session_id，只清理特定会话；否则清理用户所有会话
        """
        start_time = time.time()
        
        # 给 pubsub 发送一个 done 消息
        done_chunk_data = {
          "type": "chat:completion",
          "data": {
            "content": "",
            "phase": "done",
            "done": True
          }
        }
        done_chunk = json.dumps(done_chunk_data)
        
        # 给 pubsub 发送一个 done 消息
        if session_id:
            # 单个会话清理
            await self.redis_client.publish(self.CHANNEL_KEY.format(user_id=user_id, session_id=session_id), done_chunk)
            
            while time.time() - start_time < self.MAX_WAIT_TIME:
                subscriber_count = await self.get_channel_subscribers_count(user_id, session_id)
                
                if subscriber_count == 0:
                    break
                    
                await asyncio.sleep(self.CLEANUP_CHECK_INTERVAL)
                
            # 清理特定会话的 current message
            current_key = self.CURRENT_MSG_KEY.format(user_id=user_id, session_id=session_id)
            current_deleted = await self.redis_client.delete(current_key)
        else:
            # 兼容旧逻辑：清理用户所有会话
            user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
            sessions = await self.redis_client.smembers(user_sessions_key)
            
            # 给所有会话发送done消息
            for active_session_id in sessions:
                await self.redis_client.publish(self.CHANNEL_KEY.format(user_id=user_id, session_id=active_session_id), done_chunk)
            
            # 等待所有会话的订阅者离开
            while time.time() - start_time < self.MAX_WAIT_TIME:
                total_subscribers = 0
                for active_session_id in sessions:
                    subscriber_count = await self.get_channel_subscribers_count(user_id, active_session_id)
                    total_subscribers += subscriber_count
                
                if total_subscribers == 0:
                    break
                    
                await asyncio.sleep(self.CLEANUP_CHECK_INTERVAL)
                
            # 清理所有会话的 current message
            current_deleted = 0
            for active_session_id in sessions:
                current_key = self.CURRENT_MSG_KEY.format(user_id=user_id, session_id=active_session_id)
                deleted = await self.redis_client.delete(current_key)
                current_deleted += deleted
        
        # 清理会话限制
        if session_id:
            # 清理特定会话
            user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
            session_info_key = self.SESSION_INFO_KEY.format(user_id=user_id, session_id=session_id)
            
            async with self.redis_client.pipeline(transaction=True) as pipe:
                pipe.srem(user_sessions_key, session_id)
                pipe.delete(session_info_key)
                result = await pipe.execute()
                session_limit_deleted = result[0] + result[1]
        else:
            # 清理用户所有会话（兼容旧的调用方式）
            user_sessions_key = self.USER_SESSIONS_KEY.format(user_id=user_id)
            sessions = await self.redis_client.smembers(user_sessions_key)
            
            async with self.redis_client.pipeline(transaction=True) as pipe:
                pipe.delete(user_sessions_key)
                for session_id in sessions:
                    session_info_key = self.SESSION_INFO_KEY.format(user_id=user_id, session_id=session_id)
                    pipe.delete(session_info_key)
                result = await pipe.execute()
                session_limit_deleted = sum(result)
        
        log.debug(f"【pubsub】等待清理完成，{user_id}，清理了 {current_deleted} 个 current，{session_limit_deleted} 个 session")


