import ipaddress
import logging
from typing import List, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

log = logging.getLogger(__name__)


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """
    IP 白名单中间件，支持 CIDR 格式的 IP 地址验证
    """
    
    def __init__(self, app, whitelist: Optional[List[str]] = None, enabled: bool = True):
        """
        初始化 IP 白名单中间件
        
        Args:
            app: FastAPI 应用实例
            whitelist: IP 白名单列表，支持单个 IP 和 CIDR 格式
            enabled: 是否启用白名单检查
        """
        super().__init__(app)
        self.enabled = enabled
        self.whitelist_networks = []
        
        if whitelist and enabled:
            self._parse_whitelist(whitelist)
    
    def _parse_whitelist(self, whitelist: List[str]) -> None:
        """
        解析白名单，转换为网络对象列表
        
        Args:
            whitelist: IP 白名单列表
        """
        self.whitelist_networks = []
        
        for ip_or_cidr in whitelist:
            ip_or_cidr = ip_or_cidr.strip()
            if not ip_or_cidr:
                continue
                
            try:
                # 尝试解析为网络段（支持单个 IP 和 CIDR）
                network = ipaddress.ip_network(ip_or_cidr, strict=False)
                self.whitelist_networks.append(network)
                log.info(f"添加 IP 白名单规则: {network}")
            except ValueError as e:
                log.warning(f"无效的 IP 地址或 CIDR 格式: {ip_or_cidr}, 错误: {e}")
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端真实 IP 地址
        
        Args:
            request: FastAPI 请求对象
            
        Returns:
            客户端 IP 地址
        """
        # 优先从 X-Forwarded-For 头获取（适用于代理环境）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # X-Forwarded-For 可能包含多个 IP，取第一个
            return forwarded_for.split(",")[0].strip()
        
        # 从 X-Real-IP 头获取（Nginx 等代理服务器常用）
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 从连接信息获取
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _is_ip_allowed(self, client_ip: str) -> bool:
        """
        检查客户端 IP 是否在白名单中
        
        Args:
            client_ip: 客户端 IP 地址
            
        Returns:
            是否允许访问
        """
        if not self.whitelist_networks:
            # 如果白名单为空，允许所有访问
            return True
        
        try:
            client_ip_obj = ipaddress.ip_address(client_ip)
            
            for network in self.whitelist_networks:
                if client_ip_obj in network:
                    return True
            
            return False
        except ValueError:
            # 无法解析的 IP 地址，拒绝访问
            log.warning(f"无法解析客户端 IP 地址: {client_ip}")
            return False
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        中间件主要逻辑
        
        Args:
            request: FastAPI 请求对象
            call_next: 下一个中间件或路由处理器
            
        Returns:
            响应对象
        """
        # 如果未启用白名单检查，直接放行
        if not self.enabled:
            return await call_next(request)
        
        # 检查路径是否包含 oauth，如果包含则直接放行
        request_path = request.url.path.lower()
        if "oauth" in request_path:
            log.debug(f"OAuth 路由 {request.url} 跳过 IP 白名单检查")
            return await call_next(request)
        
        client_ip = self._get_client_ip(request)
        
        # 检查 IP 是否在白名单中
        if not self._is_ip_allowed(client_ip):
            log.warning(f"IP 地址 {client_ip} 不在白名单中，拒绝访问 {request.url}")
            return JSONResponse(
                status_code=403,
                content={
                    "detail": "访问被拒绝：您的 IP 地址不在允许的范围内",
                    "error": "IP_NOT_WHITELISTED"
                }
            )
        
        # IP 在白名单中，继续处理请求
        log.debug(f"IP 地址 {client_ip} 通过白名单检查")
        response = await call_next(request)
        return response


def create_ip_whitelist_middleware(whitelist: Optional[List[str]] = None, enabled: bool = True):
    """
    创建 IP 白名单中间件的工厂函数
    
    Args:
        whitelist: IP 白名单列表
        enabled: 是否启用白名单检查
        
    Returns:
        中间件类
    """
    def middleware_factory(app):
        return IPWhitelistMiddleware(app, whitelist=whitelist, enabled=enabled)
    
    return middleware_factory 