import time
import logging
from typing import Optional

from open_webui.env import ENV
from open_webui.utils.metrics import (
    converter_export_requests_total,
    converter_export_pages_total,
    converter_export_duration_seconds,
    converter_export_file_size_bytes,
)

log = logging.getLogger(__name__)


class ConverterMonitor:
    """Converter 导出监控器 - 简化版，只使用 Prometheus 指标"""

    def record_export_start(self, export_type: str, user_id: str, pages_count: int) -> str:
        """记录导出开始"""
        export_id = f"{export_type}_{user_id}_{int(time.time())}"

        # 记录页面数量
        converter_export_pages_total.labels(
            export_type=export_type,
            user_id=user_id,
            env=ENV
        ).inc(pages_count)

        log.info(f"[ConverterMonitor] Export started: {export_id}, type: {export_type}, pages: {pages_count}")
        return export_id

    def record_export_end(
        self,
        export_id: str,
        export_type: str,
        user_id: str,
        duration: float,
        status: str = "success",
        file_size: Optional[int] = None,
        error_message: Optional[str] = None
    ):
        """记录导出结束"""

        # 记录请求总数
        converter_export_requests_total.labels(
            export_type=export_type,
            status=status,
            user_id=user_id,
            env=ENV
        ).inc()

        # 记录导出耗时
        converter_export_duration_seconds.labels(
            export_type=export_type,
            env=ENV
        ).observe(duration)

        # 记录文件大小（如果有）
        if file_size is not None:
            converter_export_file_size_bytes.labels(
                export_type=export_type,
                env=ENV
            ).observe(file_size)

        if status != "success":
            log.warning(f"[ConverterMonitor] Export failed: {export_id}, error: {error_message}")
        else:
            log.info(f"[ConverterMonitor] Export completed: {export_id}, duration: {duration:.2f}s, size: {file_size or 'unknown'} bytes")


# 全局监控器实例
converter_monitor = ConverterMonitor()


def get_converter_monitor() -> ConverterMonitor:
    """获取全局 converter 监控器实例"""
    return converter_monitor
