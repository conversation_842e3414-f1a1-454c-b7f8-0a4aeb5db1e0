"""处理AI流式响应的辅助函数。

该模块提供了一些辅助函数，用于处理特定的任务，如参数解析等。
"""

import re
import ast
import logging
from typing import Dict, List, Any, Tuple, Optional

# 设置日志记录
log = logging.getLogger(__name__)

# 辅助函数：解析工具调用参数
def parse_tool_arguments(tool_name: str, args_str: str) -> <PERSON><PERSON>[Dict[str, Any], bool]:
    """
    解析工具调用的参数字符串为字典。
    
    Args:
        tool_name: 工具名称
        args_str: 参数字符串
        
    Returns:
        (参数字典, 解析是否成功)
    """
    arguments = {}
    
    try:
        # 无参数情况
        if not args_str:
            return {}, True
            
        # msearch工具特殊处理
        if tool_name == "msearch":
            desc_match = re.search(r'description\s*=\s*"([^"]*)"', args_str)
            queries_match = re.search(r'queries\s*=\s*\[(.*?)\]', args_str, re.DOTALL)

            arguments["description"] = desc_match.group(1) if desc_match else "执行搜索"
            arguments["queries"] = [] # 默认空列表

            if queries_match:
                content_inside_brackets = queries_match.group(1).strip()
                if content_inside_brackets:
                    # 解析查询项
                    parsed_queries = [q.strip().replace('"', '') for q in content_inside_brackets.split(',')]
                    arguments["queries"] = [q for q in parsed_queries if q and len(q) <= 300]
            
            return arguments, True
        
        # 通用参数解析
        try:
            # 使用ast.literal_eval解析Python字面量
            parsed_args = ast.literal_eval(f"dict({args_str})")
            if isinstance(parsed_args, dict):
                return parsed_args, True
            else:
                log.warning(f"解析参数非字典: {args_str}")
                return {"raw_args": args_str}, False
        except (ValueError, SyntaxError):
            log.warning(f"无法用literal_eval解析参数: {args_str}")
            return {"raw_args": args_str}, False
            
    except Exception as e:
        log.error(f"解析参数字符串出错 '{args_str}': {e}")
        return {"raw_args": args_str}, False

def process_tool_call(
    code_text: str,
    content_blocks: List[Dict[str, Any]]
) -> Tuple[bool, Dict[str, Any]]:
    """
    处理工具调用代码(code)。
    
    Args:
        code_text: 完整的代码文本
        content_blocks: 内容块列表
        
    Returns:
        (处理成功标志, 工具调用信息)
    """
    log.info(f"[ZERO HANDLER] Tool call code completed: {code_text}")
    tool_name = "unknown_tool"
    arguments = {}
    parsed_successfully = False

    try:
        # 尝试解析 函数名(参数字符串) 格式
        match = re.match(r'^\s*(\w+)\s*\((.*)\)\s*$', code_text.strip(), re.DOTALL)
        if match:
            tool_name = match.group(1)
            args_str = match.group(2).strip()

            # 使用辅助函数解析参数字符串
            arguments, parsed_successfully = parse_tool_arguments(tool_name, args_str)
            if not parsed_successfully:
                log.info(f"[ZERO HANDLER] Could not parse tool call format: {code_text}")
        else:
            # 正则不匹配，无法解析工具名和参数
            log.warning(f"[ZERO HANDLER] Could not parse tool call format: {code_text}")
            arguments = {"raw_code": code_text}
            # tool_name 保持 "unknown_tool"

        # --- 创建 tool_call 块 ---
        tool_call_block = {
            "type": "tool_call",
            "data": {
                "type": "tool_call",  # 重复的 type 字段
                "data": {
                    "name": tool_name,
                    "arguments": arguments,  # 使用解析出的字典或包含原始字符串的字典
                }
            }
        }

        # --- 寻找插入位置并插入/追加 ---
        last_tool_call_idx = -1
        for i in range(len(content_blocks) - 1, -1, -1):
            if isinstance(content_blocks[i], dict) and content_blocks[i].get("type") == "tool_call":
                last_tool_call_idx = i
                break  # 找到最后一个就停止

        if last_tool_call_idx != -1:
            # 插入到最后一个 tool_call 块之后
            content_blocks.insert(last_tool_call_idx + 1, tool_call_block)
        else:
            # 如果没有找到 tool_call 块，则追加到末尾
            content_blocks.append(tool_call_block)

        return True, {"name": tool_name, "arguments": arguments, "parsed_successfully": parsed_successfully}

    except Exception as e:
        log.error(f"[ZERO HANDLER] Failed processing tool code: {e} - Code: {code_text}", exc_info=True)
        # 回退：添加一个错误提示
        content_blocks.append({"type": "text", "content": f"Error processing tool call: {code_text}"})
        return False, {"error": str(e), "raw_code": code_text}

def process_quote_result(
    message: Dict[str, Any],
    content_blocks: List[Dict[str, Any]],
    citation_map: Dict[int, Dict[str, str]]
) -> Dict[int, Dict[str, str]]:
    """
    处理引用结果类型的内容（通常来自搜索工具）。
    
    Args:
        message: 完整消息数据
        content_blocks: 内容块列表
        citation_map: 引用映射字典，用于存储URL和标题
        
    Returns:
        (是否成功, 更新后的last_checked_tool_result_idx, 更新后的citation_map)
    """
    citations = message.get("metadata", {}).get("metadata_list", [])
    author_name = message.get("author", {}).get("name")
    tool_name = author_name if author_name else "search_tool"
    log.info(f"[ZERO HANDLER] 收到来自'{tool_name}'的工具结果: {len(citations)}个引用")

    # 查找已存在的tool_result块
    existing_tool_result_index = None
    for i, block in enumerate(content_blocks):
        if block["type"] == "tool_result":
            existing_tool_result_index = i
            break
    
    # 获取现有的处理结果或创建新列表
    if existing_tool_result_index is not None:
        processed_results_for_block = content_blocks[existing_tool_result_index]["data"]["data"]["result"]
    else:
        processed_results_for_block = []  # 用于tool_result显示块的结果

    # 处理引用列表
    if citations:
        for citation in citations:
            url = citation.get("url", "")
            idindex_str = citation.get("idindex")
            title = citation.get("title", "来源")  # 默认标题
            
            # 构建结果项
            result_item = {
                "title": title,
                "url": url,
                "media": citation.get("media", title),
                "type": citation.get("type", "webpage"),
                "query": citation.get("query", None),
                "text": citation.get("text", "")    
            }
            
            # 检查是否已存在相同URL的结果，避免重复
            exists = False
            for existing_item in processed_results_for_block:
                if existing_item.get("url") == url:
                    exists = True
                    break
            
            # 如果不存在，则添加
            if not exists:
                processed_results_for_block.append(result_item)
            
            # 更新citation_map用于后续引用替换
            if idindex_str is not None and url:
                try:
                    index = int(idindex_str)
                    if index not in citation_map:
                        citation_map[index] = {"url": url, "title": title}
                except ValueError:
                    log.warning(f"[ZERO HANDLER] 无效的idindex: {idindex_str}")

    # 创建或更新tool_result块
    tool_result_block = {
        "type": "tool_result",
        "data": {
            "type": "tool_result",  # 重复的type字段
            "data": {
                "name": "browser_multi_search",  # 使用固定值
                "result": processed_results_for_block  # 包含所有处理的citation列表
            }
        }
    }
    
    # 如果已有tool_result块，更新它；否则添加新块
    if existing_tool_result_index is not None:
        content_blocks[existing_tool_result_index] = tool_result_block
    else:
        content_blocks.append(tool_result_block)

    return citation_map