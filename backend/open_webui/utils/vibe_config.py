"""
Vibe配置工具模块

提供Vibe模板的初始化和管理功能
"""

import logging
from open_webui.models.vibe_templates import VibeTemplates, VibeTemplateForm

log = logging.getLogger(__name__)


def initialize_default_vibe_templates():
    """
    初始化默认的Vibe模板
    
    这个函数会检查数据库中是否已经存在默认模板，
    如果不存在则创建默认模板
    """
    try:
        log.info("Initializing default vibe templates...")
        VibeTemplates.create_default_templates()
        log.info("Default vibe templates initialized successfully")
        return True
    except Exception as e:
        log.error(f"Failed to initialize default vibe templates: {e}")
        return False


def get_template_by_vibe_mode(vibe_mode: str):
    """
    根据vibe模式获取对应的模板
    
    Args:
        vibe_mode: vibe模式，支持 "ppt", "artifacts", 或其他
        
    Returns:
        对应的模板，如果找不到则返回None
    """
    template_type_map = {
        "ppt": "ppt_edit",
        "artifacts": "artifacts_edit"
    }
    
    template_type = template_type_map.get(vibe_mode, "html_edit")
    return VibeTemplates.get_active_template_by_type(template_type)


def create_custom_template(template_data: dict):
    """
    创建自定义模板
    
    Args:
        template_data: 模板数据字典
        
    Returns:
        创建的模板对象，失败返回None
    """
    try:
        form_data = VibeTemplateForm(**template_data)
        return VibeTemplates.insert_new_template(form_data)
    except Exception as e:
        log.error(f"Failed to create custom template: {e}")
        return None 