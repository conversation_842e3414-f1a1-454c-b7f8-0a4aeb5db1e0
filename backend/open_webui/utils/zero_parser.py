
from enum import Enum

class EventType(Enum):
    THINKING_START = "thinking"
    THINKING_END = "thinking_end"


class ZeroParser:
    """
    Zero 模型是先搜索，再思考，再说话
    """

    def __init__(self):
        self.buffer = ""
        self.state = None

    def read(self, token: str):
        """流式解析"""
        self.buffer += token

        if self.state is None:
            idx = self.buffer.find("<think>")
            if idx != -1:
                if self.buffer[:idx]:
                    yield {"type": "content", "content": self.buffer[:idx]}
                self.state = "thinking"
                yield {"type": "state", "content": "thinking_start"}
                self.buffer = self.buffer[idx + len("<think>"):]
                if self.buffer:
                    yield from self.read("")
        elif self.state == "thinking":
            idx = self.buffer.find("</think>")
            if idx != -1:
                self.state = "speaking"
                content, self.buffer = self.buffer[:idx], self.buffer[idx + len("</think>"):]
                yield {"type": "think", "content": content}

        return self.buffer

    def parse(self, content: str):
        """完整解析"""
        pass
