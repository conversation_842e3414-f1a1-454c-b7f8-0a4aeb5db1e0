import aiohttp
import logging
import sys

from open_webui.env import (
    SRC_LOG_LEVELS,
    GLOBAL_LOG_LEVEL,
    ENV
)

logging.basicConfig(stream=sys.stdout, level=GLOBAL_LOG_LEVEL)
log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MAIN"])

HOST = "http://************:16001" if ENV == "dev" else "http://************:16001"

async def check_sensitive(text: str, input_type: str, sub_source: str = None, check_type: str = "default_text", request_id: str = None, user_id: str = None, chat_id: str = None, client_id: str = None) -> dict:
    payload = {
        "input_type": input_type,
        "check_type": check_type,
        "data": text,
        "source": "z",
        "sub_source": sub_source,
        "request_id": request_id,
        "user_id": user_id,
        "session_id": chat_id,
        "client_id": client_id,
        "dfa_intervene": "on",
        "third_intervene": "on",
        "knowledge_intervene": "off"
    }
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{HOST}/sensitive", json=payload) as response:
                response.raise_for_status()
                res = await response.json()
                return res
    except Exception as e:
        log.error(f"安全审核接口异常 {str(e)}")
        return {"is_sensitive": False}
