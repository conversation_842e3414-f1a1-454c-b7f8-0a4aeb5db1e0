import re

def reverse_markdown_citations_to_source(text):
    """
    将Markdown格式的引用链接 [[数字](URL)] 转换回 【数字†source】 格式
    
    参数:
        text (str): 包含Markdown格式引用链接的文本
    
    返回:
        str: 转换后的文本，其中所有Markdown格式的引用链接被替换为原始的n+source格式
    """
    # 定义正则表达式，匹配 [[数字](URL)] 格式
    pattern = r'\[\[(\d+)\]\((?:[^)]+)\)\]'
    
    # 内部替换函数，供 re.sub 调用
    def repl_func(match):
        try:
            # 提取匹配到的数字（引用索引）
            index = match.group(1)
            # 转换回原始格式
            return f'【{index}†source】'
        except (ValueError, IndexError):
            # 转换出错，返回原始文本
            return match.group(0)
    
    # 执行替换并返回结果
    return re.sub(pattern, repl_func, text)


# 测试代码
if __name__ == "__main__":
    # 测试用例1: 基本转换
    test_text_1 = "这是一个引用 [[1](https://example.com)] 在文本中。"
    expected_1 = "这是一个引用 【1†source】 在文本中。"
    result_1 = reverse_markdown_citations_to_source(test_text_1)
    print(f"测试1: {'通过' if result_1 == expected_1 else '失败'}")
    print(f"  输入: {test_text_1}")
    print(f"  输出: {result_1}")
    print(f"  期望: {expected_1}")
    
    # 测试用例2: 多个引用
    test_text_2 = "文本中的第一个引用 [[1](https://example.com)] 和第二个引用 [[2](https://example.org)]。"
    expected_2 = "文本中的第一个引用 【1†source】 和第二个引用 【2†source】。"
    result_2 = reverse_markdown_citations_to_source(test_text_2)
    print(f"测试2: {'通过' if result_2 == expected_2 else '失败'}")
    print(f"  输入: {test_text_2}")
    print(f"  输出: {result_2}")
    print(f"  期望: {expected_2}")
    
    # 测试用例3: 无引用
    test_text_3 = "这个文本中没有引用。"
    expected_3 = "这个文本中没有引用。"
    result_3 = reverse_markdown_citations_to_source(test_text_3)
    print(f"测试3: {'通过' if result_3 == expected_3 else '失败'}")
    print(f"  输入: {test_text_3}")
    print(f"  输出: {result_3}")
    print(f"  期望: {expected_3}")
    
    # 测试用例4: 多位数引用
    test_text_4 = "这是引用 [[123](https://example.com)] 在文本中。"
    expected_4 = "这是引用 【123†source】 在文本中。"
    result_4 = reverse_markdown_citations_to_source(test_text_4)
    print(f"测试4: {'通过' if result_4 == expected_4 else '失败'}")
    print(f"  输入: {test_text_4}")
    print(f"  输出: {result_4}")
    print(f"  期望: {expected_4}")
    
    # 测试用例5: 各种URL格式
    test_text_5 = "复杂URL [[1](https://example.com?param=value&query=123#section)] 测试。"
    expected_5 = "复杂URL 【1†source】 测试。"
    result_5 = reverse_markdown_citations_to_source(test_text_5)
    print(f"测试5: {'通过' if result_5 == expected_5 else '失败'}")
    print(f"  输入: {test_text_5}")
    print(f"  输出: {result_5}")
    print(f"  期望: {expected_5}")