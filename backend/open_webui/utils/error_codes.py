# error_codes.py
import json
from typing import Optional, Dict, Any

# 定义错误码映射表
# 结构: {码值: {"meaning": "含义", "retryable": 是否可重试}}
ERROR_MAP: Dict[int, Dict[str, Any]] = {
    # 通用错误
    10001: {"meaning": "Internal system exception", "retryable": True},
    10002: {"meaning": "Request timeout", "retryable": True},
    10003: {"meaning": "Missing configuration", "retryable": False},
    10004: {"meaning": "Rate limit triggered", "retryable": True},
    # 参数错误
    11001: {"meaning": "Missing parameter error", "retryable": False},
    11002: {"meaning": "Parameter type error", "retryable": False},
    11003: {"meaning": "Parameter format error", "retryable": False},
    # 模型错误
    12001: {"meaning": "Model request exception", "retryable": True},
    12002: {"meaning": "Model request timeout", "retryable": True},
    12003: {"meaning": "Token too long", "retryable": False},
    12004: {"meaning": "Model loop output, truncated by all-tools", "retryable": True}, # 注意：这里根据描述设为可重试，但需确认实际场景
    # Tools错误
    13001: {"meaning": "Tools request exception", "retryable": True},
    13002: {"meaning": "Tools request timeout", "retryable": True},
    13003: {"meaning": "Sandbox execution file missing", "retryable": False},
    # 敏感错误 (示例中是 41001，这里补充到映射中)
    14001: {"meaning": "Sandbox code execution pre-check failed", "retryable": False},
    41001: {"meaning": "Input text sensitive word check failed", "retryable": False}, # 根据示例补充
    # 可以根据需要添加更多错误码...
}

# 自定义异常类
class StreamProcessingError(Exception):
    """在流处理中捕获到的特定错误"""
    def __init__(self, code: int, error_message: str, message: Optional[str] = None):
        self.code = code
        self.raw_error_message = error_message # 原始的 error 字段内容 (可能是 JSON 字符串)
        self.message = message # 顶层的 message 字段 (通常为 null)
        self.details = self._parse_error_details() # 尝试解析 error_message 中的 JSON
        self.meaning = ERROR_MAP.get(code, {}).get("meaning", "未知错误")
        self.retryable = ERROR_MAP.get(code, {}).get("retryable", False) # 默认不可重试

        super().__init__(f"Error Code: {self.code}, Meaning: {self.meaning}, Details: {self.raw_error_message}")

    def _parse_error_details(self) -> Optional[Dict[str, Any]]:
        """尝试将 raw_error_message 解析为字典"""
        try:
            if isinstance(self.raw_error_message, dict):
                return self.raw_error_message
            # 尝试解析 JSON 字符串
            if isinstance(self.raw_error_message, str):
                return json.loads(self.raw_error_message)
        except json.JSONDecodeError:
            # 解析失败，返回 None
            return None
        return None # 其他类型也返回 None

    def __str__(self) -> str:
        return f"StreamProcessingError(code={self.code}, meaning='{self.meaning}', retryable={self.retryable}, details='{self.raw_error_message}')"

    def __repr__(self) -> str:
        return self.__str__()

# 辅助函数（可选，方便获取信息）
def get_error_meaning(code: int) -> str:
    """根据错误码获取详细信息"""
    return ERROR_MAP.get(code, {}).get("meaning", "未知错误")

def get_error_details(code: int) -> Dict[str, Any]:
    """根据错误码获取详细信息"""
    return ERROR_MAP.get(code, {})