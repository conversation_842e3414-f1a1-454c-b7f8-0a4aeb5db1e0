"""
Redis Manager for handling PubSub operations
处理 Redis PubSub 操作的管理器
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, Callable, Optional, Any
from dataclasses import dataclass

import redis.asyncio as redis

from open_webui.tasks import stop_task
from open_webui.env import BACKGROUND_TASK_REDIS_URL

logger = logging.getLogger(__name__)


@dataclass
class RedisConfig:
    """Redis 配置"""
    url: str = "redis://localhost:6379"
    decode_responses: bool = True
    max_connections: int = 10
    retry_on_timeout: bool = True
    socket_keepalive: bool = True
    socket_keepalive_options: dict = None
    
    def __post_init__(self):
        if self.socket_keepalive_options is None:
            self.socket_keepalive_options = {}


class RedisManager:
    """Redis 管理器，处理连接、PubSub 等操作"""

    def __init__(self, config: RedisConfig, channel: str):
        self.config = config
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None
        self.pubsub_task: Optional[asyncio.Task] = None
        self.message_handlers: Dict[str, Callable] = {}
        self.is_running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 1  # 秒
        self.channel = channel
        
    async def initialize(self) -> bool:
        """初始化 Redis 连接"""
        try:
            self.redis_client = redis.from_url(
                self.config.url,
                decode_responses=self.config.decode_responses,
                max_connections=self.config.max_connections,
                retry_on_timeout=self.config.retry_on_timeout,
                socket_keepalive=self.config.socket_keepalive,
                socket_keepalive_options=self.config.socket_keepalive_options
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis 连接初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Redis 连接初始化失败: {e}")
            return False
    
    async def start_pubsub(self) -> bool:
        """启动 PubSub 订阅"""
        if not self.redis_client:
            logger.error("Redis 客户端未初始化")
            return False
            
        try:
            self.pubsub = self.redis_client.pubsub()
  
            await self.pubsub.subscribe(self.channel)
            logger.info(f"订阅频道: {self.channel}")
            
            # 启动消息监听任务
            self.pubsub_task = asyncio.create_task(self._listen_messages())
            self.is_running = True
            logger.info("PubSub 启动成功")
            return True
            
        except Exception as e:
            logger.error(f"PubSub 启动失败: {e}")
            return False
    
    async def stop(self):
        """停止 Redis 管理器"""
        self.is_running = False
        
        # 停止 PubSub 任务
        if self.pubsub_task and not self.pubsub_task.done():
            self.pubsub_task.cancel()
            try:
                await self.pubsub_task
            except asyncio.CancelledError:
                logger.info("PubSub 任务已取消")
        
        # 关闭 PubSub 连接
        if self.pubsub:
            try:
                await self.pubsub.aclose()
                logger.info("PubSub 连接已关闭")
            except Exception as e:
                logger.error(f"关闭 PubSub 连接失败: {e}")
        
        # 关闭 Redis 连接
        if self.redis_client:
            try:
                await self.redis_client.aclose()
                logger.info("Redis 连接已关闭")
            except Exception as e:
                logger.error(f"关闭 Redis 连接失败: {e}")
    
    def register_handler(self,  handler: Callable[[dict], None]):
        """注册消息处理器"""
        self.message_handlers[self.channel] = handler
        logger.info(f"为频道 {self.channel} 注册了消息处理器")
    
    async def publish(self,  message: dict) -> bool:
        """发布消息到指定频道"""
        if not self.redis_client:
            logger.error("Redis 客户端未初始化")
            return False
        
        try:
            # 添加时间戳
            message_with_timestamp = {
                **message,
                "timestamp": time.time(),
                "sender_id": id(self)  # 简单的发送者标识
            }
            
            message_json = json.dumps(message_with_timestamp)
            await self.redis_client.publish(self.channel, message_json)
            logger.debug(f"消息已发布到频道 {self.channel}: {message}")
            return True
            
        except Exception as e:
            logger.error(f"发布消息失败: {e}")
            return False
    
    async def _listen_messages(self):
        """监听 PubSub 消息的内部方法"""
        logger.info("开始监听 PubSub 消息")
        
        while self.is_running:
            try:
                if not self.pubsub:
                    logger.warning("PubSub 连接不存在，尝试重连")
                    await self._reconnect()
                    continue
                
                # 设置超时以便定期检查 is_running 状态
                message = await asyncio.wait_for(
                    self.pubsub.get_message(), timeout=1.0
                )
                
                if message and message["type"] == "message":
                    await self._handle_message(message)
                    
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
                
            except Exception as e:
                logger.error(f"监听消息时发生错误: {e}")
                if self.is_running:
                    await self._reconnect()
                    await asyncio.sleep(self.reconnect_delay)
        
        logger.info("PubSub 消息监听已停止")
    
    async def _handle_message(self, message):
        """处理接收到的消息"""
        try:
            channel = message["channel"]
            data = json.loads(message["data"])
            
            # 检查是否是自己发送的消息（避免处理自己的消息）
            # sender_id = data.get("sender_id")
            # if sender_id == id(self):
            #     logger.debug(f"忽略自己发送的消息: {channel}")
            #     return
            
            logger.debug(f"收到消息 - 频道: {channel}, 数据: {data}")
            
            # 调用注册的处理器
            if channel in self.message_handlers:
                handler = self.message_handlers[channel]
                if asyncio.iscoroutinefunction(handler):
                    await handler(data)
                else:
                    handler(data)
            else:
                logger.warning(f"频道 {channel} 没有注册处理器")
                
        except json.JSONDecodeError as e:
            logger.error(f"解析消息 JSON 失败: {e}")
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
    
    async def _reconnect(self):
        """重连逻辑"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"超过最大重连次数 ({self.max_reconnect_attempts})，停止重连")
            self.is_running = False
            return
        
        self.reconnect_attempts += 1
        logger.info(f"尝试重连 Redis (第 {self.reconnect_attempts} 次)")
        
        try:
            # 重新初始化连接
            success = await self.initialize()
            if success:
                # 重新订阅频道
                channels = list(self.message_handlers.keys())
                if channels:
                    await self.start_pubsub(channels)
                    self.reconnect_attempts = 0  # 重置重连计数
                    logger.info("Redis 重连成功")
            else:
                await asyncio.sleep(self.reconnect_delay)
                
        except Exception as e:
            logger.error(f"重连失败: {e}")
            await asyncio.sleep(self.reconnect_delay)
    
    async def health_check(self) -> dict:
        """健康检查"""
        try:
            if not self.redis_client:
                return {"status": "unhealthy", "error": "Redis client not initialized"}
            
            await self.redis_client.ping()
            return {
                "status": "healthy",
                "redis_connected": True,
                "pubsub_running": self.is_running,
                "reconnect_attempts": self.reconnect_attempts
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "redis_connected": False,
                "error": str(e)
            }
    
    async def get_client(self) -> Optional[redis.Redis]:
        """获取 Redis 客户端实例（用于其他 Redis 操作）"""
        return self.redis_client



async def handle_task_operation(message: dict):
    """处理任务操作消息的回调函数"""
    try:
        operation = message.get("operation")
        task_id = message.get("task_id")
        
        logger.info(f"[stop_task]收到任务操作: {operation}, 任务ID: {task_id}")
        
        if operation == "stop" and task_id:
            await stop_task(task_id)
            logger.info(f"[stop_task]任务 {task_id} 已停止")
    except Exception as e:
        logger.error(f"处理任务操作失败: {e}")


async def initialize_stop_task_manager():
    """初始化 Redis 管理器"""
    config = RedisConfig(url=BACKGROUND_TASK_REDIS_URL)
    TASK_CHANNEL = os.getenv("STOP_TASK_CHANNEL","z-ai-stop-task-channel")
    redis_manager = RedisManager(config, TASK_CHANNEL)
    # 初始化连接
    success = await redis_manager.initialize()
    if not success:
        raise RuntimeError("无法连接到 Redis")
    
    # 注册消息处理器
    redis_manager.register_handler(handle_task_operation)
    
    # 启动 PubSub 订阅
    success = await redis_manager.start_pubsub()
    if not success:
        raise RuntimeError("无法启动 Redis PubSub")
    
    logger.info("[stop_task]RedisManager 初始化完成")
    return redis_manager

# 便利函数，用于任务操作的消息格式化
class TaskOperationMessage:
    """任务操作消息工具类"""
    
    @staticmethod
    def stop_task(task_id: str) -> dict:
        """创建停止任务消息"""
        return {
            "operation": "stop",
            "task_id": task_id
        }
    