import json
import logging
import uuid
import time
from typing import Any, Dict, Optional
logger = logging.getLogger(__name__)
from redis import asyncio as aioredis
from open_webui.config import WEBUI_URL
def get_timestamp_unix_ms():
    return int(time.time() * 1000)

async def get_async_redis_connection(redis_url: str, redis_sentinels=None, decode_responses=True):
    """
    获取异步Redis连接
    
    Args:
        redis_url: Redis连接URL
        redis_sentinels: Redis Sentinel节点列表(可选)
        decode_responses: 是否解码响应
        
    Returns:
        异步Redis连接
    """
    from .redis import parse_redis_sentinel_url
    
    if redis_sentinels:
        redis_config = parse_redis_sentinel_url(redis_url)
        sentinel = aioredis.sentinel.Sentinel(
            redis_sentinels,
            port=redis_config["port"],
            db=redis_config["db"],
            username=redis_config["username"],
            password=redis_config["password"],
            decode_responses=decode_responses,
        )
        # 获取主节点连接
        return await sentinel.master_for(redis_config["service"])
    else:
        # 标准Redis连接
        return await aioredis.from_url(redis_url, decode_responses=decode_responses)


class AsyncRedisStreamMQ:
    """基于Redis Stream的异步消息队列实现"""
    
    def __init__(self, redis_url: str, redis_sentinels=None, ins_prefix: str = "zai:"):
        """
        初始化Redis Stream异步消息队列
        
        Args:
            redis_url: Redis连接URL
            redis_sentinels: Redis Sentinel节点列表(可选)
            stream_prefix: Stream名称前缀
        """
        self.redis_url = redis_url
        self.redis_sentinels = redis_sentinels
        self.stream_prefix = ins_prefix + "stream:"
        self.instance_id = str(uuid.uuid4())
        self.redis = None
        self.redis_normal_prefix = ins_prefix + "normal:"
        logger.info(f"AsyncRedisStreamMQ initialized with instance_id: {self.instance_id}")
    
    async def initialize(self):
        """初始化Redis连接"""
        if self.redis is None:
            self.redis = await get_async_redis_connection(self.redis_url, self.redis_sentinels)
    
    async def set_key_with_ttl(self, key: str, value: Any, ttl: int):
        await self.redis.set(self.redis_normal_prefix + key, value, ex=ttl)
    
    async def get_key(self, key: str) -> Any:
        return await self.redis.get(self.redis_normal_prefix + key)
    
    async def delete_key(self, key: str):
        await self.redis.delete(self.redis_normal_prefix + key)
    
    def get_stream_name(self, topic: str) -> str:
        """
        获取完整的Stream名称
        
        Args:
            topic: 消息主题
        
        Returns:
            完整的Stream名称
        """
        return f"{self.stream_prefix}{topic}"
    
    async def send_message(self, topic: str, message: Dict[str, Any], msg_id: Optional[str] = None) -> str:
        """
        异步发送消息到指定主题的Stream
        
        Args:
            topic: 消息主题
            message: 消息内容(字典)
            msg_id: 自定义消息ID (可选)
        
        Returns:
            消息ID
        """
        await self.initialize()
        stream_name = self.get_stream_name(topic)
        
        # 添加元数据
        message_with_meta = message.copy()
        message_with_meta["_sender_instance"] = self.instance_id
        time_result = await self.redis.time()
        message_with_meta["_timestamp"] = time_result[0]  # Redis服务器时间
        
        # 序列化消息内容
        fields = {
            "data": json.dumps(message_with_meta)
        }
        
        try:
            # 使用'*'让Redis自动生成ID，或使用提供的msg_id
            message_id = await self.redis.xadd(
                stream_name, 
                fields,
                id=msg_id or '*',
                maxlen=10000  # 限制Stream最大长度，可根据需要调整
            )
            logger.debug(f"Message sent to {stream_name}, id: {message_id}, data: {message}")
            return message_id
        except Exception as e:
            logger.error(f"Failed to send message to {stream_name}: {e}")
            raise


    async def get_stream_info(self, topic: str) -> dict:
        """
        异步获取Stream信息
        
        Args:
            topic: 消息主题
        
        Returns:
            Stream信息字典
        """
        await self.initialize()
        stream_name = self.get_stream_name(topic)
        try:
            info = await self.redis.xinfo_stream(stream_name)
            return dict(info)
        except Exception as e:
            logger.error(f"Failed to get stream info for {stream_name}: {e}")
            return {}
    
        
    def get_email_verify_token_key(self, email: str) -> str:
        return f"email_verify_token:{email}"
    
    def get_password_reset_token_key(self, email: str) -> str:
        return f"password_reset_token:{email}"
    
    async def send_welcome_email_to_mq(self, email:str, username: str, language: str, sso_redirect: str = None):
        verify_link = f"{WEBUI_URL}/auth"
        if sso_redirect:
            verify_link += f"?sso_redirect={sso_redirect}"
        await self.send_message("email_verify", {
            "email": email,
            "username": username,
            "language": language,
            "event": "welcome",
            "verify_link": verify_link
        })
        logger.info(f"Welcome email sent to mq, email: {email}, username: {username}, language: {language}")

    # 发送密码重置邮件
    async def send_password_reset_to_mq(self, email: str, language: str, username: str):
        token = str(uuid.uuid4())
        reset_link = f"{WEBUI_URL}/auth/reset-password?token={token}&email={email}&language={language}&username={username}"
        logger.info(f"Password reset token: {token}, email: {email}, language: {language}, reset_link: {reset_link}")
        # 30分钟过期
        await self.set_key_with_ttl(self.get_password_reset_token_key(email), token, 30 * 60)
        await self.send_message("email_verify", {
            "email": email,
            "username": username,
            "token": token,
            "timestamp": get_timestamp_unix_ms(),
            "reset_link": reset_link,
            "language": language,
            "event": "password_reset"
        })
        logger.info(f"Password reset message sent to mq, email: {email}, token: {token}, language: {language}, reset_link: {reset_link}")

    # language: zh or en
    async def send_email_verify_to_mq(self, email:str, username: str, language: str, sso_redirect: str = None):
        token = str(uuid.uuid4())
        verify_link  = f"{WEBUI_URL}/auth/verify_email?token={token}&email={email}&username={username}&language={language}"
        if sso_redirect:
            verify_link += f"&sso_redirect={sso_redirect}"
        logger.info(f"Email verify token: {token}, email: {email}, username: {username}, language: {language}, verify_link: {verify_link}")
        # 24小时后过期
        await self.set_key_with_ttl(self.get_email_verify_token_key(email), token, 60 * 60 * 24 )
        await self.send_message("email_verify", {
            "email": email,
            "username": username,
            "token": token,
            "timestamp": get_timestamp_unix_ms(),
            "verify_link": verify_link,
            "language": language,
            "event": "verify"
        })
        logger.info(f"Email verify message sent to mq, email: {email}, username: {username}, token: {token}, language: {language}, verify_link: {verify_link}")

    async def mark_token_expired(self, email: str):
        await self.delete_key(self.get_email_verify_token_key(email))
        logger.info(f"Email verify expired, email: {email}")
        
    async def verify_email_token(self, email: str, token: str):
        token_key = self.get_email_verify_token_key(email)
        token_value = await self.get_key(token_key)
        if token_value and token_value == token:
            return True
        return False

    async def verify_password_reset_token(self, email: str, token: str):
        token_key = self.get_password_reset_token_key(email)
        token_value = await self.get_key(token_key)
        if token_value and token_value == token:
            return True
        return False

    async def mark_password_reset_token_expired(self, email: str):
        await self.delete_key(self.get_password_reset_token_key(email))
        logger.info(f"Password reset token expired, email: {email}")
        