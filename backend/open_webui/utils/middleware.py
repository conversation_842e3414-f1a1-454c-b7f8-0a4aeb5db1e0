import time
import logging
import sys

import asyncio
from aiohttp.client_exceptions import ClientConnectionError
from typing import Any, Dict, Literal, Optional, List  # 添加 Dict, List 类型导入
import json
import html
import ast
import hashlib
import traceback

from uuid import uuid4
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor


from fastapi import Request, HTTPException
from starlette.responses import Response, StreamingResponse

from open_webui.storage.provider import get_image_storage
from open_webui.storage.oss_provider import OSSStorageProvider
from open_webui.utils.diff_str import get_edit_index
from open_webui.utils.sse_redis import SSERedisManager
from open_webui.utils.stream_filter import StreamFilter
from open_webui.utils.tag_stream_parser import TagStreamParser  # 添加导入
from open_webui.models.chats import Chats
from open_webui.models.users import Users
from open_webui.socket.main import (
    get_event_call,
    get_event_emitter,
    get_active_status_by_user_id,
)
from open_webui.routers.tasks import (
    generate_queries,
    generate_title,
    generate_chat_tags,
)
from open_webui.routers.pipelines import (
    process_pipeline_inlet_filter,
    process_pipeline_outlet_filter,
)
from open_webui.mcp.tools import process_mcp_tools, handle_mcp_response_tool_calls
from open_webui.mcp.ui import serialize_mcp_tool_calls

from open_webui.utils.webhook import post_webhook


from open_webui.models.users import UserModel
from open_webui.models.functions import Functions
from open_webui.models.models import Models

from open_webui.retrieval.utils import get_sources_from_files


from open_webui.utils.chat import generate_chat_completion, chat_completed
from open_webui.utils.task import (
    get_task_model_id,
    rag_template,
)
from open_webui.utils.misc import (
    deep_update,
    get_message_list,
    add_or_update_user_message,
    get_last_user_message,
    get_last_assistant_message,
    prepend_to_first_user_message_content,
    convert_logit_bias_input_to_json,
)
from open_webui.utils.sensitive import check_sensitive
from open_webui.utils.ppt import connect as connect_ppt_server, Writer as PPTWriter
from open_webui.utils.tools import get_tools
from open_webui.utils.plugin import load_function_module_by_id
from open_webui.utils.filter import (
    get_sorted_filter_ids,
    process_filter_functions,
)
from open_webui.utils.lang import is_zh_preferred

from open_webui.tasks import create_task


from open_webui.env import (
    SRC_LOG_LEVELS,
    GLOBAL_LOG_LEVEL,
    BYPASS_MODEL_ACCESS_CONTROL,
    ENABLE_REALTIME_CHAT_SAVE,
    ENV
)
from open_webui.constants import TASKS
from open_webui.mcp.mcp import cleanup_mcp_servers

from open_webui.utils.error_codes import StreamProcessingError, get_error_meaning # 导入自定义异常和辅助函数

# 导入新的helper模块
from open_webui.utils.stream_handler_helpers import (
    process_quote_result,
    process_tool_call,
)

from open_webui.utils.metrics import (
    chat_requests_total,
    CHAT_STREAM_SEND_TOKEN_PACK,
    sse_heartbeat_sent_total,
    sse_concurrent_connections,
)
from open_webui.utils.connection_monitor import (
    get_connection_monitor,
    ConnectionType,
    ErrorType,
)

from open_webui.utils.queue import AsyncQueue

# 导入拆分的处理器模块
from open_webui.utils.handlers.tools_handler import chat_completion_tools_handler
from open_webui.utils.handlers.web_search_handler import chat_web_search_handler
from open_webui.utils.handlers.image_generation_handler import chat_image_generation_handler
from open_webui.utils.handlers.files_handler import chat_completion_files_handler
from open_webui.utils.handlers.zero_stream_handler import stream_body_handler_zero
from open_webui.utils.handlers.metrics_handler import (
    create_metrics_tracker,
    should_record_first_token,
    extract_completion_tokens_from_message,
)

logging.basicConfig(stream=sys.stdout, level=GLOBAL_LOG_LEVEL)
log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MAIN"])



async def handle_stream_error(error_data, metadata, event_emitter):
    """处理流中错误的辅助函数，当 data 中存在 code 和 error 时调用此函数。"""
    error_code = error_data["code"]
    error_msg = error_data["error"]
    log.error(f"[ZERO HANDLER] Detected error in stream: Code={error_code}, Error='{error_msg}'")
    Chats.upsert_message_to_chat_by_id_and_message_id(
        metadata["chat_id"],
        metadata["message_id"],
        {"error": {"content": get_error_meaning(error_code)}}
    )
    await event_emitter({
        "type": "chat:completion",
        "data": {
            "content": "",
            "error": {"detail": get_error_meaning(error_code)},
            "message_id": metadata.get("message_id"),
            "done": True
        }
    })
    return True










def apply_params_to_form_data(form_data, model):
    params = form_data.pop("params", {})
    if model.get("ollama"):
        form_data["options"] = params

        if "format" in params:
            form_data["format"] = params["format"]

        if "keep_alive" in params:
            form_data["keep_alive"] = params["keep_alive"]
    else:
        if "seed" in params and params["seed"] is not None:
            form_data["seed"] = params["seed"]

        if "stop" in params and params["stop"] is not None:
            form_data["stop"] = params["stop"]

        if "temperature" in params and params["temperature"] is not None:
            form_data["temperature"] = params["temperature"]

        if "max_tokens" in params and params["max_tokens"] is not None:
            form_data["max_tokens"] = params["max_tokens"]

        if "top_p" in params and params["top_p"] is not None:
            form_data["top_p"] = params["top_p"]

        if "frequency_penalty" in params and params["frequency_penalty"] is not None:
            form_data["frequency_penalty"] = params["frequency_penalty"]

        if "reasoning_effort" in params and params["reasoning_effort"] is not None:
            form_data["reasoning_effort"] = params["reasoning_effort"]

        if "logit_bias" in params and params["logit_bias"] is not None:
            try:
                form_data["logit_bias"] = json.loads(
                    convert_logit_bias_input_to_json(params["logit_bias"])
                )
            except Exception as e:
                print(f"Error parsing logit_bias: {e}")

    return form_data

# def parse_object_key(url: str) -> str:
#     """
#     从给定的 URL 中解析并返回对象键（通常是文件名）。
    
#     Args:
#         url (str): 要解析的完整 URL。
#         key_prefix (str): 在此实现中未使用，为保持签名一致性而保留。

#     Returns:
#         str: 从 URL 路径中提取的文件名。
#     """
#     from urllib.parse import urlparse
#     import os

#     parsed_path = urlparse(url).path
#     return os.path.basename(parsed_path)

def replace_fileid_to_oss_url(form_data):
    from open_webui.storage.provider import Storage
    if not isinstance(Storage, OSSStorageProvider):
        return
    
    for message in form_data["messages"]:
        content = message.get("content", [])
        if not isinstance(content, list):
            continue
        for content_item in content:
            if content_item.get("type", "") == "image_url":
                raw_key = content_item.get("image_url", {}).get("url", "")
                content_item["image_url"]["url"] = get_image_storage().get_public_access_file_url(raw_key)
                log.info(f"更新图片URL: {raw_key} -> {content_item['image_url']['url']}")



async def process_chat_payload(request, form_data, user, metadata, model):

    form_data = apply_params_to_form_data(form_data, model)
    log.debug(f"form_data: {form_data}")

    event_emitter = get_event_emitter(metadata)
    event_call = get_event_call(metadata)

    extra_params = {
        "__event_emitter__": event_emitter,
        "__event_call__": event_call,
        "__user__": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
        },
        "__metadata__": metadata,
        "__request__": request,
        "__model__": model,
    }

    # Initialize events to store additional event to be sent to the client
    # Initialize contexts and citation
    if getattr(request.state, "direct", False) and hasattr(request.state, "model"):
        models = {
            request.state.model["id"]: request.state.model,
        }
    else:
        models = request.app.state.MODELS

    task_model_id = get_task_model_id(
        form_data["model"],
        request.app.state.config.TASK_MODEL,
        request.app.state.config.TASK_MODEL_EXTERNAL,
        models,
    )

    events = []
    sources = []

    user_message = get_last_user_message(form_data["messages"])
    model_knowledge = model.get("info", {}).get("meta", {}).get("knowledge", False)
    is_vision_model = model.get("info", {}).get("meta", {}).get("capabilities", {}).get("vision", False)
    if model_knowledge:
        await event_emitter(
            {
                "type": "status",
                "data": {
                    "action": "knowledge_search",
                    "query": user_message,
                    "done": False,
                },
            }
        )

        knowledge_files = []
        for item in model_knowledge:
            if item.get("collection_name"):
                knowledge_files.append(
                    {
                        "id": item.get("collection_name"),
                        "name": item.get("name"),
                        "legacy": True,
                    }
                )
            elif item.get("collection_names"):
                knowledge_files.append(
                    {
                        "name": item.get("name"),
                        "type": "collection",
                        "collection_names": item.get("collection_names"),
                        "legacy": True,
                    }
                )
            else:
                knowledge_files.append(item)

        files = form_data.get("files", [])
        files.extend(knowledge_files)
        form_data["files"] = files

    variables = form_data.pop("variables", None)

    # Process the form_data through the pipeline
    try:
        log.info(f"[Request Timeline] [chat_completion] process_pipeline_inlet_filter start")
        form_data = await process_pipeline_inlet_filter(
            request, form_data, user, models
        )
        log.info(f"[Request Timeline] [chat_completion] process_pipeline_inlet_filter end")
    except Exception as e:
        raise e

    if form_data.get("stop", False):
        return form_data, metadata, events

    try:
        filter_functions = [
            Functions.get_function_by_id(filter_id)
            for filter_id in get_sorted_filter_ids(model)
        ]

        form_data, flags = await process_filter_functions(
            request=request,
            filter_functions=filter_functions,
            filter_type="inlet",
            form_data=form_data,
            extra_params=extra_params,
        )
    except Exception as e:
        raise Exception(f"Error: {e}")

    features = form_data.get("features", None)
    if features:
        if "web_search" in features and features["web_search"]:
            form_data = await chat_web_search_handler(
                request, form_data, extra_params, user
            )

        if "image_generation" in features and features["image_generation"]:
            form_data = await chat_image_generation_handler(
                request, form_data, extra_params, user
            )



    if request.app.state.config.ENABLE_MCP and metadata.get("mcp_servers", []):
        log.info("处理 MCP 工具")
        try:
            form_data = await process_mcp_tools(
                request, form_data, user, metadata
            )
        except Exception as e:
            log.exception(f"处理 MCP 工具错误: {e}")

    tool_ids = form_data.pop("tool_ids", None)
    files = form_data.pop("files", None)

    # Remove files duplicates
    if files:
        files = list({json.dumps(f, sort_keys=True): f for f in files}.values())

    metadata = {
        **metadata,
        "tool_ids": tool_ids,
        "files": files,
    }
    form_data["metadata"] = metadata

    # Server side tools
    tool_ids = metadata.get("tool_ids", None)
    # Client side tools
    tool_servers = metadata.get("tool_servers", None)

    log.debug(f"{tool_ids=}")
    log.debug(f"{tool_servers=}")

    tools_dict = {}

    if tool_ids:
        tools_dict = get_tools(
            request,
            tool_ids,
            user,
            {
                **extra_params,
                "__model__": models[task_model_id],
                "__messages__": form_data["messages"],
                "__files__": metadata.get("files", []),
            },
        )

    if tool_servers:
        for tool_server in tool_servers:
            tool_specs = tool_server.pop("specs", [])

            for tool in tool_specs:
                tools_dict[tool["name"]] = {
                    "spec": tool,
                    "direct": True,
                    "server": tool_server,
                }

    if tools_dict:
        if metadata.get("function_calling") == "native":
            # If the function calling is native, then call the tools function calling handler
            metadata["tools"] = tools_dict
            form_data["tools"] = [
                {"type": "function", "function": tool.get("spec", {})}
                for tool in tools_dict.values()
            ]
        else:
            # If the function calling is not native, then call the tools function calling handler
            try:
                form_data, flags = await chat_completion_tools_handler(
                    request, form_data, extra_params, user, models, tools_dict
                )
                sources.extend(flags.get("sources", []))

            except Exception as e:
                log.exception(e)
    if not is_vision_model:
        try:
            form_data, flags = await chat_completion_files_handler(request, form_data, user)
            sources.extend(flags.get("sources", []))
        except Exception as e:
            log.exception(e)

    # 刷新临时图片URL
    replace_fileid_to_oss_url(form_data)

    # If context is not empty, insert it into the messages
    if len(sources) > 0 and not is_vision_model:
        context_string = ""
        for source_idx, source in enumerate(sources):
            if "document" in source:
                for doc_idx, doc_context in enumerate(source["document"]):
                    context_string += (
                        f'<source id="{source_idx + 1}">{doc_context}</source>\n'
                    )

        context_string = context_string.strip()
        prompt = get_last_user_message(form_data["messages"])

        if prompt is None:
            raise Exception("No user message found")
        if (
            request.app.state.config.RELEVANCE_THRESHOLD == 0
            and context_string.strip() == ""
        ):
            log.debug(
                f"With a 0 relevancy threshold for RAG, the context cannot be empty"
            )

        # Workaround for Ollama 2.0+ system prompt issue
        # TODO: replace with add_or_update_system_message
        # if model.get("owned_by") == "ollama":
        form_data["messages"] = prepend_to_first_user_message_content(
            rag_template(
                request.app.state.config.RAG_TEMPLATE, context_string, prompt
            ),
            form_data["messages"],
        )
        # else:
        #     form_data["messages"] = add_or_update_system_message(
        #         rag_template(
        #             request.app.state.config.RAG_TEMPLATE, context_string, prompt
        #         ),
        #         form_data["messages"],
        #     )

    # If there are citations, add them to the data_items
    sources = [source for source in sources if source.get("source", {}).get("name", "")]

    if len(sources) > 0:
        events.append({"sources": sources})

    if model_knowledge:
        await event_emitter(
            {
                "type": "status",
                "data": {
                    "action": "knowledge_search",
                    "query": user_message,
                    "done": True,
                    "hidden": True,
                },
            }
        )

    return form_data, metadata, events


async def process_chat_response(
    request, response, form_data, user, metadata, model, events, tasks, request_start_time, bg_manager: SSERedisManager
):
    session_id = metadata.get("message_id")
    async def background_tasks_handler():
        message_map = Chats.get_messages_by_chat_id(metadata["chat_id"])
        message = message_map.get(metadata["message_id"]) if message_map else None

        if message:
            messages = get_message_list(message_map, message.get("id"))

            if tasks and messages:
                if TASKS.TITLE_GENERATION in tasks:
                    if tasks[TASKS.TITLE_GENERATION]:
                        res = await generate_title(
                            request,
                            {
                                "model": message["model"],
                                "messages": messages,
                                "chat_id": metadata["chat_id"],
                            },
                            user,
                        )

                        if res and isinstance(res, dict):
                            if len(res.get("choices", [])) == 1:
                                title_string = (
                                    res.get("choices", [])[0]
                                    .get("message", {})
                                    .get("content", message.get("content", "New Chat"))
                                )
                            else:
                                title_string = ""

                            title_string = title_string[
                                title_string.find("{") : title_string.rfind("}") + 1
                            ]

                            try:
                                title = json.loads(title_string).get(
                                    "title", "New Chat"
                                )
                            except Exception as e:
                                title = ""

                            if not title:
                                title = messages[0].get("content", "New Chat")

                            Chats.update_chat_title_by_id(metadata["chat_id"], title)

                            await event_emitter(
                                {
                                    "type": "chat:title",
                                    "data": title,
                                }
                            )
                    elif len(messages) == 2:
                        title = messages[0].get("content", "New Chat")

                        Chats.update_chat_title_by_id(metadata["chat_id"], title)

                        await event_emitter(
                            {
                                "type": "chat:title",
                                "data": message.get("content", "New Chat"),
                            }
                        )

                if TASKS.TAGS_GENERATION in tasks and tasks[TASKS.TAGS_GENERATION]:
                    res = await generate_chat_tags(
                        request,
                        {
                            "model": message["model"],
                            "messages": messages,
                            "chat_id": metadata["chat_id"],
                        },
                        user,
                    )

                    if res and isinstance(res, dict):
                        if len(res.get("choices", [])) == 1:
                            tags_string = (
                                res.get("choices", [])[0]
                                .get("message", {})
                                .get("content", "")
                            )
                        else:
                            tags_string = ""

                        tags_string = tags_string[
                            tags_string.find("{") : tags_string.rfind("}") + 1
                        ]

                        try:
                            tags = json.loads(tags_string).get("tags", [])
                            Chats.update_chat_tags_by_id(
                                metadata["chat_id"], tags, user
                            )

                            await event_emitter(
                                {
                                    "type": "chat:tags",
                                    "data": tags,
                                }
                            )
                        except Exception as e:
                            pass

    async def sse_channel_wrapper():
        # 心跳包配置
        HEARTBEAT_INTERVAL = 30  # 30秒发送一次心跳包
        MAX_CONNECTION_TIME = 1800  # 30分钟最大连接时间（安全阀）

        connection_start_time = time.time()
        last_event_time = time.time()
        connection_completed = False

        # 连接监控
        connection_monitor = get_connection_monitor()
        connection_id = connection_monitor.record_connection_start(
            ConnectionType.SSE_CLIENT,
            form_data.get('model', 'unknown')
        )

        # SSE 并发连接数指标增加
        model_id = form_data.get('model', 'unknown')
        sse_concurrent_connections.labels(model_id=model_id, env=ENV).inc()


        try:
            while not client_disconnected.is_set() and not connection_completed:
                try:
                    current_time = time.time()

                    # 安全阀：检查连接是否超过最大时间
                    if current_time - connection_start_time > MAX_CONNECTION_TIME:
                        log.info(f"SSE 连接超过最大时间 {MAX_CONNECTION_TIME}s，强制关闭: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}")
                        break

                    time_since_last_event = current_time - last_event_time

                    # 如果距离上次事件超过心跳间隔，发送心跳包
                    # 注意：心跳包只在 SSE 通道发送，不通过 bg_manager
                    if time_since_last_event >= HEARTBEAT_INTERVAL:
                        heartbeat_data = json.dumps({
                            "type": "heartbeat",
                            "timestamp": current_time
                        }, ensure_ascii=False)
                        yield f"data: {heartbeat_data}\n\n"
                        last_event_time = current_time

                        # 更新心跳包指标
                        sse_heartbeat_sent_total.labels(
                            model_id=form_data.get('model', 'unknown'),
                            env=ENV
                        ).inc()

                        log.info(f"发送心跳包: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}")

                    # 智能超时策略：
                    # 1. 如果接近心跳时间，使用较短超时以便及时发送心跳包
                    # 2. 否则使用较长超时减少 CPU 唤醒
                    remaining_wait_time = max(1, HEARTBEAT_INTERVAL - time_since_last_event)
                    if remaining_wait_time <= 5:
                        wait_timeout = remaining_wait_time
                    else:
                        wait_timeout = min(remaining_wait_time, 30)  # 最多等待30秒

                    # 等待事件或超时
                    event = await asyncio.wait_for(sse_channel.get(), timeout=wait_timeout)
                    if event is None:
                        # 连接完成标记
                        connection_completed = True
                        sse_channel.task_done()
                        log.info(f"SSE 连接完成: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}")
                        break

                    # 发送实际事件（这些事件来自 event_emitter，已经通过 bg_manager 处理过）
                    yield f"data: {event}\n\n"
                    last_event_time = time.time()  # 更新最后事件时间

                except asyncio.TimeoutError:
                    # 超时是正常的，用于检查心跳包或安全阀
                    continue
                except Exception as e:
                    log.error(f"SSE 处理事件出错: {str(e)}")
                    raise
        except asyncio.CancelledError:
            log.info(f"SSE 连接已取消: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}")
        except Exception as e:
            log.error(f"SSE 通道异常: {str(e)}")
            raise
        finally:
            client_disconnected.set()

            # 记录连接结束和持续时间
            connection_duration = time.time() - connection_start_time
            connection_monitor.record_connection_end(
                connection_id,
                ConnectionType.SSE_CLIENT,
                form_data.get('model', 'unknown'),
                connection_duration
            )


            # 清理队列中的所有剩余事件，避免内存泄漏
            try:
                rest_size = sse_channel.qsize()
                log.info(f"SSE 连接已关闭: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}, 剩余事件数量: {rest_size}")
                while not sse_channel.empty():
                    try:
                        sse_channel.get_nowait()
                        sse_channel.task_done()
                    except asyncio.QueueEmpty:
                        break
                    except Exception as e:
                        log.error(f"清理队列项失败: {str(e)}")
                        break
            except Exception as e:
                log.error(f"清理SSE队列失败: {str(e)}")

            log.info(f"SSE 连接已关闭: chat_id={metadata.get('chat_id', '?')}, message_id={metadata.get('message_id', '?')}")

            # SSE 并发连接数指标减少
            sse_concurrent_connections.labels(model_id=model_id, env=ENV).dec()

    sensitive_tasks = AsyncQueue()
    sensitive_lock = asyncio.Lock()
    sensitive_result = None

    async def handle_sensitive_tasks():
        nonlocal sensitive_result

        step = 0
        dedup = set()

        async for task in sensitive_tasks:
            step += 1

            input_type = task["input_type"]
            text = task["text"]
            if text == "":
                continue
            request_id = metadata.get("message_id", f"t-{request.state.trace_id}")

            model_id = task["model"]["id"]
            if model_id.lower() == "zero":
                check_type = "default_text_dr"
                sub_source = "zero"
            elif model_id.lower() == "deep-research":
                check_type = "default_text_dr"
                sub_source = "dr"
            else:
                check_type = "default_text"
                sub_source = None

            if "check_type" in task:
                check_type = task["check_type"]

            params = {
                "text": text,
                "input_type": input_type,
                "sub_source": sub_source,
                "check_type": check_type,
                "request_id": request_id,
                "chat_id": metadata.get("chat_id", None),
            }
            params_hash = hashlib.sha256(json.dumps(params, sort_keys=True).encode()).hexdigest()
            if params_hash in dedup:
                continue
            if len(dedup) > 100:
                dedup.clear()
            dedup.add(params_hash)

            if not params.get("force", False):
                threshold = 1 if check_type.startswith("search") else 50
                if step < threshold:
                    continue
                step = 0

            log.debug(f"过安全 {params}")
            sensitive_res = await check_sensitive(**params)
            if sensitive_res.get("is_sensitive", False):
                log.warning(f"触发安全: {sensitive_res}")
                async with sensitive_lock:
                    if sensitive_result is None:
                        answer = sensitive_res.get("answer", "")
                        if answer:
                            sensitive_result = {
                                "content": answer
                            }
                        else:
                            sensitive_result = {
                                "content": " ",
                                "error": {
                                    "detail": "Content Security Warning: The content may contain inappropriate content.",
                                    "content": "Content Security Warning: The content may contain inappropriate content."
                                },
                            }
                break

    event_caller = None
    # if (
    #     "session_id" in metadata
    #     and metadata["session_id"]
    #     and "chat_id" in metadata
    #     and metadata["chat_id"]
    #     and "message_id" in metadata
    #     and metadata["message_id"]
    # ):
    #     event_emitter = get_event_emitter(metadata)
    #     event_caller = get_event_call(metadata)

    sse_channel = asyncio.Queue()
    client_disconnected = asyncio.Event()
    has_sent_str = ""
    last_bg_chunk_size = 0
    async def event_emitter(event,   need_full_update=False,phase: Literal["thinking", "answer", "tool_call", "tool_result", "done", "other"] = "other" ):
        nonlocal has_sent_str
        nonlocal last_bg_chunk_size
        if event is None:
            try:
                await bg_manager.update_and_publish_message(user.id, session_id, "[DONE]")
                await sse_channel.put(None)
            except Exception as e:
                log.error(f"【pubsub】event_emitter关闭失败: {str(e)}")
            return
        # 非 chat:completion 类型，目前没用到，别管了。
        if event.get("type") != "chat:completion":
            return
        
        # 直接截流吧，不要作复杂的判断，content 字符每超过500，就发一次。 如果 done 也发一次
        send_bg_chunk = False
        # edit_index 计算一下，全量更新的时候用，而且只给 sse_channel 用，redis 那个不能用，因为保证不了某个客户端一定收到了之前的 content
        edit_index = 0
        try:
            content = event.get("data", {}).get("content", "")
            to_send_str = ""
            if content:
                if not need_full_update:
                    to_send_str = content[len(has_sent_str):]
                    event["data"]["delta_content"] = to_send_str
                    # log.debug(f"增量更新 长度为: {len(to_send_str)}")
                    del event["data"]["content"]
                    
                else:
                    log.debug(f"全量更新, has_sent_str_len: {len(has_sent_str)}, content_len: {len(content)}")
                    edit_index = get_edit_index(has_sent_str, content)
                    edit_content = content[edit_index:]
                    event["data"]["edit_index"] = edit_index
                    event["data"]["edit_content"] = edit_content
                    del event["data"]["content"]
                has_sent_str = content
            event["data"]["phase"] = phase
            chunk_str = json.dumps(event, ensure_ascii=False)
            
            if event.get("data",{}).get("done",False):
                send_bg_chunk = True
            if content and len(content) - last_bg_chunk_size > 500:
                send_bg_chunk = True
            
            if send_bg_chunk:
                last_bg_chunk_size = len(content)
                if "delta_content" in event["data"]:
                    del event["data"]["delta_content"]
                if "edit_index" in event["data"]:
                    del event["data"]["edit_index"]
                if "edit_content" in event["data"]:
                    del event["data"]["edit_content"]
                event["data"]["content"] = content
                full_chunk = json.dumps(event, ensure_ascii=False)
                await bg_manager.update_and_publish_message(user.id, session_id, full_chunk)
            if not client_disconnected.is_set():    
                await sse_channel.put(chunk_str)
        except Exception as e:
            log.error(f"【pubsub】event_emitter发送事件失败: {str(e)}")
            return

    # Non-streaming response
    # 这条道路废弃了，走到了就炸⚠️⚠️⚠️
    if not isinstance(response, StreamingResponse):
        if event_emitter:
            if "error" in response:
                error = response["error"].get("detail", response["error"])
                Chats.upsert_message_to_chat_by_id_and_message_id(
                    metadata["chat_id"],
                    metadata["message_id"],
                    {
                        "error": {"content": error},
                    },
                )

            if "selected_model_id" in response:
                Chats.upsert_message_to_chat_by_id_and_message_id(
                    metadata["chat_id"],
                    metadata["message_id"],
                    {
                        "selectedModelId": response["selected_model_id"],
                    },
                )

            choices = response.get("choices", [])
            if choices and choices[0].get("message", {}).get("content"):
                content = response["choices"][0]["message"]["content"]

                if content:

                    await event_emitter(
                        {
                            "type": "chat:completion",
                            "data": response,
                        }
                    )

                    title = Chats.get_chat_title_by_id(metadata["chat_id"])

                    await event_emitter(
                        {
                            "type": "chat:completion",
                            "data": {
                                "done": True,
                                "content": content,
                                "title": title,
                            },
                        }
                    )

                    # Save message in the database
                    Chats.upsert_message_to_chat_by_id_and_message_id(
                        metadata["chat_id"],
                        metadata["message_id"],
                        {
                            "content": content,
                        },
                    )

                    # Send a webhook notification if the user is not active
                    if get_active_status_by_user_id(user.id) is None:
                        webhook_url = Users.get_user_webhook_url_by_id(user.id)
                        webhookContent = content.replace('%', '')
                        if webhook_url:
                            post_webhook(
                                request.app.state.WEBUI_NAME,
                                webhook_url,
                                f"{title} - {request.app.state.config.WEBUI_URL}/c/{metadata['chat_id']}\n\n{webhookContent}",
                                {
                                    "action": "chat",
                                    "message": webhookContent,
                                    "title": title,
                                    "url": f"{request.app.state.config.WEBUI_URL}/c/{metadata['chat_id']}",
                                },
                            )

                    await background_tasks_handler()
            await bg_manager.release_user_limit(user.id,session_id)
            return StreamingResponse(
                sse_channel_wrapper(),
            )
        else:
            return response

    # Non standard response
    if not any(
        content_type in response.headers["Content-Type"]
        for content_type in ["text/event-stream", "application/x-ndjson"]
    ):
        await bg_manager.release_user_limit(user.id,session_id)
        return response

    extra_params = {
        "__event_emitter__": event_emitter,
        "__event_call__": event_caller,
        "__user__": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
        },
        "__metadata__": metadata,
        "__request__": request,
        "__model__": model,
    }
    filter_functions = [
        Functions.get_function_by_id(filter_id)
        for filter_id in get_sorted_filter_ids(model)
    ]

    # Streaming response
    if event_emitter:
        task_id = str(uuid4())  # Create a unique task ID.
        features = form_data.get("features", {})

        model_id = form_data.get("model", "")

        Chats.upsert_message_to_chat_by_id_and_message_id(
            metadata["chat_id"],
            metadata["message_id"],
            {
                "model": model_id,
            },
        )

        def split_content_and_whitespace(content):
            content_stripped = content.rstrip()
            original_whitespace = (
                content[len(content_stripped) :]
                if len(content) > len(content_stripped)
                else ""
            )
            return content_stripped, original_whitespace

        def is_opening_code_block(content):
            backtick_segments = content.split("```")
            # Even number of segments means the last backticks are opening a new block
            return len(backtick_segments) > 1 and len(backtick_segments) % 2 == 0

        has_clear_user_limit = False
        # Handle as a background task
        async def post_response_handler(response, events):
            nonlocal has_clear_user_limit
            stream_mcp_writers: dict[str, PPTWriter] = {}
            PPT_TOOLS = ["create_slide", "add_slide", "update_slide"]

            def serialize_content_blocks(content_blocks, raw=False):
                content = ""
                for block in content_blocks:
                    if block["type"] == "text":
                        content = f"{content}{block['content'].strip()}\n"
                    elif block["type"] == "tool_calls":
                        # 这个代码感觉走不到，是不是可以删了。
                        tool_calls_content = serialize_mcp_tool_calls(block)
                        content = f"{content}\n{tool_calls_content}\n"
                    elif block["type"] == "tool_call":
                        content = f'{content}\n<glm_block >{json.dumps(block["data"], ensure_ascii=False)}</glm_block>\n'
                    elif block["type"] == "tool_result":
                        content = f'{content}\n<glm_block >{json.dumps(block["data"], ensure_ascii=False)}</glm_block>\n'
                    elif block["type"] == "reasoning":
                        reasoning_display_content = "\n".join(
                            (f"> {line}" if not line.startswith(">") else line)
                            for line in block["content"].splitlines()
                        )
                        # reasoning_display_content = block["content"]
                        reasoning_duration = block.get("duration", None)

                        if reasoning_duration is not None:
                            if raw:
                                content = f'{content}\n<{block["start_tag"]}>{block["content"]}<{block["end_tag"]}>\n'
                            else:
                                content = f'{content}\n<details type="reasoning" done="true" duration="{reasoning_duration}">\n<summary>Thought for {reasoning_duration} seconds</summary>\n{reasoning_display_content}\n</details>\n'
                        else:
                            if raw:
                                content = f'{content}\n<{block["start_tag"]}>{block["content"]}'
                            else:
                                content = f'{content}\n<details type="reasoning" done="false">\n<summary>Thinking…</summary>\n{reasoning_display_content}\n'

                    elif block["type"] == "code_interpreter":
                        attributes = block.get("attributes", {})
                        output = block.get("output", None)
                        lang = attributes.get("lang", "")

                        content_stripped, original_whitespace = (
                            split_content_and_whitespace(content)
                        )
                        if is_opening_code_block(content_stripped):
                            # Remove trailing backticks that would open a new block
                            content = (
                                content_stripped.rstrip("`").rstrip()
                                + original_whitespace
                            )
                        else:
                            # Keep content as is - either closing backticks or no backticks
                            content = content_stripped + original_whitespace

                        if output:
                            output = html.escape(json.dumps(output))

                            if raw:
                                content = f'{content}\n<code_interpreter type="code" lang="{lang}">\n{block["content"]}\n</code_interpreter>\n```output\n{output}\n```\n'
                            else:
                                content = f'{content}\n<details type="code_interpreter" done="true" output="{output}">\n<summary>Analyzed</summary>\n```{lang}\n{block["content"]}\n```\n</details>\n'
                        else:
                            if raw:
                                content = f'{content}\n<code_interpreter type="code" lang="{lang}">\n{block["content"]}\n</code_interpreter>\n'
                            else:
                                content = f'{content}\n<details type="code_interpreter" done="false">\n<summary>Analyzing...</summary>\n```{lang}\n{block["content"]}\n```\n</details>\n'

                    else:
                        block_content = str(block["content"]).strip()
                        content = f"{content}{block['type']}: {block_content}\n"

                return content.strip()

            def convert_content_blocks_to_messages(content_blocks):
                messages = []

                for idx, block in enumerate(content_blocks):
                    if block["type"] == "text":
                        if block["content"].strip():
                            messages.append(
                                {
                                    "role": "assistant",
                                    "content": block["content"],
                                }
                            )
                    elif block["type"] == "reasoning":
                        if block["content"]:
                            messages.append(
                                {
                                    "role": "assistant",
                                    "content": "<think>" + block["content"] + "</think>",
                                }
                            )
                    elif block["type"] == "tool_calls":
                        if block.get("content"):
                            call_id = block.get("content",[])[0].get("id")
                            function_name = block.get("content",[])[0].get("function",{}).get("name")
                            function_args = block.get("content",[])[0].get("function",{}).get("arguments")
                            function_result = block.get("results",[])[0].get("content")
                            messages.append(
                                {
                                    "role": "assistant",
                                    "content": None,
                                    "tool_calls": [
                                        {
                                            "type": "function",
                                            "id": call_id,
                                            "function": {
                                                "name": function_name,
                                                "arguments": function_args,
                                            },
                                        }
                                    ],
                                }
                            )
                            messages.append({
                                "role": "tool",
                                "content": function_result,
                                "tool_call_id": call_id,
                                "name": function_name,
                            })

                return messages

            message = Chats.get_message_by_id_and_message_id(
                metadata["chat_id"], metadata["message_id"]
            )

            tool_calls = []

            last_assistant_message = None
            try:
                if form_data["messages"][-1]["role"] == "assistant":
                    last_assistant_message = get_last_assistant_message(
                        form_data["messages"]
                    )
            except Exception as e:
                pass

            content = (
                message.get("content", "")
                if message
                else last_assistant_message if last_assistant_message else ""
            )

            content_blocks = [
                {
                    "type": "text",
                    "content": content,
                }
            ]

            try:
                # 释放前置积压 events，主要是 RAG/文件处理，owui 本身搜索相关的。
                for event in events:
                    await event_emitter(
                        {
                            "type": "chat:completion",
                            "data": event,
                        }
                    )

                    # Save message in the database
                    Chats.upsert_message_to_chat_by_id_and_message_id(
                        metadata["chat_id"],
                        metadata["message_id"],
                        {
                            **event,
                        },
                    )

                async def stream_body_handler(response, round_start_time=None):
                    nonlocal content
                    nonlocal content_blocks

                    citation_urls = []
                    response_tool_calls = []

                    # 下面的配置都只针对 all tools 格式 api 生效
                    filter_config = [
                        {"pattern": "<think>", "ignore_after": False},
                        {"pattern": "</think>", "ignore_after": True},
                        # {"pattern": "```python", "ignore_after": True},
                    ]
                    stream_filter = StreamFilter(filter_config)
                    chunk_idx = 0
                    # 5 包之后再发送
                    BOOT_START_CHUNK = 2
                    current_round_start_time = round_start_time if round_start_time is not None else request_start_time
                    # 创建指标跟踪器
                    metrics_tracker = create_metrics_tracker(
                        selected_model_id, metadata, current_round_start_time
                    )
                    first_chunk_sent = False
                    log.info("[Request Timeline] [stream body handler start parse response")

                    args_counter = 0

                    async for line in response.body_iterator:

                        line = line.decode("utf-8") if isinstance(line, bytes) else line
                        data = line


                        # Skip empty lines
                        if not data.strip():
                            continue

                        # "data:" is the prefix for each event
                        if not data.startswith("data:"):
                            continue

                        # Remove the prefix
                        data = data[len("data:") :].strip()

                        if sensitive_result is not None:
                            return
                        try:
                            data = json.loads(data)
                            chunk_idx += 1


                            if data.get("message", {}).get("status", None) is not None:
                                """DeepResearch 走这里"""
                                dr_need_full_update = False
                                dr_phase = "other"

                                # 处理自定义API响应格式
                                message = data.get("message", {})
                                status = message.get("status", "")
                                content_data = message.get("content", {})
                                content_type = content_data.get("content_type", "")
                                content_text = content_data.get("content", "")

                                if content_type in ["think", "text"]:
                                    if should_record_first_token(content_type, "assistant"):
                                        metrics_tracker.record_first_token()
                                        # 倒序遍历内容块，只修改最后一个匹配的 tool_call 块
                                    for block in reversed(content_blocks):
                                        if block.get("type") == "tool_call" and block.get("data", {}).get("data", {}).get("name") in ["open", "click"]:
                                            block["data"]["data"]["loading"] = False
                                            break

                                # 根据不同的content_type处理内容
                                if content_type == "think":
                                    dr_phase = "thinking"
                                    # 处理思考内容
                                    if status == "in_progress":
                                        content_text, intercepted, status = stream_filter.add(content_text)
                                        # 上一次不是 reasoning 就新加，合理。
                                        if not content_blocks or content_blocks[-1]["type"] != "reasoning":
                                            content_text = content_text.lstrip()
                                            content_blocks.append({
                                                "type": "reasoning",
                                                "start_tag": "think",
                                                "end_tag": "/think",
                                                "attributes": {},
                                                "content": content_text,
                                                "started_at": time.time(),
                                            })
                                        else:
                                            content_blocks[-1]["content"] += content_text

                                        await sensitive_tasks.put({
                                            "input_type": "think",
                                            "text": content_blocks[-1]["content"],
                                            "model": model
                                        })
                                    elif status == "completed":
                                        dr_need_full_update = True
                                        dr_phase = "tool_call"
                                        # Parse usage data if available and add to metrics tracker
                                        completion_tokens = extract_completion_tokens_from_message(message)
                                        if completion_tokens is not None:
                                            metrics_tracker.add_completion_tokens(completion_tokens)
                                            log.debug(
                                                f"[ZERO HANDLER][COMPLETED][THINK] Added {completion_tokens} tokens to metrics tracker"
                                            )
                                        stream_filter.reset()
                                        # 完成思考，用去掉 fc 描述的去替换之前的
                                        # 提取</think>后面的JSON
                                        content_text = content_text.lstrip()
                                        think_content = content_text
                                        json_start = think_content.rfind("</think>")
                                        json_text = ""
                                        pure_text = ""
                                        if json_start != -1:
                                            json_text = think_content[json_start + 8:].strip()
                                            pure_text = think_content[:json_start + 8].strip()
                                        pure_text = pure_text.lstrip().replace("<think>", "").replace("</think>", "")
                                        content_blocks[-1]["content"] = pure_text
                                        content_blocks[-1]["ended_at"] = time.time()
                                        content_blocks[-1]["duration"] = int(content_blocks[-1]["ended_at"] - content_blocks[-1]["started_at"])

                                        await sensitive_tasks.put({
                                            "text": pure_text,
                                            "input_type": "output",
                                            "model": model
                                        })

                                        # 检查思考内容后面是否有函数调用的JSON
                                        try:
                                            # 提取</think>后面的JSON
                                            if json_text:
                                                func_call = json.loads(json_text)
                                                dr_need_full_update = True
                                                dr_phase = "tool_call"
                                                if "name" in func_call:
                                                    # 构建工具调用
                                                    tool_call = {
                                                        "id": str(uuid4()),
                                                        "function": {
                                                            "name": func_call.get("name", ""),
                                                            "arguments": json.dumps(func_call.get("arguments", {})),
                                                        }
                                                    }
                                                    log.info(f"工具调用: {tool_call}")
                                                    #! 本身已经处理了 tools，这里不需要加进去，不然后面还会处理就不行了。
                                                    #! 工具调用通过 text 块返回给前端
                                                    fc_name = func_call.get('name', '')
                                                    last_fc_name = fc_name
                                                    if fc_name != "finish":
                                                        # 处理click工具调用，替换link_id为原始URL
                                                        if fc_name == "click":
                                                            args = func_call.get('arguments', {})
                                                            if isinstance(args, str):
                                                                try:
                                                                    args = json.loads(args)
                                                                except:
                                                                    pass
                                                            link_id = args.get('link_id', None)
                                                            if link_id is not None and 0 <= link_id < len(citation_urls):
                                                                original_url = citation_urls[link_id]
                                                                # 更新参数，添加原始URL
                                                                args['url'] = original_url
                                                                func_call['arguments'] = args
                                                                log.info(f"替换link_id {link_id} 为URL: {original_url}")

                                                        content_blocks.append({
                                                            "type": "tool_call",
                                                            "data": {
                                                                "type": "tool_call",
                                                                "data": {
                                                                    "name": fc_name,
                                                                    "arguments": func_call.get('arguments', {}),
                                                                    "loading": True
                                                                }
                                                            }
                                                        })
                                                            # content_blocks.append({
                                                            #     "type": "text",
                                                            #     "content": f"- **搜索关键词: `{func_call.get('arguments', {}).get('query', '')}`**",
                                                            # })
                                                        # elif fc_name == "open":
                                                        #     content_blocks.append({
                                                        #         "type": "text",
                                                        #         "content": f"- **打开链接: `{func_call.get('arguments', {}).get('url', '')}`**",
                                                        #     })
                                                        # else:
                                                        #     content_blocks.append({
                                                        #         "type": "text",
                                                        #         "content": f"- **触发 `{func_call.get('name', '')}` 工具，参数为 `{json.dumps(func_call.get('arguments', {}), ensure_ascii=False)}`**",
                                                        #     })
                                        except Exception as e:
                                            log.error(f"解析函数调用失败: {e}")

                                elif content_type == "text":
                                    dr_phase = "answer"
                                    # 处理文本内容
                                    if status == "in_progress":
                                        # 增量更新文本内容
                                        if not content_blocks or content_blocks[-1]["type"] != "text":
                                            content_blocks.append({
                                                "type": "text",
                                                "content": content_text,
                                            })
                                        else:
                                            content_blocks[-1]["content"] += content_text
                                    elif status == "completed":
                                        dr_need_full_update = True
                                        # Parse usage data if available and add to metrics tracker
                                        completion_tokens = extract_completion_tokens_from_message(message)
                                        if completion_tokens is not None:
                                            metrics_tracker.add_completion_tokens(completion_tokens)
                                            log.debug(
                                                f"[ZERO HANDLER][COMPLETED][TEXT] Added {completion_tokens} tokens to metrics tracker"
                                            )
                                        content_blocks[-1]["content"] = content_text

                                        #! 废弃这个逻辑， 处理引用信息，添加到文本末尾
                                        # citations = message.get("metadata", {}).get("citations", [])
                                        # if citations:
                                        #     citation_text = "\n\n**参考资料:**\n"
                                        #     for i, citation in enumerate(citations):
                                        #         metadata = citation.get("metadata", {})
                                        #         title = metadata.get("title", "未知来源")
                                        #         url = metadata.get("url", "")
                                        #         citation_text += f"{i+1}. [{title}]({url})\n"

                                        #     # 添加引用信息到最后一个文本块
                                        #     content_blocks[-1]["content"] += citation_text

                                    await sensitive_tasks.put({
                                        "text": content_text,
                                        "input_type": "output",
                                        "model": model
                                    })

                                elif content_type == "browser_result":
                                    dr_phase = "tool_result"
                                    dr_need_full_update = True
                                    # 处理浏览器结果
                                    sources = []
                                    citations = message.get("metadata", {}).get("metadata_list", [])

                                    citations_results = []
                                    for citation in citations:
                                        title = citation.get("title", "未知来源")
                                        url = citation.get("url", "")
                                        # 将URL添加到全局映射中
                                        citation_urls.append(url)
                                        text = citation.get("text", "")
                                        media = citation.get("media", title)
                                        citation_type = citation.get("type", "")
                                        citations_results.append({
                                            "title": title,
                                            "url": url,
                                            "media": media,
                                            "text": text,
                                            "type": citation_type,
                                        })

                                        sources.append({
                                            "source": {
                                                "name": title,
                                                "url": url,
                                            },
                                            "document": [text],
                                            "metadata": [{"source": media}],
                                        })

                                    #! 自定义块协议
                                    content_blocks.append({
                                        "type": "tool_result",
                                        "data": {
                                            "type": "tool_result",
                                            "data": {
                                                "name": "browser_search",
                                                "result": citations_results
                                            }
                                        }
                                    })


                                    titles = "\n".join([block["title"] for block in citations_results])
                                    links = "\n".join([block["url"] for block in citations_results])
                                    await sensitive_tasks.put({
                                        "input_type": "output",
                                        "check_type": "search_text",
                                        "text": titles,
                                        "model": model
                                    })
                                # log.info(f"向前端发送更新通知: {json.dumps(content_blocks, ensure_ascii=False)}")
                                # 为每次更新发送内容到前端
                                if chunk_idx > BOOT_START_CHUNK:
                                    if not first_chunk_sent:
                                        first_chunk_sent = True
                                        log.info(f"[Request Timeline] [stream body handler] first chunk sent")
                                    await event_emitter({
                                        "type": "chat:completion",
                                        "data": {
                                            "content": serialize_content_blocks(content_blocks, raw=False), # 传递空 map
                                        },
                                    },dr_need_full_update,dr_phase)
                                    CHAT_STREAM_SEND_TOKEN_PACK.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).inc()

                                # 跳过后续常规处理
                                continue

                            data, _ = await process_filter_functions(
                                request=request,
                                filter_functions=filter_functions,
                                filter_type="stream",
                                form_data=data,
                                extra_params=extra_params,
                            )

                            need_full_update = False
                            if data:
                                #  标注 openai 走这里
                                if "selected_model_id" in data:
                                    model_id = data["selected_model_id"]
                                    Chats.upsert_message_to_chat_by_id_and_message_id(
                                        metadata["chat_id"],
                                        metadata["message_id"],
                                        {
                                            "selectedModelId": model_id,
                                        },
                                    )
                                else:
                                    choices = data.get("choices", [])
                                    if not choices:
                                        error = data.get("error", {})
                                        if error:
                                            await event_emitter(
                                                {
                                                    "type": "chat:completion",
                                                    "data": {
                                                        "error": error,
                                                    },
                                                }
                                            )
                                        usage = data.get("usage", {})
                                        if usage:
                                            #  oai 标准的 usage
                                            metrics_tracker.add_completion_tokens(usage.get("completion_tokens", 0))
                                            await event_emitter(
                                                {
                                                    "type": "chat:completion",
                                                    "data": {
                                                        "usage": usage,
                                                    },
                                                }
                                            )
                                        continue

                                    delta = choices[0].get("delta", {})
                                    delta_tool_calls = delta.get("tool_calls", None)

                                    if delta_tool_calls:
                                        # 这里 reasoning 一定结束了，改一下状态。
                                        if (
                                            content_blocks
                                            and content_blocks[-1]["type"] == "reasoning"
                                            and content_blocks[-1].get("attributes", {}).get("type") == "reasoning_content"
                                        ):
                                            need_full_update = True
                                            reasoning_block = content_blocks[-1]
                                            reasoning_block["ended_at"] = time.time()
                                            reasoning_block["duration"] = int(
                                                reasoning_block["ended_at"]
                                                - reasoning_block["started_at"]
                                            )

                                            content_blocks.append({"type": "text", "content": ""})

                                        # 对于 oai 转换器出来的，不管啥都当 first
                                        metrics_tracker.record_first_token()
                                        # 这里假设 tool_calls 之后，模型一定会进行总结
                                        # 即不会出现连续的两个 tool_calls block
                                        if content_blocks[-1]["type"] != "tool_calls":
                                            content_blocks.append({
                                                "type": "tool_calls",
                                                "content": response_tool_calls,
                                                "results": []
                                            })
                                        # delta 场景应该目前只有一个 tool_call 返回
                                        for delta_tool_call in delta_tool_calls:
                                            tool_call_index = delta_tool_call.get("index")

                                            if tool_call_index is not None:
                                                if len(response_tool_calls) <= tool_call_index:
                                                    response_tool_calls.append(delta_tool_call)
                                                    delta_arguments = response_tool_calls[tool_call_index]["function"]["arguments"]
                                                else:
                                                    delta_name = delta_tool_call.get("function", {}).get("name")
                                                    delta_arguments = delta_tool_call.get("function", {}).get("arguments")

                                                    if delta_name:
                                                        response_tool_calls[tool_call_index]["function"]["name"] += delta_name

                                                    if delta_arguments:
                                                        response_tool_calls[tool_call_index]["function"]["arguments"] += delta_arguments
                                                content_blocks[-1]["content"] = response_tool_calls
                                                if response_tool_calls[tool_call_index]["function"]["name"] in PPT_TOOLS and hasattr(request.state, 'mcp_servers') and any(server.name == 'ppt-maker' for server in request.state.mcp_servers):
                                                    response_tool_calls[tool_call_index]["mcp_server"] = {"name": "ppt-maker"}
                                                    tool_call_id = response_tool_calls[tool_call_index]["id"]
                                                    if tool_call_id not in stream_mcp_writers:
                                                        url = request.app.state.MCP_CONFIG.mcpServers.get("ppt-maker").url[len("http://"):-len("/mcp")]
                                                        log.info(f"Connect to ppt server: {url}")
                                                        stream_mcp_writers[tool_call_id] = await connect_ppt_server(
                                                            url,
                                                            metadata["user_id"],
                                                            metadata["chat_id"],
                                                            metadata["message_id"],
                                                            response_tool_calls[tool_call_index]["function"],
                                                            lang="zh" if is_zh_preferred(request) else "en",
                                                            trace_id=request.state.trace_id,
                                                        )
                                                    if delta_arguments:
                                                        args_counter += 1
                                                        try:
                                                            write_result = await stream_mcp_writers[tool_call_id].write(delta_arguments)
                                                            if write_result.get("type", "") == "ACT_DESC":
                                                                content_blocks[-1]["results"][-1]["facade_content"] = write_result["content"]
                                                                await event_emitter({
                                                                    "type": "chat:completion",
                                                                    "data": {
                                                                        "content": serialize_content_blocks(content_blocks),
                                                                    },
                                                                }, True)
                                                            elif write_result.get("type", "") == "ERROR":
                                                                raise Exception(write_result["content"])
                                                        except Exception as e:
                                                            log.error(
                                                                f"MCP: [{response_tool_calls[tool_call_index]['function']['name']}] 写入失败: {e}"
                                                            )
                                                            stream_mcp_writers[tool_call_id].failed = True
                                                            raise
                                                    
                                                    results = {res["tool_call_id"]: res for res in content_blocks[-1]["results"]}
                                                    if tool_call_id not in results:
                                                        """加入 ppt 字段，拉起预览侧边栏"""
                                                        content_blocks[-1]["results"].append({
                                                            "tool_call_id": tool_call_id,
                                                            "content": "...",
                                                            "ppt": {"name": "...", "show": response_tool_calls[tool_call_index]["function"]["name"] != "create_slide"},
                                                            "status": "executing"
                                                        })

                                                        await event_emitter({
                                                            "type": "chat:completion",
                                                            "data": {
                                                                "content": serialize_content_blocks(content_blocks),
                                                            },
                                                        },False)
                                        # 不加的话会发一堆多余的给前端
                                        continue

                                    value = delta.get("content")
                                    reasoning_content = delta.get(
                                        "reasoning_content"
                                    ) or delta.get("reasoning")
                                    if reasoning_content:
                                        # 记录首次 token（reasoning 类型）
                                        if should_record_first_token("reasoning", "assistant"):
                                            metrics_tracker.record_first_token()
                                        if not content_blocks or content_blocks[-1]["type"] != "reasoning":
                                            reasoning_block = {
                                                "type": "reasoning",
                                                "start_tag": "think",
                                                "end_tag": "/think",
                                                "attributes": {
                                                    "type": "reasoning_content"
                                                },
                                                "content": "",
                                                "started_at": time.time()
                                            }
                                            content_blocks.append(reasoning_block)
                                        else:
                                            reasoning_block = content_blocks[-1]

                                        reasoning_block["content"] += reasoning_content
                                        await sensitive_tasks.put({
                                            "input_type": "think",
                                            "text": reasoning_block["content"],
                                            "model": model
                                        })

                                        data = {
                                            "content": serialize_content_blocks(content_blocks, raw=False) # 传递空 map
                                        }

                                    if value:
                                        # 记录首次 token（文本类型）
                                        if should_record_first_token("text", "assistant"):
                                            metrics_tracker.record_first_token()
                                        # 这里 reasoning 一定结束了，改一下状态。
                                        if (
                                            content_blocks
                                            and content_blocks[-1]["type"] == "reasoning"
                                            and content_blocks[-1].get("attributes", {}).get("type") == "reasoning_content"
                                        ):
                                            need_full_update = True
                                            reasoning_block = content_blocks[-1]
                                            reasoning_block["ended_at"] = time.time()
                                            reasoning_block["duration"] = int(
                                                reasoning_block["ended_at"]
                                                - reasoning_block["started_at"]
                                            )

                                            content_blocks.append({"type": "text", "content": ""})

                                        content = f"{content}{value}"
                                        if not content_blocks:
                                            content_blocks.append({"type": "text", "content": ""})

                                        content_blocks[-1]["content"] = content_blocks[-1]["content"] + value
                                        await sensitive_tasks.put({
                                            "input_type": "output",
                                            "text": content_blocks[-1]["content"],
                                            "model": model
                                        })

                                        if ENABLE_REALTIME_CHAT_SAVE:
                                            # Save message in the database
                                            Chats.upsert_message_to_chat_by_id_and_message_id(
                                                metadata["chat_id"],
                                                metadata["message_id"],
                                                {
                                                    "content": serialize_content_blocks(
                                                        content_blocks, {}, raw=False # 传递空 map
                                                    ),
                                                },
                                            )
                                        else:
                                            data = {"content": serialize_content_blocks(content_blocks)}
                                
                                # 需要判断一下是不是 thinking
                                thinking = False
                                if content_blocks[-1]["type"] == "reasoning":
                                    thinking = True
                                await event_emitter({
                                    "type": "chat:completion",
                                    "data": data,
                                },need_full_update,phase="thinking" if thinking else "answer")
                        except Exception as e:
                            done = "data: [DONE]" in line
                            if done:
                                pass
                            else:
                                log.error(f"Error: {e}")
                                continue
                    log.info("[Request Timeline] [stream body handler] end parse response")
                    # 完成指标计算并上报
                    metrics_tracker.finalize_metrics()
                    if content_blocks:
                        # Clean up the last text block
                        if content_blocks[-1]["type"] == "text":
                            content_blocks[-1]["content"] = content_blocks[-1][
                                "content"
                            ].strip()

                            if not content_blocks[-1]["content"]:
                                content_blocks.pop()

                                if not content_blocks:
                                    content_blocks.append(
                                        {
                                            "type": "text",
                                            "content": "",
                                        }
                                    )

                    if response_tool_calls:
                        tool_calls.append(response_tool_calls)

                    if response.background is not None:
                        await response.background()
                selected_model_id = metadata.get("selected_model_id", model_id)  # 可能在处理过程中被更新
                if selected_model_id in ["zero", "zero_search", "main_chat" , "main_chat_search"]:
                    # 创建敏感内容检查回调函数，防止变量作用域问题，nonlocal传不进去
                    def get_sensitive_result():
                        return sensitive_result

                    await stream_body_handler_zero(
                        response=response,
                        content_blocks=content_blocks,
                        metadata=metadata,
                        selected_model_id=selected_model_id,
                        request_start_time=request_start_time,
                        event_emitter=event_emitter,
                        sensitive_tasks=sensitive_tasks,
                        model=model,
                        serialize_content_blocks=serialize_content_blocks,
                        sensitive_checker=get_sensitive_result,
                    )
                else:
                    await stream_body_handler(response)

                if sensitive_result is not None:
                    async with sensitive_lock:
                        log.warning("触发内容安全风控")
                        Chats.upsert_message_to_chat_by_id_and_message_id(
                            metadata["chat_id"],
                            metadata["message_id"],
                            sensitive_result,
                        )
                        await event_emitter({
                            "type": "chat:completion",
                            "data": {
                                **sensitive_result,
                                "message_id": metadata.get("message_id"),
                                "done": True
                            },
                        },True)
                        if not has_clear_user_limit:
                            has_clear_user_limit = True
                            await bg_manager.release_user_limit(user.id,session_id)
                        return

                MAX_TOOL_CALL_RETRIES = 100
                tool_call_retries = 0

                while len(tool_calls) > 0 and tool_call_retries < MAX_TOOL_CALL_RETRIES:
                    tool_call_retries += 1

                    response_tool_calls = tool_calls.pop(0)
                    # 理论上，这个情况不应该出现才对，前面有 tool call，后面还能是别的不成？🤡
                    if content_blocks[-1]["type"] != "tool_calls":
                        content_blocks.append(
                            {
                                "type": "tool_calls",
                                "content": response_tool_calls,
                                "started_at": time.time(),  # 添加开始时间戳
                            }
                        )

                        await event_emitter(
                            {
                                "type": "chat:completion",
                                "data": {
                                    "content": serialize_content_blocks(content_blocks),
                                },
                            }
                        )

                    tools = metadata.get("tools", {})

                    results = []

                    # 检查是否有 MCP 工具并处理
                    if request.app.state.config.ENABLE_MCP and metadata.get("mcp_servers", []):
                        from open_webui.mcp.mcp import get_server_name_by_tool
                        mcp_servers = request.state.mcp_servers
                        for tool_call in response_tool_calls:
                            tool_name = tool_call.get("function", {}).get("name", "")
                            if tool_name:
                                server_name = await get_server_name_by_tool(tool_name, mcp_servers)
                                if server_name:
                                    tool_call["mcp_server"] = {"name": server_name}

                        def new_progress_cb(server_name: str, tool_name: str, tool_call_id: str):

                            async def cb(i: float | None = None, n: float | None = None, msg: str | None = None):
                                if msg is None:
                                    data = {}
                                else:
                                    try:
                                        data = json.loads(msg)
                                    except Exception as e:
                                        log.error(f"MCP: [{server_name}.{tool_name}] 无法解析进度数据: {e}, raw={msg}")
                                        return

                                res = None
                                for i, exist_res in enumerate(results):
                                    if exist_res["tool_call_id"] == tool_call_id:
                                        res = exist_res
                                        break
                                if res is None:
                                    i = len(results)
                                    results.append({"tool_call_id": tool_call_id, "content": ""})
                                results[i] = {**results[i], **data}

                                content_blocks[-1]["results"] = results
                                # 添加工具调用结束时间戳
                                content_blocks[-1]["ended_at"] = time.time()
                                if "started_at" not in content_blocks[-1]:
                                    content_blocks[-1]["started_at"] = time.time() - 1  # 默认1秒前开始
                                await event_emitter(
                                    {
                                        "type": "chat:completion",
                                        "data": {
                                            "content": serialize_content_blocks(content_blocks),
                                        },
                                    },True
                                )

                            return cb

                        mcp_results = await handle_mcp_response_tool_calls(
                            request,
                            response_tool_calls,
                            metadata,
                            event_emitter,
                            progress_cb_factory=new_progress_cb,
                            stream_writers=stream_mcp_writers
                        )
                        if mcp_results:
                            # upsert results
                            for res in mcp_results:
                                for exist_res in results:
                                    if exist_res["tool_call_id"] == res["tool_call_id"]:
                                        # 保留所有字段，不只是content
                                        exist_res["content"] += res["content"]
                                        # 保留其他字段，如deep_research等
                                        for key, value in res.items():
                                            if key != "content" and key != "tool_call_id":
                                                exist_res[key] = value
                                        break
                                else:
                                    results.append(res)

                            content_blocks[-1]["results"] = results

                            # 添加工具调用结束时间戳
                            content_blocks[-1]["ended_at"] = time.time()
                            if "started_at" not in content_blocks[-1]:
                                content_blocks[-1]["started_at"] = time.time() - 1  # 默认1秒前开始
                            # 不跳过后续处理，让模型能处理工具结果
                            # 将被工具处理标记为true，以防止后续的普通工具处理覆盖结果
                            mcp_processed = True
                        else:
                            mcp_processed = False
                    else:
                        mcp_processed = False

                    # 处理普通工具，但如果MCP已经处理过则跳过
                    if not mcp_processed:
                        for tool_call in response_tool_calls:
                            tool_call_id = tool_call.get("id", "")
                            tool_name = tool_call.get("function", {}).get("name", "")

                            tool_function_params = {}
                            try:
                                # json.loads cannot be used because some models do not produce valid JSON
                                tool_function_params = ast.literal_eval(
                                    tool_call.get("function", {}).get("arguments", "{}")
                                )
                            except Exception as e:
                                log.info(e)

                            tool_result = None

                            if tool_name in tools:
                                tool = tools[tool_name]
                                spec = tool.get("spec", {})

                                try:
                                    required_params = spec.get("parameters", {}).get(
                                        "required", []
                                    )
                                    tool_function = tool["callable"]
                                    tool_function_params = {
                                        k: v
                                        for k, v in tool_function_params.items()
                                        if k in required_params
                                    }
                                    tool_result = await tool_function(
                                        **tool_function_params
                                    )
                                except Exception as e:
                                    tool_result = str(e)

                            results.append(
                                {
                                    "tool_call_id": tool_call_id,
                                    "content": tool_result,
                                }
                            )

                    content_blocks[-1]["results"] = results

                    content_blocks.append(
                        {
                            "type": "text",
                            "content": "",
                        }
                    )
                    # 这个必须有，在 fc 场景下，第二次调用前，咱们得完整给一次吧。
                    await event_emitter(
                        {
                            "type": "chat:completion",
                            "data": {
                                "content": serialize_content_blocks(content_blocks) ,
                            },
                        },True
                    )

                    try:
                        cur_tool_call_round_start_time = time.time()
                        res = await generate_chat_completion(
                            request,
                            {
                                "model": model_id,
                                "stream": True,
                                "tools": form_data.get("tools", []),
                                "messages": [
                                    *form_data["messages"],
                                    *convert_content_blocks_to_messages(content_blocks),
                                ],
                                "features": features
                            },
                            user,
                        )

                        if isinstance(res, StreamingResponse):
                            await stream_body_handler(res, cur_tool_call_round_start_time)
                        else:
                            break
                    except Exception as e:
                        log.error(f"模型回复异常中断: {e}\n\n{traceback.format_exc()}")
                        break


                #  这里已经完成了，咱们就结束了吧。
                if not has_clear_user_limit:
                    has_clear_user_limit = True
                    await bg_manager.release_user_limit(user.id,session_id)
                

                await event_emitter(
                    {
                        "type": "chat:completion",
                        "data": {
                            "done": True,
                            "delta_content": "",
                        },
                    },False,"done"
                )

                if not ENABLE_REALTIME_CHAT_SAVE:
                    # Save message in the database
                    Chats.upsert_message_to_chat_by_id_and_message_id(
                        metadata["chat_id"],
                        metadata["message_id"],
                        {
                            "content": serialize_content_blocks(content_blocks),
                        },
                    )

                # Send a webhook notification if the user is not active 这个先留着吧 到时候万一要发通知呢
                if get_active_status_by_user_id(user.id) is None:
                    webhook_url = Users.get_user_webhook_url_by_id(user.id)
                    webhookContent = content.replace('%', '')
                    if webhook_url:
                        post_webhook(
                            request.app.state.WEBUI_NAME,
                            webhook_url,
                            f"{title} - {request.app.state.config.WEBUI_URL}/c/{metadata['chat_id']}\n\n{webhookContent}",
                            {
                                "action": "chat",
                                "message": webhookContent,
                                "title": title,
                                "url": f"{request.app.state.config.WEBUI_URL}/c/{metadata['chat_id']}",
                            },
                        )

                await background_tasks_handler()
            except (asyncio.CancelledError, ClientConnectionError) as e:
                log.warning(f"Task was cancelled!, status: {e}, stack: {traceback.format_exc()}")
                #  这里已经完成了，咱们就结束了吧。
                if not has_clear_user_limit:
                    has_clear_user_limit = True
                    await bg_manager.release_user_limit(user.id,session_id)
                if not isinstance(e, ClientConnectionError):
                    await event_emitter({"type": "task-cancelled"})

                # --- 取消时的清理逻辑 ---
                log.info("Task cancelled. Final cleanup and send.")

                # 检查最后一个 reasoning 块是否未结束
                last_reasoning_idx = -1
                for i in range(len(content_blocks) - 1, -1, -1):
                    if content_blocks[i]["type"] == "reasoning":
                        last_reasoning_idx = i
                        break

                if last_reasoning_idx != -1 and "ended_at" not in content_blocks[last_reasoning_idx]:
                    log.warning("Task cancelled with an unclosed reasoning block. Closing it.")
                    content_blocks[last_reasoning_idx]["ended_at"] = time.time()
                    if "started_at" in content_blocks[last_reasoning_idx]:
                        content_blocks[last_reasoning_idx]["duration"] = int(content_blocks[last_reasoning_idx]["ended_at"] - content_blocks[last_reasoning_idx]["started_at"])

                # 清理 MCP 长连
                for writer in stream_mcp_writers.values():
                    await writer.close()

                # 发送最终完成状态
                if not isinstance(e, ClientConnectionError):
                    try:
                        final_content = serialize_content_blocks(content_blocks, raw=False)
                        await event_emitter({
                            "type": "chat:completion",
                            "data": {
                                "content": final_content,
                                "message_id": metadata.get("message_id"),
                                "done": True
                            },
                        },True)
                        log.info("Final update on cancelled task sent successfully.")
                    except Exception as e:
                        log.error(f"Sending final event after cancellation failed: {e}")

                if not ENABLE_REALTIME_CHAT_SAVE:
                    # Save message in the database
                    Chats.upsert_message_to_chat_by_id_and_message_id(
                        metadata["chat_id"],
                        metadata["message_id"],
                        {
                            "content": serialize_content_blocks(content_blocks),
                        },
                    )

            finally:
                # if request.state.mcp_servers:
                #     log.debug("清理 MCP 资源")
                #     await cleanup_mcp_servers(request.state.mcp_servers)
                #  这里已经完成了，咱们就结束了吧。
                if not has_clear_user_limit:
                    await bg_manager.release_user_limit(user.id,session_id)
                form_data["metadata"] = metadata

                if sensitive_result is None:
                    messages = form_data["messages"]
                    messages.append({"role": "assistant", "content": serialize_content_blocks(content_blocks)})
                    result = await chat_completed(request, {
                        **form_data,
                        "id": metadata["message_id"],
                        "messages": messages,
                        "chat_id": metadata["chat_id"],
                    }, user)
                    messages = result.get("messages", [])
                    Chats.upsert_message_to_chat_by_id_and_message_id(
                        metadata["chat_id"],
                        metadata["message_id"],
                        {
                            **(messages[-1])
                        },
                    )
                    # title = Chats.get_chat_title_by_id(metadata["chat_id"])
                    # TODO： 先不加 title 试试
                    # 这里虽然过安全会变，但本来就是全量更新的，所以无所谓了
                    await event_emitter(
                        {
                            "type": "chat:completion",
                            "data": {
                                **messages[-1],
                                "message_id": metadata.get("message_id"),
                                "done": True,
                                # "files": [
                                #     {
                                #         "name": "测试文件",
                                #         "type": "audio",
                                #         "url": "https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg",
                                #         "size": 1024,
                                #     }
                                # ],
                            },
                        },True
                    )

                await event_emitter(None)
            
            # 【pubsub】链路内结束，理论上外面的 cannel 接口调用过来，取消 task，也会走到这里的。
            await bg_manager.wait_and_cleanup(user.id,session_id)

            # 关闭敏感内容检测任务队列，让 handle_sensitive_tasks 任务正常结束
            await sensitive_tasks.close()

            if response.background is not None:
                await response.background()
        
        if not metadata.get("message_id"):
            log.error(f"【pubsub】metadata.get('message_id') 为空，{metadata}")
        sensitive_task_id = f"{metadata.get('message_id')}_sensitive"
        post_response_task_id = f"{metadata.get('message_id')}_post_response"
        
        # 创建异步任务
        await bg_manager.start_chat_session(user.id, session_id, metadata.get("chat_id"))
        create_task(handle_sensitive_tasks(), task_id=sensitive_task_id, model_id=model_id)
        create_task(post_response_handler(response,events), task_id=post_response_task_id, model_id=model_id)

        chat_requests_total.labels(model_id=model_id, status_code=200, env=ENV, error_type='').inc()

        return StreamingResponse(
            sse_channel_wrapper(),
            headers=dict(response.headers),
        )

    else:
        log.error("no event_emitter found")