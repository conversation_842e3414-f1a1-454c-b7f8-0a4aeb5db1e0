import aiohttp
from fastapi import HTTPException


from open_webui.env import SHARE_SERVER_URL


async def warm_share_desc(share_id: str, desc: str, title: str):
    warm_url = f"{SHARE_SERVER_URL}/desc/{share_id}"
    async with aiohttp.ClientSession() as session:
        async with session.post(warm_url, json={"input": desc, "title": title}) as response:
            if response.status != 200:
                raise HTTPException(
                    status_code=response.status,
                    detail="Failed to warm up desc prompt",
                )