

from open_webui.models.vibe_templates import VibeTemplates
import logging
log = logging.getLogger(__name__)



def make_default_prompt(content, vibe_info):
    """默认的硬编码模板，作为回退方案"""
    ppt_desc = ""
    vibe_reference = vibe_info.get("vibeReference",{})
    if vibe_info.get("vibeMode","") == "ppt":
        ppt_desc = f"需要修改第{vibe_reference.get('pptIndex',0)}页的"
    return f"""请按照以下要求修改HTML代码：

## 修改需求
{content}

## 目标代码的位置
{ppt_desc}第 {vibe_reference.get("line","未知")} 行代码涉及到的元素

## 需要修改的代码
```html
{vibe_reference.get("code","")}
```

## 输出要求
1. 请直接提供修改后的完整HTML代码
2. 保持原有的代码结构和缩进格式
3. 确保修改后的代码语法正确且功能完善
4. 只修改必要的部分，保持其他代码不变

请直接给我改好的完整代码即可，不要输出多余的内容，也不需要告诉我你修改了什么。
"""


def make_vibe_prompt(content, vibe_info):
    """使用数据库模板生成vibe提示"""
    vibe_mode = vibe_info.get("vibeMode", "")
    vibe_reference = vibe_info.get("vibeReference", {})
    
    # 根据vibe_mode选择对应模板类型
    template_type = None
    if vibe_mode == "ppt":
        template_type = "ppt_edit"
    elif vibe_mode == "artifacts":
        template_type = "artifacts_edit"
    
    # 如果没有匹配的模板类型，直接返回默认提示
    if not template_type:
        return make_default_prompt(content, vibe_info)
    
    # 获取激活的模板
    template = VibeTemplates.get_active_template_by_type(template_type)
    
    if not template:
        # 回退到默认硬编码模板
        return make_default_prompt(content, vibe_info)
    
    # 准备模板变量
    ppt_desc = ""
    if vibe_mode == "ppt":
        ppt_desc = f"需要修改第{vibe_reference.get('pptIndex', 0)}页的"
    
    template_vars = {
        "content": content,
        "ppt_desc": ppt_desc,
        "line": vibe_reference.get("line", "未知"),
        "code": vibe_reference.get("code", ""),
        "vibe_mode": vibe_mode,
        "ppt_index": vibe_reference.get("pptIndex", 0),
        "filename": vibe_reference.get("filename", ""),
        "column": vibe_reference.get("column", "")
    }
    
    try:
        # 使用模板内容和变量生成最终提示
        result =  template.content.format(**template_vars)
        log.debug(f"Vibe prompt: {result}")
        return result
    except KeyError as e:
        # 如果模板中使用了不存在的变量，回退到默认模板
        print(f"Template variable error: {e}")
        return make_default_prompt(content, vibe_info)
    except Exception as e:
        # 其他异常也回退到默认模板
        print(f"Template rendering error: {e}")
        return make_default_prompt(content, vibe_info)