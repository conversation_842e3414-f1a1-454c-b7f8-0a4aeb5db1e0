from prometheus_client import Counter, Histogram, Gauge

chat_requests_total = Counter(
    'chat_completions_requests_total',
    '聊天完成请求总数',
    ['model_id', 'status_code', 'env', 'error_type']
)

# 用于监控LLM生成tokens的间隔时间
event_emitter_interval = Histogram(
    'chat_event_emitter_interval_seconds',
    'LLM生成tokens的时间间隔',
    ['model_id', 'env'],
    buckets=(0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 1.0, 2.0, 5.0)
)

# 文件上传相关指标
file_upload_total = Counter(
    'file_upload_total',
    '文件上传总数',
    ['status', 'file_type', 'env']
)

file_upload_size_bytes = Histogram(
    'file_upload_size_bytes',
    '文件上传大小（字节）',
    ['file_type', 'env'],
    buckets=(1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824)  # 1KB to 1GB
)

file_upload_duration_seconds = Histogram(
    'file_upload_duration_seconds',
    '文件上传耗时（秒）',
    ['file_type', 'env'],
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0)
)

# 文件内容提取相关指标
file_extraction_total = Counter(
    'file_extraction_total',
    '文件内容提取总数',
    ['status', 'stage', 'env']
)

file_extraction_duration_seconds = Histogram(
    'file_extraction_duration_seconds',
    '文件内容提取各阶段耗时（秒）',
    ['stage', 'env'],
    buckets=(0.01, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0)
)

file_extraction_cache_hits = Counter(
    'file_extraction_cache_hits_total',
    '文件内容提取缓存命中次数',
    ['env']
)

file_extraction_cache_misses = Counter(
    'file_extraction_cache_misses_total',
    '文件内容提取缓存未命中次数',
    ['env']
)

# Zhipu AI API 相关指标
zhipuai_api_requests_total = Counter(
    'zhipuai_api_requests_total',
    'Zhipu AI API 请求总数',
    ['operation', 'status', 'env']
)

zhipuai_api_duration_seconds = Histogram(
    'zhipuai_api_duration_seconds',
    'Zhipu AI API 请求耗时（秒）',
    ['operation', 'env'],
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0)
)

# 用于记录token生成速

def get_file_type_category(content_type: str) -> str:
    """
    将文件的 content_type 转换为更通用的文件类型分类，用于指标标签

    Args:
        content_type: 文件的 MIME 类型

    Returns:
        文件类型分类字符串
    """
    if not content_type:
        return "unknown"

    content_type = content_type.lower()

    # 图片类型
    if content_type.startswith("image/"):
        return "image"

    # 文档类型
    if content_type in [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain",
        "text/markdown",
        "text/csv"
    ]:
        return "document"

    # 音频类型
    if content_type.startswith("audio/"):
        return "audio"

    # 视频类型
    if content_type.startswith("video/"):
        return "video"

    # 代码文件
    if content_type in [
        "text/x-python",
        "application/x-python-code",
        "text/x-java-source",
        "text/javascript",
        "application/javascript",
        "text/x-c",
        "text/x-c++",
        "text/html",
        "text/css",
        "application/json",
        "application/xml",
        "text/xml"
    ] or content_type.startswith("text/x-"):
        return "code"

    # 压缩文件
    if content_type in [
        "application/zip",
        "application/x-rar-compressed",
        "application/x-tar",
        "application/gzip",
        "application/x-7z-compressed"
    ]:
        return "archive"

    # 其他文本文件
    if content_type.startswith("text/"):
        return "text"

    # 其他应用程序文件
    if content_type.startswith("application/"):
        return "application"

    return "other"

# MCP 工具调用相关指标
mcp_tool_requests_total = Counter(
    'mcp_tool_requests_total',
    'MCP 工具调用请求总数',
    ['server_name', 'tool_name', 'status', 'env']
)

mcp_tool_duration_seconds = Histogram(
    'mcp_tool_duration_seconds', 
    'MCP 工具调用耗时（秒）',
    ['server_name', 'tool_name', 'env'],
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0)
)

mcp_tool_concurrent_executions = Gauge(
    'mcp_tool_concurrent_executions',
    '当前并发执行的MCP工具数量',
    ['server_name', 'tool_name', 'env']
)

mcp_tool_result_size_bytes = Histogram(
    'mcp_tool_result_size_bytes',
    'MCP 工具调用结果大小（字节）',
    ['server_name', 'tool_name', 'env'],
    buckets=(100, 1024, 10240, 102400, 1048576, 10485760, 104857600)  # 100B to 100MB
)

# 连接断开监控指标
connection_disconnects_total = Counter(
    'connection_disconnects_total',
    '连接断开总数',
    ['connection_type', 'error_type', 'model_id', 'env']
)

connection_timeout_total = Counter(
    'connection_timeout_total',
    '连接超时总数',
    ['connection_type', 'timeout_type', 'model_id', 'env']
)

connection_retry_total = Counter(
    'connection_retry_total',
    '连接重试总数',
    ['connection_type', 'retry_reason', 'model_id', 'env']
)

connection_duration_seconds = Histogram(
    'connection_duration_seconds',
    '连接持续时间（秒）',
    ['connection_type', 'model_id', 'env'],
    buckets=(1, 5, 10, 30, 60, 120, 300, 600, 1800, 3600)  # 1s to 1h
)



sse_heartbeat_sent_total = Counter(
    'sse_heartbeat_sent_total',
    'SSE心跳包发送总数',
    ['model_id', 'env']
)

# SSE 并发连接数监控指标
sse_concurrent_connections = Gauge(
    'sse_concurrent_connections',
    '当前并发SSE连接数',
    ['model_id', 'env']
)

# 后台任务监控指标
background_tasks_active = Gauge(
    'background_tasks_active',
    '当前活跃的后台任务数量',
    ['model_id', 'env']
)

# API响应时间监控

# 错误率监控

# 聊天流式响应相关指标
CHAT_STREAM_TTFT_SECONDS = Histogram(
    'chat_stream_ttft_seconds',
    'Histogram of Time To First Token for chat streaming responses',
    ['model_id', 'is_search', "env"],
)

CHAT_STREAM_TOKENS_PER_SECOND = Histogram(
    'chat_stream_tokens_per_second',
    'Histogram of Tokens Per Second for chat streaming responses',
    ['model_id', 'is_search', 'env']
)

# Converter 导出相关指标
converter_export_requests_total = Counter(
    'converter_export_requests_total',
    '导出请求总数',
    ['export_type', 'status', 'user_id', 'env']
)

converter_export_pages_total = Counter(
    'converter_export_pages_total',
    '导出页面总数',
    ['export_type', 'user_id', 'env']
)

converter_export_duration_seconds = Histogram(
    'converter_export_duration_seconds',
    '导出耗时（秒）',
    ['export_type', 'env'],
    buckets=(1, 5, 10, 30, 60, 120, 300, 600, 1800)  # 1s to 30min
)

converter_export_file_size_bytes = Histogram(
    'converter_export_file_size_bytes',
    '导出文件大小（字节）',
    ['export_type', 'env'],
    buckets=(1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824)  # 1KB to 1GB
)

CHAT_STREAM_MODEL_NO_REPLY_TOKEN = Counter(
    'chat_stream_model_no_reply_token',
    'Counter of tokens for chat streaming responses , model no reply',
    ['model_id', 'is_search', 'env']
)

CHAT_STREAM_SEND_TOKEN_PACK = Counter(
    'chat_stream_send_token_pack',
    'Counter of tokens for chat streaming responses , send token pack',
    ['model_id', 'is_search', 'env']
)

CHAT_STREAM_PROCESS_TIME = Histogram(
    'chat_stream_process_time',
    'Histogram of process time for chat streaming responses',
    ['model_id', 'is_search', 'env']
)

# MCP 服务器初始化相关指标
mcp_server_initialization_total = Counter(
    'mcp_server_initialization_total',
    'MCP 服务器初始化请求总数',
    ['status', 'env']
)

mcp_server_initialization_duration_seconds = Histogram(
    'mcp_server_initialization_duration_seconds',
    'MCP 服务器初始化耗时（秒）',
    ['env'],
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0)
)

mcp_individual_server_initialization_total = Counter(
    'mcp_individual_server_initialization_total',
    '单个 MCP 服务器初始化总数',
    ['server_id', 'status', 'env']
)
