import asyncio


class AsyncQueue:
    def __init__(self):
        self.queue = asyncio.Queue()

    async def put(self, item):
        await self.queue.put(item)

    async def get(self):
        return await self.queue.get()

    async def __aiter__(self):
        while True:
            item = await self.get()
            if item is None:
                break
            yield item

    async def close(self):
        await self.queue.put(None)
