from bs4 import BeautifulSoup


def extract_visible_text(html_content):
    soup = BeautifulSoup(html_content, "html.parser")

    # 移除不需要的元素，如script, style等
    for element in soup(
        ["script", "style", "meta", "link", "noscript", "head", "title"]
    ):
        element.decompose()

    # 获取所有可见文本
    text = soup.get_text(separator=" ", strip=True)

    # 合并多个空格为一个空格
    import re

    text = re.sub(r"\s+", " ", text)

    return text.strip()
