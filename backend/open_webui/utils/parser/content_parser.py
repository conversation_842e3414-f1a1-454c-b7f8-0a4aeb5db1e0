import re
import json
from typing import Dict, List, Any, Optional, Union, Literal

# 正则表达式模式
glm_block_regex = re.compile(r'(\n\s*)*<glm_block\s+[^>]*>.*?</glm_block>(\n\s*)*', re.IGNORECASE | re.DOTALL)
reasoning_regex = re.compile(r'(\n\s*)*<details\s+type="reasoning"[^>]*>.*?</details>(\n\s*)*', re.IGNORECASE | re.DOTALL)
code_interpreter_regex = re.compile(r'(\n\s*)*<details\s+type="code_interpreter"[^>]*>.*?</details>(\n\s*)*', re.IGNORECASE | re.DOTALL)

class MatchResult:
    def __init__(self, match_type: str, content: Any, index: int, match_text: str):
        self.type = match_type
        self.content = content
        self.index = index
        self.match_text = match_text

def match_first(input_text: str) -> Optional[MatchResult]:
    """查找第一个匹配的模式"""
    regexes = [
        {'regex': glm_block_regex, 'type': 'glm_block'},
        {'regex': reasoning_regex, 'type': 'reasoning'},
        {'regex': code_interpreter_regex, 'type': 'code_interpreter'}
    ]
    
    earliest_match = None
    earliest_index = float('inf')
    
    for regex_info in regexes:
        regex = regex_info['regex']
        match_type = regex_info['type']
        
        match = regex.search(input_text)
        if match and match.start() < earliest_index:
            earliest_index = match.start()
            current_type = match_type
            content = None
            
            if match_type == 'glm_block':
                # 提取标签内的内容并解析为 JSON
                inner_content_match = re.search(r'<glm_block[^>]*>(.*?)</glm_block>', match.group(0), re.IGNORECASE | re.DOTALL)
                if inner_content_match:
                    try:
                        content = json.loads(inner_content_match.group(1).strip())
                        if content and content.get('type') == 'mcp':
                            content = content.get('data', {}).get('metadata')
                            current_type = 'mcp'
                    except json.JSONDecodeError:
                        # JSON 解析失败时返回原始文本
                        content = inner_content_match.group(1).strip()
                        
            elif match_type == 'reasoning':
                # 提取标签内的内容
                inner_content_match = re.search(r'<details[^>]*>(.*?)</details>', match.group(0), re.IGNORECASE | re.DOTALL)
                if inner_content_match:
                    reasoning_content = inner_content_match.group(1).strip()
                    
                    # 去掉 summary 标签包裹的内容
                    reasoning_content = re.sub(r'<summary[^>]*>.*?</summary>', '', reasoning_content, flags=re.IGNORECASE | re.DOTALL)
                    
                    # 去掉每一行开头的 > 符号（markdown 引用格式）
                    lines = reasoning_content.split('\n')
                    cleaned_lines = []
                    for line in lines:
                        cleaned_line = re.sub(r'^\s*>\s?', '', line)
                        cleaned_lines.append(cleaned_line)
                    
                    reasoning_content = '\n'.join(cleaned_lines).strip()
                    content = f"<think>{reasoning_content}</think>"
            
            # code_interpreter 不处理，content 保持 None
            
            earliest_match = MatchResult(
                match_type=current_type,
                content=content,
                index=match.start(),
                match_text=match.group(0)
            )
    
    return earliest_match

class ResultItem:
    def __init__(self, content: Any, item_type: str):
        self.content = content
        self.type = item_type
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'content': self.content,
            'type': self.type
        }

def parse_content(input_text: str) -> List[Dict[str, Any]]:
    """解析输入文本，提取各种类型的内容块"""
    results = []
    position = 0
    max_counter = 100
    counter = 0
    
    while counter < max_counter:
        counter += 1
        这次匹配的结果 = match_first(input_text[position:])
        
        if not 这次匹配的结果:
            # 没有更多匹配，处理剩余的字符串
            剩下的字符串 = input_text[position:].strip()
            if 剩下的字符串:
                results.append(ResultItem(剩下的字符串, 'text').to_dict())
            break
        
        # 判断中间的字符串是不是 text
        中间的字符串 = input_text[position:position + 这次匹配的结果.index].strip()
        if 中间的字符串:
            results.append(ResultItem(中间的字符串, 'text').to_dict())
        
        # 判断各种类型
        if 这次匹配的结果.type != "code_interpreter":
            results.append(ResultItem(这次匹配的结果.content, 这次匹配的结果.type).to_dict())
        
        position += 这次匹配的结果.index + len(这次匹配的结果.match_text)
        
        if position >= len(input_text) - 1:
            break
    
    return results

# 测试函数（注释掉的部分）
def test():
    with open("input", "r", encoding="utf-8") as f:
        input_text = f.read()
    result = parse_content(input_text)
    print("🤡", json.dumps(result, indent=4, ensure_ascii=False))

# test() 