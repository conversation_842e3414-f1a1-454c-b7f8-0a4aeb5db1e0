from fastapi import Request

DEFAULT_LANG = "en"

def get_preferred_language(request: Request):
    accept_language = request.headers.get("accept-language")
    if accept_language:
        # 解析第一个语言（通常是首选语言）
        preferred_lang = accept_language.split(",")[0].split(";")[0].strip()
        return preferred_lang
    return DEFAULT_LANG

def is_zh_preferred(request: Request):
    return "zh" in get_preferred_language(request)
