import json
import logging
import websockets
import aiohttp
from websockets import ConnectionClosedError

from open_webui.config import WE<PERSON><PERSON>_URL
from fastapi import HTTPException

# 设置 websockets 库的日志级别为 WARNING，抑制 DEBUG 日志
logging.getLogger('websockets').setLevel(logging.WARNING)
logging.getLogger('websockets.protocol').setLevel(logging.WARNING)


class Writer:
    def __init__(self, function_name: str, conn):
        self.ws = conn
        self._closed = False
        self.failed = False

    async def write(self, delta: str):
        if self.failed:
            return {}
        payload = json.dumps({"arguments": delta})
        # TODO 这里做流式解析, key -> value delta
        await self.ws.send(payload)
        result = await self.ws.recv()
        return json.loads(result)

    async def retrieve(self):
        try:
            await self.ws.send("RETRIEVE")
            result = await self.ws.recv()
            return json.loads(result)
        except ConnectionClosedError as e:
            if e.rcvd and e.rcvd.code == 1008:
                return {"error": e.rcvd.reason}
            return {"error": "oops"}
        except Exception as e:
            return {"error": str(e)}

    async def close(self):
        if self._closed:
            return
        await self.ws.close()
        self._closed = True

async def connect(
    url: str,
    user_id: str,
    chat_id: str,
    message_id: str,
    function: dict,
    lang: str = "en",
    trace_id: str = "",
):
    function_name = function["name"]
    additional_headers = {
        "X-Chat-Id": chat_id,
        "X-User-Id": user_id,
        "X-Message-Id": message_id,
        "X-Language": lang,
        "X-Trace-Id": trace_id,
    }

    conn = await websockets.connect(f"ws://{url}/ws/{function_name}", additional_headers=additional_headers)
    return Writer(function_name, conn)

async def share(ppt_server_url: str, share_id: str, user_id: str, chat_id: str, version: str = "latest", update: bool = False) -> str:
    headers = {
        "X-User-Id": user_id,
        "X-Chat-Id": chat_id,
    }
    async with aiohttp.ClientSession() as session:
        url = ppt_server_url[:-len("/mcp")] + f"/share/{share_id}?v={version}"
        do = session.patch if update else session.post
        async with do(url=url, headers=headers) as response:
            if response.status != 200:
                raise HTTPException(
                    status_code=response.status,
                    detail="Failed to deploy ppt",
                )
            res = await response.json()
            if "id" in res:
                domain = "http://localhost:8080" if "localhost" in WEBUI_URL.value else WEBUI_URL
                return f"{domain}/api/p/{res['id']}"
            return ""

async def delete_shared(ppt_server_url: str, share_id: str):
    async with aiohttp.ClientSession() as session:
        url = ppt_server_url.rstrip("/mcp") + "/share/" + share_id
        async with session.delete(url) as response:
            if response.status not in (200, 404):
                raise HTTPException(
                    status_code=response.status,
                    detail="Failed to delete ppt",
                )

async def clone_chat(ppt_server_url: str, new_chat_id: str, user_id: str, chat_id: str):
    async with aiohttp.ClientSession() as session:
        url = ppt_server_url.rstrip("/mcp") + "/chat_clone/" + new_chat_id
        headers = {
            "X-User-Id": user_id,
            "X-Chat-Id": chat_id,
        }
        async with session.post(url, headers=headers) as response:
            if response.status not in (200, 404):
                raise HTTPException(
                    status_code=response.status,
                    detail="Failed to clone chat",
                )

async def share_chat(ppt_server_url: str, share_id: str, user_id: str, chat_id: str):
    async with aiohttp.ClientSession() as session:
        url = ppt_server_url.rstrip("/mcp") + "/chat_share/" + share_id
        headers = {
            "X-User-Id": user_id,
            "X-Chat-Id": chat_id,
        }
        async with session.post(url, headers=headers) as response:
            if response.status not in (200, 404):
                raise HTTPException(
                    status_code=response.status,
                    detail="Failed to share chat",
                )