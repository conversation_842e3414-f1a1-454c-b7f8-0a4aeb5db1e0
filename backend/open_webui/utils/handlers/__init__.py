"""
Chat handler modules for Open WebUI.

This package contains specialized handlers for different types of chat completion tasks:
- tools_handler: Handles tool/function calling
- web_search_handler: Handles web search integration
- image_generation_handler: Handles image generation requests
- files_handler: Handles file processing and RAG functionality
"""

from .tools_handler import chat_completion_tools_handler
from .web_search_handler import chat_web_search_handler
from .image_generation_handler import chat_image_generation_handler
from .files_handler import chat_completion_files_handler

__all__ = [
    "chat_completion_tools_handler",
    "chat_web_search_handler", 
    "chat_image_generation_handler",
    "chat_completion_files_handler",
]