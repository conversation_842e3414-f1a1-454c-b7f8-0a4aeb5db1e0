## 老的handler 防止拆出来拆错了 比对用， 逻辑太tm多了 沃日

#    # 这个函数定义应该放在 post_response_handler 内部
#                 async def stream_body_handler_zero(response):
#                     nonlocal content_blocks # 确保 content_blocks 可访问

#                     citation_map: Dict[int, Dict[str, str]] = {}

#                     chunk_idx = 0
#                     BOOT_START_CHUNK = 2

#                     # --- 实例化自定义解析器 ---
#                     think_parser = TagStreamParser(start_tag="<think>", end_tag="</think>")
#                     current_reasoning_block = None  # 跟踪活动的推理块

#                     #  metrics

#                     first_token_received = False
#                     first_token_time = None
#                     total_completion_tokens = 0
#                     first_chunk_sent = False
                    

#                     log.info(f"[Request Timeline] [stream body handler zero (Custom Parser)] start parse response")

#                     async for line in response.body_iterator:
#                         zero_need_full_update = False
#                         zero_phase = "other"
#                         line_bytes: bytes = line if isinstance(line, bytes) else line.encode("utf-8")
#                         line_str: str = line_bytes.decode("utf-8")

#                         if not line_str.strip():
#                             continue
#                         if not line_str.startswith("data:"):
#                             # log.warning(f"[ZERO HANDLER] Received non-data line: {line_str}") # 可选日志
#                             continue
#                         data_str: str = line_str[len("data:") :].strip()
#                         if data_str == "[DONE]":
#                             log.info("[ZERO HANDLER] Received [DONE] signal.")
#                             break
#                         if not data_str:
#                             continue

#                         if sensitive_result is not None:
#                             # 清空解析器缓冲区
#                             think_parser.flush()
#                             return
#                         try:
#                             data: Dict[str, Any] = json.loads(data_str)
#                             chunk_idx += 1
#                             if data.get("message", {}).get("status", None) is not None:
#                                 message: Dict[str, Any] = data.get("message", {})
#                                 status: str = message.get("status", "") # "in_progress" or "completed"
#                                 content_data: Dict[str, Any] = message.get("content", {})
#                                 content_type: str = content_data.get("content_type", "")
#                                 content_text: str = content_data.get("content", "") # 完整或增量内容
#                                 author: Dict[str, Any] = message.get("author", {})
#                                 author_role: str = author.get("role", "")

#                                 current_processed: bool = False

#                                 # --- 1. 使用自定义解析器处理 content_type == "text" ---
#                                 if content_type == "text" and author_role == "assistant":
#                                     if not first_token_received:
#                                         log.debug(f"[Request Timeline] [stream body handler zero] First token received")
#                                         ttft = time.time() - request_start_time
#                                         first_token_time = time.time()
#                                         CHAT_STREAM_TTFT_SECONDS.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).observe(ttft)
#                                         first_token_received = True

#                                     if status == "completed":
#                                         zero_need_full_update = True
#                                         zero_phase = "answer"
#                                         # 处理最终文本块并清空
#                                         final_elements = list(think_parser.parse_chunk(content_text))
#                                         # 清空解析器中剩余缓冲区
#                                         final_elements.extend(list(think_parser.flush()))

#                                         # 解析使用数据（如果可用）并覆盖 total_completion_tokens
#                                         if (
#                                             completion_tokens := message.get(
#                                                 "content", {}
#                                             )
#                                             .get("usage", {})
#                                             .get("completion_tokens")
#                                         ) is not None:
#                                             total_completion_tokens = completion_tokens  # 覆盖累积的token
#                                             log.debug(
#                                                 f"[ZERO HANDLER][COMPLETED][TEXT] total_completion_tokens with reported value: {total_completion_tokens}"
#                                             )

#                                         # 根据最终元素更新content_blocks
#                                         for element_type, element_content in final_elements:
#                                             if element_type == "text":
#                                                 if not content_blocks or content_blocks[-1]["type"] != "text":
#                                                     content_blocks.append({"type": "text", "content": element_content})
#                                                 else:
#                                                     content_blocks[-1]["content"] = element_content  # 替换内容而不是追加
#                                             elif element_type == "think":
#                                                 if not content_blocks or content_blocks[-1]["type"] != "reasoning":
#                                                     # 查找最后一个reasoning块
#                                                     last_reasoning_idx = -1
#                                                     for i in range(len(content_blocks) - 1, -1, -1):
#                                                         if content_blocks[i]["type"] == "reasoning":
#                                                             last_reasoning_idx = i
#                                                             break

#                                                     if last_reasoning_idx != -1:
#                                                         # 更新现有的reasoning块
#                                                         content_blocks[last_reasoning_idx]["content"] = element_content
#                                                         content_blocks[last_reasoning_idx]["ended_at"] = time.time()
#                                                         if "started_at" in content_blocks[last_reasoning_idx]:
#                                                             content_blocks[last_reasoning_idx]["duration"] = int(content_blocks[last_reasoning_idx]["ended_at"] - content_blocks[last_reasoning_idx]["started_at"])
#                                                     else:
#                                                         # 创建一个新的reasoning块
#                                                         now = time.time()
#                                                         content_blocks.append({
#                                                             "type": "reasoning", "start_tag": "think", "end_tag": "/think",
#                                                             "attributes": {}, "content": element_content,
#                                                             "started_at": now, "ended_at": now, "duration": 0
#                                                         })
#                                     else: # status == "in_progress"
#                                         # 使用自定义解析器实例解析
#                                         for element_type, element_content in think_parser.parse_chunk(content_text):
#                                             if element_type == "text":
#                                                 zero_phase = "answer"
#                                                 # 处理普通文本内容
#                                                 if not content_blocks or content_blocks[-1]["type"] != "text":
#                                                     content_blocks.append({"type": "text", "content": element_content})
#                                                 else:
#                                                     content_blocks[-1]["content"] += element_content



#                                             elif element_type == "think": # 匹配self.tag_name
#                                                 zero_phase = "thinking"
#                                                 # 处理thinking内容
#                                                 if element_content:
#                                                     if not current_reasoning_block:
#                                                         if not content_blocks or content_blocks[-1]["type"] != "reasoning":
#                                                             new_block = {
#                                                                 "type": "reasoning", "start_tag": "think", "end_tag": "/think",
#                                                                 "attributes": {}, "content": element_content, "started_at": time.time()
#                                                             }
#                                                             content_blocks.append(new_block)
#                                                             current_reasoning_block = new_block
#                                                         else:
#                                                             # 追加到最后一个reasoning块
#                                                             content_blocks[-1]["content"] += element_content
#                                                             current_reasoning_block = content_blocks[-1]

#                                                     else:
#                                                         current_reasoning_block["content"] += element_content


#                                         # 检查解析器是否在此块中退出标签
#                                         if current_reasoning_block and not think_parser.is_in_tag():
#                                             zero_phase = "answer"
#                                             zero_need_full_update = True
#                                             # 结束推理块 - 标记结束时间/持续时间
#                                             current_reasoning_block["content"] = current_reasoning_block["content"].strip()
#                                             current_reasoning_block["ended_at"] = time.time()
#                                             # 计算持续时间
#                                             if "started_at" in current_reasoning_block:
#                                                 current_reasoning_block["duration"] = int(current_reasoning_block["ended_at"] - current_reasoning_block["started_at"])
#                                             current_reasoning_block = None  # 停止跟踪
#                                     for block in content_blocks:
#                                         if block["type"] == "reasoning" or block["type"] == "text":
#                                             block["content"] = replace_citations_in_text(block["content"], citation_map)
#                                     current_processed = True

#                                     await sensitive_tasks.put({
#                                         "input_type": "output",
#                                         "text": content_blocks[-1].get("content", ""),
#                                         "model": model
#                                     })

#                                 # --- 2. 处理 content_type == "code" (工具调用) ---
#                                 elif content_type == "code" and author_role == "assistant":
#                                     zero_phase = "tool_call"
#                                     zero_need_full_update = True
#                                     if status == "completed":
#                                         # 使用辅助函数处理工具调用
#                                         success, tool_info = process_tool_call(
#                                             code_text=content_text,
#                                             content_blocks=content_blocks
#                                         )

#                                         if not success:
#                                             log.error(f"[ZERO HANDLER] 处理工具调用失败: {tool_info.get('error', '未知错误')}")

#                                     current_processed = True

#                                 # --- 3. 处理 content_type == "quote_result" (生成 tool_result 块) ---
#                                 elif content_type == "quote_result" and author_role == "tool":
#                                     zero_phase = "tool_result"
#                                     zero_need_full_update = True
#                                     if status == "completed":
#                                         # 使用辅助函数处理引用结果，简化参数
#                                         citation_map = process_quote_result(
#                                             message=message,
#                                             content_blocks=content_blocks,
#                                             citation_map=citation_map
#                                         )

#                                     titles = "\n".join([block["title"] for block in content_blocks[-1]["data"]["data"]["result"]])
#                                     links = "\n".join([block["url"] for block in content_blocks[-1]["data"]["data"]["result"]])
#                                     await sensitive_tasks.put({
#                                         "input_type": "output",
#                                         "check_type": "search_text",
#                                         "text": titles,
#                                         "model": model
#                                     })
#                                     # await sensitive_tasks.put({
#                                     #     "input_type": "output",
#                                     #     "check_type": "search_link",
#                                     #     "text": links,
#                                     #     "model": model
#                                     # })

#                                     current_processed = True

#                                 # --- 更新前端 ---
#                                 if current_processed:
#                                     if chunk_idx >= BOOT_START_CHUNK: # 使用外部定义的常量
#                                         try:
#                                             if not first_chunk_sent:
#                                                 first_chunk_sent = True
#                                                 log.info(f"[Request Timeline] [stream body handler zero] first chunk sent")
#                                             serialized_data = serialize_content_blocks(content_blocks, raw=False) # 使用外部函数
#                                             await event_emitter({
#                                                     "type": "chat:completion",
#                                                     "data": {
#                                                         "content": serialized_data,
#                                                     },
#                                                 },zero_need_full_update,zero_phase)
#                                             CHAT_STREAM_SEND_TOKEN_PACK.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).inc()
#                                         except Exception as e:
#                                             log.error(f"[ZERO HANDLER] Serialize or send event failed: {e}") # 使用外部 log
#                                     continue # 跳过回退逻辑

#                             elif data.get("code") and data.get("error"):
#                                 if await handle_stream_error(data, metadata, event_emitter):
#                                     return
#                         except json.JSONDecodeError:
#                             log.warning(f"[ZERO HANDLER] JSON decode failed: {data_str}")
#                         except Exception as e:
#                             log.error(f"[ZERO HANDLER] Error processing chunk: {e} - Data: {data_str}", exc_info=True)

#                     # --- 流结束 ---
#                     log.info("stream body handler zero (Custom Parser) end parse response")

#                     # 解析器的最终清空
#                     remaining_elements = list(think_parser.flush())
#                     if remaining_elements:
#                         for element_type, element_content in remaining_elements:
#                             if element_type == "text":
#                                 processed_text = replace_citations_in_text(element_content, citation_map)
#                                 if processed_text:
#                                     if not content_blocks or content_blocks[-1]["type"] != "text":
#                                         content_blocks.append({"type": "text", "content": processed_text})
#                                     else:
#                                         content_blocks[-1]["content"] += processed_text
#                             elif element_type == "think":
#                                 processed_think = replace_citations_in_text(element_content, citation_map)
#                                 if processed_think and current_reasoning_block:
#                                     current_reasoning_block["content"] += processed_think

#                     # 明确结束任何未关闭的reasoning块
#                     if current_reasoning_block and "ended_at" not in current_reasoning_block:
#                         current_reasoning_block["content"] = current_reasoning_block["content"].strip()
#                         current_reasoning_block["ended_at"] = time.time()
#                         if "started_at" in current_reasoning_block:
#                             current_reasoning_block["duration"] = int(current_reasoning_block["ended_at"] - current_reasoning_block["started_at"])

#                     # 指标计算
#                     if first_token_time is not None:
#                         total_duration = time.time() - first_token_time
#                         if total_duration > 0 and total_completion_tokens > 0:
#                             tokenTPS = total_completion_tokens / total_duration
#                             CHAT_STREAM_TOKENS_PER_SECOND.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).observe(tokenTPS)
#                             CHAT_STREAM_PROCESS_TIME.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).observe(total_duration)
#                     else:
#                         log.debug(f"stream body handler model no reply, model_id={selected_model_id}, is_search={metadata.get('features', {}).get('auto_web_search', False)}")
#                         CHAT_STREAM_MODEL_NO_REPLY_TOKEN.labels(model_id=selected_model_id, is_search=metadata.get("features", {}).get("auto_web_search", False), env=ENV).inc()

#                     # 发送最终完成状态
#                     try:
#                         # 清理最后一个文本块的尾随空白
#                         if content_blocks and content_blocks[-1]["type"] == "text":
#                             content_blocks[-1]["content"] = content_blocks[-1]["content"].rstrip()

#                         final_content = serialize_content_blocks(content_blocks, raw=False)
#                         await event_emitter({
#                             "type": "chat:completion",
#                             "data": {
#                                 "content": final_content,
#                                 "message_id": metadata.get("message_id"),
#                                 "done": True
#                             },
#                         },True)
#                         log.info("[ZERO HANDLER] Final update sent successfully.")
#                     except Exception as e:
#                         log.error(f"[ZERO HANDLER] Sending final event failed: {e}")

#                     log.info("[ZERO HANDLER] Stream processing complete.")
