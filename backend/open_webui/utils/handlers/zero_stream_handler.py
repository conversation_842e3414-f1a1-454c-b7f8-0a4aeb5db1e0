"""Zero 模型流式响应处理器。

该模块包含专门用于处理 Zero 模型流式响应的处理器函数。
从 middleware.py 中拆分出来以提高代码可维护性。
"""

import time
import json
import logging
import re
from typing import Dict, List, Any, Callable

from open_webui.utils.tag_stream_parser import TagStreamParser
from open_webui.utils.stream_handler_helpers import (
    process_quote_result,
    process_tool_call,
)
from open_webui.utils.metrics import (
    CHAT_STREAM_SEND_TOKEN_PACK,
)
from open_webui.utils.handlers.metrics_handler import (
    create_metrics_tracker,
    should_record_first_token,
    extract_completion_tokens_from_message,
)
from open_webui.env import ENV

# 设置日志记录
log = logging.getLogger(__name__)


def replace_citations_in_text(text: str, citation_map: Dict[int, Dict[str, str]]) -> tuple[str, bool]:
    """替换文本中的引用标记为 Markdown 链接。

    Args:
        text: 包含引用标记的文本
        citation_map: 引用映射字典

    Returns:
        tuple: (替换后的文本, 是否发生了替换)
    """

    def repl_func(match):
        try:
            # 提取匹配到的数字（引用索引）
            index = int(match.group(1))
            # 在 citation_map 中查找
            if index in citation_map:
                citation_info = citation_map[index]
                url = citation_info.get("url", "#")  # 获取 URL，找不到则用 #
                # 格式化为 Markdown 链接，链接文本直接用索引数字
                return f'[[{index}]({url})]'
            else:
                # 如果 citation_map 中还没有这个索引的信息，返回空字符串
                return ""
        except (ValueError, KeyError, IndexError):
            # 转换或查找出错，返回空字符串
            return ""

    # 定义正则表达式，匹配 【数字†source】 格式
    pattern = r'【(\d+)†source】'
    # 执行替换并返回结果
    replaced_text = re.sub(pattern, repl_func, text)
    # 检查是否发生了替换
    has_replacement = replaced_text != text
    return replaced_text, has_replacement


async def stream_body_handler_zero(
    response,
    content_blocks: List[Dict[str, Any]],
    metadata: Dict[str, Any],
    selected_model_id: str,
    request_start_time: float,
    event_emitter: Callable,
    sensitive_tasks,
    model: Dict[str, Any],
    serialize_content_blocks: Callable,
    sensitive_checker: Callable[[], Any] = None,
) -> None:
    """处理 Zero 模型的流式响应。

    Args:
        response: 响应对象
        content_blocks: 内容块列表
        metadata: 元数据字典
        selected_model_id: 选中的模型ID
        request_start_time: 请求开始时间
        event_emitter: 事件发射器函数
        sensitive_tasks: 敏感内容检测任务队列
        model: 模型信息
        serialize_content_blocks: 序列化内容块的函数
        sensitive_checker: 敏感内容检测回调函数，返回当前敏感检测结果
    """
    citation_map: Dict[int, Dict[str, str]] = {}

    chunk_idx = 0
    BOOT_START_CHUNK = 2

    # --- 实例化自定义解析器 ---
    think_parser = TagStreamParser(start_tag="<think>", end_tag="</think>")

    # 使用字典包装状态变量以确保在辅助函数中的修改能够传回主函数
    state = {
        "current_reasoning_block": None,  # 跟踪活动的推理块
        "first_chunk_sent": False
    }

    # 创建指标跟踪器
    metrics_tracker = create_metrics_tracker(
        selected_model_id, metadata, request_start_time
    )

    log.info("[Request Timeline] [stream body handler zero (Custom Parser)] start parse response")

    async for line in response.body_iterator:
        # 检查敏感内容检测结果
        if sensitive_checker is not None:
            current_sensitive_result = sensitive_checker()
            if current_sensitive_result is not None:
                # 清空解析器缓冲区
                think_parser.flush()
                return

        zero_need_full_update = False
        zero_phase = "other"
        line_bytes: bytes = line if isinstance(line, bytes) else line.encode("utf-8")
        line_str: str = line_bytes.decode("utf-8")

        if not line_str.strip():
            continue
        if not line_str.startswith("data:"):
            continue
        data_str: str = line_str[len("data:") :].strip()
        if data_str == "[DONE]":
            break
        if not data_str:
            continue

        try:
            data: Dict[str, Any] = json.loads(data_str)
            chunk_idx += 1
            if data.get("message", {}).get("status", None) is not None:
                message: Dict[str, Any] = data.get("message", {})
                status: str = message.get("status", "")  # "in_progress" or "completed"
                content_data: Dict[str, Any] = message.get("content", {})
                content_type: str = content_data.get("content_type", "")
                content_text: str = content_data.get("content", "")  # 完整或增量内容
                author: Dict[str, Any] = message.get("author", {})
                author_role: str = author.get("role", "")

                current_processed: bool = False

                # --- 1. 使用自定义解析器处理 content_type == "text" ---
                if content_type == "text" and author_role == "assistant":
                    if should_record_first_token(content_type, author_role):
                        metrics_tracker.record_first_token()

                    zero_need_full_update, zero_phase = await _handle_text_content(
                        status, content_text, think_parser, state,
                        content_blocks, message, citation_map, zero_need_full_update, zero_phase,
                        metrics_tracker
                    )
                    current_processed = True

                    await sensitive_tasks.put({
                        "input_type": "output",
                        "text": content_blocks[-1].get("content", ""),
                        "model": model
                    })

                # --- 2. 处理 content_type == "code" (工具调用) ---
                elif content_type == "code" and author_role == "assistant":
                    zero_phase = "tool_call"
                    zero_need_full_update = True
                    if status == "completed":
                        # 使用辅助函数处理工具调用
                        success, tool_info = process_tool_call(
                            code_text=content_text,
                            content_blocks=content_blocks
                        )

                        if not success:
                            log.error(f"[ZERO HANDLER] 处理工具调用失败: {tool_info.get('error', '未知错误')}")

                    current_processed = True

                # --- 3. 处理 content_type == "quote_result" (生成 tool_result 块) ---
                elif content_type == "quote_result" and author_role == "tool":
                    zero_phase = "tool_result"
                    zero_need_full_update = True
                    if status == "completed":
                        # 使用辅助函数处理引用结果，简化参数
                        citation_map = process_quote_result(
                            message=message,
                            content_blocks=content_blocks,
                            citation_map=citation_map
                        )

                    await _handle_quote_result_sensitive_check(
                        content_blocks, sensitive_tasks, model
                    )
                    current_processed = True

                # --- 更新前端 ---
                if current_processed:
                    await _send_update_to_frontend(
                        chunk_idx, BOOT_START_CHUNK, state, content_blocks,
                        event_emitter, zero_need_full_update, zero_phase,
                        selected_model_id, metadata, serialize_content_blocks
                    )
                    continue  # 跳过回退逻辑

            elif data.get("code") and data.get("error"):
                from open_webui.utils.middleware import handle_stream_error
                if await handle_stream_error(data, metadata, event_emitter):
                    return
        except json.JSONDecodeError:
            log.warning(f"[ZERO HANDLER] JSON decode failed: {data_str}")
        except Exception as e:
            log.error(f"[ZERO HANDLER] Error processing chunk: {e} - Data: {data_str}", exc_info=True)

    # --- 流结束处理 ---
    await _handle_stream_end(
        think_parser, state, content_blocks, citation_map,
        metrics_tracker, selected_model_id, metadata,
        event_emitter, serialize_content_blocks
    )


async def _handle_text_content(
    status: str, content_text: str, think_parser: TagStreamParser,
    state: Dict, content_blocks: List[Dict],
    message: Dict, citation_map: Dict, zero_need_full_update: bool, zero_phase: str,
    metrics_tracker
) -> tuple[bool, str]:
    """处理文本内容的辅助函数。

    Args:
        status: 消息状态
        content_text: 内容文本
        think_parser: 思考解析器
        state: 状态字典，包含 current_reasoning_block 等
        content_blocks: 内容块列表
        message: 消息对象
        citation_map: 引用映射
        zero_need_full_update: 是否需要完整更新
        zero_phase: 当前阶段
        metrics_tracker: 指标跟踪器

    Returns:
        tuple: (zero_need_full_update, zero_phase)
    """
    if status == "completed":
        zero_need_full_update = True
        zero_phase = "answer"
        # 处理最终文本块并清空
        final_elements = list(think_parser.parse_chunk(content_text))
        # 清空解析器中剩余缓冲区
        final_elements.extend(list(think_parser.flush()))

        # 解析使用数据（如果可用）并更新 completion_tokens
        completion_tokens = extract_completion_tokens_from_message(message)
        if completion_tokens is not None:
            # 使用 metrics_tracker 设置 completion tokens
            metrics_tracker.set_completion_tokens(completion_tokens)
            log.debug(
                f"[ZERO HANDLER][COMPLETED][TEXT] Set completion_tokens to: {completion_tokens}"
            )

        # 根据最终元素更新content_blocks
        for element_type, element_content in final_elements:
            if element_type == "text":
                if not content_blocks or content_blocks[-1]["type"] != "text":
                    content_blocks.append({"type": "text", "content": element_content})
                else:
                    content_blocks[-1]["content"] = element_content  # 替换内容而不是追加
            elif element_type == "think":
                if not content_blocks or content_blocks[-1]["type"] != "reasoning":
                    # 查找最后一个reasoning块
                    last_reasoning_idx = -1
                    for i in range(len(content_blocks) - 1, -1, -1):
                        if content_blocks[i]["type"] == "reasoning":
                            last_reasoning_idx = i
                            break

                    if last_reasoning_idx != -1:
                        # 更新现有的reasoning块
                        content_blocks[last_reasoning_idx]["content"] = element_content
                        content_blocks[last_reasoning_idx]["ended_at"] = time.time()
                        if "started_at" in content_blocks[last_reasoning_idx]:
                            content_blocks[last_reasoning_idx]["duration"] = int(
                                content_blocks[last_reasoning_idx]["ended_at"] -
                                content_blocks[last_reasoning_idx]["started_at"]
                            )
                    else:
                        # 创建一个新的reasoning块
                        now = time.time()
                        content_blocks.append({
                            "type": "reasoning", "start_tag": "think", "end_tag": "/think",
                            "attributes": {}, "content": element_content,
                            "started_at": now, "ended_at": now, "duration": 0
                        })
    else:  # status == "in_progress"
        # 使用自定义解析器实例解析
        for element_type, element_content in think_parser.parse_chunk(content_text):
            if element_type == "text":
                zero_phase = "answer"
                # 处理普通文本内容
                if not content_blocks or content_blocks[-1]["type"] != "text":
                    content_blocks.append({"type": "text", "content": element_content})
                else:
                    content_blocks[-1]["content"] += element_content

            elif element_type == "think":  # 匹配self.tag_name
                zero_phase = "thinking"
                # 处理thinking内容
                if element_content:
                    if not state["current_reasoning_block"]:
                        if not content_blocks or content_blocks[-1]["type"] != "reasoning":
                            new_block = {
                                "type": "reasoning", "start_tag": "think", "end_tag": "/think",
                                "attributes": {}, "content": element_content, "started_at": time.time()
                            }
                            content_blocks.append(new_block)
                            state["current_reasoning_block"] = new_block
                        else:
                            # 追加到最后一个reasoning块
                            content_blocks[-1]["content"] += element_content
                            state["current_reasoning_block"] = content_blocks[-1]
                    else:
                        state["current_reasoning_block"]["content"] += element_content

        # 检查解析器是否在此块中退出标签
        if state["current_reasoning_block"] and not think_parser.is_in_tag():
            zero_phase = "answer"
            zero_need_full_update = True
            # 结束推理块 - 标记结束时间/持续时间
            state["current_reasoning_block"]["content"] = state["current_reasoning_block"]["content"].strip()
            state["current_reasoning_block"]["ended_at"] = time.time()
            # 计算持续时间
            if "started_at" in state["current_reasoning_block"]:
                state["current_reasoning_block"]["duration"] = int(
                    state["current_reasoning_block"]["ended_at"] - state["current_reasoning_block"]["started_at"]
                )
            state["current_reasoning_block"] = None  # 停止跟踪

    # 替换引用
    for block in content_blocks:
        if block["type"] == "reasoning" or block["type"] == "text":
            replaced_content, has_replacement = replace_citations_in_text(block["content"], citation_map)
            block["content"] = replaced_content
            if has_replacement:
                zero_need_full_update = True

    return zero_need_full_update, zero_phase


async def _handle_quote_result_sensitive_check(
    content_blocks: List[Dict], sensitive_tasks, model: Dict
) -> None:
    """处理引用结果的敏感内容检测。"""
    titles = "\n".join([block["title"] for block in content_blocks[-1]["data"]["data"]["result"]])
    await sensitive_tasks.put({
        "input_type": "output",
        "check_type": "search_text",
        "text": titles,
        "model": model
    })


async def _send_update_to_frontend(
    chunk_idx: int, boot_start_chunk: int, state: Dict,
    content_blocks: List[Dict], event_emitter: Callable, zero_need_full_update: bool,
    zero_phase: str, selected_model_id: str, metadata: Dict, serialize_content_blocks: Callable
) -> None:
    """发送更新到前端的辅助函数。

    Args:
        chunk_idx: 当前块索引
        boot_start_chunk: 开始发送的块索引
        state: 状态字典，包含 first_chunk_sent 等
        content_blocks: 内容块列表
        event_emitter: 事件发射器
        zero_need_full_update: 是否需要完整更新
        zero_phase: 当前阶段
        selected_model_id: 模型ID
        metadata: 元数据
        serialize_content_blocks: 序列化函数
    """
    if chunk_idx >= boot_start_chunk:
        try:
            if not state["first_chunk_sent"]:
                state["first_chunk_sent"] = True
                log.info("[Request Timeline] [stream body handler zero] first chunk sent")
            serialized_data = serialize_content_blocks(content_blocks, raw=False)
            await event_emitter({
                "type": "chat:completion",
                "data": {
                    "content": serialized_data,
                },
            }, zero_need_full_update, zero_phase)
            CHAT_STREAM_SEND_TOKEN_PACK.labels(
                model_id=selected_model_id,
                is_search=metadata.get("features", {}).get("auto_web_search", False),
                env=ENV
            ).inc()
        except Exception as e:
            log.error(f"[ZERO HANDLER] Serialize or send event failed: {e}")


async def _handle_stream_end(
    think_parser: TagStreamParser, state: Dict,
    content_blocks: List[Dict], citation_map: Dict, metrics_tracker,
    selected_model_id: str, metadata: Dict,
    event_emitter: Callable, serialize_content_blocks: Callable
) -> None:
    """处理流结束的辅助函数。

    Args:
        think_parser: 思考解析器
        state: 状态字典，包含 current_reasoning_block 等
        content_blocks: 内容块列表
        citation_map: 引用映射
        metrics_tracker: 指标跟踪器
        selected_model_id: 模型ID
        metadata: 元数据
        event_emitter: 事件发射器
        serialize_content_blocks: 序列化函数
    """
    log.info("stream body handler zero (Custom Parser) end parse response")

    # 解析器的最终清空
    remaining_elements = list(think_parser.flush())
    if remaining_elements:
        for element_type, element_content in remaining_elements:
            if element_type == "text":
                processed_text, _ = replace_citations_in_text(element_content, citation_map)
                if processed_text:
                    if not content_blocks or content_blocks[-1]["type"] != "text":
                        content_blocks.append({"type": "text", "content": processed_text})
                    else:
                        content_blocks[-1]["content"] += processed_text
            elif element_type == "think":
                processed_think, _ = replace_citations_in_text(element_content, citation_map)
                if processed_think and state["current_reasoning_block"]:
                    state["current_reasoning_block"]["content"] += processed_think

    # 明确结束任何未关闭的reasoning块
    if state["current_reasoning_block"] and "ended_at" not in state["current_reasoning_block"]:
        state["current_reasoning_block"]["content"] = state["current_reasoning_block"]["content"].strip()
        state["current_reasoning_block"]["ended_at"] = time.time()
        if "started_at" in state["current_reasoning_block"]:
            state["current_reasoning_block"]["duration"] = int(
                state["current_reasoning_block"]["ended_at"] - state["current_reasoning_block"]["started_at"]
            )

    # 完成指标计算并上报
    metrics_tracker.finalize_metrics()

    # 发送最终完成状态
    try:
        # 清理最后一个文本块的尾随空白
        if content_blocks and content_blocks[-1]["type"] == "text":
            content_blocks[-1]["content"] = content_blocks[-1]["content"].rstrip()

        final_content = serialize_content_blocks(content_blocks, raw=False)
        await event_emitter({
            "type": "chat:completion",
            "data": {
                "content": final_content,
                "message_id": metadata.get("message_id"),
                "done": True
            },
        }, True)
        log.info("[ZERO HANDLER] Final update sent successfully.")
    except Exception as e:
        log.error(f"[ZERO HANDLER] Sending final event failed: {e}")

    log.info("[ZERO HANDLER] Stream processing complete.")
