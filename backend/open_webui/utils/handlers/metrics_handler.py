"""流式响应指标计算处理器。

该模块包含专门用于处理流式响应中的指标计算逻辑，包括 TTFT（Time To First Token）
和 TPS（Tokens Per Second）等指标的计算和上报。
从 middleware.py 中拆分出来以提高代码可维护性。
"""

import time
import logging
from typing import Dict, Any, Optional

from open_webui.utils.metrics import (
    CHAT_STREAM_TTFT_SECONDS,
    CHAT_STREAM_TOKENS_PER_SECOND,
    CHAT_STREAM_MODEL_NO_REPLY_TOKEN,
    CHAT_STREAM_PROCESS_TIME,
)
from open_webui.env import ENV

# 设置日志记录
log = logging.getLogger(__name__)


class StreamMetricsTracker:
    """流式响应指标跟踪器。
    
    用于跟踪和计算流式响应过程中的各种指标，包括：
    - TTFT (Time To First Token): 首次 token 接收时间
    - TPS (Tokens Per Second): token 处理速率
    - 总处理时间和 token 数量
    """
    
    def __init__(self, model_id: str, metadata: Dict[str, Any], request_start_time: float):
        """初始化指标跟踪器。
        
        Args:
            model_id: 模型ID
            metadata: 请求元数据
            request_start_time: 请求开始时间
        """
        self.model_id = model_id
        self.metadata = metadata
        self.request_start_time = request_start_time
        
        # 指标状态
        self.first_token_received = False
        self.first_token_time: Optional[float] = None
        self.total_completion_tokens = 0
        
        # 从元数据中提取搜索标识
        self.is_search = metadata.get("features", {}).get("auto_web_search", False)
    
    def record_first_token(self) -> None:
        """记录首次 token 接收。
        
        计算并上报 TTFT 指标。
        """
        if not self.first_token_received:
            self.first_token_received = True
            self.first_token_time = time.time()
            
            # 计算 TTFT
            ttft = self.first_token_time - self.request_start_time
            
            # 上报 TTFT 指标
            CHAT_STREAM_TTFT_SECONDS.labels(
                model_id=self.model_id,
                is_search=self.is_search,
                env=ENV
            ).observe(ttft)
            
            log.info(f"[Metrics] First token received for model {self.model_id}, TTFT: {ttft:.3f}s")
    
    def add_completion_tokens(self, tokens: int) -> None:
        """添加完成的 token 数量。
        
        Args:
            tokens: 新增的 token 数量
        """
        self.total_completion_tokens += tokens
        log.info(f"[Metrics] Added {tokens} tokens, total: {self.total_completion_tokens}")
    
    def set_completion_tokens(self, tokens: int) -> None:
        """设置总的完成 token 数量（覆盖模式）。
        
        Args:
            tokens: 总 token 数量
        """
        self.total_completion_tokens = tokens
        log.info(f"[Metrics] Set total tokens to: {self.total_completion_tokens}")
    
    def finalize_metrics(self) -> None:
        """完成指标计算并上报最终指标。
        
        计算并上报 TPS 和总处理时间指标。
        """
        if self.first_token_received and self.first_token_time is not None:
            # 计算总处理时间
            total_duration = time.time() - self.first_token_time
            
            if total_duration > 0 and self.total_completion_tokens > 0:
                # 计算 TPS
                token_tps = self.total_completion_tokens / total_duration
                
                # 上报 TPS 指标
                CHAT_STREAM_TOKENS_PER_SECOND.labels(
                    model_id=self.model_id,
                    is_search=self.is_search,
                    env=ENV
                ).observe(token_tps)
                
                # 上报处理时间指标
                CHAT_STREAM_PROCESS_TIME.labels(
                    model_id=self.model_id,
                    is_search=self.is_search,
                    env=ENV
                ).observe(total_duration)
                
                log.info(
                    f"[Metrics] Finalized metrics for model {self.model_id}: "
                    f"TPS={token_tps:.2f}, Duration={total_duration:.3f}s, "
                    f"Tokens={self.total_completion_tokens}"
                )
            else:
                log.info(
                    f"[Metrics] Invalid metrics data for model {self.model_id}: "
                    f"Duration={total_duration}, Tokens={self.total_completion_tokens}"
                )
        else:
            # 模型没有回复任何 token
            CHAT_STREAM_MODEL_NO_REPLY_TOKEN.labels(
                model_id=self.model_id,
                is_search=self.is_search,
                env=ENV
            ).inc()
            
            log.info(f"[Metrics] Model {self.model_id} did not reply with any tokens")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要。
        
        Returns:
            包含当前指标状态的字典
        """
        duration = None
        tps = None
        
        if self.first_token_received and self.first_token_time is not None:
            duration = time.time() - self.first_token_time
            if duration > 0 and self.total_completion_tokens > 0:
                tps = self.total_completion_tokens / duration
        
        return {
            "model_id": self.model_id,
            "first_token_received": self.first_token_received,
            "total_completion_tokens": self.total_completion_tokens,
            "duration": duration,
            "tps": tps,
            "is_search": self.is_search,
        }


def extract_completion_tokens_from_message(message: Dict[str, Any]) -> Optional[int]:
    """从消息中提取完成的 token 数量。
    
    Args:
        message: 消息对象
        
    Returns:
        提取到的 token 数量，如果没有找到则返回 None
    """
    try:
        return (
            message.get("content", {})
            .get("usage", {})
            .get("completion_tokens")
        )
    except (AttributeError, TypeError):
        return None


def should_record_first_token(content_type: str, author_role: str) -> bool:
    """判断是否应该记录首次 token。

    Args:
        content_type: 内容类型
        author_role:

    Returns:
        是否应该记录首次 token
    """
    return (
        content_type in ["think", "text", "reasoning"] and
        author_role == "assistant"
    )


def create_metrics_tracker(
    model_id: str, 
    metadata: Dict[str, Any], 
    request_start_time: float
) -> StreamMetricsTracker:
    """创建指标跟踪器的工厂函数。
    
    Args:
        model_id: 模型ID
        metadata: 请求元数据
        request_start_time: 请求开始时间
        
    Returns:
        配置好的指标跟踪器实例
    """
    return StreamMetricsTracker(model_id, metadata, request_start_time)
