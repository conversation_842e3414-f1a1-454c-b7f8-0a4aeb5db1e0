import socket
import asyncio
import aiohttp
import logging
import time
from typing import List, Optional, Dict, Any
import os
from urllib.parse import urljoin
import json

logger = logging.getLogger(__name__)


class K8sRequestForwarder:
    """K8s 环境下的请求转发器"""
    
    def __init__(self, headless_service_name: str, service_port: int = 8080, cache_duration: int = 5):
        """
        初始化请求转发器
        
        Args:
            headless_service_name: headless service 的名称
            service_port: 服务端口
            cache_duration: 缓存持续时间（秒），设为 0 表示不缓存
        """
        self.headless_service_name = headless_service_name
        self.service_port = service_port
        self.current_pod_ip = self._get_current_pod_ip()
        self.cache_duration = cache_duration
        self._cached_pods = []
        self._cache_timestamp = 0
        
    def _get_current_pod_ip(self) -> str:
        """获取当前 pod 的 IP 地址"""
        try:
            # 方法1: 从环境变量获取
            pod_ip = os.environ.get('POD_IP')
            if pod_ip:
                return pod_ip
            
            # 方法2: 从 hostname 获取（如果 hostname 是 IP）
            hostname = socket.gethostname()
            try:
                socket.inet_aton(hostname)
                return hostname
            except socket.error:
                pass
            
            # 方法3: 通过连接外部服务获取本地 IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
                
        except Exception as e:
            logger.error(f"获取当前 pod IP 失败: {e}")
            return "127.0.0.1"
    
    def _resolve_headless_service(self) -> List[str]:
        """解析 headless service 获取所有 pod IP"""
        try:
            # 解析 headless service
            result = socket.getaddrinfo(
                self.headless_service_name, 
                self.service_port, 
                socket.AF_INET, 
                socket.SOCK_STREAM
            )
            
            # 提取所有 IP 地址
            pod_ips = list(set([info[4][0] for info in result]))
            logger.info(f"解析到的 pod IPs: {pod_ips}")
            
            return pod_ips
            
        except socket.gaierror as e:
            logger.error(f"DNS 解析失败: {e}")
            return []
        except Exception as e:
            logger.error(f"解析 headless service 失败: {e}")
            return []
    
    def _get_other_pods(self) -> List[str]:
        """获取除自己以外的其他 pod IP"""
        current_time = time.time()
        
        # 如果缓存有效且在有效期内，使用缓存
        if (self.cache_duration > 0 and 
            self._cached_pods and 
            current_time - self._cache_timestamp < self.cache_duration):
            logger.debug(f"使用缓存的 pod 列表: {self._cached_pods}")
            return self._cached_pods
        
        # 重新获取 pod 列表
        all_pods = self._resolve_headless_service()
        
        # 排除自己的 IP
        other_pods = [ip for ip in all_pods if ip != self.current_pod_ip]
        
        # 更新缓存
        if self.cache_duration > 0:
            self._cached_pods = other_pods
            self._cache_timestamp = current_time
            logger.info(f"缓存更新 - 当前 pod IP: {self.current_pod_ip}, 其他 pod IPs: {other_pods}")
        else:
            logger.info(f"实时获取 - 当前 pod IP: {self.current_pod_ip}, 其他 pod IPs: {other_pods}")
        
        return other_pods
    
    def _should_forward_request(self, headers: Dict[str, str], params: Dict[str, Any]) -> bool:
        """检查请求是否应该转发"""
        # 检查请求头中的不转发标志
        no_forward_header = headers.get('X-No-Forward', '').lower()
        if no_forward_header in ['true', '1', 'yes']:
            return False
        
        # 检查查询参数中的不转发标志
        no_forward_param = str(params.get('no_forward', '')).lower()
        if no_forward_param in ['true', '1', 'yes']:
            return False
        
        # 检查是否是内部转发请求（避免循环转发）
        if headers.get('X-Forwarded-By') == 'k8s-forwarder':
            return False
        
        return True
    
    async def _forward_to_pod(self, pod_ip: str, method: str, path: str, 
                             headers: Dict[str, str], params: Dict[str, Any], 
                             body: Optional[bytes] = None) -> Dict[str, Any]:
        """转发请求到指定的 pod"""
        try:
            # 构建目标 URL
            target_url = f"http://{pod_ip}:{self.service_port}{path}"
            
            # 添加转发标识头，避免循环转发
            forward_headers = headers.copy()
            forward_headers['X-Forwarded-By'] = 'k8s-forwarder'
            forward_headers['X-Forwarded-From'] = self.current_pod_ip
            
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.request(
                    method=method,
                    url=target_url,
                    headers=forward_headers,
                    params=params,
                    data=body
                ) as response:
                    content = await response.read()
                    
                    return {
                        'pod_ip': pod_ip,
                        'status_code': response.status,
                        'headers': dict(response.headers),
                        'content': content,
                        'success': True
                    }
                    
        except asyncio.TimeoutError:
            logger.error(f"请求 {pod_ip} 超时")
            return {
                'pod_ip': pod_ip,
                'error': 'timeout',
                'success': False
            }
        except Exception as e:
            logger.error(f"转发请求到 {pod_ip} 失败: {e}")
            return {
                'pod_ip': pod_ip,
                'error': str(e),
                'success': False
            }
    
    async def forward_request(self, method: str, path: str, 
                            headers: Dict[str, str], params: Dict[str, Any],
                            body: Optional[bytes] = None) -> Dict[str, Any]:
        """
        转发请求到其他 pod
        
        Args:
            method: HTTP 方法
            path: 请求路径
            headers: 请求头
            params: 查询参数
            body: 请求体
            
        Returns:
            包含转发结果的字典
        """
        # 检查是否应该转发
        if not self._should_forward_request(headers, params):
            return {
                'forwarded': False,
                'reason': 'no_forward_flag_detected'
            }
        
        # 获取其他 pod
        other_pods = self._get_other_pods()
        
        if not other_pods:
            return {
                'forwarded': False,
                'reason': 'no_other_pods_found'
            }
        
        # 并行转发到所有其他 pod
        tasks = []
        for pod_ip in other_pods:
            task = self._forward_to_pod(pod_ip, method, path, headers, params, body)
            tasks.append(task)
        
        # 等待所有转发完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        successful_forwards = []
        failed_forwards = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_forwards.append({
                    'error': str(result),
                    'success': False
                })
            elif result.get('success'):
                successful_forwards.append(result)
            else:
                failed_forwards.append(result)
        
        return {
            'forwarded': True,
            'total_pods': len(other_pods),
            'successful_forwards': successful_forwards,
            'failed_forwards': failed_forwards,
            'success_count': len(successful_forwards),
            'failure_count': len(failed_forwards)
        }
    
    def force_refresh_cache(self):
        """强制刷新缓存"""
        self._cached_pods = []
        self._cache_timestamp = 0
        return self._get_other_pods()


# 便捷函数
async def forward_request_to_peers(
    headless_service_name: str,
    method: str,
    path: str,
    headers: Dict[str, str],
    params: Dict[str, Any],
    body: Optional[bytes] = None,
    service_port: int = 8080,
    cache_duration: int = 0
) -> Dict[str, Any]:
    """
    便捷函数：转发请求到其他 pod
    
    Args:
        headless_service_name: headless service 名称
        method: HTTP 方法
        path: 请求路径
        headers: 请求头
        params: 查询参数
        body: 请求体
        service_port: 服务端口
        cache_duration: 缓存持续时间（秒），默认 0 表示不缓存
        
    Returns:
        转发结果字典
    """
    forwarder = K8sRequestForwarder(headless_service_name, service_port, cache_duration)
    return await forwarder.forward_request(method, path, headers, params, body)


# 单例模式的全局转发器
_global_forwarder = None

def get_global_forwarder(headless_service_name: str, service_port: int = 8080, cache_duration: int = 0) -> K8sRequestForwarder:
    """获取全局转发器实例"""
    global _global_forwarder
    if _global_forwarder is None:
        _global_forwarder = K8sRequestForwarder(headless_service_name, service_port, cache_duration)
    return _global_forwarder
