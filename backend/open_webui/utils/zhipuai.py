import os
import logging
import asyncio
import tempfile
import requests
import time
from typing import Dict, Any, Optional, BinaryIO, List
from pathlib import Path
from zhipuai import ZhipuAI
from open_webui.models.files import Files
from cachetools import TTLCache

from open_webui.env import SRC_LOG_LEVELS, ENV
from open_webui.storage.provider import Storage
from open_webui.utils.metrics import (
    file_extraction_total,
    file_extraction_duration_seconds,
    file_extraction_cache_hits,
    file_extraction_cache_misses,
    zhipuai_api_requests_total,
    zhipuai_api_duration_seconds
)

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MAIN"])

# Maximum content length for extraction (50K tokens, approximately 400K characters)
MAX_CONTENT_LENGTH = 50000

# 创建一个TTL缓存，最大容量为1000个项目，过期时间为1小时（3600秒）
# 可以根据实际需求调整这些参数
FILE_CONTENT_CACHE = TTLCache(maxsize=5000, ttl=600)

def get_zhipuai_client(api_key: str = None, base_url: str = None) -> ZhipuAI:
    """
    Get a Zhipuai client instance.

    Args:
        api_key: The API key for Zhipuai. If not provided, will try to get from environment variable.
        base_url: The base URL for Zhipuai API. If not provided, will use the default.

    Returns:
        ZhipuAI client instance
    """
    api_key = api_key or os.environ.get("ZHIPUAI_API_KEY")
    if not api_key:
        raise ValueError("Zhipuai API key is required. Please provide it or set ZHIPUAI_API_KEY environment variable.")

    base_url = base_url or os.environ.get("ZHIPUAI_API_BASE_URL", "https://open.bigmodel.cn/api/paas/v4")

    return ZhipuAI(api_key=api_key, base_url=base_url)


async def upload_file_to_zhipuai(file_path: str, purpose: str = "file-extract", api_key: str = None) -> Dict[str, Any]:
    """
    Helper function to upload a file to Zhipuai.

    Args:
        file_path: Path to the file to upload
        purpose: The purpose of the file
        api_key: Zhipuai API key

    Returns:
        API response containing the file ID and other metadata
    """
    start_time = time.time()

    try:
        client = get_zhipuai_client(api_key)

        # Run in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: client.files.create(
                file=Path(file_path),
                purpose=purpose
            )
        )

        # Record successful upload metrics
        duration = time.time() - start_time
        zhipuai_api_requests_total.labels(operation="upload", status="success", env=ENV).inc()
        zhipuai_api_duration_seconds.labels(operation="upload", env=ENV).observe(duration)

        log.info(f"文件上传到Zhipu AI成功，耗时: {duration:.2f}秒")
        return result

    except Exception as e:
        # Record failed upload metrics
        duration = time.time() - start_time
        zhipuai_api_requests_total.labels(operation="upload", status="error", env=ENV).inc()
        zhipuai_api_duration_seconds.labels(operation="upload", env=ENV).observe(duration)

        log.error(f"文件上传到Zhipu AI失败，耗时: {duration:.2f}秒，错误: {e}")
        raise


async def extract_file_content(file_id: str, api_key: str = None) -> Dict[str, Any]:
    """
    Helper function to extract content from a file.

    Args:
        file_id: The ID of the file to extract content from
        api_key: Zhipuai API key

    Returns:
        API response containing the extracted content
    """
    start_time = time.time()

    try:
        client = get_zhipuai_client(api_key)

        # Run in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: client.files.content(file_id=file_id)
        )

        # Record successful extraction metrics
        duration = time.time() - start_time
        zhipuai_api_requests_total.labels(operation="extract", status="success", env=ENV).inc()
        zhipuai_api_duration_seconds.labels(operation="extract", env=ENV).observe(duration)

        log.info(f"文件内容提取成功，耗时: {duration:.2f}秒")
        return result

    except Exception as e:
        # Record failed extraction metrics
        duration = time.time() - start_time
        zhipuai_api_requests_total.labels(operation="extract", status="error", env=ENV).inc()
        zhipuai_api_duration_seconds.labels(operation="extract", env=ENV).observe(duration)

        log.error(f"文件内容提取失败，耗时: {duration:.2f}秒，错误: {e}")
        raise


async def upload_file_object_to_zhipuai(file_object: BinaryIO, file_path: str, purpose: str = "file-extract", api_key: str = None) -> Dict[str, Any]:
    """
    Helper function to upload a file object to Zhipuai.

    Args:
        file_object: The file object to upload
        file_path: Temporary path to save the file
        purpose: The purpose of the file
        api_key: Zhipuai API key

    Returns:
        API response containing the file ID and other metadata
    """
    # Save the file object to a temporary file
    with open(file_path, "wb") as f:
        f.write(file_object.read())

    # Upload the file to Zhipuai
    result = await upload_file_to_zhipuai(file_path, purpose, api_key)

    return result

async def delete_file_from_zhipuai(file_id: str, api_key: str = None) -> None:
    """
    删除Zhipuai中的文件
    """
    client = get_zhipuai_client(api_key)
    client.files.delete(file_id=file_id)



async def extract_content_from_files(files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从文件中提取内容用于聊天补全。

    Args:
        files: 文件信息字典列表

    Returns:
        带有提取内容的文件信息字典列表
    """
    updated_files = []
    total_start_time = time.time()

    for file in files:
        file_start_time = time.time()
        file_id = file.get("id")

        try:
            # 阶段1: 检查缓存
            cache_check_start = time.time()
            cache_key = f"file_content_{file_id}"

            if cache_key in FILE_CONTENT_CACHE:
                cache_check_duration = time.time() - cache_check_start
                file_extraction_duration_seconds.labels(stage="cache_check", env=ENV).observe(cache_check_duration)
                file_extraction_cache_hits.labels(env=ENV).inc()

                log.info(f"使用缓存的文件内容: {file_id}，缓存检查耗时: {cache_check_duration:.3f}秒")

                # 复制文件信息并添加缓存的提取内容
                file_copy = file.copy()
                file_copy["_extracted_content"] = FILE_CONTENT_CACHE[cache_key]
                file_copy["_cache_hit"] = True  # 用于调试/监控
                updated_files.append(file_copy)
                file_extraction_total.labels(status="success", stage="cache_hit", env=ENV).inc()
                continue

            # 缓存未命中
            cache_check_duration = time.time() - cache_check_start
            file_extraction_duration_seconds.labels(stage="cache_check", env=ENV).observe(cache_check_duration)
            file_extraction_cache_misses.labels(env=ENV).inc()

            # 阶段2: 从数据库获取文件信息
            db_query_start = time.time()
            db_file = Files.get_file_by_id(file_id)
            db_query_duration = time.time() - db_query_start
            file_extraction_duration_seconds.labels(stage="db_query", env=ENV).observe(db_query_duration)

            if not db_file:
                file["_extraction_error"] = f"无法找到ID为{file_id}的文件"
                file["_timing"] = {
                    "cache_check": cache_check_duration,
                    "db_query": db_query_duration,
                    "total": time.time() - file_start_time
                }
                file_extraction_total.labels(status="error", stage="db_query", env=ENV).inc()
                updated_files.append(file)
                continue

            # 获取OSS文件路径
            oss_path = db_file.path
            if not oss_path:
                file["_extraction_error"] = f"文件{file_id}没有OSS路径"
                file_extraction_total.labels(status="error", stage="validation", env=ENV).inc()
                updated_files.append(file)
                continue

            # 阶段3: 从OSS下载文件到本地
            download_start = time.time()
            local_path = Storage.get_file(oss_path)
            download_duration = time.time() - download_start
            file_extraction_duration_seconds.labels(stage="file_download", env=ENV).observe(download_duration)

            # 将path字段更新为本地路径，用于上传到ZhipuAI
            file["path"] = local_path

            # 阶段4: 上传文件到ZhipuAI
            upload_start = time.time()
            zhipuai_file = await upload_file_to_zhipuai(local_path, purpose="file-extract")
            upload_duration = time.time() - upload_start
            file_extraction_duration_seconds.labels(stage="zhipu_upload", env=ENV).observe(upload_duration)

            # 阶段5: 从文件中提取内容
            extraction_start = time.time()
            content_response = await extract_file_content(zhipuai_file.id)
            extraction_duration = time.time() - extraction_start
            file_extraction_duration_seconds.labels(stage="content_extraction", env=ENV).observe(extraction_duration)

            # 阶段6: 处理提取的内容
            content_processing_start = time.time()
            extracted_content = content_response.content.decode() if hasattr(content_response, 'content') else ""

            # 如果内容太长则截断
            if len(extracted_content) > MAX_CONTENT_LENGTH:
                extracted_content = extracted_content[:MAX_CONTENT_LENGTH] + "\n\n[...]"

            # 将提取的内容存入缓存
            FILE_CONTENT_CACHE[cache_key] = extracted_content
            content_processing_duration = time.time() - content_processing_start
            file_extraction_duration_seconds.labels(stage="content_processing", env=ENV).observe(content_processing_duration)

            # 阶段7: 清理资源
            cleanup_start = time.time()

            # 删除临时本地文件
            if os.path.exists(local_path):
                os.remove(local_path)
            # 删除Zhipuai中的文件
            await delete_file_from_zhipuai(zhipuai_file.id)

            cleanup_duration = time.time() - cleanup_start
            file_extraction_duration_seconds.labels(stage="cleanup", env=ENV).observe(cleanup_duration)

            # 计算总耗时
            total_file_duration = time.time() - file_start_time

            # 添加ZhipuAI文件ID到文件信息中，但不存储提取的内容
            # 内容将用于当前请求，但不存储在数据库中
            file["zhipuai_file_id"] = zhipuai_file.id
            file["_extracted_content"] = extracted_content  # 临时字段，下划线前缀表示不用于存储

            file_extraction_total.labels(status="success", stage="complete", env=ENV).inc()
            file_extraction_duration_seconds.labels(stage="total", env=ENV).observe(total_file_duration)

            log.info(f"文件内容提取完成: {file_id}，总耗时: {total_file_duration:.2f}秒，"
                    f"各阶段耗时: 缓存检查={cache_check_duration:.3f}s, "
                    f"数据库查询={db_query_duration:.3f}s, "
                    f"文件下载={download_duration:.3f}s, "
                    f"上传Zhipu={upload_duration:.3f}s, "
                    f"内容提取={extraction_duration:.3f}s, "
                    f"内容处理={content_processing_duration:.3f}s, "
                    f"清理资源={cleanup_duration:.3f}s")

            updated_files.append(file)

        except Exception as e:
            total_file_duration = time.time() - file_start_time
            log.exception(f"提取文件内容时出错: {e}，总耗时: {total_file_duration:.2f}秒")

            file["_extraction_error"] = str(e)  # 临时字段，下划线前缀表示不用于存储
            file_extraction_total.labels(status="error", stage="exception", env=ENV).inc()
            file_extraction_duration_seconds.labels(stage="total", env=ENV).observe(total_file_duration)

            updated_files.append(file)

    # 记录整体处理时间
    total_duration = time.time() - total_start_time
    log.info(f"文件内容提取批处理完成，处理了{len(files)}个文件，总耗时: {total_duration:.2f}秒")

    return updated_files


def clear_file_content_cache():
    """清空文件内容缓存"""
    global FILE_CONTENT_CACHE
    FILE_CONTENT_CACHE.clear()
    log.info("文件内容缓存已清空")


def get_cache_stats():
    """获取缓存统计信息"""
    cache_info = {
        "size": len(FILE_CONTENT_CACHE),
        "maxsize": FILE_CONTENT_CACHE.maxsize,
        "ttl": FILE_CONTENT_CACHE.ttl,
        "currsize": len(FILE_CONTENT_CACHE)
    }

    # 记录缓存使用情况指标
    from open_webui.utils.metrics import Gauge
    cache_size_gauge = Gauge(
        'file_content_cache_size',
        '文件内容缓存当前大小',
        ['env']
    )
    cache_size_gauge.labels(env=ENV).set(len(FILE_CONTENT_CACHE))

    return cache_info
