import re
import time
import logging

log = logging.getLogger(__name__)

class TagStreamParser:
    """
    解析特定标签对(例如<think>/</think>)的流文本块，
    处理跨块的部分标签。比完整的字符状态机更简单。
    生成(type, content)元组，其中type是'text'或标签名称。
    """
    def __init__(self, start_tag: str, end_tag: str):
        if not start_tag.startswith("<") or not start_tag.endswith(">"):
            raise ValueError("start_tag必须被<>包裹")
        if not end_tag.startswith("</") or not end_tag.endswith(">"):
            raise ValueError("end_tag必须被</>包裹")

        self.start_tag = start_tag
        self.end_tag = end_tag
        self.tag_name = start_tag.strip('<>')
        self._buffer = ""
        self._in_tag = False
        self._partial_start_tag = ""
        self._partial_end_tag = ""
        log.debug(f"TagStreamParser初始化用于<{self.tag_name}>...</{self.tag_name}>")

    def _reset_partial_tags(self):
        self._partial_start_tag = ""
        self._partial_end_tag = ""

    def parse_chunk(self, chunk: str):
        # 预置部分标签
        if not self._in_tag and self._partial_start_tag:
            chunk = self._partial_start_tag + chunk
            self._partial_start_tag = ""
        elif self._in_tag and self._partial_end_tag:
            chunk = self._partial_end_tag + chunk
            self._partial_end_tag = ""

        self._buffer += chunk

        while self._buffer:
            # 首先检查部分标签
            buffer_changed = False  # 标记缓冲区是否被部分检查修改

            # 部分开始标签检查
            if not self._in_tag:
                for i in range(len(self.start_tag) - 1, 0, -1):
                    partial = self.start_tag[:i]
                    if self._buffer.endswith(partial):
                        self._partial_start_tag = partial
                        text_part = self._buffer[:-i]
                        if text_part: yield "text", text_part
                        self._buffer = ""
                        buffer_changed = True
                        break
                if buffer_changed: continue  # 如果缓冲区变化，重新开始循环

            # 部分结束标签检查
            if self._in_tag:
                for i in range(len(self.end_tag) - 1, 0, -1):
                    partial = self.end_tag[:i]
                    if self._buffer.endswith(partial):
                        self._partial_end_tag = partial
                        content_part = self._buffer[:-i]
                        if content_part: yield self.tag_name, content_part
                        self._buffer = ""
                        buffer_changed = True
                        break
                if buffer_changed: continue  # 如果缓冲区变化，重新开始循环

            # 主解析逻辑（如果没有部分标签消耗缓冲区）
            if not self._in_tag:
                start_index = self._buffer.find(self.start_tag)
                if start_index != -1:
                    text_part = self._buffer[:start_index]
                    if text_part: yield "text", text_part
                    self._buffer = self._buffer[start_index + len(self.start_tag):]
                    self._in_tag = True
                    self._reset_partial_tags()
                else:  # 未找到开始标签
                    yield "text", self._buffer
                    self._buffer = ""
                    break  # 消耗缓冲区
            else:  # 标签内部
                end_index = self._buffer.find(self.end_tag)
                if end_index != -1:
                    tag_content = self._buffer[:end_index]
                    if tag_content: yield self.tag_name, tag_content
                    self._buffer = self._buffer[end_index + len(self.end_tag):]
                    self._in_tag = False
                    self._reset_partial_tags()
                else:  # 未找到结束标签
                    yield self.tag_name, self._buffer
                    self._buffer = ""
                    break  # 消耗缓冲区

    def is_in_tag(self) -> bool:
        return self._in_tag

    def flush(self):
        if self._buffer:
            log.debug(f"清除剩余缓冲区: '{self._buffer[:50]}...' 在标签内: {self._in_tag}")
            if self._in_tag:
                yield self.tag_name, self._buffer
            else:
                yield "text", self._buffer
            self._buffer = "" 