import time
import os
import logging
from typing import Binary<PERSON>, <PERSON><PERSON>
import oss2
from oss2.exceptions import OssError

from open_webui.storage.provider import StorageProvider, LocalStorageProvider
from open_webui.env import PUBLIC_OSS_ENDPOINT, SRC_LOG_LEVELS
from open_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MAIN"])

class OSSStorageProvider(StorageProvider):
    """
    Alibaba Cloud OSS (Object Storage Service) provider for file storage.
    """
    def __init__(self):
        """
        Initialize the OSS storage provider with credentials from environment variables.
        """
        # Get OSS configuration from environment variables
        self.access_key_id = os.environ.get("OSS_ACCESS_KEY_ID")
        self.access_key_secret = os.environ.get("OSS_ACCESS_KEY_SECRET")
        self.endpoint = os.environ.get("OSS_ENDPOINT")
        self.bucket_name = os.environ.get("OSS_BUCKET_NAME")
        self.key_prefix = os.environ.get("OSS_KEY_PREFIX", "files/")
        
        # 确保 key_prefix 以 / 结尾
        if self.key_prefix and not self.key_prefix.endswith("/"):
            self.key_prefix += "/"
        
        # Validate required configuration
        if not all([self.access_key_id, self.access_key_secret, self.endpoint, self.bucket_name]):
            raise ValueError("Missing required OSS configuration. Please set OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_ENDPOINT, and OSS_BUCKET_NAME environment variables.")
        
        # Initialize OSS auth and bucket
        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
        
        # Verify bucket exists and is accessible
        try:
            self.bucket.get_bucket_info()
            log.info(f"Successfully connected to OSS bucket: {self.bucket_name}")
        except OssError as e:
            log.error(f"Failed to connect to OSS bucket: {e}")
            raise RuntimeError(f"Error connecting to OSS bucket: {e}")

    def upload_file(self, file: BinaryIO, filename: str) -> Tuple[bytes, str]:
        """
        Uploads a file to OSS storage.
        
        Args:
            file: The file-like object to upload
            filename: The name to give the file in storage
            
        Returns:
            Tuple containing the file contents and the OSS URL
        """
        try:
            # 读取文件内容
            contents = file.read()
            
            # 确保 filename 不带路径前缀
            filename = os.path.basename(filename)
            
            # Construct the OSS key with prefix
            oss_key = f"{self.key_prefix}{filename}"
            
            # 直接上传二进制内容到 OSS
            self.bucket.put_object(oss_key, contents)
            # Construct the OSS URL
            oss_url = f"https://{self.bucket_name}.{self.endpoint}/{oss_key}"
            
            log.info(f"Successfully uploaded file to OSS: {oss_url}")
            return contents, oss_url
            
        except OssError as e:
            log.error(f"Error uploading file to OSS: {e}")
            raise RuntimeError(f"Error uploading file to OSS: {e}")

    def get_file(self, file_path: str) -> str:
        """
        Downloads a file from OSS storage.
        
        Args:
            file_path: The OSS URL or path of the file to download
            
        Returns:
            The local path to the downloaded file
        """
        try:
            # Extract the OSS key from the file path
            oss_key = self._extract_oss_key(file_path)
            
            # Get the local file path
            local_file_path = self._get_local_file_path(oss_key)
            
            # Download the file from OSS
            self.bucket.get_object_to_file(oss_key, local_file_path)
            
            return local_file_path
            
        except OssError as e:
            log.error(f"Error downloading file from OSS: {e}")
            raise RuntimeError(f"Error downloading file from OSS: {e}")

    def delete_file(self, file_path: str) -> None:
        """
        Deletes a file from OSS storage.
        
        Args:
            file_path: The OSS URL or path of the file to delete
        """
        try:
            # Extract the OSS key from the file path
            oss_key = self._extract_oss_key(file_path)
            
            # Delete the file from OSS
            self.bucket.delete_object(oss_key)
            
            log.info(f"Successfully deleted file from OSS: {oss_key}")
            
            # Also delete from local storage
            LocalStorageProvider.delete_file(file_path)
            
        except OssError as e:
            log.error(f"Error deleting file from OSS: {e}")
            raise RuntimeError(f"Error deleting file from OSS: {e}")

    def delete_all_files(self) -> None:
        """
        Deletes all files in the OSS bucket with the configured key prefix.
        """
        try:
            # List all objects with the configured prefix
            for obj in oss2.ObjectIterator(self.bucket, prefix=self.key_prefix):
                # Delete each object
                self.bucket.delete_object(obj.key)
                log.info(f"Deleted file from OSS: {obj.key}")
                
        except OssError as e:
            log.error(f"Error deleting all files from OSS: {e}")
            raise RuntimeError(f"Error deleting all files from OSS: {e}")

    def _extract_oss_key(self, file_path: str) -> str:
        """
        Extracts the OSS key from a file path or URL.
        
        Args:
            file_path: The file path or URL
            
        Returns:
            The OSS key
        """
        # Handle OSS URL format
        if file_path.startswith(f"https://{self.bucket_name}.{self.endpoint}/"):
            oss_key = file_path.replace(f"https://{self.bucket_name}.{self.endpoint}/", "")
            # 确保返回的 key 带有 files/ 前缀
            if not oss_key.startswith(self.key_prefix) and self.key_prefix:
                oss_key = f"{self.key_prefix}{os.path.basename(oss_key)}"
            return oss_key
        
        # Handle OSS path format
        if file_path.startswith(f"oss://{self.bucket_name}/"):
            oss_key = file_path.replace(f"oss://{self.bucket_name}/", "")
            # 确保返回的 key 带有 files/ 前缀
            if not oss_key.startswith(self.key_prefix) and self.key_prefix:
                oss_key = f"{self.key_prefix}{os.path.basename(oss_key)}"
            return oss_key
            
        # If it's just a filename, return it with prefix
        filename = os.path.basename(file_path)
        return f"{self.key_prefix}{filename}"

    def _get_local_file_path(self, oss_key: str) -> str:
        """
        Gets the local file path for an OSS key.
        
        Args:
            oss_key: The OSS key
            
        Returns:
            The local file path
        """
        from open_webui.config import UPLOAD_DIR
        filename = os.path.basename(oss_key)
        return f"{UPLOAD_DIR}/{filename}"
    
    def generate_temporary_url(self, filename: str, expires: int = 60) -> str:
        """
        Generates a temporary URL for an OSS key.
        
        Args:
            oss_key: The OSS key
            
        Returns:
            The temporary URL
        """
        start_time = time.time()
        oss_key = f"{self.key_prefix}{filename}"
        end_time = time.time()
        url = self.bucket.sign_url('GET', oss_key, expires, slash_safe=True)
        log.info(f"生成临时URL耗时: {end_time - start_time:.3f}秒")
        return url
    
    def get_public_access_file_url(self, filename: str) -> str:
        """
        Gets the OSS URL for a file.
        注意：这个URL是可公共访问，endpoint需要使用PUBLIC_OSS_ENDPOINT
        """
        oss_key = f"{self.key_prefix}{filename}"
        return f"https://{self.bucket_name}.{PUBLIC_OSS_ENDPOINT}/{oss_key}"

