import unittest
import re
import ast

class TestMSearchParser(unittest.TestCase):
    def setUp(self):
        """初始化解析函数"""
        self.maxDiff = None
        
    def parse_msearch(self, final_code):
        """解析msearch参数的核心逻辑"""
        tool_name = "msearch"
        arguments = {}
        
        try:
            # 提取函数参数部分
            args_str = re.search(rf'{tool_name}\s*\((.*)\)', final_code, re.DOTALL)
            if args_str:
                args_str = args_str.group(1).strip()
                
                # 解析description
                desc_match = re.search(r'description\s*=\s*"([^"]*)"', args_str)
                arguments["description"] = desc_match.group(1) if desc_match else "执行搜索"
                
                # 解析queries
                queries_match = re.search(r'queries\s*=\s*\[(.*?)\]', args_str, re.DOTALL)
                arguments["queries"] = []
                
                if queries_match:
                    content = queries_match.group(1).strip()
                    if content:
                        try:
                            # 分割并清理查询项
                            parsed = [q.strip().replace('"', '') for q in content.split(',')]
                            arguments["queries"] = [q for q in parsed if q and len(q) <= 300]
                        except Exception as e:
                            print(f"Query解析错误: {str(e)}")
        except Exception as e:
            print(f"解析异常: {str(e)}")
            
        return arguments

    def test_normal_case(self):
        """测试正常用例"""
        code = '''msearch(description="Alibaba分析", 
                 queries=["Business Segment", "Financial Data", "Strategic Direction"])'''
        
        result = self.parse_msearch(code)
        
        self.assertEqual(result["description"], "Alibaba分析")
        self.assertListEqual(result["queries"], [
            "Business Segment",
            "Financial Data", 
            "Strategic Direction"
        ])
    
    def test_long_queries(self):
        """测试长查询项过滤"""
        code = '''msearch(
            queries=["Short", "This is a very long search query that exceeds 100 characters limit..."+ 
                     "x"*100, "Normal length query"]
        )'''
        
        result = self.parse_msearch(code)
        self.assertEqual(len(result["queries"][1]), 100)  # 验证长度过滤
    
    def test_special_characters(self):
        """测试特殊字符处理"""
        code = '''msearch(queries=["Hello#World", "Data&Analytics", "Price$100"])'''
        result = self.parse_msearch(code)
        self.assertListEqual(result["queries"], [
            "Hello#World",
            "Data&Analytics",
            "Price$100"
        ])
    
    def test_missing_parameters(self):
        """测试参数缺失情况"""
        # 缺少description
        code1 = "msearch(queries=[])"
        result1 = self.parse_msearch(code1)
        self.assertEqual(result1["description"], "执行搜索")
        
        # 缺少queries
        code2 = "msearch(description='test')"
        result2 = self.parse_msearch(code2)
        self.assertListEqual(result2["queries"], [])
    
    def test_edge_cases(self):
        """测试边界条件"""
        # 精确100字符
        edge_query = "x" * 100
        code = f'''msearch(queries=["{edge_query}"])'''
        result = self.parse_msearch(code)
        self.assertEqual(len(result["queries"][0]), 100)
        
        # 空查询
        code = "msearch(queries=[])"
        result = self.parse_msearch(code)
        self.assertListEqual(result["queries"], [])

if __name__ == '__main__':
    unittest.main(verbosity=2)