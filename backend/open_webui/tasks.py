# tasks.py
import asyncio
from typing import Dict
from uuid import uuid4

from open_webui.utils.metrics import background_tasks_active
from open_webui.env import ENV

# A dictionary to keep track of active tasks
tasks: Dict[str, asyncio.Task] = {}


# 存储任务的模型ID信息
task_model_mapping: Dict[str, str] = {}

def cleanup_task(task_id: str):
    """
    Remove a completed or canceled task from the global `tasks` dictionary.
    """
    # 获取模型ID并更新指标
    model_id = task_model_mapping.pop(task_id, 'unknown')
    background_tasks_active.labels(model_id=model_id, env=ENV).dec()

    tasks.pop(task_id, None)  # Remove the task if it exists


def create_task(coroutine, task_id: str = None, model_id: str = 'unknown'):
    """
    Create a new asyncio task and add it to the global task dictionary.
    """
    if not task_id:
        task_id = str(uuid4())  # Generate a unique ID for the task
    if tasks.get(task_id):
        # 如果任务管理器发现已经有一样 id 的任务，那自动取消上面的一个任务。
        asyncio.gather(stop_task(task_id))
    task = asyncio.create_task(coroutine)  # Create the task

    # 存储模型ID信息并更新指标
    task_model_mapping[task_id] = model_id
    background_tasks_active.labels(model_id=model_id, env=ENV).inc()

    # Add a done callback for cleanup
    task.add_done_callback(lambda t: cleanup_task(task_id))

    tasks[task_id] = task
    return task_id, task


def get_task(task_id: str):
    """
    Retrieve a task by its task ID.
    """
    return tasks.get(task_id)


def list_tasks():
    """
    List all currently active task IDs.
    """
    return list(tasks.keys())

async def stop_task(message_id: str):
    sensitive_task_id = f"{message_id}_sensitive"
    post_response_task_id = f"{message_id}_post_response"
    await stop_task_inner(sensitive_task_id)
    await stop_task_inner(post_response_task_id)
    

async def stop_task_inner(task_id: str):
    """
    Cancel a running task and remove it from the global task list.
    """
    task = tasks.get(task_id)
    if not task:
        return {"status": True, "message": f"Task {task_id} not found."}

    task.cancel()  # Request task cancellation
    try:
        await task  # Wait for the task to handle the cancellation
    except asyncio.CancelledError:
        # Task successfully canceled
        # 获取模型ID并更新指标
        model_id = task_model_mapping.pop(task_id, 'unknown')
        background_tasks_active.labels(model_id=model_id, env=ENV).dec()

        tasks.pop(task_id, None)  # Remove it from the dictionary
        return {"status": True, "message": f"Task {task_id} successfully stopped."}

    return {"status": False, "message": f"Failed to stop task {task_id}."}
