import json
import logging
import aiohttp
import asyncio
import math
import time
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Request, Response, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from open_webui.utils.auth import get_verified_user
from open_webui.models.users import UserModel
from fastapi_limiter.depends import RateLimiter

from open_webui.env import CONVERTER_BASE_URL, CONVERTER_DAILY_LIMIT
from open_webui.utils.converter_monitor import get_converter_monitor

log = logging.getLogger(__name__)
router = APIRouter()

# 限流错误类型常量
RATE_LIMIT_ERROR_TYPE = "CONVERTER_DAILY_LIMIT_EXCEEDED"

# 转换服务限流配置


# 转换服务限流标识符函数
async def converter_user_identifier(request: Request):
    """
    基于用户ID和接口路径的限流标识符
    使用现有的认证逻辑来获取用户ID
    """
    try:
        # 使用现有的认证逻辑
        from open_webui.utils.auth import get_current_user
        from fastapi import BackgroundTasks
        from fastapi.security import HTTPAuthorizationCredentials

        # 创建必要的依赖
        background_tasks = BackgroundTasks()

        # 获取认证凭据
        auth_token = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            auth_token = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=token
            )

        # 尝试获取当前用户
        if auth_token:
            try:
                user = get_current_user(request, background_tasks, auth_token)
                user_id = user.id
            except Exception:
                user_id = "anonymous"
        else:
            user_id = "anonymous"

    except Exception as e:
        log.warning(f"Failed to get user from request: {e}")
        user_id = "anonymous"

    scope = request.scope["path"]
    identifier = f"{scope}_{user_id}"
    log.debug(f"CONVERTER_IDENTIFIER: {identifier}")
    return identifier


async def converter_rate_limit_callback(
    request: Request, response: Response, pexpire: int
):
    """
    转换服务限流回调函数
    """
    expire = math.ceil(pexpire / 1000)

    # 返回结构化的错误信息，让前端处理i18n
    error_detail = {
        "error_type": RATE_LIMIT_ERROR_TYPE,
        "limit": CONVERTER_DAILY_LIMIT,
        "retry_after": expire
    }

    raise HTTPException(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        detail=error_detail,
        headers={"Retry-After": str(expire)},
    )


class ConversionRequest(BaseModel):
    chatId: str  # 改为必需的chatId字段
    options: Optional[Dict[str, Any]] = {}
    pageMetadata: Optional[list] = []  # 前端计算的页面尺寸数据
    pptVersion: Optional[int] = 0  # PPT版本号
    exportMethod: Optional[str] = "wps"  # 导出方案：wps（默认）或 inhouse（自研）


async def get_ppt_slides(
    request: Request, chat_id: str, user_id: str, ppt_version: int
) -> Dict[str, Any]:
    """从PPT服务器获取slides数据"""
    try:
        # 获取PPT服务器URL，参考get_chat_product的实现
        from open_webui.main import app

        ppt_maker_url = (
            app.state.MCP_CONFIG.mcpServers.get("ppt-maker").url[: -len("/mcp")]
            + f"/get_slides/{chat_id}?v={ppt_version}"
        )

        headers = {
            "X-User-Id": user_id,
            "X-Trace-Id": request.state.trace_id
        }

        log.info(
            f"Requesting PPT slides from: {ppt_maker_url} and  ppt_version {ppt_version}"
        )
        log.info(f"Request headers: {headers}")

        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
        async with aiohttp.ClientSession(timeout=timeout, trust_env=True) as session:
            async with session.get(ppt_maker_url, headers=headers) as response:
                if response.status != 200:
                    log.error(f"Failed to get PPT slides: HTTP {response.status}")
                    raise HTTPException(
                        status_code=response.status,
                        detail=f"Failed to get PPT slides: HTTP {response.status}",
                    )

                slides_data = await response.json()
                log.info(
                    f"Successfully retrieved PPT slides: {json.dumps(slides_data, ensure_ascii=False)}"
                )
                return slides_data

    except Exception as e:
        log.error(f"Error getting PPT slides: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get PPT slides: {str(e)}",
        )


async def cleanup_response(response, session):
    """清理响应和会话资源"""
    try:
        if response:
            response.close()
        if session:
            await session.close()
    except Exception as e:
        log.error(f"Error cleaning up response/session: {e}")


@router.post(
    "/pdf/stream",
    dependencies=[
        Depends(
            RateLimiter(
                times=CONVERTER_DAILY_LIMIT,
                hours=24,
                identifier=converter_user_identifier,
                callback=converter_rate_limit_callback,
            )
        )
    ],
)
async def convert_to_pdf_stream(
    conversion_request: ConversionRequest, request: Request, user: UserModel = Depends(get_verified_user)
):
    """
    代理 PDF 流式转换请求到 converter 服务
    """
    log.info(
        f"PDF stream conversion request from user {user.id}, chatId: {conversion_request.chatId}"
    )

    # 获取监控器
    monitor = get_converter_monitor()
    start_time = time.time()
    export_id = None
    session = None
    response = None

    try:
        # 从PPT服务器获取slides数据
        slides_data = await get_ppt_slides(request, conversion_request.chatId, user.id, conversion_request.pptVersion)

        # 构造htmlPages
        html_pages = []
        pages = slides_data.get("pages", [])
        for i, page_content in enumerate(pages):
            # 使用前端传递的页面尺寸数据，如果没有则使用默认值
            if i < len(conversion_request.pageMetadata) and conversion_request.pageMetadata[i]:
                metadata = conversion_request.pageMetadata[i]
            else:
                # 默认尺寸（1280x720px 的精确转换）
                metadata = {
                    "pageWidth": 33.87,
                    "pageHeight": 19.05,
                }

            html_pages.append(
                {
                    "content": page_content,
                    "metadata": metadata,
                }
            )

        # 在options中添加title、slide_name等字段
        enhanced_options = dict(conversion_request.options)
        enhanced_options.update(
            {
                "title": slides_data.get("title", "A Nice Slide"),
                "slide_name": slides_data.get("title", "slide.pptx"),
                "desc": slides_data.get("desc", "A Nice Slide"),
                "page_num": slides_data.get("page_num", len(html_pages)),
            }
        )

        # 准备转发的请求数据
        request_data = {"htmlPages": html_pages, "options": enhanced_options}

        # 记录导出开始
        pages_count = len(html_pages)
        export_id = monitor.record_export_start("pdf", user.id, pages_count)

        log.info(f"Forwarding PDF request with {pages_count} pages, export_id: {export_id}")
        log.debug(
            f"Request data: {json.dumps(request_data, ensure_ascii=False, indent=2)}"
        )

        # 创建 aiohttp 会话
        timeout = aiohttp.ClientTimeout(total=300)  # 5分钟超时
        session = aiohttp.ClientSession(timeout=timeout, trust_env=True)

        # 转发请求到 converter 服务
        converter_url = f"{CONVERTER_BASE_URL}/api/convert/pdf/stream"
        log.info(f"Forwarding PDF request to: {converter_url}")

        response = await session.post(
            converter_url,
            json=request_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "text/event-stream",
                "User-Agent": f"OpenWebUI-Proxy/1.0 (User: {user.id})",
            },
        )

        # 检查响应状态
        if not response.ok:
            error_text = await response.text()
            log.error(f"Converter service error: {response.status} - {error_text}")
            raise HTTPException(
                status_code=response.status,
                detail=f"Converter service error: {error_text}",
            )

        # 检查响应类型
        content_type = response.headers.get("Content-Type", "")
        if "text/event-stream" not in content_type:
            error_text = await response.text()
            log.error(f"Expected SSE stream, got: {content_type}")
            raise HTTPException(
                status_code=500,
                detail=f"Expected SSE stream, got: {content_type}. Response: {error_text}",
            )

        # 创建流式响应生成器
        async def stream_generator():
            file_size = None
            try:
                async for chunk in response.content.iter_chunked(8192):
                    if chunk:
                        yield chunk
                        # 尝试从响应头获取文件大小
                        if file_size is None:
                            content_length = response.headers.get("Content-Length")
                            if content_length:
                                try:
                                    file_size = int(content_length)
                                except ValueError:
                                    pass

                # 记录成功完成
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "pdf", user.id, duration, "success", file_size)

            except asyncio.CancelledError:
                log.info("PDF stream cancelled by client")
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "pdf", user.id, duration, "cancelled")
                raise
            except Exception as e:
                log.error(f"Error in PDF stream generator: {e}")
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "pdf", user.id, duration, "failed", error_message=str(e))
                # 发送错误事件
                error_data = json.dumps(
                    {"status": "failed", "message": f"Stream error: {str(e)}"}
                )
                yield f"data: {error_data}\n\n".encode()
            finally:
                # 清理资源
                await cleanup_response(response, session)

        # 返回流式响应
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except HTTPException:
        # 重新抛出 HTTP 异常
        duration = time.time() - start_time
        if export_id:
            monitor.record_export_end(export_id, "pdf", user.id, duration, "failed", error_message="HTTP Exception")
        await cleanup_response(response, session)
        raise
    except Exception as e:
        log.error(f"PDF stream conversion error: {e}")
        duration = time.time() - start_time
        if export_id:
            monitor.record_export_end(export_id, "pdf", user.id, duration, "failed", error_message=str(e))
        await cleanup_response(response, session)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post(
    "/ppt/stream",
    dependencies=[
        Depends(
            RateLimiter(
                times=CONVERTER_DAILY_LIMIT,
                hours=24,
                identifier=converter_user_identifier,
                callback=converter_rate_limit_callback,
            )
        )
    ],
)
async def convert_to_ppt_stream(
    conversion_request: ConversionRequest, request: Request, user: UserModel = Depends(get_verified_user)
):
    """
    代理 PPTX 流式转换请求到 converter 服务
    """
    log.info(
        f"PPTX stream conversion request from user {user.id}, chatId: {conversion_request.chatId}"
    )

    # 获取监控器
    monitor = get_converter_monitor()
    start_time = time.time()
    export_id = None
    session = None
    response = None

    try:
        # 从PPT服务器获取slides数据
        slides_data = await get_ppt_slides(request, conversion_request.chatId, user.id, conversion_request.pptVersion)

        # 构造htmlPages
        html_pages = []
        pages = slides_data.get("pages", [])
        for i, page_content in enumerate(pages):
            # 使用前端传递的页面尺寸数据，如果没有则使用默认值
            if i < len(conversion_request.pageMetadata) and conversion_request.pageMetadata[i]:
                metadata = conversion_request.pageMetadata[i]
            else:
                # 默认尺寸（1280x720px 的精确转换）
                metadata = {
                    "pageWidth": 33.87,
                    "pageHeight": 19.05,
                }

            html_pages.append(
                {
                    "content": page_content,
                    "metadata": metadata,
                }
            )

        # 在options中添加title、slide_name等字段
        enhanced_options = dict(conversion_request.options)
        enhanced_options.update(
            {
                "title": slides_data.get("title", "A Nice Slide"),
                "slide_name": slides_data.get("title", "slide.pptx"),
                "desc": slides_data.get("desc", "A Nice Slide"),
                "page_num": slides_data.get("page_num", len(html_pages)),
                "exportMethod": conversion_request.exportMethod,  # 透传导出方案参数
            }
        )

        # 准备转发的请求数据
        request_data = {"htmlPages": html_pages, "options": enhanced_options}

        # 记录导出开始
        pages_count = len(html_pages)
        export_id = monitor.record_export_start("ppt", user.id, pages_count)

        log.info(f"Forwarding PPTX request with {pages_count} pages, export_id: {export_id}")
        log.info(
            f"Request data: {json.dumps(request_data, ensure_ascii=False, indent=2)}"
        )

        # 创建 aiohttp 会话
        timeout = aiohttp.ClientTimeout(total=300)  # 5分钟超时
        session = aiohttp.ClientSession(timeout=timeout, trust_env=True)

        # 转发请求到 converter 服务
        converter_url = f"{CONVERTER_BASE_URL}/api/convert/ppt/stream"
        log.info(f"Forwarding PPTX request to: {converter_url}")

        response = await session.post(
            converter_url,
            json=request_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "text/event-stream",
                "User-Agent": f"OpenWebUI-Proxy/1.0 (User: {user.id})",
            },
        )

        # 检查响应状态
        if not response.ok:
            error_text = await response.text()
            log.error(f"Converter service error: {response.status} - {error_text}")
            raise HTTPException(
                status_code=response.status,
                detail=f"Converter service error: {error_text}",
            )

        # 检查响应类型
        content_type = response.headers.get("Content-Type", "")
        if "text/event-stream" not in content_type:
            error_text = await response.text()
            log.error(f"Expected SSE stream, got: {content_type}")
            raise HTTPException(
                status_code=500,
                detail=f"Expected SSE stream, got: {content_type}. Response: {error_text}",
            )

        # 创建流式响应生成器
        async def stream_generator():
            file_size = None
            try:
                async for chunk in response.content.iter_chunked(8192):
                    if chunk:
                        yield chunk
                        # 尝试从响应头获取文件大小
                        if file_size is None:
                            content_length = response.headers.get("Content-Length")
                            if content_length:
                                try:
                                    file_size = int(content_length)
                                except ValueError:
                                    pass

                # 记录成功完成
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "ppt", user.id, duration, "success", file_size)

            except asyncio.CancelledError:
                log.info("PPTX stream cancelled by client")
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "ppt", user.id, duration, "cancelled")
                raise
            except Exception as e:
                log.error(f"Error in PPTX stream generator: {e}")
                duration = time.time() - start_time
                monitor.record_export_end(export_id, "ppt", user.id, duration, "failed", error_message=str(e))
                # 发送错误事件
                error_data = json.dumps(
                    {"status": "failed", "message": f"Stream error: {str(e)}"}
                )
                yield f"data: {error_data}\n\n".encode()
            finally:
                # 清理资源
                await cleanup_response(response, session)

        # 返回流式响应
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except HTTPException:
        # 重新抛出 HTTP 异常
        duration = time.time() - start_time
        if export_id:
            monitor.record_export_end(export_id, "ppt", user.id, duration, "failed", error_message="HTTP Exception")
        await cleanup_response(response, session)
        raise
    except Exception as e:
        log.error(f"PPTX stream conversion error: {e}")
        duration = time.time() - start_time
        if export_id:
            monitor.record_export_end(export_id, "ppt", user.id, duration, "failed", error_message=str(e))
        await cleanup_response(response, session)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/config")
async def get_converter_config(_: UserModel = Depends(get_verified_user)):
    """
    获取转换服务配置信息
    """
    return {"converter_base_url": CONVERTER_BASE_URL, "status": "available"}



