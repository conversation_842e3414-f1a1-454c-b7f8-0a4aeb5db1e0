import logging
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from open_webui.models.vibe_templates import (
    VibeTemplates,
    VibeTemplateModel,
    VibeTemplateForm,
)
from open_webui.utils.auth import get_admin_user
from open_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)

router = APIRouter()

############################
# VibeTemplateResponse
############################


class VibeTemplateResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    template_type: str
    content: str
    is_active: bool
    updated_at: int
    created_at: int


############################
# GetVibeTemplates
############################


@router.get("", response_model=list[VibeTemplateResponse])
async def get_vibe_templates(user=Depends(get_admin_user)):
    return VibeTemplates.get_templates()


############################
# GetVibeTemplateById
############################


@router.get("/{template_id}", response_model=Optional[VibeTemplateResponse])
async def get_vibe_template_by_id(template_id: str, user=Depends(get_admin_user)):
    template = VibeTemplates.get_template_by_id(template_id)
    if template:
        return VibeTemplateResponse(**template.model_dump())
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ERROR_MESSAGES.NOT_FOUND,
        )


############################
# CreateNewVibeTemplate
############################


@router.post("/", response_model=Optional[VibeTemplateResponse])
async def create_new_vibe_template(
    form_data: VibeTemplateForm, user=Depends(get_admin_user)
):
    # 检查ID是否已存在
    existing_template = VibeTemplates.get_template_by_id(form_data.id)
    if existing_template:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Template with this ID already exists",
        )

    template = VibeTemplates.insert_new_template(form_data)
    if template:
        return VibeTemplateResponse(**template.model_dump())
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ERROR_MESSAGES.DEFAULT(),
        )


############################
# UpdateVibeTemplateById
############################


@router.post("/{template_id}/update", response_model=Optional[VibeTemplateResponse])
async def update_vibe_template_by_id(
    template_id: str, form_data: VibeTemplateForm, user=Depends(get_admin_user)
):
    template = VibeTemplates.update_template_by_id(template_id, form_data)
    if template:
        return VibeTemplateResponse(**template.model_dump())
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ERROR_MESSAGES.NOT_FOUND,
        )


############################
# DeleteVibeTemplateById
############################


@router.delete("/{template_id}")
async def delete_vibe_template_by_id(template_id: str, user=Depends(get_admin_user)):
    result = VibeTemplates.delete_template_by_id(template_id)
    if result:
        return {"message": "Template deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ERROR_MESSAGES.NOT_FOUND,
        )


############################
# GetVibeTemplatesByType
############################


@router.get("/type/{template_type}", response_model=list[VibeTemplateResponse])
async def get_vibe_templates_by_type(
    template_type: str, user=Depends(get_admin_user)
):
    templates = VibeTemplates.get_templates_by_type(template_type)
    return [VibeTemplateResponse(**template.model_dump()) for template in templates]


############################
# InitializeDefaultTemplates
############################


@router.post("/initialize")
async def initialize_default_templates(user=Depends(get_admin_user)):
    """初始化默认模板"""
    try:
        VibeTemplates.create_default_templates()
        return {"message": "Default templates initialized successfully"}
    except Exception as e:
        log.error(f"Error initializing default templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize default templates",
        ) 