"""Peewee migrations -- 020_add_vibe_templates.py."""

from contextlib import suppress
import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext

def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    @migrator.create_model
    class VibeTemplate(pw.Model):
        id = pw.TextField(primary_key=True)
        name = pw.TextField()
        description = pw.TextField(null=True)
        template_type = pw.TextField()
        content = pw.TextField()
        is_active = pw.BooleanField(default=True)
        updated_at = pw.BigIntegerField()
        created_at = pw.BigIntegerField()

        class Meta:
            table_name = "vibe_template"


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    migrator.remove_model("vibe_template") 