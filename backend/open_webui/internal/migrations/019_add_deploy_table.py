"""Peewee migrations -- 019_add_your_table_name.py."""

from contextlib import suppress
import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext

def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    @migrator.create_model
    class Snippet(pw.Model):
        id = pw.TextField(unique=True)
        user_id = pw.TextField()
        chat_id = pw.TextField()
        message_id = pw.TextField()
        code_block_index = pw.IntegerField()
        lang = pw.TextField()
        code = pw.TextField()
        created_at = pw.BigIntegerField(null=False)

        class Meta:
            indexes = ((("user_id", "chat_id", "message_id", "code_block_index"), True), )
            table_name = "snippet"


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    migrator.remove_model("snippet")
