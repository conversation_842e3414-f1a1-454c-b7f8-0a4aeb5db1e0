from sqlalchemy import Column, String, Text, Integer, BigInteger
from open_webui.internal.db import Base, JSONField, get_db
import time
from typing import Optional
import logging
from open_webui.env import SRC_LOG_LEVELS
from pydantic import BaseModel, ConfigDict

log = logging.getLogger(__name__)
log.setLevel(SRC_LOG_LEVELS["MODELS"])

class Snippet(Base):
    __tablename__ = "snippet"
    id = Column(String, primary_key=True)
    user_id = Column(String)
    chat_id = Column(String)
    message_id = Column(String)
    code_block_index = Column(Integer)
    lang = Column(String)
    code = Column(Text)
    created_at = Column(BigInteger)


class SnippetMeta(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    user_id: str
    chat_id: str
    message_id: str
    code_block_index: int
    lang: str
    code: str
    created_at: int



class SnippetForm(BaseModel):
    id: str
    user_id: str
    chat_id: str
    message_id: str
    code_block_index: int
    lang: str
    code: str


class SnippetModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    user_id: str
    chat_id: str
    message_id: str
    code_block_index: int
    lang: str
    code: str
    created_at: int


class SnippetTable:
    def insert_new_snippet(self, form_data: SnippetForm) -> Optional[SnippetModel]:
        with get_db() as db:
            snippet = SnippetModel(
                **{
                    **form_data.model_dump(),
                    "created_at": int(time.time()),
                }
            )

            try:
                result = Snippet(**snippet.model_dump())
                db.add(result)
                db.commit()
                db.refresh(result)
                if result:
                    return SnippetModel.model_validate(result)
                else:
                    return None
            except Exception as e:
                log.exception(f"Error inserting a new snippet: {e}")
                return None

    def update_snippet(self, snippet_id: str, html: str) -> Optional[SnippetModel]:
        with get_db() as db:
            try:
                result = db.get(Snippet, snippet_id)
                result.code = html
                db.add(result)
                db.commit()
                db.refresh(result)
                return SnippetModel.model_validate(result)
            except Exception as e:
                log.exception(f"Error updating a snippet: {e}")
                return None

    def get_snippet_by_id(self, id: str) -> Optional[SnippetModel]:
        with get_db() as db:
            try:
                snippet = db.get(Snippet, id)
                return SnippetModel.model_validate(snippet)
            except Exception as e:
                log.exception(f"Error getting a snippet by id: {e}")
                return None
    
    def get_snippet_by_ucmi(self, user_id: str, chat_id: str, message_id: str, code_block_index: int) -> Optional[SnippetModel]:
        with get_db() as db:
            try:
                snippet = db.query(Snippet).filter_by(
                    user_id=user_id,
                    chat_id=chat_id,
                    message_id=message_id,
                    code_block_index=code_block_index
                ).first()
                return SnippetModel.model_validate(snippet) if snippet else None
            except Exception as e:
                log.exception(f"Error getting a snippet by ucmi: {e}")
                return None
