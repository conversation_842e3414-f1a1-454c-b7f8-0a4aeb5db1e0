import time
from typing import Optional

from open_webui.internal.db import Base, get_db

from pydantic import BaseModel, ConfigDict
from sqlalchemy import Big<PERSON>nteger, Column, String, Text, Boolean

####################
# Vibe Templates DB Schema
####################


class VibeTemplate(Base):
    __tablename__ = "vibe_template"

    id = Column(String, primary_key=True)  # 模板ID，如 "html_edit", "ppt_edit"
    name = Column(Text)  # 模板名称，如 "HTML代码修改模板"
    description = Column(Text, nullable=True)  # 模板描述
    template_type = Column(String)  # 模板类型: "html_edit", "ppt_edit", "artifacts_edit"
    content = Column(Text)  # 模板内容
    is_active = Column(Boolean, default=True)  # 是否启用
    updated_at = Column(BigInteger)
    created_at = Column(BigInteger)


class VibeTemplateModel(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    template_type: str
    content: str
    is_active: bool = True
    updated_at: int  # timestamp in epoch
    created_at: int  # timestamp in epoch

    model_config = ConfigDict(from_attributes=True)


####################
# Forms
####################


class VibeTemplateForm(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    template_type: str
    content: str
    is_active: bool = True


class VibeTemplatesTable:
    def insert_new_template(
        self, form_data: VibeTemplateForm
    ) -> Optional[VibeTemplateModel]:
        template = VibeTemplateModel(
            **{
                **form_data.model_dump(),
                "updated_at": int(time.time()),
                "created_at": int(time.time()),
            }
        )

        try:
            with get_db() as db:
                # 如果新模板是激活状态，先将同类型的其他模板设置为非激活
                if template.is_active:
                    existing_templates = db.query(VibeTemplate).filter_by(
                        template_type=template.template_type,
                        is_active=True
                    ).all()
                    for existing in existing_templates:
                        existing.is_active = False
                
                result = VibeTemplate(**template.model_dump())
                db.add(result)
                db.commit()
                db.refresh(result)
                if result:
                    return VibeTemplateModel.model_validate(result)
                else:
                    return None
        except Exception:
            return None

    def get_template_by_id(self, template_id: str) -> Optional[VibeTemplateModel]:
        try:
            with get_db() as db:
                template = db.query(VibeTemplate).filter_by(id=template_id).first()
                if template:
                    return VibeTemplateModel.model_validate(template)
                return None
        except Exception:
            return None

    def get_active_template_by_type(self, template_type: str) -> Optional[VibeTemplateModel]:
        try:
            with get_db() as db:
                template = db.query(VibeTemplate).filter_by(
                    template_type=template_type, is_active=True
                ).first()
                if template:
                    return VibeTemplateModel.model_validate(template)
                return None
        except Exception:
            return None

    def get_templates(self) -> list[VibeTemplateModel]:
        with get_db() as db:
            return [
                VibeTemplateModel.model_validate(template)
                for template in db.query(VibeTemplate).order_by(VibeTemplate.created_at.desc()).all()
            ]

    def get_templates_by_type(self, template_type: str) -> list[VibeTemplateModel]:
        with get_db() as db:
            return [
                VibeTemplateModel.model_validate(template)
                for template in db.query(VibeTemplate).filter_by(template_type=template_type).all()
            ]

    def update_template_by_id(
        self, template_id: str, form_data: VibeTemplateForm
    ) -> Optional[VibeTemplateModel]:
        try:
            with get_db() as db:
                template = db.query(VibeTemplate).filter_by(id=template_id).first()
                if template:
                    # 如果要将模板设置为激活状态，先将同类型的其他模板设置为非激活
                    if form_data.is_active:
                        existing_templates = db.query(VibeTemplate).filter_by(
                            template_type=form_data.template_type,
                            is_active=True
                        ).filter(VibeTemplate.id != template_id).all()
                        for existing in existing_templates:
                            existing.is_active = False
                    
                    template.name = form_data.name
                    template.description = form_data.description
                    template.template_type = form_data.template_type
                    template.content = form_data.content
                    template.is_active = form_data.is_active
                    template.updated_at = int(time.time())
                    db.commit()
                    return VibeTemplateModel.model_validate(template)
                return None
        except Exception:
            return None

    def delete_template_by_id(self, template_id: str) -> bool:
        try:
            with get_db() as db:
                template = db.query(VibeTemplate).filter_by(id=template_id).first()
                if template:
                    db.delete(template)
                    db.commit()
                    return True
                return False
        except Exception:
            return False

    def create_default_templates(self):
        """创建默认模板"""
        default_templates = [
            {
                "id": "ppt_edit",
                "name": "PPT模式模板",
                "description": "用于PPT模式的代码修改模板",
                "template_type": "ppt_edit",
                "content": """请按照以下要求修改PPT代码：

## 修改需求
{content}

## 目标代码的位置
{ppt_desc}第 {line} 行代码涉及到的元素

## 需要修改的代码
```html
{code}
```

## 输出要求
1. 请直接提供修改后的完整代码
2. 保持原有的代码结构和缩进格式
3. 确保修改后的代码语法正确且功能完善
4. 只修改必要的部分，保持其他代码不变
5. 注意PPT页面的布局和样式

请直接给我改好的完整代码即可，不要输出多余的内容，也不需要告诉我你修改了什么。""",
                "is_active": True
            },
            {
                "id": "artifacts_edit",
                "name": "Artifacts模式模板",
                "description": "用于Artifacts模式的代码修改模板",
                "template_type": "artifacts_edit",
                "content": """请按照以下要求修改代码：

## 修改需求
{content}

## 目标代码的位置
第 {line} 行代码涉及到的元素

## 需要修改的代码
```html
{code}
```

## 输出要求
1. 请直接提供修改后的完整代码
2. 保持原有的代码结构和缩进格式
3. 确保修改后的代码语法正确且功能完善
4. 只修改必要的部分，保持其他代码不变
5. 注意代码的功能性和交互性

请直接给我改好的完整代码即可，不要输出多余的内容，也不需要告诉我你修改了什么。""",
                "is_active": True
            }
        ]

        for template_data in default_templates:
            # 检查是否已存在
            existing = self.get_template_by_id(template_data["id"])
            if not existing:
                form_data = VibeTemplateForm(**template_data)
                self.insert_new_template(form_data)


VibeTemplates = VibeTemplatesTable() 