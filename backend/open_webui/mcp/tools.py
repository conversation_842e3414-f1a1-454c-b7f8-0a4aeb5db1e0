import json
import logging
import asyncio
import uuid
import os
import time
import re
from typing import Any, Dict, List, Optional, Tuple
from fastapi import Depends, Request
from mcp.shared.session import ProgressFnT


from open_webui.utils.auth import get_current_user
from open_webui.models.users import UserModel
from open_webui.socket.main import get_event_emitter
from open_webui.mcp.mcp import Server
from open_webui.env import ENV
from open_webui.utils.metrics import (
    mcp_tool_requests_total,
    mcp_tool_duration_seconds,
)
from open_webui.utils.ppt import Writer

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

# MCP工具流式回调配置
# 可以通过环境变量 MCP_STREAMING_ENABLED_TOOLS 来覆盖
# 格式: {"server1": ["tool1", "tool2"], "server2": ["*"], "*": ["specific_tool"]}
DEFAULT_STREAMING_ENABLED_TOOLS = {
    "deep-web-search": ["*"]  # deep-web-search 工具默认启用流式回调
}


def get_streaming_enabled_tools() -> Dict[str, List[str]]:
    """
    获取启用流式回调的工具配置

    Returns:
        Dict[str, List[str]]: 服务器名称到工具名称列表的映射
    """
    try:
        # 尝试从环境变量读取配置
        env_config = os.getenv("MCP_STREAMING_ENABLED_TOOLS")
        if env_config:
            config = json.loads(env_config)
            log.info(f"使用环境变量配置的MCP流式工具: {config}")
            return config
    except (json.JSONDecodeError, TypeError) as e:
        log.warning(f"解析MCP_STREAMING_ENABLED_TOOLS环境变量失败: {e}, 使用默认配置")

    return DEFAULT_STREAMING_ENABLED_TOOLS


def is_streaming_enabled(
    server_name: str, tool_name: str, config: Dict[str, List[str]] = None
) -> bool:
    """
    检查指定的服务器和工具组合是否启用流式回调

    Args:
        server_name: MCP服务器名称
        tool_name: 工具名称
        config: 配置字典，如果为None则使用默认配置

    Returns:
        bool: 是否启用流式回调
    """
    if config is None:
        config = get_streaming_enabled_tools()

    # 检查具体服务器的具体工具
    if server_name in config:
        tools = config[server_name]
        if "*" in tools or tool_name in tools:
            return True

    # 检查所有服务器的具体工具
    if "*" in config:
        tools = config["*"]
        if "*" in tools or tool_name in tools:
            return True

    return False


def conditional_progress_callback(
    server_name: str, tool_name: str, original_callback: ProgressFnT | None
) -> ProgressFnT | None:
    """
    创建条件性的progress_callback包装器

    Args:
        server_name: MCP服务器名称
        tool_name: 工具名称
        original_callback: 原始的progress_callback函数

    Returns:
        ProgressFnT | None: 包装后的callback函数或None
    """
    if original_callback is None:
        return None

    # 检查是否启用流式回调
    if not is_streaming_enabled(server_name, tool_name):
        log.debug(f"跳过MCP工具流式回调: {server_name}.{tool_name} (未在配置中启用)")
        return None

    log.debug(f"启用MCP工具流式回调: {server_name}.{tool_name}")

    # 返回原始callback
    return original_callback


def convert_mcp_tool_to_openai_function(tool: Dict[str, Any]) -> Dict[str, Any]:
    """
    将 MCP 工具转换为 OpenAI 函数格式

    Args:
        tool: MCP 工具信息

    Returns:
        符合 OpenAI function calling 格式的工具定义
    """
    def remove_title_fields(schema):
        """递归移除 schema 中的 title 字段"""
        if isinstance(schema, dict):
            # 创建新字典，排除 title 字段
            cleaned_schema = {}
            for key, value in schema.items():
                if key != "title":
                    cleaned_schema[key] = remove_title_fields(value)
            return cleaned_schema
        elif isinstance(schema, list):
            return [remove_title_fields(item) for item in schema]
        else:
            return schema

    # 确保输入模式符合规范
    input_schema = tool["inputSchema"]
    if "type" not in input_schema:
        input_schema["type"] = "object"
    
    # 移除 input_schema 中的 title 字段
    cleaned_input_schema = remove_title_fields(input_schema)

    return {
        "type": "function",
        "function": {
            "name": tool["name"],
            "description": tool["description"],
            "parameters": cleaned_input_schema,
        },
    }


async def handle_mcp_tool_call(
    server: Server,
    tool_name: str,
    tool_call_id: str,
    arguments: Dict[str, Any],
    event_emitter: Any,
    progress_callback: ProgressFnT | None = None,
) -> Any:
    """
    处理 MCP 工具调用，支持流式处理

    Args:
        server: MCP 服务器实例
        tool_name: 工具名称
        tool_call_id: 工具调用 ID
        arguments: 工具调用参数
        event_emitter: 事件发射器
        progress_callback: 进度回调函数，用于流式更新

    Returns:
        工具调用结果
    """
    # 记录开始时间，用于统计耗时
    start_time = time.time()

    # 记录详细的开始日志
    log.info(
        f"[MCP工具调用开始] 服务器: {server.name}, 工具: {tool_name}, 调用ID: {tool_call_id}"
    )
    log.info(
        f"[MCP工具调用参数] {tool_name}: {json.dumps(arguments, ensure_ascii=False, indent=2)}"
    )

    # 执行工具调用
    try:
        log.info(f"[MCP工具执行] 开始调用 {server.name}.{tool_name}")

        result = await server.execute_tool(
            tool_name, arguments, progress_callback=progress_callback
        )

        # 计算执行耗时
        execution_time = time.time() - start_time
        log.info(
            f"[MCP工具执行完成] {server.name}.{tool_name} 耗时: {execution_time:.3f}秒"
        )

        # 提取结果中的纯文本内容
        result_text = ""

        # 检查结果格式并提取文本
        if hasattr(result, "content") and isinstance(result.content, list):
            # 从 content 列表中提取文本和图片
            for item in result.content:
                if hasattr(item, "type"):
                    if item.type == "text" and hasattr(item, "text"):
                        result_text += item.text
                    elif item.type == "image" and hasattr(item, "data"):
                        # 处理图片内容
                        img_type = getattr(item, "mimeType", "image/png")
                        result_text += (
                            f"\n![图片](data:{img_type};base64,{item.data})\n"
                        )
                    elif item.type == "resource" and hasattr(item, "resource"):
                        # 处理嵌入资源
                        resource = item.resource
                        if hasattr(resource, "text"):
                            result_text += resource.text
                        elif hasattr(resource, "blob"):
                            mime_type = getattr(
                                resource, "mimeType", "application/octet-stream"
                            )
                            if "image" in mime_type:
                                result_text += f"\n![资源图片](data:{mime_type};base64,{resource.blob})\n"
                            else:
                                result_text += (
                                    f"\n[嵌入资源 ({mime_type})]({resource.uri})\n"
                                )
        elif isinstance(result, str):
            # 如果结果本身就是字符串
            result_text = result
        else:
            # 尝试转换为字符串
            try:
                result_text = str(result)
            except Exception:
                result_text = "工具执行成功，但结果格式无法处理"

        # 计算结果大小
        result_size = len(result_text.encode("utf-8"))

        # 记录成功的指标
        mcp_tool_requests_total.labels(
            server_name=server.name, tool_name=tool_name, status="success", env=ENV
        ).inc()

        mcp_tool_duration_seconds.labels(
            server_name=server.name, tool_name=tool_name, env=ENV
        ).observe(execution_time)

        # 记录成功日志
        log.info(
            f"[MCP工具调用成功] {server.name}.{tool_name} 结果大小: {result_size}字节, 总耗时: {execution_time:.3f}秒"
        )

        return result_text

    except Exception as e:
        # 计算失败时的耗时
        execution_time = time.time() - start_time
        error_message = f"调用失败: {str(e)}"

        # 记录失败的指标
        mcp_tool_requests_total.labels(
            server_name=server.name, tool_name=tool_name, status="error", env=ENV
        ).inc()

        mcp_tool_duration_seconds.labels(
            server_name=server.name, tool_name=tool_name, env=ENV
        ).observe(execution_time)

        # 记录详细的错误日志
        log.error(
            f"[MCP工具调用失败] {server.name}.{tool_name} 错误: {error_message}, 耗时: {execution_time:.3f}秒"
        )
        log.error(
            f"[MCP工具调用异常] {server.name}.{tool_name} 异常详情: {type(e).__name__}: {str(e)}"
        )

        return error_message

    finally:
        # 记录最终日志
        total_time = time.time() - start_time
        log.info(
            f"[MCP工具调用结束] {server.name}.{tool_name} 调用ID: {tool_call_id}, 总耗时: {total_time:.3f}秒"
        )


async def get_mcp_tools(
    request: Request, metadata: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    获取 MCP 服务器中的所有工具

    Args:
        request: FastAPI 请求对象
        metadata: 请求元数据

    Returns:
        MCP 工具列表，转换为 OpenAI 函数格式
    """
    mcp_servers = request.state.mcp_servers
    if not mcp_servers:
        return []

    all_tools = []

    for server in mcp_servers:
        try:
            tools = await server.list_tools()
            if server.name == "ppt-maker":
                all_tools.extend(
                    {"type": "function", "function": tool}
                    for tool in [
                        {
                            "description": "本工具用于创建新的演示文稿。在准备好幻灯片所需的材料后，即可使用。它将自动设定幻灯片的名称、尺寸和页数",
                            "name": "create_slide",
                            "parameters": {
                                "properties": {
                                    "description": {
                                        "description": "创建幻灯片的简要描述和概要，其中包括设计风格",
                                        "type": "string",
                                    },
                                    "title": {
                                        "description": "幻灯片的标题",
                                        "type": "string",
                                    },
                                    "slide_name": {
                                        "description": "幻灯片文件名",
                                        "type": "string",
                                    },
                                    "height": {
                                        "description": "幻灯片高度，默认720px",
                                        "type": "number",
                                    },
                                    "slide_num": {
                                        "description": "幻灯片页数，适配用户的意图和具体搜索到的内容",
                                        "type": "number",
                                    },
                                    "width": {
                                        "description": "幻灯片宽度，默认1280px",
                                        "type": "number",
                                    },
                                },
                                "type": "object",
                            },
                        },
                        {
                            "description": "根据收集的材料在特定位置插入一张新幻灯片",
                            "name": "add_slide",
                            "parameters": {
                                "properties": {
                                    "index": {
                                        "description": "插入幻灯片的下标",
                                        "type": "number",
                                    },
                                    "action_description": {
                                        "description": "插入幻灯片的动作描述，仅一句话",
                                        "type": "string",
                                    },
                                    "html": {
                                        "description": "该幻灯片的html代码",
                                        "type": "string",
                                    },
                                },
                                "type": "object",
                            },
                        },
                        {
                            "description": "删除幻灯片",
                            "name": "remove_slides",
                            "parameters": {
                                "properties": {
                                    "indexes": {
                                        "description": "待删除的幻灯片下标数组",
                                        "items": {"type": "number"},
                                        "type": "array",
                                    },
                                    "action_description": {
                                        "description": "删除幻灯片的动作描述，仅一句话",
                                        "type": "string",
                                    },
                                },
                                "type": "object",
                            },
                        },
                        {
                            "description": "修改幻灯片",
                            "name": "update_slide",
                            "parameters": {
                                "properties": {
                                    "index": {
                                        "description": "待修改幻灯片下标",
                                        "type": "number",
                                    },
                                    "action_description": {
                                        "description": "修改幻灯片的动作描述，仅一句话",
                                        "type": "string",
                                    },
                                    "html": {
                                        "description": "该幻灯片修改后的html代码",
                                        "type": "string",
                                    },
                                },
                                "type": "object",
                            },
                        },
                    ]
                )
            elif server.name == "web-search":
                all_tools.extend([
                    {
                        "type": "function",
                        "function": {
                            "name": "search",
                            "description": "在网络上搜索信息。这个工具可以帮你查找任何主题的相关信息，包括最新新闻、技术文档、学术资料等。你可以提供多个搜索查询，工具会并行执行所有搜索并返回相关网页，包括页面标题、URL和内容摘要。适合用于获取实时信息、验证事实、研究特定主题或寻找具体资源。",
                            "parameters": {
                                "properties": {
                                    "queries": {
                                        "description": "要执行的搜索查询列表。每个查询应该是一个具体的搜索词或短语，描述你想要查找的信息。例如：['Python异步编程教程', '2024年人工智能发展趋势', '如何优化数据库性能']",
                                        "items": {"type": "string"},
                                        "type": "array"
                                    }
                                },
                                "required": ["queries"],
                                "type": "object"
                            }
                        }
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "visit_page",
                            "description": "Opens a specific webpage in a browser for viewing. The URL provided points to the webpage to open. The tool loads the webpage for browsing and returns its main content for first page in Markdown format.",
                            "parameters": {
                                "properties": {
                                    "url": {
                                        "description": "The URL of the webpage to visit",
                                        "type": "string"
                                    }
                                },
                                "required": ["url"],
                                "type": "object"
                            }
                        }
                    }
                ])
            else:
                for tool in tools:
                    tool_spec = convert_mcp_tool_to_openai_function(
                        {
                            "name": tool.name,
                            "description": tool.description,
                            "inputSchema": tool.input_schema,
                        }
                    )
                    all_tools.append(tool_spec)
        except Exception as e:
            log.error(f"获取 MCP 工具列表失败: {e}")

    return all_tools


async def process_mcp_tools(
    request: Request,
    form_data: Dict[str, Any],
    user: UserModel,
    metadata: Dict[str, Any],
) -> Dict[str, Any]:
    """
    处理 MCP 工具调用，将工具添加到请求中

    Args:
        request: FastAPI 请求对象
        form_data: 请求表单数据
        user: 用户对象
        metadata: 请求元数据

    Returns:
        处理后的表单数据和额外的元数据标志
    """
    mcp_servers = request.state.mcp_servers
    if not mcp_servers:
        return form_data

    # 获取所有 MCP 工具，转换为 OpenAI 格式
    tools = await get_mcp_tools(request, metadata)

    if not tools:
        return form_data

    # 如果模型原来已有工具，合并它们并去重
    if "tools" in form_data:
        existing_tools = form_data.get("tools", [])
        if isinstance(existing_tools, list):
            # 创建工具名称到工具的映射
            tool_map = {
                tool.get("function", {}).get("name"): tool for tool in existing_tools
            }
            # 只添加不存在的工具
            for new_tool in tools:
                tool_name = new_tool.get("function", {}).get("name")
                if tool_name not in tool_map:
                    tool_map[tool_name] = new_tool
            # 将映射转换回列表
            form_data["tools"] = list(tool_map.values())
    else:
        form_data["tools"] = tools

    # 存储 MCP 服务器和工具到元数据中，方便后续响应处理
    metadata["mcp_enabled"] = True

    return form_data


async def handle_mcp_response_tool_calls(
    request: Request,
    tool_calls: List[Dict[str, Any]],
    metadata: Dict[str, Any],
    event_emitter=None,
    progress_cb_factory=None,
    stream_writers: dict = None,
) -> List[Dict[str, Any]]:
    """
    处理响应中的 MCP 工具调用，支持流式处理

    Args:
        tool_calls: 工具调用列表
        metadata: 请求元数据
        event_emitter: 事件发射器
        progress_cb_factory: 进度回调工厂函数
        stream_writers: 流式写入器字典

    Returns:
        工具调用结果列表
    """
    if not metadata.get("mcp_enabled", False):
        return []

    mcp_servers: List[Server] = request.state.mcp_servers
    if not mcp_servers:
        return []

    results = []
    batch_start_time = time.time()

    # 记录批量工具调用开始日志
    log.info(f"[MCP批量工具调用开始] 总数: {len(tool_calls)} 个工具")
    for i, tool_call in enumerate(tool_calls):
        tool_name = tool_call.get("function", {}).get("name", "未知工具")
        tool_call_id = tool_call.get("id", "未知ID")
        log.info(f"[MCP批量工具调用] 第{i + 1}个: {tool_name} (ID: {tool_call_id})")

    for tool_call in tool_calls:
        tool_call_id = tool_call.get("id", f"call_{uuid.uuid4()}")
        tool_name = tool_call.get("function", {}).get("name", "")
        tool_arguments_str = tool_call.get("function", {}).get("arguments", "{}")

        if tool_call_id in stream_writers:

            writer: Writer = stream_writers.pop(tool_call_id)
            result = await writer.retrieve()
            if result.get("error", ""):
                results.append({
                    "tool_call_id": tool_call_id,
                    "content": result.get("error", ""),
                    "status": "error",
                })
                mcp_tool_requests_total.labels(
                    server_name="ppt-maker",
                    tool_name=tool_name,
                    status="error",
                    env=ENV
                ).inc()
                await writer.close()
                continue
            mcp_tool_requests_total.labels(
                server_name="ppt-maker",
                tool_name=tool_name,
                status="success",
                env=ENV
            ).inc()
            title = result.get("title", "")
            position = result.get("position", 0)
            content = result.get("content", "")
            facade_content = result.get("action_description")
            version = result.get("version", "")
            results.append(
                {
                    "tool_call_id": tool_call_id,
                    "content": content,
                    "facade_content": facade_content,
                    "ppt": {
                        "name": title,
                        "show": tool_name != "create_slide",
                        "position": position,
                        "version": version,
                    },
                    "status": "completed",
                }
            )
            await writer.close()
            continue

        log.info(
            f"处理工具调用: 名称 {tool_name} 参数 {tool_arguments_str} ID: {tool_call_id}"
        )

        try:
            # 尝试解析参数
            tool_arguments = json.loads(tool_arguments_str)
        except json.JSONDecodeError:
            log.error(f"解析工具调用参数失败: {tool_arguments_str}")
            tool_arguments = {}
        # tool_arguments["MAGIC_PARAMS"] = {"foo": "bar"}

        # 在所有 MCP 服务器中查找并执行工具
        tool_result = None
        for server in mcp_servers:
            try:
                # 检查服务器是否有此工具
                server_tools = await server.list_tools()
                server_tool_names = [t.name for t in server_tools]

                if tool_name in server_tool_names:
                    log.info(f"在服务器 {server.name} 上找到工具 {tool_name}，开始执行")

                    # 创建进度回调，用于流式更新
                    progress_callback = None
                    if progress_cb_factory:
                        progress_callback = progress_cb_factory(
                            server.name, tool_name, tool_call_id
                        )

                    # 使用条件性包装器来决定是否启用流式回调
                    conditional_status_change_callback = conditional_progress_callback(
                        server_name=server.name,
                        tool_name=tool_name,
                        original_callback=progress_callback,
                    )

                    # 通过conditional_callback发送工具开始执行的状态
                    if conditional_status_change_callback:
                        await conditional_status_change_callback(
                            i=0,
                            n=1,
                            msg=json.dumps(
                                {
                                    "tool_call_id": tool_call_id,
                                    "content": "",
                                    "status": "executing",
                                }
                            ),
                        )
                    # 执行工具调用
                    tool_result = await handle_mcp_tool_call(
                        server,
                        tool_name,
                        tool_call_id,
                        tool_arguments,
                        event_emitter,
                        progress_callback=progress_callback,
                    )
                    log.info(
                        f"工具 {tool_name} 执行完成，结果长度: {len(str(tool_result))}"
                    )

                    # 工具执行完成后，发送完成状态的 callback
                    if conditional_status_change_callback:
                        await conditional_status_change_callback(
                            i=1,
                            n=1,
                            msg=json.dumps(
                                {
                                    "tool_call_id": tool_call_id,
                                    "content": "",
                                    "status": "completed",
                                }
                            ),
                        )

                    break
            except Exception as e:
                log.error(f"MCP 工具调用失败: {e}")
                tool_result = f"调用失败: {str(e)}"

        if tool_result is None:
            tool_result = f"未找到工具: {tool_name}"
            log.warning(f"未找到工具: {tool_name}")

        # 检查是否是search工具，如果是，解析结果并添加browser字段
        if is_deep_web_search_tool(tool_name) and isinstance(tool_result, str):
            parsed_citations = []
            if tool_name == "search":
                parsed_citations = parse_mcp_search_citations_from_metadata(tool_result)
                if parsed_citations:
                    log.info(f"成功为工具调用 {tool_call_id} 解析出 {len(parsed_citations)} 个搜索结果引用")
                else:
                    log.warning(f"工具调用 {tool_call_id} 未能解析出有效的搜索结果引用")

            metadata = parse_browser_metadata(tool_result)

            # 提取纯文本内容（如果是JSON格式，提取content字段；否则使用原始内容）
            clean_content = extract_pure_content_from_mcp_result(tool_result)

            # 构建包含搜索结果的响应
            result_entry = {
                "tool_call_id": tool_call_id,
                "content": clean_content,  # 只包含纯文本内容，不包含元数据
                "browser": {
                    "search_result": parsed_citations,
                    "current_url": metadata.get("browser_state", {}).get("current_url", ""),
                    "page_title": metadata.get("browser_state", {}).get("page_title", ""),
                },
            }

            # 如果解析到了有效的引用，记录成功统计
          
        else:
            result_entry = {
                "tool_call_id": tool_call_id,
                "content": tool_result,
            }

        results.append(result_entry)

    # 记录批量工具调用结束日志
    batch_total_time = time.time() - batch_start_time
    successful_calls = len(
        [
            r
            for r in results
            if not str(r.get("content", "")).startswith("调用失败")
            and not str(r.get("content", "")).startswith("未找到工具")
        ]
    )
    failed_calls = len(results) - successful_calls

    log.info(
        f"[MCP批量工具调用完成] 总数: {len(tool_calls)}, 成功: {successful_calls}, 失败: {failed_calls}, 总耗时: {batch_total_time:.3f}秒"
    )

    return results


def is_deep_web_search_tool(tool_name: str) -> bool:
    """
    判断是否是搜索工具

    Args:
        tool_name: 工具名称

    Returns:
        是否是搜索工具
    """
    search_tool_names = [
        "search",
        "visit_page",                    # 访问指定网页
        "click",                         # 点击页面元素
        "page_up",                       # 向上滚动一页
        "page_down",                     # 向下滚动一页
        "find_on_page_ctrl_f",          # 在页面中搜索字符串
        "find_next",                     # 查找下一个搜索结果
        "go_back",                       # 返回上一页
    ]
    tool_name_lower = tool_name.lower()
    # 使用包含匹配而不是精确匹配，因为工具名称可能有前缀
    is_search = any(
        search_name.lower() in tool_name_lower for search_name in search_tool_names
    )
    return is_search


def parse_browser_metadata(text: str) -> Dict[str, Any]:
    """
    解析搜索结果的元数据，从 MCP 工具返回的 JSON 结果中提取元数据信息

    Args:
        text: MCP 工具返回的原始文本，可能包含 JSON 格式的元数据

    Returns:
        解析出的元数据字典，包含 browser_state 等信息
    """
    metadata = {}

    try:
        # 尝试从文本中提取 JSON 数据
        json_data = extract_json_from_text(text)
        if json_data and isinstance(json_data, dict):
            # 提取 metadata 字段
            if "metadata" in json_data:
                metadata = json_data["metadata"]
                log.debug(f"成功从 MCP 结果中提取元数据: {list(metadata.keys())}")
            else:
                log.debug("MCP 结果中未找到 metadata 字段")
        else:
            log.debug("MCP 结果不是有效的 JSON 格式")

    except Exception as e:
        log.warning(f"解析 MCP 结果元数据失败: {e}")

    return metadata


def extract_pure_content_from_mcp_result(text: str) -> str:
    """
    从 MCP 工具返回结果中提取纯文本内容

    如果输入是 JSON 格式，提取 content 字段；
    否则返回原始文本内容。

    Args:
        text: MCP 工具返回的原始文本

    Returns:
        提取的纯文本内容
    """
    if not text or not isinstance(text, str):
        return ""

    try:
        # 尝试解析为 JSON
        data = extract_json_from_text(text)
        if data and isinstance(data, dict):
            # 如果是 JSON 格式，提取 content 字段
            content = data.get("content", "")
            if content:
                return str(content)
    except (json.JSONDecodeError, KeyError, AttributeError):
        pass

    # 如果不是 JSON 格式或解析失败，返回原始文本
    return text


def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    从文本中提取 JSON 数据

    支持多种格式：
    1. 完整的 JSON 字符串
    2. 包含 JSON 的文本（提取第一个完整的 JSON 对象）
    3. 多行 JSON 格式

    Args:
        text: 包含 JSON 数据的文本

    Returns:
        解析出的 JSON 对象，如果解析失败则返回 None
    """
    if not text or not isinstance(text, str):
        return None

    try:
        # 首先尝试直接解析整个文本
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    try:
        # 尝试查找并提取 JSON 对象
        # 查找以 { 开始的 JSON 对象
        json_start = text.find('{')
        if json_start == -1:
            return None

        # 从找到的位置开始，尝试解析 JSON
        json_text = text[json_start:]

        # 使用简单的括号匹配来找到完整的 JSON 对象
        brace_count = 0
        json_end = -1

        for i, char in enumerate(json_text):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    json_end = i + 1
                    break

        if json_end > 0:
            complete_json = json_text[:json_end]
            return json.loads(complete_json)

    except (json.JSONDecodeError, IndexError):
        pass

    return None





def parse_mcp_search_citations_from_metadata(text: str) -> List[Dict[str, Any]]:
    """
    解析 MCP search 工具返回的 JSON 格式元数据

    从 MCP 工具返回的 JSON 结构中提取 metadata.search_results 数组，
    并将其转换为 OpenWebUI 兼容的 citation 格式。

    Args:
        text: MCP 工具返回的原始文本（可能包含 JSON 格式的元数据）

    Returns:
        解析出的搜索结果列表，每个元素包含 title, url, text, index 等字段
    """
    citations = []

    try:
        # 使用改进的 JSON 提取函数
        data = extract_json_from_text(text)
        if not data:
            return []

        # 检查是否有 metadata 字段
        metadata = data.get("metadata")
        if not metadata:
            return []

        # 提取 search_results 数组
        search_results = metadata.get("search_results")
        if not search_results or not isinstance(search_results, list):
            return []


        # 转换每个搜索结果为 citation 格式
        for i, result in enumerate(search_results):
            try:
                # 构建原始 citation 对象
                raw_citation = {
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "text": result.get("snippet", result.get("text", "")),
                    "index": result.get("index", i + 1),
                    "favicon": result.get("favicon", ""),
                }

                # 添加可选字段
                if "host_name" in result:
                    raw_citation["host_name"] = result["host_name"]

                # 验证基本字段
                if raw_citation.get("title") and raw_citation.get("url"):
                    citations.append(raw_citation)
                else:
                    log.warning(f"跳过无效的搜索结果 {i+1}: {result}")

            except Exception as e:
                log.warning(f"解析第 {i+1} 个搜索结果时出错: {e}")
                continue

        # 按 index 排序并重新分配连续索引
        citations.sort(key=lambda x: x.get("index", 0))
        for i, citation in enumerate(citations):
            citation["index"] = i + 1


    except Exception as e:
        log.warning(f"解析 MCP 搜索元数据失败: {e}")
        return []

    return citations


"""
新增的MCP工具调用监控功能说明:

1. 详细的结构化日志记录:
   - [MCP工具调用开始] 记录调用开始信息
   - [MCP工具调用参数] 记录详细参数（格式化JSON）
   - [MCP工具执行] 记录实际执行过程
   - [MCP工具执行完成] 记录执行完成和耗时
   - [MCP工具调用成功] 记录成功统计信息
   - [MCP工具调用失败] 记录失败详情和异常
   - [MCP工具调用结束] 记录最终总结
   - [MCP批量工具调用开始/完成] 记录批量处理统计

2. Prometheus指标监控:
   - mcp_tool_requests_total: 请求总数计数器(按服务器、工具、状态分类)
   - mcp_tool_duration_seconds: 执行耗时直方图(0.1s到300s分桶)

3. 监控维度:
   - server_name: MCP服务器名称
   - tool_name: 工具名称
   - status: 执行状态(success/error)
   - env: 环境标识

4. 性能统计:
   - 单个工具调用耗时
   - 批量工具调用总耗时
   - 结果数据大小统计
   - 成功失败比例统计

示例Prometheus查询:
- 工具调用成功率: rate(mcp_tool_requests_total{status="success"}[5m]) / rate(mcp_tool_requests_total[5m])
- 平均执行时间: rate(mcp_tool_duration_seconds_sum[5m]) / rate(mcp_tool_duration_seconds_count[5m])
- 当前并发数: mcp_tool_concurrent_executions
"""
