# Open WebUI MCP 配置示例
# 可以将此文件复制到 DATA_DIR/mcp.yaml 并进行修改

mcpServers:
  deep-web-search:
    id: deep-web-search
    displayName: 深度网络搜索
    displayName_zh: 深度网络搜索
    displayName_en: Deep Web Search
    description: 用于操作浏览器进行深度网络搜索
    enabled: true
    icon_name: search
    url: http://toolkit-wrap.mcp.server/mcp
  ppt-maker:
    id: ppt-maker
    displayName: PPT 制作工具
    displayName_zh: PPT 制作工具
    displayName_en: PPT Maker
    description: Embrace the Vibe, Elevate Your Slides!
    enabled: true
    icon_name: presentation
    url: http://svc-mcp-ppt:8000/mcp
  web-search:
    id: web-search
    displayName: 普通网络搜索
    displayName_zh: 普通网络搜索
    displayName_en: Web Search
    description: 普普通通的搜索
    enabled: true
    icon_name: search
    url: http://toolkit-wrap.mcp.server/mcp
