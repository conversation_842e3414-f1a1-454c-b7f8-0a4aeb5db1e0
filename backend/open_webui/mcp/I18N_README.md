# MCP 配置国际化支持

本文档描述了 MCP (Model Context Protocol) 配置的国际化功能实现。

## 功能概述

为 MCP 配置添加了国际化支持，允许根据用户的语言偏好显示不同语言的服务器名称，并新增了图标名称字段用于前端显示。

## 新增字段

### MCPServerConfig 模型新增字段

- `displayName_zh`: 中文显示名称（可选）
- `displayName_en`: 英文显示名称（可选）
- `icon_name`: 图标名称，用于前端显示（可选）

### 字段说明

1. **displayName_zh**: 服务器的中文显示名称
2. **displayName_en**: 服务器的英文显示名称
3. **icon_name**: 前端显示图标的名称标识
4. **displayName**: 保留原有的默认显示名称字段，作为回退选项

## 语言回退机制

本地化显示名称的选择遵循以下优先级：

### 中文环境 (lang.startswith("zh"))
1. `displayName_zh` - 中文显示名称
2. `displayName` - 默认显示名称
3. `id` - 服务器 ID

### 英文环境 (其他语言)
1. `displayName_en` - 英文显示名称
2. `displayName` - 默认显示名称
3. `id` - 服务器 ID

## API 变更

### 主配置接口 (/api/config)

- 自动解析 `Accept-Language` 请求头
- 根据用户语言偏好返回本地化的 MCP 服务器配置
- 新增 `icon_name` 字段在返回的配置中

### 返回格式示例

```json
{
  "mcp_servers": [
    {
      "name": "deep-web-search",
      "title": "深度网络搜索",  // 根据语言本地化
      "description": "用于操作浏览器进行深度网络搜索",
      "icon_name": "search"
    }
  ]
}
```

## 配置文件示例

```yaml
mcpServers:
  deep-web-search:
    id: deep-web-search
    displayName: 深度网络搜索          # 默认显示名称
    displayName_zh: 深度网络搜索       # 中文显示名称
    displayName_en: Deep Web Search    # 英文显示名称
    description: 用于操作浏览器进行深度网络搜索
    enabled: true
    icon_name: search                  # 图标名称
    url: http://toolkit-wrap.mcp.server/mcp
```

## 实现细节

### 核心方法

1. **MCPServerConfig.get_localized_display_name(lang: str)**
   - 根据语言代码返回本地化的显示名称
   - 支持语言代码格式：`zh`, `zh-CN`, `en`, `en-US` 等

2. **MCPConfig.to_app_config(lang: str)**
   - 将 MCP 配置转换为前端应用配置
   - 包含本地化的显示名称和图标信息

### 语言检测

- 从 HTTP 请求头 `Accept-Language` 中提取用户语言偏好
- 解析格式：`zh-CN,zh;q=0.9,en;q=0.8` → `zh-CN`
- 支持带权重的语言偏好设置

## 向后兼容性

- 保持与现有配置文件的完全兼容
- 新字段均为可选，不影响现有配置
- 如果没有提供本地化字段，自动回退到默认值

## 使用场景

1. **多语言环境**: 为不同语言用户提供本地化的服务器名称
2. **图标显示**: 前端可以根据 `icon_name` 显示相应的图标
3. **用户体验**: 提升多语言用户的使用体验

## 注意事项

- 语言代码检测基于 `Accept-Language` 请求头
- 目前主要支持中文 (`zh`) 和英文 (`en`) 两种语言
- 图标名称需要与前端图标资源保持一致
- 配置更新后需要重启应用或重新加载配置才能生效
