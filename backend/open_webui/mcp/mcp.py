from contextlib import AsyncExitStack
import os
import shutil
import time
from fastapi import FastAPI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client
import asyncio
from typing import Any
import logging
from datetime import timedelta

from open_webui.models.users import User
from mcp.shared.session import ProgressFnT
from open_webui.utils.metrics import (
    mcp_server_initialization_total,
    mcp_server_initialization_duration_seconds,
    mcp_individual_server_initialization_total
)
from open_webui.env import ENV

log = logging.getLogger(__name__)
log.setLevel(logging.DEBUG)

class Server:
    """Manages MCP server connections and tool execution."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()

    async def initialize(self, headers: dict[str, str]) -> None:
        """Initialize the server connection."""
        try:
            if self.config.url.endswith("/mcp"):
                # 使用 StreamableHTTP 协议
                log.info(
                    f"Initializing StreamableHTTP server {self.name} with URL: {self.config.url}"
                )
                streams = await self.exit_stack.enter_async_context(
                    streamablehttp_client(self.config.url, headers=headers, timeout=timedelta(seconds=3000))
                )
                read, write, _ = streams
                session = await self.exit_stack.enter_async_context(
                    ClientSession(read, write)
                )
            elif self.config.url:
                # 使用 SSE 协议
                log.info(f"Initializing SSE server {self.name} with URL: {self.config.url}")
                streams = await self.exit_stack.enter_async_context(
                    sse_client(self.config.url, headers=headers)
                )
                read, write = streams
                session = await self.exit_stack.enter_async_context(
                    ClientSession(read, write)
                )
            else:
                # 使用 STDIO 协议
                log.info(f"Initializing STDIO server {self.name}")
                command = (
                    shutil.which("npx")
                    if self.config.command == "npx"
                    else self.config.command
                )
                if command is None:
                    raise ValueError("The command must be a valid string and cannot be None.")

                server_params = StdioServerParameters(
                    command=command,
                    args=self.config.args,
                    env={**os.environ, **self.config.env}
                    if self.config.env
                    else None,
                )
                stdio_transport = await self.exit_stack.enter_async_context(
                    stdio_client(server_params)
                )
                read, write = stdio_transport
                session = await self.exit_stack.enter_async_context(
                    ClientSession(read, write)
                )

            await session.initialize()
            self.session = session
            log.info(f"[MCP] Server {self.name} initialized successfully")
        except asyncio.CancelledError:
            log.error(f"[MCP] Server {self.name} initialization cancelled")
            # 安全地进行清理，不让清理异常掩盖原始的 CancelledError
            try:
                await self.cleanup()
            except Exception as cleanup_error:
                log.error(f"[MCP] Error during cleanup after cancellation for server {self.name}: {cleanup_error}")
            raise
        except Exception as e:
            log.error(f"[MCP] Error initializing server {self.name}: {e}")
            # 安全地进行清理，不让清理异常掩盖原始异常
            try:
                await self.cleanup()
            except Exception as cleanup_error:
                log.error(f"[MCP] Error during cleanup after initialization failure for server {self.name}: {cleanup_error}")
            raise Exception(f"[MCP] Error initializing server {self.name}: {e}")

    async def list_tools(self) -> list[Any]:
        """List available tools from the server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self.session:
            raise RuntimeError(f"[MCP] Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []

        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                for tool in item[1]:
                    tools.append(Tool(tool.name, tool.description, tool.inputSchema))

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
        progress_callback: ProgressFnT | None = None,
    ) -> Any:
        """Execute a tool with retry mechanism.

        Args:
            tool_name: Name of the tool to execute.
            arguments: Tool arguments.
            retries: Number of retry attempts.
            delay: Delay between retries in seconds.

        Returns:
            Tool execution result.

        Raises:
            RuntimeError: If server is not initialized.
            Exception: If tool execution fails after all retries.
        """
        if not self.session:
            raise RuntimeError(f"[MCP] Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                log.info(f"[MCP] Executing {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments, progress_callback=progress_callback)

                return result

            except Exception as e:
                attempt += 1
                log.warning(
                    f"[MCP] Error executing tool: {e}. Attempt {attempt} of {retries}."
                )
                if attempt < retries:
                    log.info(f"[MCP] Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    log.error("[MCP] Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        async with self._cleanup_lock:
            try:
                try:
                    await self.exit_stack.aclose()
                except (asyncio.CancelledError, asyncio.InvalidStateError) as e:
                    log.warning(f"[MCP] Async context cleanup issue for server {self.name}: {e}. This may be due to task context changes.")
                self.session = None
                self.stdio_context = None
            except Exception as e:
                log.error(f"[MCP] Error during cleanup of server {self.name}: {e}")


class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self, name: str, description: str, input_schema: dict[str, Any]
    ) -> None:
        self.name: str = name
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """Format tool information for LLM.

        Returns:
            A formatted string describing the tool.
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        return f"""
Tool: {self.name}
Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""

async def initialize_mcp_servers(
    app: FastAPI,
    server_ids: list[str],
    user: User,
    message_id: str | None = None,
    chat_id: str | None = None,
    language: str = "en"
) -> dict[str, Server]:
    """Initialize all MCP servers."""
    start_time = time.time()
    i = 0
    mcp_servers = {}

    try:
        for server_config in app.state.MCP_CONFIG.mcpServers.values():
            if not server_config.enabled:
                continue
            if server_config.id not in server_ids:
                continue

            server = Server(server_config.id, server_config)
            try:
                await server.initialize(headers={
                    "X-User-Id": user.id,
                    "X-User-Email": user.email,
                    "X-User-Role": user.role,
                    "X-Message-Id": message_id,
                    "X-Chat-Id": chat_id,
                    "Accept-Language": language
                })
                mcp_servers[server_config.id] = server
                log.info(f"[MCP] Initialized server {server_config.id}")
                i += 1

                # 记录单个服务器初始化成功
                mcp_individual_server_initialization_total.labels(
                    server_id=server_config.id,
                    status="success",
                    env=ENV
                ).inc()

            except asyncio.CancelledError as e:
                log.error(f"[MCP] Server {server_config.id} initialization cancelled: {e}")
                # 记录单个服务器初始化失败
                mcp_individual_server_initialization_total.labels(
                    server_id=server_config.id,
                    status="failure",
                    env=ENV
                ).inc()
                raise
            except Exception as e:
                log.error(f"[MCP] Failed to initialize server {server_config.id}: {e}")
                # 记录单个服务器初始化失败
                mcp_individual_server_initialization_total.labels(
                    server_id=server_config.id,
                    status="failure",
                    env=ENV
                ).inc()
                raise
        #  抛出异常，不处理
        # 不再构建工具映射缓存，改为实时查找
        log.info(f"[MCP] Initialized {i} servers 😊")

        # 记录整体初始化成功
        mcp_server_initialization_total.labels(
            status="success",
            env=ENV
        ).inc()

        return mcp_servers

    except asyncio.CancelledError as e:
        log.error(f"[MCP] Servers initialization cancelled: {e}")
        # 记录整体初始化失败
        mcp_server_initialization_total.labels(
            status="failure",
            env=ENV
        ).inc()
        raise
    except Exception as e:
        log.error(f"[MCP] Error during servers initialization: {e}")
        # 记录整体初始化失败
        mcp_server_initialization_total.labels(
            status="failure",
            env=ENV
        ).inc()
        raise
    finally:
        # 记录初始化耗时
        duration = time.time() - start_time
        mcp_server_initialization_duration_seconds.labels(env=ENV).observe(duration)

async def cleanup_mcp_servers(mcp_servers: list[Server]):
    """Clean up all MCP servers.
    
    Args:
        mcp_servers: List of MCP server instances to clean up
    """
    cleanup_count = 0
    for server in mcp_servers:
        try:
            await server.cleanup()
            cleanup_count += 1
        except Exception as e:
            log.error(f"[MCP] Failed to clean up server {server.name}: {e}")
    
    log.info(f"[MCP] Cleaned up {cleanup_count}/{len(mcp_servers)} servers 😊")


def invalidate_tool_cache():
    """清理工具缓存，在MCP配置更新时调用（保留接口兼容性）"""
    log.info("[MCP] Tool cache invalidated (no-op in direct lookup mode)")

async def get_server_name_by_tool(tool_name: str, mcp_servers: list[Server]) -> str | None:
    """根据工具名称获取服务器名称（实时查找）

    Args:
        tool_name: 工具名称
        mcp_servers: MCP服务器字典（必需）

    Returns:
        str | None: 服务器名称，如果未找到则返回None
    """
    if not mcp_servers:
        log.warning(f"[MCP] No mcp_servers provided for tool: {tool_name}")
        return None

    # 遍历所有服务器，查找包含指定工具的服务器
    for server in mcp_servers:
        try:
            tools = await server.list_tools()
            for tool in tools:
                if tool.name == tool_name:
                    log.info(f"[MCP] Found tool '{tool_name}' in server '{server.name}'")
                    return server.name
        except Exception as e:
            log.error(f"[MCP] Failed to list tools for server {server.name}: {e}")
            continue

    log.warning(f"[MCP] Tool '{tool_name}' not found in any server")
    return None