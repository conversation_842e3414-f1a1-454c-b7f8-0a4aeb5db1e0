# OpenWebUI Prometheus 监控指标

本文档描述了 OpenWebUI 后端中新增的 Prometheus 监控指标，用于监控 SSE 连接和后台任务的状态。

## 新增指标

### 1. SSE 并发连接数监控

**指标名称**: `sse_concurrent_connections`
**类型**: Gauge
**描述**: 当前并发的 SSE 连接数
**标签**:
- `model_id`: 模型ID
- `env`: 环境标识（prod/dev）

**使用场景**:
- 监控当前有多少个活跃的 SSE 连接
- 检测连接数异常增长
- 优化连接池配置

**更新时机**:
- SSE 连接建立时：`sse_concurrent_connections.labels(model_id=model_id, env=env).inc()`
- SSE 连接断开时：`sse_concurrent_connections.labels(model_id=model_id, env=env).dec()`

### 2. 后台任务监控

**指标名称**: `background_tasks_active`
**类型**: Gauge
**描述**: 当前活跃的后台任务数量
**标签**:
- `model_id`: 模型ID
- `env`: 环境标识（prod/dev）

**使用场景**:
- 监控后台任务的执行情况
- 检测任务堆积问题
- 分析不同模型的任务负载

**更新时机**:
- 后台任务创建时：`background_tasks_active.labels(model_id=model_id, env=env).inc()`
- 后台任务完成/取消时：`background_tasks_active.labels(model_id=model_id, env=env).dec()`

## 实现细节

### 代码位置

1. **指标定义**: `backend/open_webui/utils/metrics.py`
2. **SSE 连接监控**: `backend/open_webui/utils/middleware.py`
3. **后台任务监控**: `backend/open_webui/tasks.py`

### SSE 连接监控实现

在 `middleware.py` 的 `sse_channel_wrapper` 函数中：

```python
# 连接建立时
model_id = form_data.get('model', 'unknown')
sse_concurrent_connections.labels(model_id=model_id, env=ENV).inc()

# 连接断开时
sse_concurrent_connections.labels(model_id=model_id, env=ENV).dec()
```

### 后台任务监控实现

在 `tasks.py` 中：

```python
# 任务创建时
def create_task(coroutine, task_id: str = None, model_id: str = 'unknown'):
    # ... 其他逻辑
    task_model_mapping[task_id] = model_id
    background_tasks_active.labels(model_id=model_id, env=ENV).inc()

# 任务清理时
def cleanup_task(task_id: str):
    model_id = task_model_mapping.pop(task_id, 'unknown')
    background_tasks_active.labels(model_id=model_id, env=ENV).dec()
```

## 监控和告警建议

### Prometheus 查询示例

1. **查看当前 SSE 连接数**:
   ```promql
   sse_concurrent_connections
   ```

2. **按模型分组的 SSE 连接数**:
   ```promql
   sum(sse_concurrent_connections) by (model_id)
   ```

3. **查看当前后台任务数**:
   ```promql
   background_tasks_active
   ```

4. **按模型分组的后台任务数**:
   ```promql
   sum(background_tasks_active) by (model_id)
   ```

### 告警规则建议

1. **SSE 连接数过高告警**:
   ```yaml
   - alert: HighSSEConnections
     expr: sum(sse_concurrent_connections) > 100
     for: 5m
     labels:
       severity: warning
     annotations:
       summary: "SSE 连接数过高"
       description: "当前 SSE 连接数为 {{ $value }}，超过阈值"
   ```

2. **后台任务堆积告警**:
   ```yaml
   - alert: BackgroundTasksAccumulation
     expr: sum(background_tasks_active) > 50
     for: 10m
     labels:
       severity: warning
     annotations:
       summary: "后台任务堆积"
       description: "当前后台任务数为 {{ $value }}，可能存在任务堆积"
   ```

## 访问指标

Prometheus 指标可以通过以下端点访问：
- URL: `http://localhost:8081/metrics`
- 端口: 8081（在 `main.py` 中配置）

## 测试

可以使用提供的测试脚本验证指标是否正常工作：

```bash
cd backend
python test_metrics.py
```

## 注意事项

1. **线程安全**: 所有指标操作都是线程安全的，可以在并发环境中使用
2. **内存管理**: `task_model_mapping` 字典会在任务完成时自动清理，避免内存泄漏
3. **错误处理**: 如果模型ID未知，会使用 'unknown' 作为默认值
4. **环境标识**: 通过 `ENV` 变量区分生产和开发环境

## 扩展建议

未来可以考虑添加以下指标：
- SSE 连接持续时间分布
- 后台任务执行时间分布
- 任务失败率统计
- 按用户分组的连接数统计
