# 简化连接监控系统

这是一个简化的连接监控系统，主要功能是记录连接事件并上报Prometheus指标。

## 功能特性

### 1. 连接事件记录
- 记录连接开始和结束
- 分类错误类型（服务器断开、超时、网络错误等）
- 记录连接持续时间
- 记录重试事件

### 2. Prometheus指标上报
- `connection_disconnects_total` - 连接断开总数
- `connection_timeout_total` - 连接超时总数
- `connection_retry_total` - 连接重试总数
- `connection_duration_seconds` - 连接持续时间
- `sse_connection_active` - 当前活跃的SSE连接数
- `sse_heartbeat_sent_total` - SSE心跳包发送总数
- `sse_connection_duration_seconds` - SSE连接持续时间

## 使用方法

### 1. 基本使用

```python
from open_webui.utils.connection_monitor import (
    get_connection_monitor,
    ConnectionType,
    ErrorType
)

# 获取监控器实例
monitor = get_connection_monitor()

# 记录连接开始
connection_id = monitor.record_connection_start(
    ConnectionType.OPENAI_API, 
    "gpt-4"
)

# 记录正常连接结束
monitor.record_connection_end(
    connection_id,
    ConnectionType.OPENAI_API,
    "gpt-4",
    duration=2.5  # 连接持续时间（秒）
)

# 记录连接错误
monitor.record_connection_end(
    connection_id,
    ConnectionType.OPENAI_API,
    "gpt-4",
    duration=1.0,
    error_type=ErrorType.SERVER_DISCONNECTED,
    error_message="Server disconnected unexpectedly"
)
```

### 2. 记录超时和重试

```python
# 记录超时事件
monitor.record_timeout(
    ConnectionType.OPENAI_API,
    "read_timeout",
    "gpt-4"
)

# 记录重试事件
monitor.record_retry(
    ConnectionType.OPENAI_API,
    "connection_error",
    "gpt-4"
)
```

### 3. 获取统计信息

```python
# 获取简单统计
stats = monitor.get_stats()
print(f"总连接数: {stats['total_connections']}")
print(f"总断开数: {stats['total_disconnects']}")
print(f"总超时数: {stats['total_timeouts']}")
print(f"总重试数: {stats['total_retries']}")
```

## 连接类型

支持以下连接类型：

- `ConnectionType.OPENAI_API` - OpenAI API连接
- `ConnectionType.OLLAMA_API` - Ollama API连接
- `ConnectionType.ZHIPU_API` - 智谱API连接
- `ConnectionType.SSE_CLIENT` - SSE客户端连接
- `ConnectionType.WEBSOCKET` - WebSocket连接

## 错误类型

支持以下错误类型：

- `ErrorType.SERVER_DISCONNECTED` - 服务器断开连接
- `ErrorType.CONNECTION_TIMEOUT` - 连接超时
- `ErrorType.NETWORK_ERROR` - 网络错误
- `ErrorType.SSL_ERROR` - SSL错误
- `ErrorType.DNS_ERROR` - DNS错误
- `ErrorType.UNKNOWN_ERROR` - 未知错误

## Prometheus指标查看

启动应用后，可以通过以下URL查看指标：

```
http://localhost:8081/metrics
```

查找以下指标：

```
# 连接断开总数
connection_disconnects_total{connection_type="openai_api",error_type="server_disconnected",model_id="gpt-4",env="prod"} 5

# 连接超时总数
connection_timeout_total{connection_type="openai_api",timeout_type="read_timeout",model_id="gpt-4",env="prod"} 2

# 连接重试总数
connection_retry_total{connection_type="openai_api",retry_reason="connection_error",model_id="gpt-4",env="prod"} 3

# 连接持续时间
connection_duration_seconds_bucket{connection_type="openai_api",model_id="gpt-4",env="prod",le="1"} 10
```

## 集成示例

### 在API路由中使用

```python
import aiohttp
from open_webui.utils.connection_monitor import (
    get_connection_monitor,
    ConnectionType,
    ErrorType
)

async def call_external_api(model_id: str):
    monitor = get_connection_monitor()
    connection_id = monitor.record_connection_start(
        ConnectionType.OPENAI_API, 
        model_id
    )
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("https://api.openai.com/v1/models") as response:
                data = await response.json()
                
                # 记录成功连接
                duration = time.time() - start_time
                monitor.record_connection_end(
                    connection_id,
                    ConnectionType.OPENAI_API,
                    model_id,
                    duration
                )
                
                return data
                
    except aiohttp.ServerDisconnectedError as e:
        # 记录服务器断开错误
        duration = time.time() - start_time
        monitor.record_connection_end(
            connection_id,
            ConnectionType.OPENAI_API,
            model_id,
            duration,
            ErrorType.SERVER_DISCONNECTED,
            str(e)
        )
        raise
        
    except asyncio.TimeoutError as e:
        # 记录超时错误
        duration = time.time() - start_time
        monitor.record_connection_end(
            connection_id,
            ConnectionType.OPENAI_API,
            model_id,
            duration,
            ErrorType.CONNECTION_TIMEOUT,
            str(e)
        )
        raise
```

## 日志输出

监控器会输出以下日志：

```
DEBUG [SimpleConnectionMonitor] Connection started: openai_api_gpt-4_1704067200.123
WARNING [SimpleConnectionMonitor] Connection error: openai_api, error: server_disconnected, model: gpt-4
WARNING [SimpleConnectionMonitor] Timeout: openai_api, type: read_timeout, model: gpt-4
INFO [SimpleConnectionMonitor] Retry: openai_api, reason: connection_error, model: gpt-4
```

## 注意事项

1. **轻量级设计**：这是一个简化版本，只负责记录事件和上报指标
2. **无告警功能**：如需告警，请通过Prometheus AlertManager配置
3. **无历史存储**：不保存历史事件，只维护简单计数器
4. **线程安全**：所有操作都是线程安全的
5. **性能友好**：最小化性能开销，适合生产环境使用

## 扩展建议

如果需要更多功能，可以考虑：

1. **外部监控**：使用Grafana + Prometheus进行可视化监控
2. **告警配置**：通过Prometheus AlertManager配置告警规则
3. **日志聚合**：使用ELK或类似工具聚合和分析日志
4. **自定义指标**：根据业务需求添加更多指标维度
