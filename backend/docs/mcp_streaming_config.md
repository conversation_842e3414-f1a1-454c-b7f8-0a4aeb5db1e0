# MCP工具流式回调配置指南

## 概述

MCP工具流式回调功能允许您选择性地为特定的服务器和工具组合启用实时流式处理。这个功能通过条件性包装器实现，可以通过配置来控制哪些工具需要启用流式回调。

## 配置方式

### 1. 环境变量配置

您可以通过设置 `MCP_STREAMING_ENABLED_TOOLS` 环境变量来配置启用流式回调的工具：

```bash
export MCP_STREAMING_ENABLED_TOOLS='{"server1": ["tool1", "tool2"], "server2": ["*"], "*": ["specific_tool"]}'
```

### 2. 默认配置

如果没有设置环境变量，系统将使用默认配置：

```python
DEFAULT_STREAMING_ENABLED_TOOLS = {
    "*": ["*"]  # 默认为所有服务器的所有工具启用流式回调
}
```

## 配置格式

配置是一个JSON对象，格式如下：

```json
{
  "服务器名称": ["工具名称1", "工具名称2", ...],
  "服务器名称2": ["*"],
  "*": ["特定工具名称"]
}
```

### 配置规则

1. **具体服务器的具体工具**：`"server1": ["tool1", "tool2"]`
   - 只为 `server1` 服务器的 `tool1` 和 `tool2` 工具启用流式回调

2. **具体服务器的所有工具**：`"server2": ["*"]`
   - 为 `server2` 服务器的所有工具启用流式回调

3. **所有服务器的特定工具**：`"*": ["specific_tool"]`
   - 为所有服务器的 `specific_tool` 工具启用流式回调

4. **所有服务器的所有工具**：`"*": ["*"]`
   - 为所有服务器的所有工具启用流式回调（默认行为）

## 配置示例

### 示例1：只为特定工具启用流式回调

```json
{
  "firecrawl": ["scrape", "search"],
  "browser": ["*"],
  "*": ["file_upload"]
}
```

这个配置将：
- 为 `firecrawl` 服务器的 `scrape` 和 `search` 工具启用流式回调
- 为 `browser` 服务器的所有工具启用流式回调
- 为所有服务器的 `file_upload` 工具启用流式回调

### 示例2：禁用所有流式回调

```json
{}
```

空配置将禁用所有工具的流式回调。

### 示例3：只为特定服务器启用

```json
{
  "important_server": ["*"]
}
```

只为 `important_server` 服务器的所有工具启用流式回调。

## 使用场景

### 1. 性能优化

对于执行时间较短的工具，可以禁用流式回调以减少网络开销：

```json
{
  "*": ["long_running_tool", "complex_analysis"],
  "fast_server": []
}
```

### 2. 调试和开发

在开发阶段，可以只为正在开发的工具启用流式回调：

```json
{
  "dev_server": ["new_tool"],
  "*": []
}
```

### 3. 用户体验优化

为用户关心进度的工具启用流式回调：

```json
{
  "*": ["file_processing", "data_analysis", "web_scraping"]
}
```

## 日志记录

系统会记录相关的日志信息：

- **启用流式回调**：`DEBUG: 启用MCP工具流式回调: server_name.tool_name`
- **跳过流式回调**：`DEBUG: 跳过MCP工具流式回调: server_name.tool_name (未在配置中启用)`
- **配置加载**：`INFO: 使用环境变量配置的MCP流式工具: {...}`
- **配置错误**：`WARNING: 解析MCP_STREAMING_ENABLED_TOOLS环境变量失败: ..., 使用默认配置`

## 向后兼容性

- 如果没有设置配置，默认行为是为所有工具启用流式回调
- 现有的代码无需修改，包装器会自动处理条件性执行
- 如果 `progress_callback` 为 `None`，包装器会直接返回 `None`

## 注意事项

1. **配置验证**：确保JSON格式正确，否则会回退到默认配置
2. **服务器名称**：服务器名称必须与实际的MCP服务器名称完全匹配
3. **工具名称**：工具名称必须与实际的工具名称完全匹配
4. **通配符**：使用 `"*"` 作为通配符来匹配所有服务器或所有工具
5. **优先级**：具体的服务器/工具配置优先于通配符配置

## 故障排除

### 1. 流式回调没有生效

检查：
- 环境变量格式是否正确
- 服务器名称和工具名称是否匹配
- 查看日志中的调试信息

### 2. 配置不生效

检查：
- JSON格式是否有效
- 环境变量是否正确设置
- 重启应用程序以加载新配置

### 3. 性能问题

如果遇到性能问题：
- 考虑禁用不必要的工具的流式回调
- 监控日志中的回调执行频率
- 调整配置以优化性能
