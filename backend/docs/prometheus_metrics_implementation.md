# OpenWebUI Prometheus 监控指标实现总结

## 实现概述

本次实现为 OpenWebUI 后端添加了两个关键的 Prometheus 监控指标，用于监控 SSE 连接状态和后台任务执行情况。

## 已实现的指标

### 1. SSE 并发连接数监控 (`sse_concurrent_connections`)

**指标类型**: Gauge  
**描述**: 监控当前并发的 SSE 连接数  
**标签**: `model_id`, `env`

**实现位置**:
- 指标定义: `backend/open_webui/utils/metrics.py:220-224`
- 连接建立: `backend/open_webui/utils/middleware.py:601-602`
- 连接断开: `backend/open_webui/utils/middleware.py:699-700`

**工作原理**:
- 在 SSE 连接建立时调用 `.inc()` 增加计数
- 在 SSE 连接断开时调用 `.dec()` 减少计数
- 使用连接的模型ID和环境标识进行标签分组

### 2. 后台任务监控 (`background_tasks_active`)

**指标类型**: Gauge  
**描述**: 监控当前活跃的后台任务数量  
**标签**: `model_id`, `env`

**实现位置**:
- 指标定义: `backend/open_webui/utils/metrics.py:226-230`
- 任务创建: `backend/open_webui/tasks.py:39-40`
- 任务清理: `backend/open_webui/tasks.py:18-19`
- 任务停止: `backend/open_webui/tasks.py:81-82`

**工作原理**:
- 在后台任务创建时调用 `.inc()` 增加计数
- 在后台任务完成或取消时调用 `.dec()` 减少计数
- 通过 `task_model_mapping` 字典维护任务ID到模型ID的映射

## 代码修改详情

### 1. 指标定义 (`metrics.py`)

```python
# SSE 并发连接数监控指标
sse_concurrent_connections = Gauge(
    'sse_concurrent_connections',
    '当前并发SSE连接数',
    ['model_id', 'env']
)

# 后台任务监控指标
background_tasks_active = Gauge(
    'background_tasks_active',
    '当前活跃的后台任务数量',
    ['model_id', 'env']
)
```

### 2. SSE 连接监控 (`middleware.py`)

```python
# 导入新指标
from open_webui.utils.metrics import (
    # ... 其他指标
    sse_concurrent_connections,
    background_tasks_active,
)

# 连接建立时
model_id = form_data.get('model', 'unknown')
sse_concurrent_connections.labels(model_id=model_id, env=ENV).inc()

# 连接断开时
sse_concurrent_connections.labels(model_id=model_id, env=ENV).dec()
```

### 3. 后台任务监控 (`tasks.py`)

```python
# 添加模型ID映射
task_model_mapping: Dict[str, str] = {}

def create_task(coroutine, task_id: str = None, model_id: str = 'unknown'):
    # 存储模型ID信息并更新指标
    task_model_mapping[task_id] = model_id
    background_tasks_active.labels(model_id=model_id, env=ENV).inc()

def cleanup_task(task_id: str):
    # 获取模型ID并更新指标
    model_id = task_model_mapping.pop(task_id, 'unknown')
    background_tasks_active.labels(model_id=model_id, env=ENV).dec()
```

### 4. 任务创建调用更新 (`middleware.py`)

```python
# 创建异步任务时传递模型ID
create_task(handle_sensitive_tasks(), task_id=sensitive_task_id, model_id=model_id)
create_task(post_response_handler(response,events), task_id=post_response_task_id, model_id=model_id)
```

## 测试验证

### 测试脚本
创建了 `backend/test_metrics.py` 用于验证指标功能：
- 测试后台任务指标的增减
- 测试 SSE 连接指标的增减
- 验证 Prometheus 格式输出

### 测试结果
```
=== 测试后台任务监控指标 ===
初始任务数量: 0
创建任务后的任务数量: 2
停止任务后的任务数量: 1
所有任务完成后的任务数量: 0

=== 测试 SSE 并发连接数指标 ===
模拟连接建立...
模拟连接断开...
SSE 连接数指标测试完成
```

### Prometheus 输出验证
```
# HELP sse_concurrent_connections 当前并发SSE连接数
# TYPE sse_concurrent_connections gauge
sse_concurrent_connections{env="dev",model_id="test_model"} 5.0

# HELP background_tasks_active 当前活跃的后台任务数量
# TYPE background_tasks_active gauge
background_tasks_active{env="dev",model_id="test_model"} 3.0
```

## 技术特性

### 线程安全
- 所有 Prometheus 指标操作都是线程安全的
- 可以在并发环境中安全使用

### 内存管理
- `task_model_mapping` 字典在任务完成时自动清理
- 避免内存泄漏问题

### 错误处理
- 当模型ID未知时使用 'unknown' 作为默认值
- 确保指标始终可用

### 环境区分
- 通过 `ENV` 变量区分生产和开发环境
- 便于不同环境的监控分析

## 监控访问

指标可通过以下方式访问：
- **URL**: `http://localhost:8081/metrics`
- **端口**: 8081（在 `main.py` 中配置的 Prometheus 指标服务器）

## 后续建议

1. **告警配置**: 根据业务需求配置 Prometheus 告警规则
2. **仪表板**: 在 Grafana 中创建监控仪表板
3. **扩展指标**: 考虑添加更多细粒度的监控指标
4. **性能优化**: 监控指标对系统性能的影响

## 文档

- 详细使用文档: `backend/docs/prometheus_metrics.md`
- 测试脚本: `backend/test_metrics.py`
- 实现总结: `backend/docs/prometheus_metrics_implementation.md`（本文档）
