#!/usr/bin/env python3
"""
测试优化后的 refresh_tmp_image_url 函数
运行方式：python test_refresh_image_url.py
"""

import json
import time
from unittest.mock import MagicMock, patch
from urllib.parse import urlparse
import os


def parse_object_key(url: str) -> str:
    """从URL中解析文件名"""
    parsed_path = urlparse(url).path
    return os.path.basename(parsed_path)


def refresh_tmp_image_url_optimized(form_data, mock_generate_url=None):
    """
    优化后的 refresh_tmp_image_url 函数的简化版本用于测试
    """
    from concurrent.futures import ThreadPoolExecutor
    
    # 模拟日志
    class MockLog:
        def info(self, msg): print(f"[INFO] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
    
    log = MockLog()
    
    # 第一步：收集所有有效的image_url信息
    image_info_list = []
    
    for message_idx, message in enumerate(form_data["messages"]):
        content = message.get("content", [])
        if isinstance(content, str):
            continue
            
        for content_idx, content_item in enumerate(content):
            if content_item.get("type", "") == "image_url":
                old_tmp_url = content_item.get("image_url", {}).get("url", "")
                if old_tmp_url:
                    filename = parse_object_key(old_tmp_url)
                    if filename:  # 只处理非空的filename
                        image_info_list.append({
                            'message_idx': message_idx,
                            'content_idx': content_idx,
                            'old_url': old_tmp_url,
                            'filename': filename
                        })
                        log.info(f"找到有效图片: {filename} from {old_tmp_url}")
    
    if not image_info_list:
        log.info("没有找到需要刷新的图片URL")
        return [], 0
    
    log.info(f"总共找到 {len(image_info_list)} 个需要刷新的图片URL")
    
    # 第二步：并发生成临时URL
    def generate_url_for_filename(filename):
        """为单个filename生成临时URL的辅助函数"""
        try:
            if mock_generate_url:
                return mock_generate_url(filename)
            return f"https://temp-url.example.com/{filename}?expires=1234567890"
        except Exception as e:
            log.error(f"生成临时URL失败 {filename}: {e}")
            return None
    
    # 使用ThreadPoolExecutor并发处理
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提取所有filename
        filenames = [info['filename'] for info in image_info_list]
        
        # 并发生成临时URL
        log.info(f"开始并发生成 {len(filenames)} 个临时URL...")
        start_time = time.time()
        new_urls = list(executor.map(generate_url_for_filename, filenames))
        end_time = time.time()
        log.info(f"并发生成临时URL完成，耗时: {end_time - start_time:.3f}秒")
    
    # 第三步：更新form_data中的URL
    updated_count = 0
    for info, new_url in zip(image_info_list, new_urls):
        if new_url:
            form_data["messages"][info['message_idx']]["content"][info['content_idx']]["image_url"]["url"] = new_url
            log.info(f"更新图片URL: {info['filename']} -> {new_url[:50]}...")
            updated_count += 1
        else:
            log.warning(f"跳过更新失败的图片: {info['filename']}")
    
    log.info(f"成功更新了 {updated_count}/{len(image_info_list)} 个图片URL")
    return image_info_list, updated_count


def test_refresh_tmp_image_url():
    """测试优化后的 refresh_tmp_image_url 函数"""
    
    print("🧪 开始测试优化后的 refresh_tmp_image_url 函数...")
    
    # 测试用例 1: 包含多个图片的消息
    test_form_data = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请看这些图片"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "http://example.com/files/image1.png?token=abc123"
                        }
                    }
                ]
            },
            {
                "role": "user", 
                "content": [
                    {
                        "type": "text",
                        "text": "还有这个图片"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://oss.example.com/bucket/image2.jpg?expires=123456"
                        }
                    }
                ]
            },
            {
                "role": "assistant",
                "content": "这是回复"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://cdn.example.com/photos/vacation/beach.jpeg?v=1"
                        }
                    },
                    {
                        "type": "text",
                        "text": "最后一张图片"
                    }
                ]
            }
        ]
    }
    
    # 模拟生成临时URL的函数
    def mock_generate_temp_url(filename):
        # 模拟一些处理时间
        time.sleep(0.1)
        return f"https://temp-cdn.example.com/secure/{filename}?token=xyz789&expires=9999999999"
    
    print("\n📋 测试用例：包含3个图片的多条消息")
    print("=" * 60)
    
    # 执行测试
    start_time = time.time()
    image_info_list, updated_count = refresh_tmp_image_url_optimized(
        test_form_data, 
        mock_generate_temp_url
    )
    end_time = time.time()
    
    print(f"\n⏱️  总执行时间: {end_time - start_time:.3f}秒")
    print(f"📊 处理结果: 找到 {len(image_info_list)} 个图片，成功更新 {updated_count} 个")
    
    # 验证结果
    print("\n🔍 验证更新后的URL:")
    for i, message in enumerate(test_form_data["messages"]):
        content = message.get("content", [])
        if isinstance(content, list):
            for j, item in enumerate(content):
                if item.get("type") == "image_url":
                    new_url = item["image_url"]["url"]
                    print(f"   消息 {i}, 内容 {j}: {new_url}")
    
    # 测试用例 2: 没有图片的消息
    print(f"\n📋 测试用例2：没有图片的消息")
    print("=" * 60)
    
    empty_form_data = {
        "messages": [
            {
                "role": "user",
                "content": [{"type": "text", "text": "只有文本消息"}]
            },
            {
                "role": "assistant", 
                "content": "这是回复"
            }
        ]
    }
    
    image_info_list2, updated_count2 = refresh_tmp_image_url_optimized(empty_form_data)
    
    # 测试用例 3: 边界情况 - 空filename
    print(f"\n📋 测试用例3：包含无效URL的情况")
    print("=" * 60)
    
    edge_case_data = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": "https://example.com/"}  # 空filename
                    },
                    {
                        "type": "image_url", 
                        "image_url": {"url": "https://example.com/valid_image.png"}  # 有效filename
                    }
                ]
            }
        ]
    }
    
    image_info_list3, updated_count3 = refresh_tmp_image_url_optimized(edge_case_data, mock_generate_temp_url)
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print(f"✅ 测试1: 处理了 {updated_count}/3 个图片")
    print(f"✅ 测试2: 处理了 {updated_count2}/0 个图片")  
    print(f"✅ 测试3: 处理了 {updated_count3}/1 个图片 (过滤了无效URL)")


if __name__ == "__main__":
    test_refresh_tmp_image_url() 