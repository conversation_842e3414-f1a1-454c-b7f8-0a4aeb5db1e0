import oss from 'ali-oss';
import path from 'path';
import fs from 'fs';


const gitTag = process.env.GIT_TAG || 'latest';
if (gitTag === "latest") {
  console.warn('⚠️GIT TAG 为 latest, 将不会上传到 OSS');
  process.exit(1);
}


const client = new oss({
  // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
  accessKeyId: process.env.OSS_ACCESS_KEY_ID || "LTAI5tHRNCCCYA4dihFkjhMH",
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || "******************************",
  // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
  region: 'oss-cn-hongkong',
  authorizationV4: true,
  // yourBucketName填写Bucket名称。
  bucket: 'glm-chat',
});


// 自定义请求头
const headers = {
  // 指定Object的存储类型。
  'x-oss-storage-class': 'Standard',
  // 指定Object的访问权限。
  'x-oss-object-acl': 'public-read',
  // 通过文件URL访问文件时，指定以附件形式下载文件，下载后的文件名称定义为example.txt。
  // 'Content-Disposition': 'attachment; filename="example.txt"',
  // 设置Object的标签，可同时设置多个标签。
  // 'x-oss-tagging': 'Tag1=1&Tag2=2',
  // 指定PutObject操作时是否覆盖同名目标Object。此处设置为true，表示禁止覆盖同名Object。
  'x-oss-forbid-overwrite': 'false',
};

// 示例单文件上传函数
// async function put() {
//   try {
//     // 填写OSS文件完整路径和本地文件的完整路径。OSS文件完整路径中不能包含Bucket名称。
//     // 如果本地文件的完整路径中未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
//     const result = await client.put('/z-ai/frontend/static/hello-world.txt', path.normalize('scripts/hello-world')
//       // 自定义headers
//       , { headers }
//     );
//     console.log(result);
//   } catch (e) {
//     console.log(e);
//   }
// }

// 全局变量保存文件统计信息
const globalStats = {
  total: 0,
  totalSize: 0,
  uploaded: 0,
  uploadedSize: 0
};

// 保存所有的 url 方便后面进行预热
const allUrls = [];


async function uploadDirectory(localBasePath, ossBasePath = '/z-ai/frontend/') {
  try {
    // 确保OSS基础路径末尾有斜杠
    if (!ossBasePath.endsWith('/')) {
      ossBasePath += '/';
    }

    // 规范化本地基础路径
    const normalizedBasePath = path.normalize(localBasePath);

    // 收集所有文件路径的函数
    function collectAllFiles(dir, fileList = []) {
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const fullPath = path.join(dir, file);
        const stats = fs.statSync(fullPath);

        if (stats.isDirectory()) {
          collectAllFiles(fullPath, fileList);
        } else {
          fileList.push(fullPath);
        }
      }

      return fileList;
    }

    // 收集所有文件
    const allFiles = collectAllFiles(normalizedBasePath);

    // 上传计数
    let successCount = 0;
    let failCount = 0;

    // 处理每个文件
    for (const fullPath of allFiles) {
      try {
        // 计算相对于基础目录的路径
        let relativePath = path.relative(normalizedBasePath, fullPath);

        // 转换为前向斜杠
        relativePath = relativePath.replace(/\\/g, '/');

        // 构建OSS路径
        const ossFilePath = ossBasePath + relativePath;

        // 上传文件
        await client.put(ossFilePath, fullPath, { headers });

        // 保存 url 方便后面进行预热
        allUrls.push(`https://z-cdn.chatglm.cn` + ossFilePath);

        // 更新统计信息
        globalStats.uploaded++;
        globalStats.uploadedSize += fs.statSync(fullPath).size;

        // 显示进度
        const percentage = Math.min((globalStats.uploaded / globalStats.total * 100), 100).toFixed(2);
        const sizePercentage = Math.min((globalStats.uploadedSize / globalStats.totalSize * 100), 100).toFixed(2);
        process.stdout.write(`\r上传进度: ${globalStats.uploaded}/${globalStats.total} 文件 (${percentage}%) | 大小: ${formatBytes(globalStats.uploadedSize)}/${formatBytes(globalStats.totalSize)} (${sizePercentage}%)`);

        successCount++;
      } catch (error) {
        console.error(`\n上传失败: ${fullPath}`, error);
        failCount++;
      }
    }

    return { successCount, failCount };
  } catch (error) {
    console.error('上传目录时出错:', error);
    return { successCount: 0, failCount: 1 };
  }
}
// 格式化字节数为可读格式
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 统计总文件数和大小
function calculateTotalStats(dirPath) {
  const stats = { total: 0, totalSize: 0 };
  
  function traverse(currentPath) {
    const files = fs.readdirSync(currentPath);
    
    for (const file of files) {
      const filePath = path.join(currentPath, file);
      const fileStat = fs.statSync(filePath);
      
      if (fileStat.isDirectory()) {
        traverse(filePath);
      } else {
        stats.total++;
        stats.totalSize += fileStat.size;
      }
    }
  }
  
  traverse(dirPath);
  return stats;
}


console.log("✅\x1b[36mGIT TAG:\x1b[0m", "\x1b[32m" + gitTag + "\x1b[0m");


const main = async () => {
  const ossBasePath = `/z-ai/frontend/${gitTag}`;
  console.log(`开始上传build目录..., 目标路径: ${ossBasePath}`);
  
  // 计算总文件数和大小
  console.log('计算文件总数和大小...');
  
  try {
    const stats = calculateTotalStats('build');
    globalStats.total = Math.max(stats.total, 1);
    globalStats.totalSize = Math.max(stats.totalSize, 1);
    globalStats.uploaded = 0;
    globalStats.uploadedSize = 0;
  } catch (error) {
    console.error('计算文件统计信息出错:', error);
    globalStats.total = 1;
    globalStats.totalSize = 1;
    globalStats.uploaded = 0;
    globalStats.uploadedSize = 0;
  }
  
  console.log(`总文件数: ${globalStats.total}, 总大小: ${formatBytes(globalStats.totalSize)}`);
  
  const result = await uploadDirectory('build', ossBasePath);
  console.log(`\n上传完成! 成功: ${result.successCount} 失败: ${result.failCount}`);

  console.log(`\n预热 url 数量: ${allUrls.length}`);
  // 预热的 url 写到一个文件中
  fs.writeFileSync('preload-urls.txt', allUrls.join('\n'));
}

main();