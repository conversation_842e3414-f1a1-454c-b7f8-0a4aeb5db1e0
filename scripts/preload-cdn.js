// This file is auto-generated, don't edit it
// 依赖的模块可通过下载工程中的模块依赖文件或右上角的获取 SDK 依赖信息查看
import OAI from '@alicloud/openapi-client';
import OAIU from '@alicloud/openapi-util';
import * as $Util from '@alicloud/tea-util';
import CR from '@alicloud/credentials';
const Credential = CR.default;
const CredentialConfig = CR.Config;
const OpenApi = OAI.default;
const $OpenApi = OAI;
const OpenApiUtil = OAIU.default;
import fs from 'fs';
const loadUrls = () => {
  const urls = fs.readFileSync('preload-urls.txt', 'utf-8');
  return urls.split('\n');
}
const urls = loadUrls();
if (urls.length === 0) {
  console.error('☹️没有找到需要预热的 url');
  process.exit(1);
}

class Client {

  /**
   * @remarks
   * 使用凭据初始化账号Client
   * @returns Client
   * 
   * @throws Exception
   */
  static createClient(){
    const cfg = new CredentialConfig({
      // 凭证类型。
      type: 'access_key',
      // 设置为AccessKey ID值。
      accessKeyId: process.env.ALIBABA_CLOUD_ACCESS_KEY_ID || "LTAI5tHRNCCCYA4dihFkjhMH"  ,
      // 设置为AccessKey Secret值。
      accessKeySecret: process.env.ALIBABA_CLOUD_ACCESS_KEY_SECRET || "******************************",
    });
    // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378664.html。
    let credential = new Credential(cfg);
    let config = new $OpenApi.Config({
      credential: credential,
    });
    // Endpoint 请参考 https://api.aliyun.com/product/Cdn
    config.endpoint = `cdn.aliyuncs.com`;
    return new OpenApi(config);
  }

  /**
   * @remarks
   * API 相关
   * 
   * @param path - string Path parameters
   * @returns OpenApi.Params
   */
  static createApiInfo() {
    let params = new $OpenApi.Params({
      // 接口名称
      action: "PushObjectCache",
      // 接口版本
      version: "2018-05-10",
      // 接口协议
      protocol: "HTTPS",
      // 接口 HTTP 方法
      method: "POST",
      authType: "AK",
      style: "RPC",
      // 接口 PATH
      pathname: `/`,
      // 接口请求体内容格式
      reqBodyType: "json",
      // 接口响应体内容格式
      bodyType: "json",
    });
    return params;
  }

  static async preload(urls) {
    let client = Client.createClient();
    let params = Client.createApiInfo();
    // query params
    let queries = { };
    queries["ObjectPath"] =  urls.join("\n")
    queries["L2Preload"] = true;
    let runtime = new $Util.RuntimeOptions({});
    let request = new $OpenApi.OpenApiRequest({
      query: OpenApiUtil.query(queries),
    });
    // 复制代码运行请自行打印 API 的返回值
    // 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
    const res = await client.callApi(params, request, runtime);
    if (res.statusCode !== 200) {
      console.error(`❌预热失败: ${urls.join("\n")}`);
      process.exit(1);
    } else {
      console.log(`✅预热成功: ${urls.join("\n")}, 预热数量: ${urls.length}, 返回内容: ${JSON.stringify(res.body,null,2)}`);
    }
  }
}


// 99 个一组
const splitUrlsChunkFn = (urls) => {
  const result = [];
  for (let i = 0; i < urls.length; i += 99) {
    result.push(urls.slice(i, i + 99));
  }
  return result;
}

const main = async () => {

  console.log(`🔥预热数量: ${urls.length}`);
  // 等待1min确保 oss 都知道文件 ready 了
  await new Promise(resolve => setTimeout(resolve, 60000));
  console.log(`🔥预热开始`);
  const splitUrls = splitUrlsChunkFn(urls);
  const total = urls.length;
  let success = 0;
  for (const urls of splitUrls) {
    await Client.preload(urls);
    success = success + urls.length;
    console.log(`🔥预热成功: ${success}/${total}`);
  }
  console.log(`🔥预热全部完成: ${success}/${total}`);

  console.log(`⠀⠀⠀⠀
      ⠰⢷⢿⠄
  ⠀⠀⠀⠀⠀⣼⣷⣄
  ⠀⠀⣤⣿⣇⣿⣿⣧⣿⡄
  ⢴⠾⠋⠀⠀⠻⣿⣷⣿⣿⡀
 🏀   ⠀⣿⣿⡿⢿⠈⣿
  ⠀⠀⠀⢠⣿⡿🐔⡊⠀⠙
  ⠀⠀⠀⢿⣿⠀⠀⠹⣿
  ⠀⠀⠀⠀⠹⣷⡀⠀⣿⡄
  ⠀⠀⠀⠀⣀⣼⣿⠀⢈⣧`)
}

main();