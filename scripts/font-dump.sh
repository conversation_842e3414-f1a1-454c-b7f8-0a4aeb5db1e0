#!/bin/bash

# 创建目录来存储字体
mkdir -p assets/fonts

# 定义要下载的字体列表
fonts=("NotoSansHans-Light.otf" "NotoSansHans-Regular.otf" "NotoSansHans-Medium.otf" "NotoSansHans-Bold.otf")

# 基础URL
base_url="https://assets.alicdn.com/g/qwenweb/qwen-webui-fe/0.0.72/assets/fonts"

# 通用请求头
headers=(
  -H 'accept: */*'
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8'
  -H 'cache-control: no-cache'
  -H 'origin: https://chat.qwen.ai'
  -H 'pragma: no-cache'
  -H 'priority: u=0'
  -H 'referer: https://assets.alicdn.com/g/qwenweb/qwen-webui-fe/0.0.72/_app/immutable/assets/0.tpmti8K3.css'
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
  -H 'sec-ch-ua-mobile: ?0'
  -H 'sec-ch-ua-platform: "macOS"'
  -H 'sec-fetch-dest: font'
  -H 'sec-fetch-mode: cors'
  -H 'sec-fetch-site: cross-site'
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
)

# 下载每个字体
for font in "${fonts[@]}"; do
  echo "下载 $font..."
  curl "$base_url/$font" "${headers[@]}" --output "assets/fonts/$font"
  
  # 检查下载是否成功
  if [ $? -eq 0 ]; then
    echo "✓ $font 下载成功"
  else
    echo "✗ $font 下载失败"
  fi
done

echo "所有字体下载完成！"