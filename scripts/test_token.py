import jwt
from datetime import datetime, timedelta, UTC
from typing import Optional, Union

SESSION_SECRET="1234557123412"

private_key = """**********************************************************************************************************************************************************************************************************************************"""
pub_key = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAESwFqiS3Y0eizyASKa9qtm40lugJz
rWHnpNG9Bg3mKaM2PxYX7RuWDygxWnxJ7bEw9WMqcBZNtgbYi791IBIdFw==
-----END PUBLIC KEY-----"""


ALGORITHM = "HS256"
NEW_ALGORITHM = "ES256"
def create_token_old(data: dict, expires_delta: Union[timedelta, None] = None) -> str:
    payload = data.copy()

    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
        payload.update({"exp": expire})
    
    encoded_jwt = jwt.encode(payload, SESSION_SECRET, algorithm=ALGORITHM)
    return encoded_jwt


def create_token(data: dict, expires_delta: Union[timedelta, None] = None) -> str:
    payload = data.copy()

    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
        payload.update({"exp": expire})

    encoded_jwt = jwt.encode(payload, private_key,  algorithm=NEW_ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> Optional[dict]:
    # 尝试用新的解密
    try:
      decoded = jwt.decode(token, pub_key, algorithms=[NEW_ALGORITHM], options={"verify_exp": False})
      print("new decoded", decoded)
      return decoded
    except Exception:
      decoded = jwt.decode(token, SESSION_SECRET, algorithms=[ALGORITHM], options={"verify_exp": False})
      print("old decoded", decoded)
      return decoded

def test_token():
    data = {
        "id": "1234567890",
        "email": "<EMAIL>",
    }
    print("测试老的token")
    # 创建一个老的token
    token = create_token_old(data)
    print(token)
    # 解密
    decoded = decode_token(token)
    print(decoded)
    print("测试新的token")
    # 创建一个新的token
    token = create_token(data)
    print(token)
    # 解密
    decoded = decode_token(token)
    print(decoded)


if __name__ == "__main__":
    test_token()