import base64
import time
import requests
import json

def test_first_packet_time():
    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer d8a245eb7d764f6ba4af94f562a1e9e6.rUtijIQRICQrwnsG"
    }
    
    data = {
        "stream": True,
        "model": "GLM-4.1V-Thinking-FlashX",
        "messages": [
            {
                "role": "system",
                "content": "你是智谱AI发布的最新的多模态模型"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你看到什么"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://z-cdn.oss-cn-hongkong.aliyuncs.com/files/53102de2-30ea-4262-9f8d-011a9fb66c0b_pasted_image_1752030930706.png"
                        }
                    }
                ]
            }
        ],
        "features": {
            "image_generation": False,
            "code_interpreter": False,
            "web_search": False,
            "auto_web_search": False,
            "preview_mode": True,
            "flags": []
        }
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(url, headers=headers, json=data, stream=True)
        
        # 获取第一个响应包的时间
        first_packet_time = None
        for i, chunk in enumerate(response.iter_content(chunk_size=1)):
            if i == 0:  # 第一个包
                first_packet_time = time.time()
                print(f"首包耗时: {first_packet_time - start_time:.3f}秒")
                break
        
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                try:
                    if line.startswith(b'data: '):
                        data_str = line[6:].decode('utf-8')
                        if data_str.strip() == '[DONE]':
                            break
                        chunk_data = json.loads(data_str)
                        print(f"收到数据: {chunk_data}")
                except json.JSONDecodeError:
                    continue
                    
    except Exception as e:
        print(f"请求失败: {e}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.3f}秒")


def test_first_packet_time_base64_image():
    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer d8a245eb7d764f6ba4af94f562a1e9e6.rUtijIQRICQrwnsG"
    }
    base64_image = ""
    with open("/Users/<USER>/Downloads/53102de2-30ea-4262-9f8d-011a9fb66c0b_pasted_image_1752030930706.png", "rb") as image_file:
        base64_image = base64.b64encode(image_file.read()).decode('utf-8')
    print(base64_image)
    data = {
        "stream": True,
        "model": "GLM-4.1V-Thinking-FlashX",
        "messages": [
            {
                "role": "system",
                "content": "你是智谱AI发布的最新的多模态模型"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你看到什么"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": base64_image
                        }
                    }
                ]
            }
        ],
        "features": {
            "image_generation": False,
            "code_interpreter": False,
            "web_search": False,
            "auto_web_search": False,
            "preview_mode": True,
            "flags": []
        }
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(url, headers=headers, json=data, stream=True)
        
        # 获取第一个响应包的时间
        first_packet_time = None
        for i, chunk in enumerate(response.iter_content(chunk_size=1)):
            if i == 0:  # 第一个包
                first_packet_time = time.time()
                print(f"首包耗时: {first_packet_time - start_time:.3f}秒")
                break
        
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                try:
                    if line.startswith(b'data: '):
                        data_str = line[6:].decode('utf-8')
                        if data_str.strip() == '[DONE]':
                            break
                        chunk_data = json.loads(data_str)
                        print(f"收到数据: {chunk_data}")
                except json.JSONDecodeError:
                    continue
                    
    except Exception as e:
        print(f"请求失败: {e}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.3f}秒")
# 运行测试
# test_first_packet_time()
test_first_packet_time_base64_image()