{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Open WebUI Backend",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "open_webui.main:app",
                "--port",
                "8080",
                "--host",
                "0.0.0.0",
                "--forwarded-allow-ips",
                "*",
                // "--reload"
            ],
            "jinja": true,
            "cwd": "${workspaceFolder}/backend",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/backend",
                "ENABLE_OPENAI_API": "true",
                // "OPENAI_API_BASE_URL": "https://api-gateway.glm.ai/v1",
                // "OPENAI_API_KEY": "sk-eZ6LyEvatLadaZA2Ba4qfy5NfqAs0Njp",
                "ENABLE_OAUTH_SIGNUP": "true",
                "CORS_ALLOW_ORIGIN": "*",
                "ENABLE_OLLAMA_API": "false",
                "ENABLE_WEBSOCKET_SUPPORT": "false",
                "ENABLE_EVALUATION_ARENA_MODELS": "false",
                "WEBUI_SECRET_KEY": "alkdfjlkasdjflaksdfjlaksdfj",
                "GLOBAL_LOG_LEVEL": "DEBUG",
                "ENABLE_MCP": "true",
                // "MQ_REDIS_URL": "redis://localhost:6379",
                "MQ_REDIS_URL": "redis://************:6379",
                "STORAGE_PROVIDER": "aliyun_oss",
                "OSS_ACCESS_KEY_SECRET": "******************************",
                "OSS_REGION": "oss-cn-hongkong",
                "OSS_BUCKET_NAME": "glm-chat",
                "OSS_AUTHORIZATION_V4": "true",
                "OSS_ACCESS_KEY_ID": "LTAI5tHRNCCCYA4dihFkjhMH",
                "OSS_ENDPOINT": "oss-cn-hongkong.aliyuncs.com",
                "ZHIPUAI_API_KEY": "d8a245eb7d764f6ba4af94f562a1e9e6.rUtijIQRICQrwnsG",
                "GOOGLE_CLIENT_ID": "800424391928-cq33u0aqcht7h5q96hr9llprk6f540rs.apps.googleusercontent.com",
                "GOOGLE_CLIENT_SECRET": "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",
                "SKIP_USER_TASK_LIMIT_CHECK": "true",
                "USER_CONCURRENCY_LIMIT": "1"
                // "DATABASE_URL": "postgresql://openwebui_staging:HgMev8!x%6#<EMAIL>:5432/openwebui_staging"
            }
        }
    ]
}