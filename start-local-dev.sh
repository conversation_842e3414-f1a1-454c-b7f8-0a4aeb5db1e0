#!/bin/bash

# 一键启动脚本 - 同时启动前端和后端， 后端的虚拟环境位于backend/.venv

echo "🚀 启动前端和后端服务..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 启动前端服务 (后台运行)
echo "📦 启动前端服务 (npm run dev)..." 
cd "$SCRIPT_DIR"
npm run dev &
FRONTEND_PID=$!

# 启动后端服务 (后台运行)
echo "🔧 启动后端服务 (./dev.sh)..."
cd "$SCRIPT_DIR/backend"
source .venv/bin/activate
./dev.sh &
BACKEND_PID=$!

echo "✅ 服务启动完成!"
echo "前端PID: $FRONTEND_PID"
echo "后端PID: $BACKEND_PID"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 创建清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    kill $FRONTEND_PID 2>/dev/null
    kill $BACKEND_PID 2>/dev/null
    deactivate  # 停止时退出虚拟环境
    echo "✅ 所有服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 等待子进程
wait
