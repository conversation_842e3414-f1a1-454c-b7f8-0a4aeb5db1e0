ARG BUILD_HASH=dev-build
# Override at your own risk - non-root configurations are untested
ARG UID=0
ARG GID=0
ARG GIT_TAG=latest


######## WebUI frontend ########
FROM --platform=$BUILDPLATFORM openwebui-hk-registry-vpc.cn-hongkong.cr.aliyuncs.com/glm-chat/node:22-alpine3.20 AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN --mount=type=cache,target=/root/.npm \
    npm ci --prefer-offline
    

FROM --platform=$BUILDPLATFORM openwebui-hk-registry-vpc.cn-hongkong.cr.aliyuncs.com/glm-chat/node:22-alpine3.20 AS build
ARG BUILD_HASH
ARG GIT_TAG=latest

ENV NODE_ENV=production \
    # 降低内存占用和加速构建
    NODE_OPTIONS="--max-old-space-size=8192" 

ENV GIT_TAG=${GIT_TAG}

WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules

# 首先复制不经常变化的配置文件
COPY package.json package-lock.json tsconfig.json svelte.config.js vite.config.ts postcss.config.js tailwind.config.js i18next-parser.config.ts ./

COPY scripts/*.js ./scripts/

# 然后复制静态资源（变化不太频繁）
COPY static/ ./static/

# 复制前端源代码（变化较频繁）
COPY src/ ./src/

# 复制其他必要的前端文件
COPY .npmrc .prettierrc .eslintrc.cjs ./

ENV APP_BUILD_HASH=${BUILD_HASH}

RUN npm run build

RUN npm run upload:oss && npm run preload:cdn


# ----------------------------- NGINX -----------------------------
FROM --platform=$BUILDPLATFORM nginx:latest AS nginx

COPY --from=build /app/build /usr/share/nginx/html

COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]